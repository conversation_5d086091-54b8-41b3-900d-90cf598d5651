<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Logger\Logger.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MessagePack" Version="3.1.3" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="7.0.1" />
    <PackageReference Include="NetMQ" Version="4.0.1.12" />
    <PackageReference Include="NLog" Version="5.1.1" />
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="System.Reactive" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="MetallicMaterials-FatigueCrackGrowthMethod.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="MetallicMaterials-FatigueTesting-AnalysisOfData.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="MetallicMaterials-PlaneStrainFractureToughnessMethod.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="MetallicMaterials-QuasistaticFractureToughnessMethod.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
