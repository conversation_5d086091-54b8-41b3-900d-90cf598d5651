using System;
using System.Buffers;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;

namespace Consts;

/// <summary>
/// 实际有效的性能优化：对象池和缓存重用
/// </summary>
public static class JsonPerformanceOptimizer
{
    // 配置一个共享的 JsonSerializerOptions 实例以避免重复创建
    public static readonly JsonSerializerOptions OptimizedOptions = new JsonSerializerOptions
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        // 禁用一些不必要的功能以提升性能
        IgnoreReadOnlyProperties = true,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
        // 使用更高效的序列化模式
        WriteIndented = false
    };

    /// <summary>
    /// 高性能反序列化方法
    /// </summary>
    public static T? DeserializeOptimized<T>(ReadOnlySpan<byte> utf8Json) where T : class
    {
        return JsonSerializer.Deserialize<T>(utf8Json, OptimizedOptions);
    }

    /// <summary>
    /// 高性能反序列化方法（字符串版本）
    /// </summary>
    public static T? DeserializeOptimized<T>(string json) where T : class
    {
        return JsonSerializer.Deserialize<T>(json, OptimizedOptions);
    }
}

/// <summary>
/// 用于 CDataBlock 的内存池优化
/// </summary>
public static class CDataBlockPool
{
    private static readonly ArrayPool<byte> _bytePool = ArrayPool<byte>.Shared;
    
    /// <summary>
    /// 使用内存池进行高效的字符串到字节转换和反序列化
    /// </summary>
    public static CDataBlock? DeserializeWithPool(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        // 估算需要的字节数
        int maxByteCount = System.Text.Encoding.UTF8.GetMaxByteCount(json.Length);
        byte[] buffer = _bytePool.Rent(maxByteCount);
        
        try
        {
            // 将字符串转换为 UTF8 字节
            int actualByteCount = System.Text.Encoding.UTF8.GetBytes(json, buffer);
            
            // 使用字节数组进行反序列化（比字符串更高效）
            return JsonSerializer.Deserialize<CDataBlock>(
                buffer.AsSpan(0, actualByteCount), 
                JsonPerformanceOptimizer.OptimizedOptions);
        }
        finally
        {
            _bytePool.Return(buffer);
        }
    }
}

/// <summary>
/// 简单有效的性能统计器
/// </summary>
public static class PerformanceStats
{
    private static long _deserializationCount = 0;
    private static long _totalDeserializationTime = 0;
    
    public static void RecordDeserialization(long elapsedMilliseconds)
    {
        Interlocked.Increment(ref _deserializationCount);
        Interlocked.Add(ref _totalDeserializationTime, elapsedMilliseconds);
    }
    
    public static (long count, long totalTime, double avgTime) GetStats()
    {
        long count = Interlocked.Read(ref _deserializationCount);
        long totalTime = Interlocked.Read(ref _totalDeserializationTime);
        double avgTime = count > 0 ? (double)totalTime / count : 0;
        
        return (count, totalTime, avgTime);
    }
    
    public static void Reset()
    {
        Interlocked.Exchange(ref _deserializationCount, 0);
        Interlocked.Exchange(ref _totalDeserializationTime, 0);
    }
}
