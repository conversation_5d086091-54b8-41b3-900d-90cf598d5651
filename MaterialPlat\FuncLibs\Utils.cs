using System.Collections;
using NetMQ;
using static Logging.CCSSLogger;

namespace Utils;

public interface TaskRegistry<T>
{
    public T GetSubTaskByName(string name);
}

////// <summary>
/// Topic的工具类
/// </summary>
public static class TopicUtils
{
    /// <summary>
    /// 将进程ID和主题组合成一个总线主题
    /// </summary>
    /// <param name="processID">进程ID</param>
    /// <param name="topic">主题</param>
    /// <returns>总线主题</returns>
    public static string TopicWithProcessID(string processID, string topic)
        => processID + "-" + topic;

    /// <summary>
    /// 将模版ID、子任务ID和主题组合成一个总线主题
    /// </summary>
    /// <param name="processID">模版ID</param>
    /// <param name="subtaskID">子任务ID</param>
    /// <param name="topic">主题</param>
    /// <returns>总线主题</returns>
    public static string TopicWithTempSubtaskID(string processID, string? subtaskID, string topic)
        => processID + "-" + subtaskID + "-" + topic;

    /// <summary>
    /// 从总线主题中提取主题
    /// </summary>
    /// <param name="str">总线主题</param>
    /// <returns>主题</returns>
    public static string SplitTopicWithProcessID(string str)
        => str.Split('-').Last<string>();

    /// <summary>
    /// 子任务订阅daq采集的信号变量主题
    /// </summary>
    /// <param name="processID">模板id</param>
    /// <param name="daqCode">daqCode</param>
    /// <returns></returns>
    public static string TopicWithDaqSubtaskID(string processID, string? daqCode)
        => $"DAQ-{processID}-{daqCode}";
}


public static class UtilsExtensions
{

    /// <summary>
    /// 将 NetMQMessage 对象序列化为字节数组。
    /// </summary>
    /// <param name="message">要序列化的 NetMQMessage 对象。</param>
    /// <returns>表示序列化后的 NetMQMessage 对象的字节数组。</returns>
    public static byte[] SerializeNetMQMessage(this NetMQMessage message)
    {
        List<byte> result = new List<byte>();

        // 将帧数量添加到结果中
        result.AddRange(BitConverter.GetBytes(message.FrameCount));

        foreach (var frame in message)
        {
            // 将每个帧的大小和内容添加到结果中
            result.AddRange(BitConverter.GetBytes(frame.BufferSize));
            result.AddRange(frame.Buffer);
        }

        return result.ToArray();
    }


    /// <summary>
    /// 从字节数组中反序列化为 NetMQMessage 对象。
    /// </summary>
    /// <param name="bytes">要反序列化的字节数组。</param>
    /// <returns>反序列化后的 NetMQMessage 对象。</returns>
    public static NetMQMessage DeserializeNetMQMessage(this byte[] bytes)
    {
        NetMQMessage message = new NetMQMessage();
        int offset = 0;

        // 获取帧的数量
        int frameCount = BitConverter.ToInt32(bytes, offset);
        offset += sizeof(int);

        for (int i = 0; i < frameCount; i++)
        {
            // 获取每个帧的大小和内容
            int frameSize = BitConverter.ToInt32(bytes, offset);
            offset += sizeof(int);

            byte[] frameBytes = new byte[frameSize];
            Array.Copy(bytes, offset, frameBytes, 0, frameSize);
            offset += frameSize;

            message.Append(frameBytes);
        }

        return message;
    }

    /// debug
    public static T DebugIdentity<T>(this T value)
    {
        if (value is IEnumerable enumerableValue)
        {
            foreach (var item in enumerableValue)
            {
                Console.WriteLine(item);
            }
        }
        else
        {
            Console.WriteLine(value);
        }
        return value;
    }

    /// NetMQFrame的转换
    public static double toDouble(this byte[] bytes)
    => BitConverter.ToDouble(bytes);
    public static byte[] toBytes(this double value)
    => BitConverter.GetBytes(value);

    /// <summary>
    /// Task的异常处理
    /// </summary>
    public static Task FailFastOnException(this Task task)
    {
        task.ContinueWith(c => Environment.FailFast("Task faulted", c.Exception),
                          TaskContinuationOptions.OnlyOnFaulted); // Exception发生时执行
        return task;
    }

    public static Task IgnoreExceptions(this Task task)
    {
        task.ContinueWith(c => Logger.Info("exception:" + c.Exception),
                          TaskContinuationOptions.OnlyOnFaulted); // Exception发生时执行
        return task;
    }

    public static byte[] ToByteArray(this double[] doubleArray)
    {
        byte[] result = new byte[doubleArray.Length * sizeof(double)];

        for (int i = 0; i < doubleArray.Length; i++)
        {
            byte[] doubleBytes = BitConverter.GetBytes(doubleArray[i]);
            Array.Copy(doubleBytes, 0, result, i * sizeof(double), sizeof(double));
        }

        return result;
    }

    public static double[] ToDoubleArray(this byte[] byteArray)
    {
        int doubleCount = byteArray.Length / sizeof(double);
        double[] doubleArray = new double[doubleCount];

        for (int i = 0; i < doubleCount; i++)
        {
            doubleArray[i] = BitConverter.ToDouble(byteArray, i * sizeof(double));
        }
        return doubleArray;
    }

    public static bool IsDevEnv()
    {
        string? environmentName = Environment.GetEnvironmentVariable("DOTNET_DEV");
        return "DEV".Equals(environmentName);
    }
    /// <summary>
    /// 设置或清除 bit位
    /// </summary>
    /// <param name="data"></param>数据
    /// <param name="index"></param>bit Index  位索引值
    /// <param name="flag"></param>  true =置位  false=清除某位
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public static int SetbitValue(int data, int index, bool flag)
    {
        if (index > 32 || index < 0)
            throw new ArgumentOutOfRangeException();
        int v = 1 << index;
        if (flag)
            data = data | v;
        else
            data = data & ~v;
        //return flag ? (int)(data | v) : (int)(data & ~v);
        return data;
    }

    /// <summary>
    /// 读取bit位
    /// </summary>
    /// <param name="b"></param>
    /// <param name="index"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public static bool GetBit(int b, int index)
    {
        if (index > 32 || index < 0)
            throw new ArgumentOutOfRangeException();
        bool bit;
        if ((b & (1 << index)) != 0)
            bit = true;
        else
            bit = false;
        return bit;
    }

}
