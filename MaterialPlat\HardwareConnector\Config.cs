using IHardware;
using System.Reflection;
using System.Text.Json;
using static Logging.CCSSLogger;

/// <summary>
/// 硬件连接器的配置
/// </summary>
public record Config(string HardwareImplementation, string DllPath)
{
    /// <summary>
    /// 获取硬件实现。
    /// </summary>
    /// <remarks>
    /// 如果配置文件存在，此方法将从中读取硬件实现的类型名称。
    /// 如果没有配置文件或解析失败，将使用默认硬件实现"HwSim16.HwClassMain, HwSim16"。
    /// </remarks>
    /// <returns>硬件实现的实例。</returns>
    /// <exception cref="InvalidOperationException">如果找不到类型或无法创建类型的实例。</exception>
    public static Hw.IHardware GetHardware(string configPath)
    {
        // 默认值  TMC控制器  替换为config.json  TMCiPro.HwTMCiPro, TMCiPro
        string hardwareImplementation = "HwSim16.HwClassMain, HwSim16";
        Assembly? assembly = null;

        //当前运行的路径
        string currentDirectory = AppDomain.CurrentDomain.BaseDirectory;
        string configFilePath = Path.Join(currentDirectory, "config.json");
        Logger.Info("配置文件的路径configFilePath：" + configFilePath);


        if (File.Exists(configFilePath))
        {
            string jsonString = File.ReadAllText(configFilePath);
            var config = JsonSerializer.Deserialize<Config>(jsonString);
            if (config != null)
            {
                hardwareImplementation = config.HardwareImplementation;

                string relativePath = config.DllPath;
                string absolutePath = Path.Join(currentDirectory, relativePath);// 将相对路径转换为绝对路径
                if (File.Exists(absolutePath)) // 检查文件是否存在
                {
                    Logger.Info("硬件dll的absolutePath：" + absolutePath);
                    assembly = Assembly.LoadFrom(absolutePath);
                }
                else
                {
                    throw new InvalidOperationException($"找不到对应的dll {absolutePath}");
                }
            }
        }


        // 使用反射从配置中创建硬件对象
        Type? hardwareType = Type.GetType(hardwareImplementation);
        Logger.Info("HardWareConnector加载的类名：" + hardwareType?.FullName);
        if (hardwareType == null)
        {
            if (null != assembly)
            {
                hardwareType = assembly.GetType(hardwareImplementation);
            }
            if (null == hardwareType)
            {
                throw new InvalidOperationException($"找不到对应的硬件 {hardwareImplementation}");
            }

        }

        Hw.IHardware? hardware = (Hw.IHardware)Activator.CreateInstance(hardwareType)!
            ?? throw new InvalidOperationException($"创建硬件 {hardwareImplementation} 实例失败");
        return hardware;
    }
}
