using CCSS.HWConnector;
using static Logging.CCSSLogger;
using IHardware;
using NetMQ;
using Utils;
using Consts;
using System.Text;
using System.Text.Json;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System;
using System.Text.Json.Serialization;
using System.Diagnostics;


namespace Events;


public static class EventHandlers
{
    /// <summary>
    /// 控制DebugInfoPrint是否激活，0=不打印，1=打印
    /// </summary>
    public static int DebugIsActive { get; set; } = 0;
    
    /// <summary>
    /// 控制DebugInfoPrint的打印间隔，每隔多少次调用才打印一次
    /// </summary>
    public static int DebugInterval { get; set; } = 10;
    
    /// <summary>
    /// 控制调试信息打印模式：
    /// 0 = 不打印Count相关信息
    /// 1-9 = 打印基础Count信息（CDataBlock的各种Count）
    /// 10-29 = 打印ServoData详细信息（10=基础DataCount，11=包含CData的SensorCount）
    /// 30-39 = 打印TempData详细信息（30=基础DataCount，31=包含CData的SensorCount）
    /// 40-49 = 打印CreepData详细信息（40=基础DataCount，41=包含CData的SensorCount）
    /// 50 = 打印ADData详细信息（包含DataCount）
    /// 60 = 打印BitIn数组信息（最后一个数据）
    /// </summary>
    public static int DebugPrintMode { get; set; } = 0;
    
    /// <summary>
    /// 控制CData详细信息打印的索引：
    /// -1 = 只打印SensorCount信息
    /// >=0 = 打印指定索引的CData完整信息（包括所有属性和传感器数据）
    /// </summary>
    public static int DebugPrintIndex { get; set; } = -1;
    
    /// <summary>
    /// 内部计数器，用于跟踪DebugInfoPrint的调用次数
    /// </summary>
    private static int debugCallCounter = 0;
    
    /// <summary>
    /// 用于统计DataBlockHandler函数执行时间的Stopwatch
    /// </summary>
    private static readonly Stopwatch dataBlockHandlerStopwatch = new Stopwatch();
    
    /// <summary>
    /// DataBlockHandler函数在当前时间间隔内的累计执行时间（毫秒）
    /// </summary>
    private static long intervalTotalElapsedMs = 0;
    
    /// <summary>
    /// DataBlock.Servo[0].DataCount的累加值
    /// </summary>
    private static long totalDataCount = 0;

    private static long totalSevorDataCount = 0;
    private static long totalSevorDataLength = 0;

    public static Hw.CDataBlock FilterCDataBlockByCount(Hw.CDataBlock original)
    {
        // 创建一个新的CDataBlock对象
        Hw.CDataBlock filtered = new Hw.CDataBlock
        {
            ServoChCount = original.ServoChCount,
            TempChCount = original.TempChCount,
            CreepChCount = original.CreepChCount,
            InCount = original.InCount,
            OutCount = original.OutCount,
            ADCount = original.ADCount,
        };

        // 一个通用函数用于过滤SingleChDatas数组
        Func<Hw.SingleChDatas[], int, Hw.SingleChDatas[]> filterSingleChDatasArray = (originalArray, count) =>
        {
            //Logger.Debug($"cout: {count}");
            int idx = 0;
            Hw.SingleChDatas[] filteredArray = new Hw.SingleChDatas[count];
            for (int i = 0; i < count; i++)
                filteredArray[i] = new Hw.SingleChDatas(0, 0);

            foreach (var singleChDatas in filteredArray)
            {
                try
                {
                    var originData = originalArray[idx];
                    //Logger.Debug($"chdata count:{originData.DataCount}");
                    singleChDatas.DataCount = originData.DataCount;
                    singleChDatas.ChData = originData.ChData.Take(originData.DataCount).ToArray();
                    idx++;
                }
                catch (Exception e)
                {
                    Logger.Error("eventhandler中数组越界:" + idx);
                    Logger.Error("错误信息为：" + e.Message);
                }

            }
            return filteredArray;
        };

        // 根据ServoChCount过滤ServoData
        filtered.ServoData = filterSingleChDatasArray(original.ServoData!, original.ServoChCount);

        // 根据TempChCount过滤TempData
        filtered.TempData = filterSingleChDatasArray(original.TempData!, original.TempChCount);

        // 根据CreepChCount过滤CreepData
        filtered.CreepData = filterSingleChDatasArray(original.CreepData!, original.CreepChCount);

        // 根据InCount和OutCount过滤BitIn和BitOut
        filtered.BitIn = original.BitIn?.Take(original.InCount).ToArray();
        filtered.BitOut = original.BitOut?.Take(original.OutCount).ToArray();

        // 根据ADCount过滤ADData
        if (original.ADData != null && original.ADData.Length > original.ADCount)
        {
            filtered.ADData = original.ADData.Take(original.ADCount).ToArray();
            foreach (var singleAD in filtered.ADData)
            {
                if (singleAD.Sensor != null && singleAD.Sensor.Length > singleAD.DataCount)
                {
                    singleAD.Sensor = singleAD.Sensor.Take(singleAD.DataCount).ToArray();
                    singleAD.MaxSensor = singleAD.MaxSensor?.Take(singleAD.DataCount).ToArray();
                    singleAD.MinSensor = singleAD.MinSensor?.Take(singleAD.DataCount).ToArray();
                    singleAD.Time = singleAD.Time?.Take(singleAD.DataCount).ToArray();
                }
            }
        }
        else
        {
            filtered.ADData = original.ADData;
        }

        return filtered;
    }


    static EventHandlers() =>
        NetMQMsgPublisherSubject.Subscribe(msg => HWDataClient.sendMsg(msg));
    private static readonly Subject<NetMQMessage> NetMQMsgPublisherSubject = new();
    private static void SendMsg(NetMQMessage msg) =>
                NetMQMsgPublisherSubject.OnNext(msg);


    public static int servoDAQRate = 0;

    private static readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
    {
        WriteIndented = false,
    };
    
    /// <summary>
    /// 调试信息打印函数，用于输出CDataBlock的详细Count信息
    /// 根据DebugPrintMode的值控制不同级别的调试信息输出：
    /// 
    /// DebugPrintMode = 0: 不打印任何Count相关信息
    /// DebugPrintMode = 1-9: 打印CDataBlock基础Count信息
    ///   - ServoChCount, TempChCount, CreepChCount
    ///   - InCount, OutCount, ADCount
    /// 
    /// DebugPrintMode = 10-29: 打印ServoData详细信息
    ///   - 10: 打印每个ServoData轴的DataCount
    ///   - 11: 根据DebugPrintIndex打印CData详细信息
    /// 
    /// DebugPrintMode = 30-39: 打印TempData详细信息
    ///   - 30: 打印每个TempData轴的DataCount
    ///   - 31: 根据DebugPrintIndex打印CData详细信息
    /// 
    /// DebugPrintMode = 40-49: 打印CreepData详细信息
    ///   - 40: 打印每个CreepData轴的DataCount
    ///   - 41: 根据DebugPrintIndex打印CData详细信息
    /// 
    /// DebugPrintMode == 50: 打印ADData详细信息
    ///   - 打印每个ADData轴的DataCount
    /// DebugPrintMode == 60: 打印BitIn数组信息
    ///   - 打印BitIn数组的最后一个数据
    /// 
    /// DebugPrintIndex控制CData详细信息打印：
    ///   - -1: 只打印SensorCount信息
    ///   - >=0: 打印指定索引的CData完整信息（包括所有属性和传感器数据）
    /// </summary>
    /// <param name="dataBlock">要调试的CDataBlock数据块</param>
    private static void DebugInfoPrint(Hw.CDataBlock dataBlock)
    {
        try
        {
            var sb = new StringBuilder();
            sb.AppendLine($"=== DataBlockHandler Debug Info (调用次数: {debugCallCounter}) ===");
            
            // 根据DebugPrintMode控制是否打印Count相关信息
            if (DebugPrintMode != 0)
            {
                // 打印CDataBlock的所有Count信息
                sb.AppendLine($"CDataBlock Counts:");
                sb.AppendLine($"  ServoChCount: {dataBlock.ServoChCount}");
                sb.AppendLine($"  TempChCount: {dataBlock.TempChCount}");
                sb.AppendLine($"  CreepChCount: {dataBlock.CreepChCount}");
                sb.AppendLine($"  InCount: {dataBlock.InCount}");
                sb.AppendLine($"  OutCount: {dataBlock.OutCount}");
                sb.AppendLine($"  ADCount: {dataBlock.ADCount}");
                
                // 打印ServoData中每个SingleChDatas的DataCount
                if (dataBlock.ServoData != null && DebugPrintMode >= 10 && DebugPrintMode < 30)
                {
                    sb.AppendLine($"ServoData SingleChDatas DataCounts:");
                    for (int i = 0; i < dataBlock.ServoData.Length && i < dataBlock.ServoChCount; i++)
                    {
                        if (dataBlock.ServoData[i] != null)
                        {
                            sb.AppendLine($"  Servo[{i}].DataCount: {dataBlock.ServoData[i].DataCount}");
                            
                            // 打印该轴下CData的详细信息
                            if (dataBlock.ServoData[i].ChData != null && DebugPrintMode == 11)
                            {
                                if (DebugPrintIndex == -1)
                                {
                                    // 只打印SensorCount信息
                                    for (int j = 0; j < dataBlock.ServoData[i].ChData.Length && j < dataBlock.ServoData[i].DataCount; j++)
                                    {
                                        if (dataBlock.ServoData[i].ChData[j] != null)
                                        {
                                            sb.AppendLine($"    Servo[{i}].ChData[{j}].SensorCount: {dataBlock.ServoData[i].ChData[j].SensorCount}");
                                        }
                                    }
                                }
                                else if (DebugPrintIndex >= 0 && DebugPrintIndex < dataBlock.ServoData[i].ChData.Length && DebugPrintIndex < dataBlock.ServoData[i].DataCount)
                                {
                                    // 打印指定索引的CData完整信息
                                    var cData = dataBlock.ServoData[i].ChData[DebugPrintIndex];
                                    if (cData != null)
                                    {
                                        totalSevorDataCount += dataBlock.ServoData[i].DataCount;
                                        totalSevorDataLength += dataBlock.ServoData[i].ChData.Length;
                                        sb.AppendLine($"    Servo[{i}].ChData totalSevorDataCount: {totalSevorDataCount}, totalSevorDataLength: {totalSevorDataLength}");
                                        sb.AppendLine($"    Servo[{i}].ChData[{DebugPrintIndex}] 完整信息:");
                                        sb.AppendLine($"      ActiveCtrl: {cData.ActiveCtrl}");
                                        sb.AppendLine($"      Command: {cData.Command}");
                                        sb.AppendLine($"      Feedback: {cData.Feedback}");
                                        sb.AppendLine($"      Output: {cData.Output}");
                                        sb.AppendLine($"      Timer: {cData.Timer}");
                                        sb.AppendLine($"      BlockCycles: {cData.BlockCycles}");
                                        sb.AppendLine($"      Cycles: {cData.Cycles}");
                                        sb.AppendLine($"      BlockLine: {cData.BlockLine}");
                                        sb.AppendLine($"      CmdFrequency: {cData.CmdFrequency}");
                                        sb.AppendLine($"      UpperLimits: {cData.UpperLimits}");
                                        sb.AppendLine($"      UpperSft: {cData.UpperSft}");
                                        sb.AppendLine($"      LowerLimits: {cData.LowerLimits}");
                                        sb.AppendLine($"      LowerSft: {cData.LowerSft}");
                                        sb.AppendLine($"      SensorCount: {cData.SensorCount}");
                                        sb.AppendLine($"      InSignals: {cData.InSignals}");
                                        
                                        // 打印传感器数据
                                        if (cData.Sensor != null && cData.Sensor.Length > 0)
                                        {
                                            sb.AppendLine($"      Sensor数据: [{string.Join(", ", cData.Sensor.Take(Math.Min(10, cData.Sensor.Length)))}]{(cData.Sensor.Length > 10 ? "..." : "")}");
                                        }
                                        if (cData.MaxSensor != null && cData.MaxSensor.Length > 0)
                                        {
                                            sb.AppendLine($"      MaxSensor数据: [{string.Join(", ", cData.MaxSensor.Take(Math.Min(10, cData.MaxSensor.Length)))}]{(cData.MaxSensor.Length > 10 ? "..." : "")}");
                                        }
                                        if (cData.MinSensor != null && cData.MinSensor.Length > 0)
                                        {
                                            sb.AppendLine($"      MinSensor数据: [{string.Join(", ", cData.MinSensor.Take(Math.Min(10, cData.MinSensor.Length)))}]{(cData.MinSensor.Length > 10 ? "..." : "")}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 打印TempData中每个SingleChDatas的DataCount
                if (dataBlock.TempData != null && DebugPrintMode >= 30 && DebugPrintMode < 40)
                {
                    sb.AppendLine($"TempData SingleChDatas DataCounts:");
                    for (int i = 0; i < dataBlock.TempData.Length && i < dataBlock.TempChCount; i++)
                    {
                        if (dataBlock.TempData[i] != null)
                        {
                            sb.AppendLine($"  Temp[{i}].DataCount: {dataBlock.TempData[i].DataCount}");
                            
                            // 打印该轴下CData的详细信息
                            if (dataBlock.TempData[i].ChData != null && DebugPrintMode == 31)
                            {
                                if (DebugPrintIndex == -1)
                                {
                                    // 只打印SensorCount信息
                                    for (int j = 0; j < dataBlock.TempData[i].ChData.Length && j < dataBlock.TempData[i].DataCount; j++)
                                    {
                                        if (dataBlock.TempData[i].ChData[j] != null)
                                        {
                                            sb.AppendLine($"    Temp[{i}].ChData[{j}].SensorCount: {dataBlock.TempData[i].ChData[j].SensorCount}");
                                        }
                                    }
                                }
                                else if (DebugPrintIndex >= 0 && DebugPrintIndex < dataBlock.TempData[i].ChData.Length && DebugPrintIndex < dataBlock.TempData[i].DataCount)
                                {
                                    // 打印指定索引的CData完整信息
                                    var cData = dataBlock.TempData[i].ChData[DebugPrintIndex];
                                    if (cData != null)
                                    {
                                        sb.AppendLine($"    Temp[{i}].ChData[{DebugPrintIndex}] 完整信息:");
                                        sb.AppendLine($"      ActiveCtrl: {cData.ActiveCtrl}");
                                        sb.AppendLine($"      Command: {cData.Command}");
                                        sb.AppendLine($"      Feedback: {cData.Feedback}");
                                        sb.AppendLine($"      Output: {cData.Output}");
                                        sb.AppendLine($"      Timer: {cData.Timer}");
                                        sb.AppendLine($"      BlockCycles: {cData.BlockCycles}");
                                        sb.AppendLine($"      Cycles: {cData.Cycles}");
                                        sb.AppendLine($"      BlockLine: {cData.BlockLine}");
                                        sb.AppendLine($"      CmdFrequency: {cData.CmdFrequency}");
                                        sb.AppendLine($"      UpperLimits: {cData.UpperLimits}");
                                        sb.AppendLine($"      UpperSft: {cData.UpperSft}");
                                        sb.AppendLine($"      LowerLimits: {cData.LowerLimits}");
                                        sb.AppendLine($"      LowerSft: {cData.LowerSft}");
                                        sb.AppendLine($"      SensorCount: {cData.SensorCount}");
                                        sb.AppendLine($"      InSignals: {cData.InSignals}");
                                        
                                        // 打印传感器数据
                                        if (cData.Sensor != null && cData.Sensor.Length > 0)
                                        {
                                            sb.AppendLine($"      Sensor数据: [{string.Join(", ", cData.Sensor.Take(Math.Min(10, cData.Sensor.Length)))}]{(cData.Sensor.Length > 10 ? "..." : "")}");
                                        }
                                        if (cData.MaxSensor != null && cData.MaxSensor.Length > 0)
                                        {
                                            sb.AppendLine($"      MaxSensor数据: [{string.Join(", ", cData.MaxSensor.Take(Math.Min(10, cData.MaxSensor.Length)))}]{(cData.MaxSensor.Length > 10 ? "..." : "")}");
                                        }
                                        if (cData.MinSensor != null && cData.MinSensor.Length > 0)
                                        {
                                            sb.AppendLine($"      MinSensor数据: [{string.Join(", ", cData.MinSensor.Take(Math.Min(10, cData.MinSensor.Length)))}]{(cData.MinSensor.Length > 10 ? "..." : "")}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 打印CreepData中每个SingleChDatas的DataCount
                if (dataBlock.CreepData != null && DebugPrintMode >= 40 && DebugPrintMode < 50)
                {
                    sb.AppendLine($"CreepData SingleChDatas DataCounts:");
                    for (int i = 0; i < dataBlock.CreepData.Length && i < dataBlock.CreepChCount; i++)
                    {
                        if (dataBlock.CreepData[i] != null)
                        {
                            sb.AppendLine($"  Creep[{i}].DataCount: {dataBlock.CreepData[i].DataCount}");
                            
                            // 打印该轴下CData的详细信息
                            if (dataBlock.CreepData[i].ChData != null && DebugPrintMode == 41)
                            {
                                if (DebugPrintIndex == -1)
                                {
                                    // 只打印SensorCount信息
                                    for (int j = 0; j < dataBlock.CreepData[i].ChData.Length && j < dataBlock.CreepData[i].DataCount; j++)
                                    {
                                        if (dataBlock.CreepData[i].ChData[j] != null)
                                        {
                                            sb.AppendLine($"    Creep[{i}].ChData[{j}].SensorCount: {dataBlock.CreepData[i].ChData[j].SensorCount}");
                                        }
                                    }
                                }
                                else if (DebugPrintIndex >= 0 && DebugPrintIndex < dataBlock.CreepData[i].ChData.Length && DebugPrintIndex < dataBlock.CreepData[i].DataCount)
                                {
                                    // 打印指定索引的CData完整信息
                                    var cData = dataBlock.CreepData[i].ChData[DebugPrintIndex];
                                    if (cData != null)
                                    {
                                        sb.AppendLine($"    Creep[{i}].ChData[{DebugPrintIndex}] 完整信息:");
                                        sb.AppendLine($"      ActiveCtrl: {cData.ActiveCtrl}");
                                        sb.AppendLine($"      Command: {cData.Command}");
                                        sb.AppendLine($"      Feedback: {cData.Feedback}");
                                        sb.AppendLine($"      Output: {cData.Output}");
                                        sb.AppendLine($"      Timer: {cData.Timer}");
                                        sb.AppendLine($"      BlockCycles: {cData.BlockCycles}");
                                        sb.AppendLine($"      Cycles: {cData.Cycles}");
                                        sb.AppendLine($"      BlockLine: {cData.BlockLine}");
                                        sb.AppendLine($"      CmdFrequency: {cData.CmdFrequency}");
                                        sb.AppendLine($"      UpperLimits: {cData.UpperLimits}");
                                        sb.AppendLine($"      UpperSft: {cData.UpperSft}");
                                        sb.AppendLine($"      LowerLimits: {cData.LowerLimits}");
                                        sb.AppendLine($"      LowerSft: {cData.LowerSft}");
                                        sb.AppendLine($"      SensorCount: {cData.SensorCount}");
                                        sb.AppendLine($"      InSignals: {cData.InSignals}");
                                        
                                        // 打印传感器数据
                                        if (cData.Sensor != null && cData.Sensor.Length > 0)
                                        {
                                            sb.AppendLine($"      Sensor数据: [{string.Join(", ", cData.Sensor.Take(Math.Min(10, cData.Sensor.Length)))}]{(cData.Sensor.Length > 10 ? "..." : "")}");
                                        }
                                        if (cData.MaxSensor != null && cData.MaxSensor.Length > 0)
                                        {
                                            sb.AppendLine($"      MaxSensor数据: [{string.Join(", ", cData.MaxSensor.Take(Math.Min(10, cData.MaxSensor.Length)))}]{(cData.MaxSensor.Length > 10 ? "..." : "")}");
                                        }
                                        if (cData.MinSensor != null && cData.MinSensor.Length > 0)
                                        {
                                            sb.AppendLine($"      MinSensor数据: [{string.Join(", ", cData.MinSensor.Take(Math.Min(10, cData.MinSensor.Length)))}]{(cData.MinSensor.Length > 10 ? "..." : "")}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 打印ADData中每个SingleADDatas的DataCount
                if (dataBlock.ADData != null && DebugPrintMode == 50)
                {
                    sb.AppendLine($"ADData SingleADDatas DataCounts:");
                    for (int i = 0; i < dataBlock.ADData.Length && i < dataBlock.ADCount; i++)
                    {
                        if (dataBlock.ADData[i] != null)
                        {
                            sb.AppendLine($"  AD[{i}].DataCount: {dataBlock.ADData[i].DataCount}");
                        }
                    }
                }

                // 保留原有的BitIn信息
                if(DebugPrintMode == 60)
                {
                    if (dataBlock?.BitIn != null)
                    {
                        int lastBitInValue = dataBlock.BitIn[dataBlock.InCount - 1];
                        sb.AppendLine($"BitIn数组最后一个数据: {lastBitInValue}");
                    }
                    else
                    {
                        sb.AppendLine($"BitIn数组为空或null");
                    }
                }
            } // 结束DebugPrintMode控制的Count信息打印

            sb.AppendLine($"=== Debug Info End ===");
            Logger.Error(sb.ToString());
        }
        catch (Exception ex)
        {
            Logger.Error($"DebugInfoPrint执行异常: {ex.Message}");
        }
    }

    public static int DataBlockHandler(int _sender, ref Hw.CDataBlock DataBlock)
    {   
        try
        {
            // 检查是否激活调试打印
            if (DebugIsActive != 0)
            {
                // 开始统计执行时间
                dataBlockHandlerStopwatch.Restart();

                // 累加DataCount值
                if (DataBlock.ServoData != null && DataBlock.ServoData.Length > 0)
                {
                    totalDataCount += DataBlock.ServoData[0].DataCount;
                }
                
                // 增加调用计数器，防止int溢出
                debugCallCounter++;
                if (debugCallCounter < 0) // 检测到溢出，重置计数器
                {
                    debugCallCounter = 1;
                }
                
                // 检查是否达到打印间隔
                if (debugCallCounter % DebugInterval == 0)
                {
                    DebugInfoPrint(DataBlock);
                    Logger.Error($"DataCount累加值: {totalDataCount} (间隔: {DebugInterval} 次调用)");
                    // 重置累加值
                    totalDataCount = 0;
                }
            } else {
                totalSevorDataCount = 0;
                totalSevorDataLength = 0;
                debugCallCounter = 1;
            }
            
            NetMQMessage msg = new();
            // header
            msg.Append(BitConverter.GetBytes((int)HardwareDataMsg.DataBlock));

            // 使用FlatCDataBlock进行高性能序列化，输出二进制数据
            var flatFilteredData = FilterFlatCDataBlockByCount(DataBlock);
            var messagePackBytes = Consts.MessagePackSerializer.Serialize(flatFilteredData);
            msg.Append(messagePackBytes);
            
            // 使用FlatCDataBlock版本的数据检查
            if (CanSendFlatMsg(flatFilteredData))
                SendMsg(msg);

            // Logger.Info("DoOnDataBlockEvent call back  Timer:" + DataBlock.CreepData[0].DataCount == null ? 0 : DataBlock.CreepData[0].DataCount);
            //if (DataBlock.ServoData[0].ChData[0].Sensor[0] != 0 && DataBlock.ServoData[0].ChData[0].Sensor[1] != 0 && DataBlock.ServoData[0].ChData[0].Sensor[2] != 0)
            //{
            //    Logger.Debug("DoOnDataBlockEvent call back");
            //    Logger.Debug("DoOnDataBlockEvent call back  Timer:" + DataBlock.ServoData[0].ChData[0].Timer);
            //    Logger.Debug("DoOnDataBlockEvent call back  DAQRate:" + servoDAQRate);
            //    Logger.Debug("DoOnDataBlockEvent call back  Move:" + DataBlock.ServoData[0].ChData[0].Sensor[0]);
            //    Logger.Debug("DoOnDataBlockEvent call back  Load:" + DataBlock.ServoData[0].ChData[0].Sensor[1]);
            //    Logger.Debug("DoOnDataBlockEvent call back  Stren:" + DataBlock.ServoData[0].ChData[0].Sensor[2]);
            //}
            
            return 0;
        }
        finally
        {
            if (DebugIsActive != 0)
            {       
                // 停止计时并累计执行时间
                dataBlockHandlerStopwatch.Stop();

                intervalTotalElapsedMs += dataBlockHandlerStopwatch.ElapsedMilliseconds;
                // 检查是否达到打印间隔
                if (debugCallCounter % DebugInterval == 0)
                {
                    Logger.Error($"DataBlockHandler 时间间隔内累计执行时间: {intervalTotalElapsedMs} ms (间隔: {DebugInterval} 次调用)");
                    // 重置累计时间
                    intervalTotalElapsedMs = 0;
                }
            }
        }
    }

    // 设备状态回调
    // TODO  SubID 这个参数不确定客户的dll中的回调是否封装，有可能收到的全都是 0
    public static int LineEventHandler(int SubId, ref int LineState)
    {
        NetMQMessage msg = new();
        // header
        msg.Append(BitConverter.GetBytes((int)Consts.HardwareDataMsg.Line));
        //sender 其实是设备ID  即subId
        msg.Append(BitConverter.GetBytes(SubId));
        msg.Append(BitConverter.GetBytes(LineState));
        SendMsg(msg);
        Logger.Error($"LineEventHandler call back  SubId：{SubId}  LineState：{LineState}");
        return 0;
    }

    // 设备命令执行完成回调
    public static int PosMsgEventHandler(int _sender, ref Hw.OnPosMsg PosMsg)
    {
        NetMQMessage msg = new();
        msg.Append(BitConverter.GetBytes((int)Consts.HardwareDataMsg.PosMsg));
        msg.Append(BitConverter.GetBytes(PosMsg.HwType));
        msg.Append(BitConverter.GetBytes(PosMsg.DeviceID));
        msg.Append(BitConverter.GetBytes(PosMsg.ADSensorID));
        msg.Append(BitConverter.GetBytes(PosMsg.Time));
        msg.Append(BitConverter.GetBytes(PosMsg.Position));
        msg.Append(BitConverter.GetBytes(PosMsg.DestPosition));
        msg.Append(BitConverter.GetBytes(PosMsg.CMDType));
        msg.Append(BitConverter.GetBytes(PosMsg.SingleReached));
        msg.Append(BitConverter.GetBytes(PosMsg.AllReached));
        msg.Append(BitConverter.GetBytes(PosMsg.Tan));
        SendMsg(msg);
        Logger.Info("PosMsgEventHandler call back");
        Logger.Info("PosMsgEventHandler call back  Consts:" + (int)Consts.HardwareDataMsg.PosMsg);
        Logger.Info("PosMsgEventHandler call back  HwType:" + PosMsg.HwType);
        Logger.Info("PosMsgEventHandler call back  DeviceID:" + PosMsg.DeviceID);
        Logger.Info("PosMsgEventHandler call back  ADSensorID:" + PosMsg.ADSensorID);
        Logger.Info("PosMsgEventHandler call back  Time:" + PosMsg.Time);
        Logger.Info("PosMsgEventHandler call back  Position:" + PosMsg.Position);
        Logger.Info("PosMsgEventHandler call back  DestPosition:" + PosMsg.DestPosition);
        Logger.Info("PosMsgEventHandler call back  CMDType:" + PosMsg.CMDType);
        Logger.Info("PosMsgEventHandler call back  SingleReached:" + PosMsg.SingleReached);
        Logger.Info("PosMsgEventHandler call back  AllReached:" + PosMsg.AllReached);
        Logger.Error("PosMsgEventHandler call back  Tan:" + PosMsg.Tan);
        return 0;
    }

    // 设备系统消息回调
    // 类型有   0：状态信息(0)  1：错误信息(10000)  2.警告(10001)
    public static int SystemMsgEventHandler(int bak, ref Hw.OnSystemMsg SystemMsg)
    {
        // 20250307, 蠕变测试，两(多)个轴同时回调，需要快照一份数据避免mq报错。问题原因：云效XJRS-4854
        int hwType = SystemMsg.HwType;
        int deviceID = SystemMsg.DeviceID;
        int adSensorID = SystemMsg.ADSensorID;
        int msgNumber = (int)SystemMsg.MsgNumber;
        string text = SystemMsg.Text;
        double time = SystemMsg.Time;
        NetMQMessage msg = new();
        // header
        msg.Append(BitConverter.GetBytes((int)Consts.HardwareDataMsg.SystemMsg));
        msg.Append(BitConverter.GetBytes(hwType));
        msg.Append(BitConverter.GetBytes(deviceID));
        msg.Append(BitConverter.GetBytes(adSensorID));
        msg.Append(BitConverter.GetBytes(msgNumber));
        msg.Append(Encoding.UTF8.GetBytes(text));
        msg.Append(time.toBytes());
        SendMsg(msg);
        Logger.Info("SystemMsgEventHandler call back");
        Logger.Info("SystemMsgEventHandler call back Text:" + text);
        return 0;
    }

    // 手控盒信息
    public static int RmcMsgEventHandler(int bak, ref Hw.OnHwMsg HwEvent)
    {
        NetMQMessage msg = new();
        // header
        msg.Append(BitConverter.GetBytes((int)Consts.HardwareDataMsg.HwMsg));
        msg.Append(BitConverter.GetBytes(HwEvent.HwType));
        msg.Append(BitConverter.GetBytes(HwEvent.HwID));
        msg.Append(Encoding.UTF8.GetBytes(HwEvent.BackString));
        SendMsg(msg);
        Logger.Error($"RmcMsgEventHandler call back msg:{bak} HwEventHwtype:{HwEvent.HwType} HwEventHwID:{HwEvent.HwID} HwEventBackString:{HwEvent.BackString}");
        return 0;
    }

    /// <summary>
    /// 判断这条数据是否需要发送,全都是0 则不发送
    /// </summary>
    /// <param name="cDataBlock"></param>
    /// <returns>返回false则不发送数据</returns>
    private static bool CanSendMsg(Hw.CDataBlock cDataBlock)
    {
        //类轴的数据全为0，则不发送
        bool allZero1 = (cDataBlock.ServoData == null || cDataBlock.ServoData!.Length == 0) ? true : cDataBlock.ServoData[0].DataCount == 0;
        bool allZero2 = (cDataBlock.CreepData == null || cDataBlock.CreepData!.Length == 0) ? true : cDataBlock.CreepData[0].DataCount == 0;
        bool allZero3 = (cDataBlock.TempData == null || cDataBlock.TempData!.Length == 0) ? true : cDataBlock.TempData[0].DataCount == 0;

        if (allZero1 && allZero2 && allZero3)
        {
            Logger.Error("所有轴的数据都为0!");
            return false;
        }
        return true;
    }

    /// <summary>
    /// 判断这条FlatCDataBlock数据是否需要发送,全都是0 则不发送
    /// </summary>
    /// <param name="flatDataBlock"></param>
    /// <returns>返回false则不发送数据</returns>
    private static bool CanSendFlatMsg(Consts.FlatCDataBlock flatDataBlock)
    {
        if (flatDataBlock.ServoData != null && flatDataBlock.ServoData!.Length != 0)
        {
            for (int i = 0; i < flatDataBlock.ServoData.Length; i++)
            {
                if (flatDataBlock.ServoData[i].DataCount != 0)
                {
                    return true;
                }
            }
        }
        if (flatDataBlock.CreepData != null && flatDataBlock.CreepData!.Length != 0)
        {
            for (int i = 0; i < flatDataBlock.CreepData.Length; i++)
            {
                if (flatDataBlock.CreepData[i].DataCount != 0)
                {
                    return true;
                }
            }
        }
        if (flatDataBlock.TempData != null && flatDataBlock.TempData!.Length != 0)
        {
            for (int i = 0; i < flatDataBlock.TempData.Length; i++)
            {
                if (flatDataBlock.TempData[i].DataCount != 0)
                {
                    return true;
                }
            }
        }
        Logger.Error("所有轴的数据都为0!");
        return false;
    }

    public static Consts.FlatCDataBlock FilterFlatCDataBlockByCount(Hw.CDataBlock original)
    {
        // 先过滤原始数据
        var filteredOriginal = FilterCDataBlockByCount(original);

        var flatDataBlock = Consts.FlatCDataBlock.FromCDataBlock(new Consts.CDataBlock
        {
            ServoChCount = filteredOriginal.ServoChCount,
            TempChCount = filteredOriginal.TempChCount,
            CreepChCount = filteredOriginal.CreepChCount,
            InCount = filteredOriginal.InCount,
            OutCount = filteredOriginal.OutCount,
            ADCount = filteredOriginal.ADCount,
            ServoData = ConvertToConstsFormat(filteredOriginal.ServoData),
            TempData = ConvertToConstsFormat(filteredOriginal.TempData),
            CreepData = ConvertToConstsFormat(filteredOriginal.CreepData),
            BitIn = filteredOriginal.BitIn,
            BitOut = filteredOriginal.BitOut,
            ADData = ConvertADDataToConstsFormat(filteredOriginal.ADData)
        });
        
        return flatDataBlock;
    }

    private static Consts.SingleChDatas[]? ConvertToConstsFormat(Hw.SingleChDatas[]? hwData)
    {
        if (hwData == null) return null;
        
        return hwData.Select(hw => new Consts.SingleChDatas
        {
            DataCount = hw.DataCount,
            ChData = hw.ChData?.Select(cd => new Consts.CData
            {
                ActiveCtrl = cd.ActiveCtrl,
                Command = cd.Command,
                Feedback = cd.Feedback,
                Output = cd.Output,
                Timer = cd.Timer,
                BlockCycles = cd.BlockCycles,
                Cycles = cd.Cycles,
                BlockLine = cd.BlockLine,
                CmdFrequency = cd.CmdFrequency,
                UpperLimits = cd.UpperLimits,
                UpperSft = cd.UpperSft,
                LowerLimits = cd.LowerLimits,
                LowerSft = cd.LowerSft,
                SensorCount = cd.SensorCount,
                Sensor = cd.Sensor,
                MaxSensor = cd.MaxSensor,
                MinSensor = cd.MinSensor,
                InSignals = cd.InSignals
            }).ToArray()
        }).ToArray();
    }

    private static Consts.SingleADDatas[]? ConvertADDataToConstsFormat(Hw.SingleADDatas[]? hwAdData)
    {
        if (hwAdData == null) return null;
        
        return hwAdData.Select(ad => new Consts.SingleADDatas
        {
            DataCount = ad.DataCount,
            Sensor = ad.Sensor,
            MaxSensor = ad.MaxSensor,
            MinSensor = ad.MinSensor,
            Time = ad.Time
        }).ToArray();
    }

}
