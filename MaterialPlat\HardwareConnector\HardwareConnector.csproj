<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- PlatformTarget removed for cross-platform test execution -->
    <PlatformTarget>x86</PlatformTarget>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="NetMQ" Version="4.0.1.12" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NLog" Version="5.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.IO.Ports" Version="8.0.0" />
    <PackageReference Include="System.Reactive" Version="5.0.0" />
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
  </ItemGroup>



  <ItemGroup>
    <ProjectReference Include="..\HardwareSim\Hwsim\HwSim16.csproj" />
    <ProjectReference Include="..\Logger\Logger.csproj" />
    <ProjectReference Include="..\FuncLibs\FuncLibs.csproj" />
    <ProjectReference Include="..\HardwareSim\IHardware\IHardware.csproj" />
  </ItemGroup>

</Project>