using System.Diagnostics;
using static IHardware.Hw;

namespace HardwareConnectorWinForm   
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
     
        static void Main(string[] args)
        {
            //HwEDCi.HwClassMain HW = new HwEDCi.HwClassMain();
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            
            //限定该Exe的运行路径
            Environment.CurrentDirectory=AppDomain.CurrentDomain.BaseDirectory;
            ApplicationConfiguration.Initialize();
            Application.Run(new StartFrom(args));

        }

    }
}