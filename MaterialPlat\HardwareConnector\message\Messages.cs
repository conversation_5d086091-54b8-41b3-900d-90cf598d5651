using NetMQ;

namespace Ccss.HWConnector.Messages;

public record CloseDevice (int DeviceID);
public record GetState(int DeviceID);
public record GetInfo(int HardWareType, int DeviceID);
public record CreepCtrlStrCommand(int HardWareUnitID, string CMDString);

/// 试验机-> 硬件连接器
public record FunctionCmd(string FuncName, string? ProcessId, string? SubTaskID, object[] Params, string? CmdId = null);
/// <summary>
/// 硬件连接器 -> 试验机
/// FIXME: 增加字段CmdId作为消息返回的标识, 后续删除ProcessId和SubtaskId 修改Cmd为必填字段
/// </summary>
public record FuncReturn (int ErrorCode, string FuncName, string? ProcessId, string? SubTaskID, object? Result, string? CmdId = null);

static public class MsgHander{
    public static bool SendMessage(this object o){
        return true;
    }

}



public static class NetMQHelper
{
// This method converts a generic record into a NetMQMessage
    public static NetMQMessage ToNetMQMessage<T>(T record)
    {
    // create a new NetMQMessage object
        var message = new NetMQMessage();
    // append a string to the message
        // loop through all the properties of the record
        foreach (var property in record!.GetType().GetProperties())
        {
        // get the value of the property
            Object value = property.GetValue(record)!;
            if (value is int i) message.Append(new NetMQFrame(i));
            // if the value is null, set it to an empty string
            if (value is string s) message.Append(new NetMQFrame(s));
            // if the value is a float, convert it to a byte array using BitConverter
            if (value is float f) message.Append(new NetMQFrame(BitConverter.GetBytes(f)));
            // if the value is a double, convert it to a byte array using BitConverter
            if (value is double d) message.Append(new NetMQFrame(BitConverter.GetBytes(d)));
            // append the value to the message
            Console.WriteLine($"value:{value}, value-type:{value.GetType()}");
        }
    // return the NetMQMessage object
        return message;
    }

    public static T FromNetMQMessage<T>(NetMQMessage message)
    {
        var values = new List<object>();
        Object value;
        foreach (var part in message)
        {
            value = part.ConvertToString() ?? "";
            if (typeof(float).IsAssignableFrom(typeof(T).GetProperty(values.Count.ToString())!.PropertyType))
                value = BitConverter.ToSingle(part.ToByteArray());
            if (typeof(double).IsAssignableFrom(typeof(T).GetProperty(values.Count.ToString())!.PropertyType))
                value = BitConverter.ToDouble(part.ToByteArray());
            values.Add(value);
        }
        return (T)Activator.CreateInstance(typeof(T), values.ToArray())!;
    }
}
