using System;
using NUnit.Framework;
using IHardware;
using static IHardware.Hw;

namespace HwSim.Test
{
    /// <summary>
    /// HardwareSim项目中FlatCDataBlock的功能测试
    /// </summary>
    public class FlatCDataBlockTests
    {
        [Test]
        public void Test_FlatCDataBlock_Conversion_Basic()
        {
            // Arrange - 创建一个基本的CDataBlock
            var para = new DataBlockPara
            {
                ServoAxisCount = 2,
                ServoAxisDataCount = 3,
                ServoSensorCount = 4,
                TempAxisCount = 1,
                TempAxisDataCount = 2,
                TempSensorCount = 2,
                CreepAxisCount = 1,
                CreepAxisDataCount = 2,
                CreepSensorCount = 3,
                InCount = 8,
                OutCount = 8,
                ADCount = 10,
                ADDataCount = 100
            };

            var original = new CDataBlock(para);
            
            // 填充一些测试数据
            FillTestData(original);

            // Act - 转换为扁平化结构再转换回来
            var flatBlock = FlatCDataBlock.FromCDataBlock(original);
            var restored = flatBlock.ToCDataBlock();

            // Assert - 验证数据完整性
            Assert.AreEqual(original.ServoChCount, flatBlock.ServoChCount);
            Assert.AreEqual(original.TempChCount, flatBlock.TempChCount);
            Assert.AreEqual(original.CreepChCount, flatBlock.CreepChCount);
            Assert.AreEqual(original.InCount, flatBlock.InCount);
            Assert.AreEqual(original.OutCount, flatBlock.OutCount);
            Assert.AreEqual(original.ADCount, flatBlock.ADCount);

            // 验证转换回来的数据
            Assert.AreEqual(original.ServoChCount, restored.ServoChCount);
            Assert.AreEqual(original.TempChCount, restored.TempChCount);
            Assert.AreEqual(original.CreepChCount, restored.CreepChCount);

            // 验证伺服轴数据基本结构
            if (original.ServoData != null && restored.ServoData != null)
            {
                Assert.AreEqual(original.ServoData.Length, restored.ServoData.Length);
                for (int i = 0; i < original.ServoData.Length; i++)
                {
                    Assert.AreEqual(original.ServoData[i].DataCount, restored.ServoData[i].DataCount);
                }
            }
        }

        [Test]
        public void Test_FlatCDataBlock_ObjectCount_Reduction()
        {
            // Arrange
            var para = new DataBlockPara
            {
                ServoAxisCount = 5,
                ServoAxisDataCount = 10,
                ServoSensorCount = 8,
                TempAxisCount = 2,
                TempAxisDataCount = 5,
                TempSensorCount = 4,
                CreepAxisCount = 2,
                CreepAxisDataCount = 5,
                CreepSensorCount = 6,
                InCount = 16,
                OutCount = 16,
                ADCount = 20,
                ADDataCount = 50
            };

            var original = new CDataBlock(para);
            FillTestData(original);

            // Act
            var flatBlock = FlatCDataBlock.FromCDataBlock(original);

            // Assert - 验证扁平化结构特征
            Assert.IsNotNull(flatBlock.Sensors);
            Assert.IsNotNull(flatBlock.MaxSensors);
            Assert.IsNotNull(flatBlock.MinSensors);

            // 验证传感器池存在
            Assert.Greater(flatBlock.Sensors.Length, 0);
            Assert.AreEqual(flatBlock.Sensors.Length, flatBlock.MaxSensors.Length);
            Assert.AreEqual(flatBlock.Sensors.Length, flatBlock.MinSensors.Length);
        }

        [Test]
        public void Test_FlatCDataBlock_Null_Handling()
        {
            // Test with null input
            var flatBlock = FlatCDataBlock.FromCDataBlock(null);
            Assert.IsNotNull(flatBlock);
            Assert.AreEqual(0, flatBlock.ServoChCount);
            Assert.AreEqual(0, flatBlock.TempChCount);
            Assert.AreEqual(0, flatBlock.CreepChCount);

            // Test conversion back
            var restored = flatBlock.ToCDataBlock();
            Assert.IsNotNull(restored);
            Assert.AreEqual(0, restored.ServoChCount);
        }

        private void FillTestData(CDataBlock dataBlock)
        {
            // 填充伺服轴数据
            if (dataBlock.ServoData != null)
            {
                for (int i = 0; i < dataBlock.ServoData.Length; i++)
                {
                    if (dataBlock.ServoData[i]?.ChData != null)
                    {
                        dataBlock.ServoData[i].DataCount = Math.Min(3, dataBlock.ServoData[i].ChData.Length);
                        
                        for (int j = 0; j < dataBlock.ServoData[i].DataCount; j++)
                        {
                            var data = dataBlock.ServoData[i].ChData[j];
                            if (data != null)
                            {
                                data.Command = 100.0 + i * 10 + j;
                                data.Feedback = 99.0 + i * 10 + j;
                                data.Output = 50.0 + i * 5 + j;
                                data.Timer = i * 1000 + j * 100;

                                if (data.Sensor != null)
                                {
                                    for (int k = 0; k < data.Sensor.Length; k++)
                                    {
                                        data.Sensor[k] = (i + 1) * (j + 1) * (k + 1) * 1.5;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 填充温度轴数据
            if (dataBlock.TempData != null)
            {
                for (int i = 0; i < dataBlock.TempData.Length; i++)
                {
                    if (dataBlock.TempData[i]?.ChData != null)
                    {
                        dataBlock.TempData[i].DataCount = Math.Min(2, dataBlock.TempData[i].ChData.Length);
                        
                        for (int j = 0; j < dataBlock.TempData[i].DataCount; j++)
                        {
                            var data = dataBlock.TempData[i].ChData[j];
                            if (data != null)
                            {
                                data.Command = 25.0 + i * 5 + j;
                                data.Feedback = 24.5 + i * 5 + j;

                                if (data.Sensor != null)
                                {
                                    for (int k = 0; k < data.Sensor.Length; k++)
                                    {
                                        data.Sensor[k] = 20.0 + i * 2 + j * 0.5 + k * 0.1;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 填充IO数据
            if (dataBlock.BitIn != null)
            {
                for (int i = 0; i < dataBlock.BitIn.Length; i++)
                {
                    dataBlock.BitIn[i] = i % 2;
                }
            }

            if (dataBlock.BitOut != null)
            {
                for (int i = 0; i < dataBlock.BitOut.Length; i++)
                {
                    dataBlock.BitOut[i] = (i + 1) % 2;
                }
            }

            // 填充AD数据 (简化版)
            if (dataBlock.ADData != null)
            {
                for (int i = 0; i < dataBlock.ADData.Length; i++)
                {
                    if (dataBlock.ADData[i] != null)
                    {
                        dataBlock.ADData[i].DataCount = 10;
                        
                        if (dataBlock.ADData[i].Sensor != null && dataBlock.ADData[i].Sensor.Length > 10)
                        {
                            for (int j = 0; j < 10; j++)
                            {
                                dataBlock.ADData[i].Sensor[j] = i * 10.0 + j * 0.1;
                            }
                        }
                    }
                }
            }
        }
    }
}
