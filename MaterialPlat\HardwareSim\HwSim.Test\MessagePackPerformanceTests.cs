using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using IHardware;
using MessagePack;
using NUnit.Framework;
using static IHardware.Hw;

namespace HwSim.Test
{
    /// <summary>
    /// MessagePack 序列化性能对比测试
    /// 专注测试原始CDataBlock与FlatCDataBlock的序列化/反序列化性能
    /// </summary>
    [TestFixture]
    public class MessagePackPerformanceTests
    {
        private const int TestIterations = 1000;
        private const int WarmupIterations = 100;

        [Test]
        [TestCase(10, 50, 4, Description = "小规模数据")]
        [TestCase(50, 200, 16, Description = "中规模数据")]
        [TestCase(100, 500, 64, Description = "大规模数据")]
        public void CompareMessagePackPerformance(int servoAxisCount, int dataCountPerAxis, int sensorCount)
        {
            Console.WriteLine($"\n=== MessagePack 序列化性能测试 ===");
            Console.WriteLine($"测试规模: {servoAxisCount}轴 x {dataCountPerAxis}点/轴 x {sensorCount}传感器/点");
            Console.WriteLine($"测试次数: {TestIterations}次 (预热{WarmupIterations}次)\n");

            // 创建测试数据
            var para = CreateTestParameters(servoAxisCount, dataCountPerAxis, sensorCount);
            var originalData = CreateTestCDataBlock(para);
            var flatData = FlatCDataBlock.FromCDataBlock(originalData);

            // 预热JIT编译器
            Console.WriteLine("预热JIT编译器...");
            PerformSerializationTest(originalData, flatData, WarmupIterations, isWarmup: true);

            // 正式性能测试
            Console.WriteLine("开始正式性能测试...\n");
            var results = PerformSerializationTest(originalData, flatData, TestIterations, isWarmup: false);

            // 输出结果
            PrintPerformanceResults(results);

            // 断言性能提升
            Assert.That(results.FlatSerializationTime, Is.LessThan(results.OriginalSerializationTime), 
                "扁平化数据的序列化时间应该小于原始数据");
            Assert.That(results.FlatSize, Is.LessThan(results.OriginalSize), 
                "扁平化数据的序列化大小应该小于原始数据");
        }

        private SerializationResults PerformSerializationTest(CDataBlock originalData, FlatCDataBlock flatData, int iterations, bool isWarmup)
        {
            var results = new SerializationResults();

            // 测试原始数据序列化
            var originalTimes = new List<double>();
            var flatTimes = new List<double>();
            var originalDeserializeTimes = new List<double>();
            var flatDeserializeTimes = new List<double>();

            byte[]? originalSerialized = null;
            byte[]? flatSerialized = null;

            // 原始数据序列化测试
            for (int i = 0; i < iterations; i++)
            {
                var sw = Stopwatch.StartNew();
                originalSerialized = MessagePackSerializer.Serialize(originalData);
                sw.Stop();
                
                if (!isWarmup)
                {
                    originalTimes.Add(sw.Elapsed.TotalMilliseconds);
                }
            }

            // 扁平数据序列化测试
            for (int i = 0; i < iterations; i++)
            {
                var sw = Stopwatch.StartNew();
                flatSerialized = MessagePackSerializer.Serialize(flatData);
                sw.Stop();
                
                if (!isWarmup)
                {
                    flatTimes.Add(sw.Elapsed.TotalMilliseconds);
                }
            }

            // 反序列化测试
            if (originalSerialized != null && flatSerialized != null)
            {
                // 原始数据反序列化测试
                for (int i = 0; i < iterations; i++)
                {
                    var sw = Stopwatch.StartNew();
                    var _ = MessagePackSerializer.Deserialize<CDataBlock>(originalSerialized);
                    sw.Stop();
                    
                    if (!isWarmup)
                    {
                        originalDeserializeTimes.Add(sw.Elapsed.TotalMilliseconds);
                    }
                }

                // 扁平数据反序列化测试
                for (int i = 0; i < iterations; i++)
                {
                    var sw = Stopwatch.StartNew();
                    var _ = MessagePackSerializer.Deserialize<FlatCDataBlock>(flatSerialized);
                    sw.Stop();
                    
                    if (!isWarmup)
                    {
                        flatDeserializeTimes.Add(sw.Elapsed.TotalMilliseconds);
                    }
                }

                results.OriginalSize = originalSerialized.Length;
                results.FlatSize = flatSerialized.Length;
            }

            if (!isWarmup)
            {
                results.OriginalSerializationTime = originalTimes.Average();
                results.FlatSerializationTime = flatTimes.Average();
                results.OriginalDeserializationTime = originalDeserializeTimes.Average();
                results.FlatDeserializationTime = flatDeserializeTimes.Average();
            }

            return results;
        }

        private void PrintPerformanceResults(SerializationResults results)
        {
            Console.WriteLine("=== 性能测试结果 ===\n");

            Console.WriteLine("数据大小对比:");
            Console.WriteLine($"  原始数据: {results.OriginalSize:N0} bytes");
            Console.WriteLine($"  扁平数据: {results.FlatSize:N0} bytes");
            Console.WriteLine($"  大小减少: {(1.0 - (double)results.FlatSize / results.OriginalSize) * 100:F2}%\n");

            Console.WriteLine("序列化性能 (平均时间):");
            Console.WriteLine($"  原始数据: {results.OriginalSerializationTime:F4} ms");
            Console.WriteLine($"  扁平数据: {results.FlatSerializationTime:F4} ms");
            Console.WriteLine($"  性能提升: {((results.OriginalSerializationTime - results.FlatSerializationTime) / results.OriginalSerializationTime * 100):F2}%\n");

            Console.WriteLine("反序列化性能 (平均时间):");
            Console.WriteLine($"  原始数据: {results.OriginalDeserializationTime:F4} ms");
            Console.WriteLine($"  扁平数据: {results.FlatDeserializationTime:F4} ms");
            Console.WriteLine($"  性能提升: {((results.OriginalDeserializationTime - results.FlatDeserializationTime) / results.OriginalDeserializationTime * 100):F2}%\n");

            Console.WriteLine("综合性能分析:");
            var originalThroughput = (double)results.OriginalSize / (results.OriginalSerializationTime / 1000) / 1024 / 1024;
            var flatThroughput = (double)results.FlatSize / (results.FlatSerializationTime / 1000) / 1024 / 1024;
            Console.WriteLine($"  原始数据序列化吞吐量: {originalThroughput:F2} MB/s");
            Console.WriteLine($"  扁平数据序列化吞吐量: {flatThroughput:F2} MB/s");
            Console.WriteLine($"  吞吐量提升: {((flatThroughput - originalThroughput) / originalThroughput * 100):F2}%\n");

            Console.WriteLine("MessagePack优化总结:");
            Console.WriteLine($"  数据压缩率: {(1.0 - (double)results.FlatSize / results.OriginalSize) * 100:F2}%");
            Console.WriteLine($"  序列化性能提升: {((results.OriginalSerializationTime - results.FlatSerializationTime) / results.OriginalSerializationTime * 100):F2}%");
            Console.WriteLine($"  反序列化性能提升: {((results.OriginalDeserializationTime - results.FlatDeserializationTime) / results.OriginalDeserializationTime * 100):F2}%");
        }

        private DataBlockPara CreateTestParameters(int servoAxisCount, int dataCountPerAxis, int sensorCount)
        {
            return new DataBlockPara
            {
                ServoAxisCount = servoAxisCount,
                ServoAxisDataCount = dataCountPerAxis,
                ServoSensorCount = sensorCount,
                TempAxisCount = 2,
                TempAxisDataCount = 20,
                TempSensorCount = 2,
                CreepAxisCount = 1,
                CreepAxisDataCount = 10,
                CreepSensorCount = 1,
                InCount = 8,
                OutCount = 8,
                ADCount = 16
            };
        }

        private CDataBlock CreateTestCDataBlock(DataBlockPara para)
        {
            var dataBlock = new CDataBlock();
            InitializeDataBlock(dataBlock, para);
            FillSimulatedData(dataBlock, para);
            return dataBlock;
        }

        private void InitializeDataBlock(CDataBlock dataBlock, DataBlockPara para)
        {
            // 初始化伺服数据
            dataBlock.ServoData = new SingleChDatas[para.ServoAxisCount];
            for (int i = 0; i < para.ServoAxisCount; i++)
            {
                dataBlock.ServoData[i] = new SingleChDatas
                {
                    ChData = new CData[para.ServoAxisDataCount]
                };
                
                for (int j = 0; j < para.ServoAxisDataCount; j++)
                {
                    dataBlock.ServoData[i].ChData[j] = new CData
                    {
                        Sensor = new double[para.ServoSensorCount],
                        MaxSensor = new double[para.ServoSensorCount],
                        MinSensor = new double[para.ServoSensorCount]
                    };
                }
            }

            // 初始化其他数据结构
            dataBlock.TempData = new SingleChDatas[para.TempAxisCount];
            dataBlock.CreepData = new SingleChDatas[para.CreepAxisCount];
            dataBlock.BitIn = new int[para.InCount];
            dataBlock.BitOut = new int[para.OutCount];
            dataBlock.ADData = new SingleADDatas[para.ADCount];
        }

        private void FillSimulatedData(CDataBlock dataBlock, DataBlockPara para)
        {
            var random = new Random(42); // 固定种子确保测试一致性

            // 填充伺服数据
            if (dataBlock.ServoData != null)
            {
                for (int i = 0; i < Math.Min(para.ServoAxisCount, dataBlock.ServoData.Length); i++)
                {
                    if (dataBlock.ServoData[i] != null)
                    {
                        dataBlock.ServoData[i].DataCount = para.ServoAxisDataCount;
                        
                        if (dataBlock.ServoData[i].ChData != null)
                        {
                            for (int j = 0; j < Math.Min(para.ServoAxisDataCount, dataBlock.ServoData[i].ChData.Length); j++)
                            {
                                var data = dataBlock.ServoData[i].ChData[j];
                                if (data?.Sensor != null)
                                {
                                    data.Command = 100.0 + random.NextDouble() * 50;
                                    data.Feedback = data.Command + (random.NextDouble() - 0.5) * 2;
                                    data.Output = random.NextDouble() * 100;
                                    data.Timer = Environment.TickCount;
                                    
                                    for (int k = 0; k < Math.Min(para.ServoSensorCount, data.Sensor.Length); k++)
                                    {
                                        data.Sensor[k] = random.NextDouble() * 1000;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 设置计数器
            dataBlock.ServoChCount = para.ServoAxisCount;
            dataBlock.TempChCount = para.TempAxisCount;
            dataBlock.CreepChCount = para.CreepAxisCount;
            dataBlock.InCount = para.InCount;
            dataBlock.OutCount = para.OutCount;
            dataBlock.ADCount = para.ADCount;
        }

        private class SerializationResults
        {
            public double OriginalSerializationTime { get; set; }
            public double FlatSerializationTime { get; set; }
            public double OriginalDeserializationTime { get; set; }
            public double FlatDeserializationTime { get; set; }
            public int OriginalSize { get; set; }
            public int FlatSize { get; set; }
        }
    }
}
