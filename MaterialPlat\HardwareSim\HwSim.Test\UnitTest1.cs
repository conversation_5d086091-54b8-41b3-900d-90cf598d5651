using IHardware;
using NUnit.Framework;
using System;
using System.Threading.Tasks;
using System.Runtime.Serialization.Formatters.Binary;
using System.IO;

namespace HwSim.Test;

public class Tests
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public async Task Test1()
    {

        HwSim16.HwClassMain myHw = new HwSim16.HwClassMain();


        myHw.OnDataBlockEvent += (int sender, ref Hw.CDataBlock DataBlock) =>
            {
                Console.WriteLine("DoOnDataBlockEvent call back");
                Console.WriteLine("DoOnDataBlockEvent call back  Timer:" + DataBlock.ServoData[0].ChData[0].Timer);
                Console.WriteLine("DoOnDataBlockEvent call back  Move:" + DataBlock.ServoData[0].ChData[0].Sensor[0]);
                Console.WriteLine("DoOnDataBlockEvent call back  Stren:" + DataBlock.ServoData[0].ChData[0].Sensor[1]);
                return 0;

            };

        var result = myHw.CcssOpenDeviceID(0, "1.0");
        Console.WriteLine(result);

        await Task.Delay(30000);
        Assert.Pass();


    }


    public byte[] DoubleArrayToByteArray(double[] doubleArray)
    {
        byte[] result = new byte[doubleArray.Length * sizeof(double)];

        for (int i = 0; i < doubleArray.Length; i++)
        {
            byte[] doubleBytes = BitConverter.GetBytes(doubleArray[i]);
            Array.Copy(doubleBytes, 0, result, i * sizeof(double), sizeof(double));
        }
        return result;
    }

    [Test]
    public void Test2()
    {
        double[] x = {1.0, 2.0};
        var y = DoubleArrayToByteArray(x);
        Console.WriteLine(y);



   }
}
