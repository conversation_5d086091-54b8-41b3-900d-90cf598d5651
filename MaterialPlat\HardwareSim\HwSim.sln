﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33110.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IHardware", "IHardware\IHardware.csproj", "{895C2E95-C858-46BD-BD90-862CBF545CB2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HwSim16", "HwSim\HwSim16.csproj", "{97F6780A-5788-4F6F-AD87-AD85EAD17565}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Logger", "D:\WorkProject\ZJ\MaterialPlat\Logger\Logger.csproj", "{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FuncLibs", "D:\WorkProject\ZJ\MaterialPlat\FuncLibs\FuncLibs.csproj", "{C3B2331B-F4C9-7BB2-B81E-3403AF264965}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HardwareConnector", "..\HardwareConnector\HardwareConnector.csproj", "{67B8115A-05D9-D78D-08B9-9BE085B35862}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Debug|x64.Build.0 = Debug|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Debug|x86.Build.0 = Debug|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Release|Any CPU.Build.0 = Release|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Release|x64.ActiveCfg = Release|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Release|x64.Build.0 = Release|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Release|x86.ActiveCfg = Release|Any CPU
		{895C2E95-C858-46BD-BD90-862CBF545CB2}.Release|x86.Build.0 = Release|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Debug|x64.ActiveCfg = Debug|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Debug|x64.Build.0 = Debug|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Debug|x86.ActiveCfg = Debug|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Debug|x86.Build.0 = Debug|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Release|Any CPU.Build.0 = Release|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Release|x64.ActiveCfg = Release|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Release|x64.Build.0 = Release|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Release|x86.ActiveCfg = Release|Any CPU
		{97F6780A-5788-4F6F-AD87-AD85EAD17565}.Release|x86.Build.0 = Release|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Debug|x64.Build.0 = Debug|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Debug|x86.Build.0 = Debug|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Release|Any CPU.Build.0 = Release|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Release|x64.ActiveCfg = Release|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Release|x64.Build.0 = Release|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Release|x86.ActiveCfg = Release|Any CPU
		{6EA0A52D-57D4-3163-1A2D-A3D7DD2B6748}.Release|x86.Build.0 = Release|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Debug|x64.Build.0 = Debug|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Debug|x86.Build.0 = Debug|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Release|x64.ActiveCfg = Release|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Release|x64.Build.0 = Release|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Release|x86.ActiveCfg = Release|Any CPU
		{C3B2331B-F4C9-7BB2-B81E-3403AF264965}.Release|x86.Build.0 = Release|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Debug|x64.ActiveCfg = Debug|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Debug|x64.Build.0 = Debug|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Debug|x86.ActiveCfg = Debug|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Debug|x86.Build.0 = Debug|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Release|Any CPU.Build.0 = Release|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Release|x64.ActiveCfg = Release|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Release|x64.Build.0 = Release|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Release|x86.ActiveCfg = Release|Any CPU
		{67B8115A-05D9-D78D-08B9-9BE085B35862}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {983C0954-6803-4D5C-9C93-75B3DCED2B1E}
	EndGlobalSection
EndGlobal
