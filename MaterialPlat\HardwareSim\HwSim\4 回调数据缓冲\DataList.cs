﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Reflection;
using System.Windows;
using IHardware;

namespace HwSim16
{
    //======================回调数据类，处理缓冲回调数据，数据转换格式工作================================================

    public class CDataList
    {

        private HwClassMain dHwClassMain;        
        public int GetDataPos = 0;//缓冲区取数的末尾数
        int Counter = 0;
        int AcqState = 0;
        public CDataList(HwClassMain HwClassMain)
        {
            dHwClassMain = HwClassMain;
            CreatBindingData();
        }


        //================定时采集数据(AD卡，温度传感器需要）====================================
        /// <summary>
        /// 针对没有回调数据的硬件，专门设置的采集函数。
        /// </summary>
        public void AcquireData()
        {
            if (CommValues.Init == 1)
            {
                SDC2000.CsharpCall(0, 0, 0, 0);//采集数据并马上送到缓冲区。
            }
            return;//本硬件没有这个需要
            //====采集数据===========
            switch (AcqState)
            {


                case 0:
                    //采集数据函数发送

                    AcqState = 1;
                    break;
                case 1://针对串口，蠕变等等待一定时间的采集方式
                    if (true)//判断数据是否到达
                    {
                        //转换数据到标准格式
                        CommValues.DataBlock = GetDataToStandData(1100);
                        //====== 送入缓冲区 DataList ============
                        AcqState = 2;
                    }
                    break;
                case 2://返回初始采集状态

                    AcqState = 0;

                    break;

            }
        }




        //================定时回调数据====================================
        public void CallBackData()
        {
            
            try
            {
                int tmpAxisCount;
                //======从缓冲区 DataList 取出当前所有数据,进行回调============
                do
                {
                    tmpAxisCount = (CommValues.DataBlockBuffer.ServoData[0].DataCount + CommValues.Blockbuffer - GetDataPos) % CommValues.Blockbuffer;

                    if (tmpAxisCount > 0)
                    {
                        //从缓冲区取数并转换
                        CommValues.DataBlock = GetDataToStandData(1100);//最多取1000点
                        dHwClassMain.DoOnDataBlockEvent(0, ref CommValues.DataBlock);
                        
                    }
                }
                while (tmpAxisCount > 0);
                //FlashFormData();//刷新屏幕数据
                return;
            }
            catch
            {
                throw new Exception("HwSim-DataList CallBackData()  error！");
                //return;

            }
        }
        //==================把硬件专有数据转换成标准回调数据===================
        //输入参数应该是硬件特有结构
        /// <summary>
        /// 从DataList中取数
        /// </summary>
        /// <param name="MaxGetCount"></param>最多取数点数
        /// <returns></returns>
        Hw.CDataBlock GetDataToStandData(int MaxGetCount)
        {
            //Hw.OnDataBlock OutData = new Hw.OnDataBlock(CommValues.dataBlockPara);//创造新的DataBlock对象
            try
            {
                int i, j, k, Pos;
                int tmpAxisDataCount;//保存轴的采集数据点数
                int tmpADDataCount;//保存AD点数


                //=================从环形缓冲区取数据，保存初始的点数值=======================================
                tmpAxisDataCount = (CommValues.DataBlockBuffer.ServoData[0].DataCount + CommValues.Blockbuffer - GetDataPos) % CommValues.Blockbuffer;
                if (tmpAxisDataCount > MaxGetCount) tmpAxisDataCount = MaxGetCount;//>最大值时，取最大值
                tmpADDataCount = tmpAxisDataCount;



                for (i = 0; i < CommValues.CommHwInfo.ServoAxisCount; i++)//扫描系统所有的缸，发现属于这个硬件的通道就采集
                {
                    for (j = 0; j < tmpAxisDataCount; j++)//扫描采集点数
                    {
                        Pos = (GetDataPos + j) % CommValues.Blockbuffer;    //总控制器的当前缸的曲线循环队列指针
                        for (k = 0; k < CommValues.CommHwInfo.ServoAxisSensor[i].ADSensorCount; k++)//扫描传感器
                        {
                            CommValues.DataBlock.ServoData[i].ChData[j].Sensor[k] = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].Sensor[k];
                            CommValues.DataBlock.ServoData[i].ChData[j].MaxSensor[k] = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].MaxSensor[k];
                            CommValues.DataBlock.ServoData[i].ChData[j].MinSensor[k] = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].MinSensor[k];
                        }

                        CommValues.DataBlock.ServoData[i].ChData[j].Command = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].Command;//Command
                        CommValues.DataBlock.ServoData[i].ChData[j].Feedback = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].Feedback;//DA
                        CommValues.DataBlock.ServoData[i].ChData[j].Cycles = (UInt32)CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].Cycles;//TopData.Curve[i].Arr[13 * 1000 + j];//循环数
                        CommValues.DataBlock.ServoData[i].ChData[j].BlockCycles = (UInt32)CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].BlockCycles;//大循环数
                        CommValues.DataBlock.ServoData[i].ChData[j].BlockLine = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].BlockLine;//BlockLine
                        CommValues.DataBlock.ServoData[i].ChData[j].InSignals = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].InSignals;//输入信号
                        CommValues.DataBlock.ServoData[i].ChData[j].UpperSft = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].UpperSft;
                        CommValues.DataBlock.ServoData[i].ChData[j].LowerSft = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].LowerSft;
                        CommValues.DataBlock.ServoData[i].ChData[j].Timer = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].Timer;//时间
                        CommValues.DataBlock.ServoData[i].ChData[j].CmdFrequency = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].CmdFrequency;//命令频率
                        CommValues.DataBlock.ServoData[i].ChData[j].ActiveCtrl = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].ActiveCtrl;//ctrlmode
                        CommValues.DataBlock.ServoData[i].ChData[j].Output = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].Output;//DA
                        CommValues.DataBlock.ServoData[i].ChData[j].LowerLimits = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].LowerLimits;//传感器最小限位
                        CommValues.DataBlock.ServoData[i].ChData[j].UpperLimits = CommValues.DataBlockBuffer.ServoData[i].ChData[Pos].UpperLimits;//传感器最大限位
                    }
                    //CommValues.DataBlock.ServoData[i].DAQRate = CommValues.CommHwInfo.ServoAxisSensor[0].AxisSensor.DAQRate;//对采集速率赋值
                    CommValues.DataBlock.ServoData[i].DataCount = tmpAxisDataCount;
                }
                CommValues.DataBlock.ServoChCount = CommValues.CommHwInfo.ServoAxisCount;
                //==================用各个轴的位移信号模拟AD信号===============================================
                for (i = 0; i < CommValues.CommHwInfo.ADCount; i++)
                {
                    for (j = 0; j < tmpADDataCount; j++)
                    {
                        Pos = (GetDataPos + j) % CommValues.Blockbuffer;    //总控制器的当前缸的曲线循环队列指针
                        CommValues.DataBlock.ADData[i].Sensor[j] = CommValues.DataBlockBuffer.ADData[i].Sensor[Pos];
                        CommValues.DataBlock.ADData[i].MaxSensor[j] = CommValues.DataBlockBuffer.ADData[i].MaxSensor[Pos];
                        CommValues.DataBlock.ADData[i].MinSensor[j] = CommValues.DataBlockBuffer.ADData[i].MinSensor[Pos];
                        CommValues.DataBlock.ADData[i].Time[j] = CommValues.DataBlockBuffer.ADData[i].Time[Pos];//时间
                    }
                    CommValues.DataBlock.ADData[i].DataCount = tmpADDataCount;
                    //CommValues.DataBlock.ADData[i].DAQRate = CommValues.CommHwInfo.myAD.DAQRate;//对采集速率赋值
                }
                //======================取Input信号=============================================
                for(i=0;i< CommValues.CommHwInfo.InputCount;i++)
                {
                    CommValues.DataBlock.BitIn[i] = CommValues.DataBlockBuffer.BitIn[i];
                }
                //====================更新指针=====================
                GetDataPos = (GetDataPos + tmpAxisDataCount) % CommValues.Blockbuffer;


                //================更新显示数据======================================
                Counter++;
                //if (Counter % 3 != 0) return CommValues.DataBlock;//每秒3.3次
                //for (i = 0; i < CommValues.CommHwInfo.ServoAxisCount; i++)//扫描系统所有的缸，
                //{
                //    for (j = 0; j < CommValues.CommHwInfo.ServoAxisSensor[i].ADSensorCount; j++)
                //    {

                //        switch (j)
                //        {
                //            case 0:
                //                CommValues.DataGridModels1[i].Sensor0 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 1:
                //                CommValues.DataGridModels1[i].Sensor1 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 2:
                //                CommValues.DataGridModels1[i].Sensor2 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 3:
                //                CommValues.DataGridModels1[i].Sensor3 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 4:
                //                CommValues.DataGridModels1[i].Sensor4 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 5:
                //                CommValues.DataGridModels1[i].Sensor5 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 6:
                //                CommValues.DataGridModels1[i].Sensor6 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 7:
                //                CommValues.DataGridModels1[i].Sensor7 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 8:
                //                CommValues.DataGridModels1[i].Sensor8 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;
                //            case 9:
                //                CommValues.DataGridModels1[i].Sensor9 = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Sensor[j].ToString("0.0##");
                //                break;

                //        }

                //    }
                //    CommValues.DataGridModels1[i].Cmd = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Command.ToString("0.0##");
                //    CommValues.DataGridModels1[i].Cycle = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Cycles.ToString("0");
                //    CommValues.DataGridModels1[i].Timer = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Timer.ToString("0.0");
                //    CommValues.DataGridModels1[i].BlockLine = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].BlockLine.ToString("0");
                //    CommValues.DataGridModels1[i].Feedback = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].Feedback.ToString("0.0##");
                //    CommValues.DataGridModels1[i].BlockCycle = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].BlockCycles.ToString("0");
                //    CommValues.DataGridModels1[i].InSignals = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].InSignals.ToString("0");
                //    CommValues.DataGridModels1[i].UpperSft = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].UpperSft.ToString("0");
                //    CommValues.DataGridModels1[i].LowerSft = CommValues.DataBlock.ServoData[i].ChData[tmpAxisDataCount - 1].LowerSft.ToString("0");
                //}



                return CommValues.DataBlock;

            }
            catch
            {
                throw new Exception("HwSim-DataList GetDataToStandData()  error！");
                return CommValues.DataBlock;
            }

        }
        /// <summary>
        /// 更新窗体数据，实现界面数据点的刷新
        /// </summary>
        public void FlashFormData()
        {
            //Counter++;          
            //CommValues.DataGridModels1[0].Sensor0 = Counter;
            //CommValues.DataGridModels1[0].Timer = Counter;

        }

        public void CreatBindingData()
        {
            //double[] myPrice = new double[10] { 1,2,3,4,5,6,7,8,9,10};
            //int AxisCount = CommValues.CommHwInfo.ServoAxisCount;
            //DataGridModel[] myGridData = new DataGridModel[AxisCount];
            //for (int i = 0; i < AxisCount; i++)
            //{
            //    myGridData[i] = new DataGridModel();
            //    myGridData[i].Name = "轴" + i.ToString();
            //    CommValues.DataGridModels1.Add(myGridData[i]);
            //}


        }




    }
}





















