﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.InteropServices;  

namespace HwSim16
{
    /// <summary>
    /// ============== 一个控制器硬件有一套这样的数据结构作为缓冲=========================
    /// SDC系列的各种数据结构都在这里定义，包含回调函数的处理函数及缓冲数据结构。
    /// 所有的回调函数缓冲数据都在这里   各个传感器数据，各个曲线，各个最大值最小值
    /// CCallBackData中的数据，需要传递给CFormCurveData中，变成整个系统的数据
    /// 目前设计的回调函数只能适应一个控制器。不能适应多个控制器，因为是直接对数组0-16赋值的，将来可能需要修改
    /// 
    /// </summary>
    [Serializable]
    public class CCallBackData
    {
        public StationS stnpara;   //控制器软硬件参数
        public DoPEData1[] TestAllData;   //回调显示的所有缸的显示数据   32个
        //sensor(0)--Sensor（9）  当前缸的传感器
        //sensor(10)  TheoryCmd
        //sensor(11)  TrueCmd
        //sensor(12)  DA
        //sensor(13)  cycle
        //sensor(14)  
        //sensor(15)  当前通道控制值的绝对值
        //sensor(16)  Timer
        //Sensor(17)  TheoryCmd
        //Sensor(18) TrueCmd 
        //Sensor(19)  DA

       

        //  '                        【】【0-9】传感器反馈曲线
        //'                        【】【10】标准命令
        //'                        【】【11】补偿后命令
        //'                        【】【12】DA命令
        //'                        【】【13】循环周期
        public double[, ,] CurveArr;//所有缸的曲线缓冲区   32个  [缸号，传感器号，曲线]   循环队列
        
        public Int32 ProcessMsgStartPos=0;   //读消息开始位置，回调函数的消息是一个缓冲区的形式返回的，所以需要有个指针指向回调函数的消息缓冲队列的读数据位置。

        //===============最大最小块缓冲区（共用TestDataEndPos）=====================
        public MaxMinWin32DataStruct[] MaxMinDataBuff; //最大最小值的缓冲区。 100 
        public int MaxMinStartPos = 0;   //最大最小值的读数据指针，结束指针与TestDataEndPos配合
        //===============当前曲线临时块缓冲区（共用TestDataEndPos）=====================
        public Css10Arr1000Struct[,] CurrArrBuf;   //临时数组缓冲  100个缓冲区  32个缸
        public int CurrDataStartPos = 0;//曲线读取指针开始位置，结束指针与TestDataEndPos配合

        //===============数据块缓冲区（共用TestDataEndPos）=====================
        public Css10ArrDataStruct[] TestDataBuf;   //数据块缓冲  【100】        
        public int TestDataStartPos=0;//数据块开始位置  32个缸，就要32个指针

        public int TestDataEndPos=0;   //数据块结束位置，这个共用，因为缓冲区都是100个


        //Output数据       
        public int[] NowOutputBit;//60个输出bit 数据

        

        public CCallBackData()  ///初始化这些数据
        {
            int i,j;
            stnpara.Initialize();  //结构初始化
            //===============TestAllData==============================================
            TestAllData = new DoPEData1[32];
            for (i = 0; i < 32; i++)
            {
                TestAllData[i] = new DoPEData1();
            }
            for (i = 0; i < 32; i++)
            {
                TestAllData[i].Initialize();
            }
            //============MaxMinData============================================
            
            MaxMinDataBuff = new MaxMinWin32DataStruct[100];
            for ( i = 0; i < 100; i++)
            {
                MaxMinDataBuff[i] = new MaxMinWin32DataStruct();
            }
            for ( i = 0; i < 100; i++)
            {
                MaxMinDataBuff[i].Initialize();
            }
            
            
            //============CurveArr============================================
            CurveArr = new double[32, 20, 20000];   //
            //============CurveStartPos============================================
          

            //===============CurrArrBuf==============================================
            CurrArrBuf = new Css10Arr1000Struct[100,32];
            for (i = 0; i < 100; i++)
            {
                for(j=0;j<32;j++)
                    CurrArrBuf[i,j] = new Css10Arr1000Struct();
            }
            for (i = 0; i < 100; i++)
            {
                for (j = 0; j < 32; j++)
                    CurrArrBuf[i,j].Initialize();
            }
            TestDataBuf = new Css10ArrDataStruct[100];
            for (i = 0; i < 100; i++)
            {
                TestDataBuf[i] = new Css10ArrDataStruct();
            }
            for (i = 0; i < 100; i++)
            {
                TestDataBuf[i].Initialize();
            }
            TestDataBuf.Initialize();
            TestDataStartPos = 0;
            TestDataEndPos = 0;
            NowOutputBit = new int[32];
        }

        /// <summary>
        /// 生成中间缓冲数据，从回调函数中将数据送进回调缓冲区
        /// </summary>

        public  void BuildBufferData(Css10ArrDataStruct a1,
               Css10Arr1000Struct Curve0,
               Css10Arr1000Struct Curve1,
               Css10Arr1000Struct Curve2,
               Css10Arr1000Struct Curve3,
               Css10Arr1000Struct Curve4,
               Css10Arr1000Struct Curve5,
               Css10Arr1000Struct Curve6,
               Css10Arr1000Struct Curve7,
               Css10Arr1000Struct Curve8,
               Css10Arr1000Struct Curve9,
               Css10Arr1000Struct Curve10,
               Css10Arr1000Struct Curve11,
               Css10Arr1000Struct Curve12,
               Css10Arr1000Struct Curve13,
               Css10Arr1000Struct Curve14,
               Css10Arr1000Struct Curve15,
               Css10Arr1000Struct Curve16,
               Css10Arr1000Struct Curve17,
               Css10Arr1000Struct Curve18,
               MaxMinWin32DataStruct xMaxMinData,
               int ArrCount)
        {


            //===========================放入临时缓冲=========================================
            TestDataBuf[TestDataEndPos] = CommValues.myFile.StructToStruct<Css10ArrDataStruct>(a1);//保存传感器数据到缓冲区
            TestDataBuf[TestDataEndPos].DataCount = ArrCount;  //保存曲线点数
            MaxMinDataBuff[TestDataEndPos] = CommValues.myFile.StructToStruct<MaxMinWin32DataStruct>(xMaxMinData);//保存传感器最大最小值数据到缓冲区
            //if (xMaxMinData.Counts[0] != 0)
            //{
            //    MaxMinDataBuff[TestDataEndPos] = CommValues.myFile.StructToStruct<MaxMinWin32DataStruct>(xMaxMinData);//保存传感器最大最小值数据到缓冲区
                
            //}
           for (int i = 0; i < 16; i++)
                    xMaxMinData.Counts[i] = 0;  //数据清零

            //=========判断方式特殊===========
            if (stnpara.StationNumber > 0)
            {
                CurrArrBuf[TestDataEndPos, 0] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve0);
                if (stnpara.StationNumber > 1)
                {
                    CurrArrBuf[TestDataEndPos, 1] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve1);
                    if (stnpara.StationNumber > 2)
                    {
                        CurrArrBuf[TestDataEndPos, 2] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve2);

                        if (stnpara.StationNumber > 3)
                        {
                            CurrArrBuf[TestDataEndPos, 3] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve3);
                            if (stnpara.StationNumber > 4)
                            {
                                CurrArrBuf[TestDataEndPos, 4] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve4);
                                if (stnpara.StationNumber > 5)
                                {
                                    CurrArrBuf[TestDataEndPos, 5] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve5);
                                    if (stnpara.StationNumber > 6)
                                    {
                                        CurrArrBuf[TestDataEndPos, 6] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve6);
                                        if (stnpara.StationNumber > 7)
                                        {
                                            CurrArrBuf[TestDataEndPos, 7] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve7);
                                            if (stnpara.StationNumber > 8)
                                            {
                                                CurrArrBuf[TestDataEndPos, 8] =CommValues.myFile.StructToStruct<Css10Arr1000Struct>( Curve8);
                                                if (stnpara.StationNumber > 9)
                                                {
                                                    CurrArrBuf[TestDataEndPos, 9] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve9);
                                                    if (stnpara.StationNumber > 10)
                                                    {
                                                        CurrArrBuf[TestDataEndPos, 10] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve10);
                                                        if (stnpara.StationNumber > 11)
                                                        {
                                                            CurrArrBuf[TestDataEndPos, 11] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve11);
                                                            if (stnpara.StationNumber > 12)
                                                            {
                                                                CurrArrBuf[TestDataEndPos, 12] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve12);
                                                                if (stnpara.StationNumber > 13)
                                                                {
                                                                    CurrArrBuf[TestDataEndPos, 13] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve13);
                                                                    if (stnpara.StationNumber > 14)
                                                                    {
                                                                        CurrArrBuf[TestDataEndPos, 14] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve14);
                                                                        if (stnpara.StationNumber > 15)
                                                                        {
                                                                            CurrArrBuf[TestDataEndPos, 15] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve15);
                                                                            if (stnpara.StationNumber > 16)
                                                                            {
                                                                                CurrArrBuf[TestDataEndPos, 16] = CommValues.myFile.StructToStruct<Css10Arr1000Struct>(Curve16);
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            TestDataEndPos = (TestDataEndPos + 1) % 100;




            /////=======各个缸单点数据==================
            ////原来的TestDataBuf.TestData[i].Sensor() 的次序
            ////TestDataBuf.TestData[i].sensor(15)  当前通道控制值的绝对值
            ////TestDataBuf.TestData[i].sensor(16)  Time
            ////TestDataBuf.TestData[i].Sensor(17)  TheoryCmd
            ////TestDataBuf.TestData[i].Sensor(18)  TrueCmd
            ////TestDataBuf.TestData[i].Sensor(19)  DA  DoPEData1

            for (int i = 0; i < 19; i++)  //缸号扫描
            {
                TestAllData[i] = CommValues.myFile.StructToStruct<DoPEData1>(a1.TestData[i]);
                TestAllData[i].Sensor[10] = TestAllData[i].Sensor[17];  //TheoryCmd
                TestAllData[i].Sensor[11] = TestAllData[i].Sensor[18];  //TrueCmd
                TestAllData[i].Sensor[12] = TestAllData[i].Sensor[19];  //DA
                
            }


        }

    }


    [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
    public struct FileCmdStruct
    {

        /// int
        public int Count;

        /// int[11]
        [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 11, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
        public int[] Free;

        /// double[8000]
        [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 8000, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R8)]
        public double[] WaveData;

        public void Initialize()
        {
            Count = 1;
            WaveData = new double[8000];
            Free = new int[11];
        }
    }



        //'传感器和控制器结构
        //struct SensorAndControllers
        //{

        //    short CardID ; //          '控制卡号    /////new
        //    short SensorID;          //传感器号     //////new
        //    short UnitClass ;//        '单位类型

        //    short  Unit ;//            '单位

        //    short InvPolarity ;//      '是否转换极性
        //    short EEPROMFlag;         //有EEPROM标志
        //    float LowPID ;//         //低压启动PID比例
        //   //short ControlFlag;        //是否控制标志


        //    float MaxValue ;//        '传感器最大值
        //    float MinValue ;//        '传感器最小值

        //    short FilterPoints1 ;//    '控制用滤波
        //    short FilterPoints2 ;//    '采样、显示用滤波

        //    long  AbsoluteZero ;//    '绝对零点

        //    float CaliValue ;//       '标定值
        //    float SensorK ;//     '外部传感器系数
        //    float AmpK ;//         '放大器系数

        //    long LinearPoints ;//   '线性修正点数
        //    float Standard[10] ;//      '标准值
        //    float AD[10] ;     //      '实际值




        //    //'以下为控制器参数
        //    short UpIsPositive ;//     '是否指定上升为正向变化   升为正=1，降为正=-1
        //    short CtrlInv ;//         '是否转换控制方向

        //    float IntegralTime ;//     '积分时间   ms
        //    float IntTime;      //     中断时间    ms
        //    float MaxSpeed ;//        '最大速度
        //    float Acc ;//            '加速度

        //    float kP ;//              '比例系数
        //    float kI ;//              '积分系数
        //    float kD ;//             '微分系数

        //    float SpeedP ;//          '速度比例系数
        //    float SpeedI ;//          '速度积分系数
        //    float SpeedD ;//         '速度微分系数

        //    float CtrlValueDev ;//    '控制值偏差

        //    float FeedForward;   //   前馈
        //    float AccFeedForward; //

        //    char  Name[12];         // 通道名称 ，如负荷、变形、位移等

        //    float Spare[4] ;//


        //};



        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
        public struct SensorAndControllers
        {

            /// short
            public short CardID;

            /// short
            public short SensorID;

            /// short
            public short CtrlMode;

            /// short
            public short Unit;

            /// short
            public short InvPolarity;

            /// short
            public short EEPROMFlag;

            /// float
            public float LowPID;

            /// float
            public float MaxValue;

            /// float
            public float MinValue;

            /// short
            public short FilterPoints1;

            /// short
            public short FilterPoints2;

            /// int
            public int AbsoluteZero;

            /// float
            public float CaliValue;

            /// float
            public float SensorK;

            /// float
            public float AmpK;

            /// int
            public int LinearPoints;

            /// float[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R4)]
            public float[] Standard;
            
            /// float[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R4)]
            public float[] AD;

            /// short
            public short UpIsPositive;

            /// short
            public short CtrlInv;

            /// float
            public float IntegralTime;

            /// float
            public float IntTime;

            /// float
            public float MaxSpeed;

            /// float
            public float Acc;

            /// float
            public float kP;

            /// float
            public float kI;

            /// float
            public float kD;

            /// float
            public float SpeedP;

            /// float
            public float SpeedI;

            /// float
            public float SpeedD;

            /// float
            public float CtrlValueDev;

            /// float
            public float FeedForward;

            /// float
            public float AccFeedForward;

            /// char[12]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 12, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I1)]
            public byte[] Name;

            /// float[4]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 4, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R4)]
            public float[] Spare;

            public void Initialize()
            {
                Standard = new float[10];
                AD = new float[10];
                Name = new byte[12];
                Spare = new float[4];
                CardID = 0;
                SensorID = 1;
                CtrlMode = 2;
                Unit = 1;
                InvPolarity = 1;
                EEPROMFlag = 0;   //无EEPROM
                LowPID = 1; //低压PID比例
                MaxValue = 1000;
                MinValue = -1000;
                FilterPoints1 = 5;
                FilterPoints2 = 40;
                AbsoluteZero = 0;
                CaliValue = 1;  //232656
                SensorK = 1;
                AmpK = 1;
                LinearPoints = 2;
                Standard[0] = 0;
                AD[0] = 0;
                Standard[1] = 5;
                AD[1] = 180000;
                UpIsPositive = 1;
                CtrlInv = 1;
                IntegralTime = 20;     //20ms
                IntTime = 1;        //1.25ms
                MaxSpeed = 8;
                Acc = 25;
                kP = 100000;
                kI = 0;
                kD = 0;
                SpeedP = 100;
                SpeedI = 10;
                SpeedD = 0;
                FeedForward = 0;   //   前馈
                AccFeedForward = 0; //  加速度前馈
                CtrlValueDev = 30;
            }

        }

        ////'外部命令结构
        //     struct ExternalCommandS
        //     {   short UnitClass ;//               '单位类别
        //          short Unit ;//                   '单位
        //          float MinValue ;//               '最小值
        //          float MaxValue ;//              '最大值
        //          long  Polarity ;//             '极性
        //          float Gain ;//                  '增益
        //          float UpLimit ;//               '上限
        //          float LowLimit ;//               '下限
        //          float Offset ;//                 '偏置或零点
        //          float Spare[4] ;//

        //     };

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
        //[StructLayout(LayoutKind.Sequential, Pack:=1, CharSet:=CharSet.Ansi)] 
        public struct ExternalCommandS
        {

            /// short
            public short UnitClass;

            /// short
            public short Unit;

            /// float
            public float MinValue;

            /// float
            public float MaxValue;

            /// int
            public int Polarity;

            /// float
            public float Gain;

            /// float
            public float UpLimit;

            /// float
            public float LowLimit;

            /// float
            public float Offset;

            /// float[4]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 4, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R4)]
            public float[] Spare;
            public void Initialize()
            {

                Spare = new float[4];
                UnitClass = 1;//               '单位类别
                Unit = 1;//                   '单位
                MinValue = 100;//               '最小值
                MaxValue = -100;//              '最大值
                Polarity = 1;//             '极性
                Gain = 1;//                  '增益
                UpLimit = 110;//               '上限
                LowLimit = -110;//               '下限
                Offset = 0;//                 '偏置或零点
            }

        }

        //'补偿命令结构
        //struct CompensatorS
        //{
        //    long Type;//               '补偿类型

        //    //'踏步补偿法使用
        //    float ErrTolerance;//        '% 差值容限
        //    float Timeout;//             '超时值
        //    long Action;//            '超时行为

        //    //'PVC峰谷值、APC幅值相位补偿和PVP峰谷值相位补偿使用
        //    float ConvergenceTate;//    '收敛速率
        //    float Sensitivity;//        '敏感度
        //    long AdaptState;//        '适应状态
        //    //AIC特有的
        //    long FirCount;  //Fir阶数
        //    long AcqCount;  //采样点数
        //    float Delay;    //延迟
        //    float Step;    //步长		  
        //    //float Spare[4]  ;//
        //};


        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
        public struct CompensatorS
        {

            /// int
            public int Type;

            /// float
            public float ErrTolerance;

            /// float
            public float Timeout;

            /// int
            public int Action;

            /// float
            public float ConvergenceTate;

            /// float
            public float Sensitivity;

            /// int
            public int AdaptState;

            /// int
            public int FirCount;

            /// int
            public int AcqCount;

            /// float
            public float Delay;

            /// float
            public float Step;
            public void Initialize()
            {
                Type = 0;

                /// float
                ErrTolerance = 1;

                /// float
                Timeout = 0;

                /// int
                Action = 0;

                /// float
                ConvergenceTate = 0;

                /// float
                Sensitivity = 1;

                /// int
                AdaptState = 0;

                /// int
                FirCount = 0;

                /// int
                AcqCount = 0;

                /// float
                Delay = 0;
                Step = 0;
            }
        }

        //   struct DAS      /////DA
        //{
        //     short CardID ; //          '控制卡号    /////new
        //     short OutputID;          //DA号     //////new

        //     long  Zero;    //        DA零点
        //     long  Free[2];

        //};

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
        public struct DAS
        {

            /// short
            public short CardID;

            /// short
            public short OutputID;

            /// int
            public int Zero;

            /// int[2]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 2, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Free;
            public void Initialize()
            {

                Free = new int[2];
                CardID = 0;
                OutputID = 0;
                Zero = 0;
            }

        }



        ////'子站结构
        //     struct SubStationS
        //     {
        //          short PowerID;//                            '动力源   
        //          short UpdateRate ;//                       '延时时间 ms
        //          //'Program and Control channels
        //          short DefaultSensorID ;//       '默认控制通道号
        //          short Enable;          //子站有效否     1=有效  0=无效
        //          long  IOCardID;                      //设定IO操作的卡号
        //          short Type ;//                            '子站类型    0=单环  1=双环  2=电液伺服单环等等
        //          short HasExternalCom ;//                  '是否有外部命令

        //          short ChannelCount ;//                   '实际通道数
        //          short Ctrl    ;                           //控制或不控制   1=控制  -1=不控制
        //          long  HandMode;                         //手轮模式（0=没有手轮，1=速度模式，2=位移模式）
        //          long  HandleCircleSourceCh;             //手柄旋转传感器来源 这个传感器即可以用作手轮，又可以用作手柄
        //          long  HandleUpSourceCh;                 //手柄上下传感器来源
        //          float HandCircleK;                      //手轮灵敏系数
        //          float MinMoveDev;                       //最小移动偏差 ，在停止时，在设置范围内不移动。
        //          SensorAndControllers Channel[10] ;//        '通道结构类型数组
        //          ExternalCommandS ExCommand ;//             '外部命令实例
        //          CompensatorS Compensator ;//               '补偿器实例
        //          DAS  DA;		  
        //          short PowerMode;            //启动模式  0=无      1=单通道低压高压模式  2=主控制卡子站DA1模式 3=主控制卡子站DA2模式
        //          short SoftWaveA ;                  //颤振幅值  最大32767  最小-32767  DA值
        //          unsigned char bak  ;       //输入备用
        //          unsigned char InStopBit ;        //输入急停input来源          0-31表示IO来源，99表示没有
        //          unsigned char OutLowPowerBit;    //输出低压output来源      0-57表示IO来源，99表示没有
        //          unsigned char OutHighPowerBit ;   //输出高压output来源      0-57表示IO来源，99表示没有

        //          float Spare[8]  ;//

        //     };
        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
        public struct SubStationS
        {

            /// short
            public short PowerID;

            /// short
            public short UpdateRate;

            /// short
            public short DefaultSensorID;

            /// short
            public short Enable;

            /// int
            public int IOCardID;

            /// short
            public short Type;

            /// short
            public short HasExternalCom;

            /// short
            public short ChannelCount;

            /// short
            public short Ctrl;

            /// int
            public int HandMode;

            /// int
            public int HandleCircleSourceCh;

            /// int
            public int HandleUpSourceCh;

            /// float
            public float HandCircleK;

            /// float
            public float MinMoveDev;

            /// SensorAndControllers[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public SensorAndControllers[] Channel;

            /// ExternalCommandS
            public ExternalCommandS ExCommand;

            /// CompensatorS
            public CompensatorS Compensator;

            /// DAS
            public DAS DA;

            /// short
            public short PowerMode;

            /// short
            public short SoftWaveA;

            /// unsigned char
            public byte bak;

            /// unsigned char
            public byte InStopBit;

            /// unsigned char
            public byte OutLowPowerBit;

            /// unsigned char
            public byte OutHighPowerBit;

            /// float[8]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 8, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R4)]
            public float[] Spare;


            /// <summary>
            /// 初始化
            /// </summary>
            public void Initialize()
            {
                int i;

                Channel = new SensorAndControllers[10];
                for (i = 0; i < 10; i++)
                {
                    Channel[i] = new SensorAndControllers();
                }
                ExCommand = new ExternalCommandS();
                Compensator = new CompensatorS();
                DA = new DAS();


                Spare = new float[8];
                for (i = 0; i < 10; i++)
                {

                    Channel[i].Initialize();
                }

                ExCommand.Initialize();
                Compensator.Initialize();
                DA.Initialize();
                Enable = 1;
                PowerID = 1;//                            '动力源
                UpdateRate = 1;//                       '更新速率
                DefaultSensorID = 0;//       '默认控制通道号
                //'Program and Control channels
                Type = 2;//                            '液压控制
                HasExternalCom = 0;//                  '是否有外部命令

                ChannelCount = 2;//                   '实际通道数
                IOCardID = 0;
                Ctrl = 1;
                HandMode = 0;   //无手动盒
                HandCircleK = 0.5f;//手轮灵敏度
                HandleUpSourceCh = 5; //手柄上下 移动光码来源。
                HandleCircleSourceCh = 4;//来自第4个传感器,这个传感器即可以用作手轮，又可以用作手柄
                MinMoveDev = 0.1f;  //移动的最小不控制量。
                PowerMode = 0;
                SoftWaveA = 1000;  //颤振幅值
                bak = 0;
                InStopBit = 0;
                OutLowPowerBit = 0;
                OutHighPowerBit = 0;
            }

        }

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
        public struct DIS1
        {

            /// unsigned char
            public byte CardID;

            /// unsigned char
            public byte InputID;

            /// unsigned char
            public byte InputMode;

            /// unsigned char
            public byte Free;
            public void Initialize()
            {
                CardID = 0;
                InputID = 0;
                InputMode = 0;
                Free = 0;
            }
        }

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
        public struct DOS1
        {

            /// unsigned char
            public byte CardID;

            /// unsigned char
            public byte OutputID;

            /// unsigned char
            public byte InitValue;

            /// unsigned char
            public byte Free;
            public void Initialize()
            {
                CardID = 0;
                OutputID = 0;
                InitValue = 0;
                Free = 0;
            }
        }


    //一个变形的相关参数
    //由4个信号组成
    [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
    public struct OneExtSensorStruct
    {
        [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 4, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I2)]
        public short[] StationID;//[4];
        [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 4, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I2)]
        public short[] SensorID;//[4];
        [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 4, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
        public float[] K;//[4];
        public void Initialize()
        {
            StationID = new short[4];//
            SensorID = new short[4];//
            K = new float[4];//
            for (int i = 0; i < 4; i++)
            {
                StationID[i] = 0;
                SensorID[i] = 0;
                K[i] = 0;
            }
        }
    };
    //多个变形的相关参数
    [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]

    public struct ExtHwStruct
    {
        [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 32, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
        public OneExtSensorStruct[] mySoftSensor;//[32];
        public int Count;//软硬件数量
        public void Initialize()
        {
            mySoftSensor = new OneExtSensorStruct[32];
            for (int i = 0; i < 32; i++)
            {
                mySoftSensor[i] = new OneExtSensorStruct();
                mySoftSensor[i].Initialize();

            }
        }
    };





    ////'站结构
    //     struct StationS
    //     {
    //                long CardNumber ;//                   '卡数量
    //                long  CardVendor; //PCI卡的向量 0x9049
    //                short CardType[10] ;//                 '卡类型数组
    //                short CardAddress[10]; //              '卡的序号0，1，2,10,11,12,20,21,22.新的卡是10的倍数通过这个来区分
    //                                                       //这个可以在vb程序中变成具体的文字。
    //                long  CardSensorNumber[10];           //每块卡的传感器数量
    //                long  CardDANumber[10];               //每块卡的DA资源
    //                long  StationNumber; //                '站数量
    //                SubStationS SubStation[StationNum]; //              '子站数组
    //                short RTSSINT;     //RTSS中的中断次数，默认5000次中断
    //                short DllINT;      //dll中断次数，默认1000次。

    //                //DIS   DigiI[20]   ;//                        '数字量输入数组
    //                //DOS   DigiO[20]   ;//                         '数字量输出数组
    //                unsigned char  CardInputNumber[12] ; //As Byte   ';    //输入数量 3个long
    //                unsigned char    CardOutputNumber[12]; // As Byte '   //输出数量  3个long
    //                //unsigned char    OutputInitValue[40]; // As Byte '; //输出信号初始值 10个long
    //                //unsigned char    InputInitValue[20]; // As Byte ';  //输入信号初始值5个long
    //                DIS1    DigiI[20]; // As DIS1 ' ;   //25个long
    //                DOS1    DigiO[40]; // As DOS1 ';   //50个long
    //                short InputFuncNumber ; //'输入触发器总数
    //                short OutputFuncNumber; //'输出动作总数
    //                unsigned char  InStopAction;   //油源急停触发器
    //                unsigned char  InStartAction;   //油源启动触发器
    //                unsigned char  OutPowerOnAction1;   //油源启动动作
    //                unsigned char  OutPowerOffAction1;   //油源停止动作
    //                unsigned char  OutPowerOnAction2;   //油源启动动作				
    //                unsigned char  OutPowerOffAction2;   //油源停止动作
    //                unsigned char  OutbakAction1;   //备用行为1				
    //                unsigned char  OutbakAction2;   //备用行为2
    //                unsigned char  bak[4]; // As Byte  '//1.5个long

    //                short SimulateFlag ;//是否进行模拟
    //                short SimulateNo2Sensor ;//模拟第二个传感器号
    //                short HandMode;//手动控制
    //                short bak1;
    //                float AddWaveAmplitude;// As Single  '颤振幅值
    //                float AddWaveHz;// As Single   '颤振频率
    //                long Spare[6];
    //     };



    [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential, Pack = 1, CharSet = System.Runtime.InteropServices.CharSet.Ansi)]
        public struct StationS
        {

            /// int
            public int CardNumber;

            /// int
            public int CardVendor;

            /// short[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I2)]
            public short[] CardType;

            /// short[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I2)]
            public short[] CardAddress;

            /// int[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] CardSensorNumber;

            /// int[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] CardDANumber;

            /// int
            public int StationNumber;

            /// SubStationS[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public SubStationS[] SubStation;

            /// short
            public short RTSSINT;

            /// short
            public short DllINT;

            /// unsigned char[12]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 12, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I1)]
            public byte[] CardInputNumber;

            /// unsigned char[12]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 12, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I1)]
            public byte[] CardOutputNumber;

            /// DIS1[32]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 32, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public DIS1[] DigiI;

            /// DOS1[58]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 58, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public DOS1[] DigiO;

            /// unsigned char
            public byte InputFuncNumber;

            /// unsigned char
            public byte OutputFuncNumber;

            /// unsigned char
            public byte UserInputNumber;

            /// unsigned char
            public byte UserOutputNumber;

            /// unsigned char
            public byte OtherInputStop;

            /// unsigned char
            public byte InStopBit;

            /// unsigned char
            public byte OutLowPowerBit;

            /// unsigned char
            public byte OutHighPowerBit;

            /// unsigned char[8]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 8, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I1)]
            public byte[] bak;

            /// short
            public short SimulateFlag;

            /// short
            public short SimulateNo2Sensor;

            /// short
            public short HandMode;

            /// short
            public short bak1;

            /// float
            public float AddWaveAmplitude;

            /// float
            public float AddWaveHz;

            /// int[6]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 6, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Spare;
        
            public ExtHwStruct ComboExtData;  //组合后的变形数据

        /// <summary>
        /// 初始化
        /// </summary>
        public void Initialize()
            {
                int i;
                CardType = new short[10];
                CardAddress = new short[10];
                CardSensorNumber = new int[10];
                CardDANumber = new int[10];

                SubStation = new SubStationS[20];
                for (i = 0; i < 20; i++)
                {
                    SubStation[i] = new SubStationS();
                }

                CardInputNumber = new byte[12];
                CardOutputNumber = new byte[12];

                DigiI = new DIS1[32];
                for (i = 0; i < 32; i++)
                {
                    DigiI[i] = new DIS1();
                }

                DigiO = new DOS1[58];
                for (i = 0; i < 58; i++)
                {
                    DigiO[i] = new DOS1();
                }

                bak = new byte[8];
                Spare = new int[6];

                for (i = 0; i < 20; i++)
                {
                    SubStation[i].Initialize();

                }
                for (i = 0; i < 32; i++)
                {
                    DigiI[i].Initialize();
                }
                for (i = 0; i < 58; i++)
                {
                    DigiO[i].Initialize();
                }

                CardNumber = 2;
                CardVendor = 0;
                StationNumber = 2;
                RTSSINT = 5000;
                DllINT = 1000;
                InputFuncNumber = 0;

                OutputFuncNumber = 0;

                UserInputNumber = 0;

                UserOutputNumber = 0;

                OtherInputStop = 0;

                InStopBit = 0;

                OutLowPowerBit = 0;

                OutHighPowerBit = 0;
                SimulateFlag = 0;
                SimulateNo2Sensor = 0;
                HandMode = 0;
                bak1 = 0;
                AddWaveAmplitude = 0;
                AddWaveHz = 0;
                ComboExtData.Initialize();
            }



        }





        ///win32传递给VB数据   大约64k数据
        //struct Css10ArrDataStruct
        //{

        //     //long OK;                   //这个数据结构是否有效是否有效
        //     long CurveCounts;          //曲线的数量	
        //     long DataStart;            //是否读取数据   1=数据在RTSS中采集完毕，0=数据在dll中读取完毕
        //     long DataCount;            //数组的数据数量，
        //     long MsgCount;             //控制过程消息数目
        //     long Station[10];          //站来源
        //     long Sensor[10];           //传感器来源   0-9是传感器数，10是标准数，11是补偿数，12是DA数
        //     long PlatFormPosCount;     //平台位置点数
        //     long Axis6NewFlag;         //是否是多轴协调控制
        //     PlatFormPosStruct PlatFormPos[20];  //平台位置数据
        //     CssMessageStruct PlatFormMsg[20];   //平台消息
        //     DoPEData1 TestData[20];    //单点数据	 
        //     CssMessageStruct Msg[400];  //控制过程的消息
        //     long PlatFormMsgCount;    //平台数据消息个数
        //     long ComputerID;          //控制器的计算机ID号
        //     long Free[18];             //备用

        //};

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct Css10ArrDataStruct
        {

            /// int
            public int CurveCounts;

            /// int
            public int DataStart;

            /// int
            public int DataCount;

            /// int
            public int MsgCount;

            /// int[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Station;

            /// int[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Sensor;

            /// int
            public int PlatFormPosCount;

            /// int
            public int Axis6NewFlag;

            /// PlatFormPosStruct[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public PlatFormPosStruct[] PlatFormPos;

            /// CssMessageStruct[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public CssMessageStruct[] PlatFormMsg;

            /// DoPEData1[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public DoPEData1[] TestData;

            /// CssMessageStruct[400]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 400, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public CssMessageStruct[] Msg;

            /// int
            public int PlatFormMsgCount;

            /// int[19]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 19, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Free;

            public void Initialize()
            {
                int i;
                Station = new int[10];
                Sensor = new int[10];
                PlatFormPos = new PlatFormPosStruct[20];
                for (i = 0; i < 20; i++)
                {
                    PlatFormPos[i] = new PlatFormPosStruct();

                }

                PlatFormMsg = new CssMessageStruct[20];
                for (i = 0; i < 20; i++)
                {
                    PlatFormMsg[i] = new CssMessageStruct();

                }
                TestData = new DoPEData1[20];
                for (i = 0; i < 20; i++)
                {
                    TestData[i] = new DoPEData1();

                }
                Msg = new CssMessageStruct[400];
                for (i = 0; i < 400; i++)
                {
                    Msg[i] = new CssMessageStruct();

                }
                Free = new int[19];
                //====================================
                for (i = 0; i < 20; i++)
                {
                    PlatFormPos[i].Initialize();

                }
                for (i = 0; i < 20; i++)
                {
                    PlatFormMsg[i].Initialize();

                }
                for (i = 0; i < 20; i++)
                {
                    TestData[i].Initialize();

                }
                for (i = 0; i < 400; i++)
                {
                    Msg[i].Initialize();

                }

                CurveCounts = 0;

                /// int
                DataStart = 0;

                /// int
                DataCount = 0;

                /// int
                MsgCount = 0;
                /// int
                PlatFormPosCount = 0;

                /// int
                Axis6NewFlag = 0;

                PlatFormMsgCount = 0;

            }
        }
        /////15条曲线数组
        //【0-9】【】传感器反馈曲线
        //【10】【】标准命令
        //【11】【】补偿后命令
        //【12】【】DA命令
        //【13】【】周期数
        //struct Css10Arr1000Struct
        //{
        //    float Arr[15][1000];

        //};
        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct Css10Arr1000Struct
        {

            /// float[15000]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 15000, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R4)]
            public float[] Arr;

            public void Initialize()
            {

                Arr = new float[15000];
                for (int i = 0; i < 15000; i++)
                    Arr[i] = 0;

            }
        }
        //单个点最大最小值结构
        //struct MaxMinSingleDataStruct
        //{
        //    float MaxDataArr[10];  //每个通道10个传感器
        //    float MinDataArr[10];  //每个通道10个传感器
        //    long CycleArr;  //循环周期数，20个通道
        //    long RTSSintCounter; //中断数
        //};
        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct MaxMinSingleDataStruct
        {

            /// float[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R4)]
            public float[] MaxDataArr;

            /// float[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R4)]
            public float[] MinDataArr;

            /// int
            public int Cycle;

            /// int
            public int RTSSintCounter;

            public void Initialize()
            {
                MaxDataArr = new float[10];
                MinDataArr = new float[10];
                Cycle = 0;
                RTSSintCounter = 0;
            }
        }

        //struct MaxMinWin32DataStruct
        //{

        //    long Counts[20];	//各个站的峰谷值总数
        //    MaxMinSingleDataStruct  Data[20][30];  //30个最大最小数据缓冲
        //    long free[1780];

        //};

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct MaxMinWin32DataStruct
        {

            /// int[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Counts;

            /// MaxMinSingleDataStruct[600]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 600, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public MaxMinSingleDataStruct[] Data;//原来的[20, 30];

            /// int[1780]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 1780, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] free;

            public void Initialize()
            {
                int i;
                Counts = new int[20];
                Data = new MaxMinSingleDataStruct[600];
                for (i = 0; i < 600; i++)//20个缸，每个缸30个缓冲
                {
                    Data[i] = new MaxMinSingleDataStruct();
                }
                free = new int[1780];
                for (i = 0; i < 600; i++)
                {
                    Data[i].Initialize();
                }

            }
        }

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct MaxMinProtectDataStruct
        {

            /// int[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] action;

            /// double[200]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 200, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R8)]
            public double[] MaxDataArr;//[20, 10]  X*20+i

            /// double[200]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 200, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R8)]
            public double[] MinDataArr;//[20, 10]  X*20+i

            /// int[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] free;

            public void Initialize()
            {

                action = new int[20];
                MaxDataArr = new double[200];
                MinDataArr = new double[200];
                for (int i = 0; i < 200; i++)  
                {
                    
                        MaxDataArr[i] = 10000000000;//初始化最大最小值
                        MinDataArr[i] = -10000000000;//初始化最大最小值
                    
                }
                free = new int[20];

            }
        }

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct XYZStruct
        {

            /// double
            public double x;

            /// double
            public double y;

            /// double
            public double z;

            public void Initialize()
            {
                x = 0;
                y = 0;
                z = 0;

            }
        }

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct PlatFormPosStruct
        {

            /// XYZStruct
            public XYZStruct CenterPoint;

            /// XYZStruct
            public XYZStruct NormalPoint;

            /// double
            public double CircleAngle;

            /// double[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R8)]
            public double[] LVDSValue;

            /// XYZStruct[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public XYZStruct[] SupportPos;

            public void Initialize()
            {
                int i;
                CenterPoint = new XYZStruct();
                NormalPoint = new XYZStruct();
                LVDSValue = new double[20];
                SupportPos = new XYZStruct[20];
                for (i = 0; i < 20; i++)
                {
                    SupportPos[i] = new XYZStruct();
                }
                CenterPoint.Initialize();
                NormalPoint.Initialize();
                for (i = 0; i < 20; i++)
                {
                    SupportPos[i].Initialize();
                }
                CircleAngle = 0;
            }
        }



        /// <summary>
        /// DynwaveData.CtrlMode = jxSensorID ';                             //控制传感器

        //DynwaveData.WaveMode = 0   ';                             //什么波形控制0=正弦  1=三角  2=方波3=指数
        //DynwaveData.Modify = 0  ';                               //是否继续原来的控制周期，但是改变了频率和幅值
        //DynwaveData.PeakCtrl = 3   ';                             //是否峰值补偿  0=不补偿  >=2是几个周期完成补偿
        //DynwaveData.RelativeDestination = 0   ';                  //是否相对当前位置

        //  DynwaveData.Offset = mjxdata.Cjxdata.HSFSQValue(jxStation * 3 + 1) ';                               //偏移量
        //  DynwaveData.Amplitude = mjxdata.Cjxdata.HSFSQValue(jxStation * 3)   ';                            //幅值
        //  DynwaveData.Frequency = mjxdata.Cjxdata.HSFSQValue(jxStation * 3 + 2)   ';                            //频率    也可作起始频率
        //  DynwaveData.StartPhase = 0  ';                          //起始相位
        //  DynwaveData.HaltTimeAtPlusAmplitude = 0  ' ;             //峰值保持时间
        //  DynwaveData.HaltTimeAtMinusAmplitude = 0  ' ;            //峰谷保持时间
        //  DynwaveData.Cycles = 100000000  ';                               //周期数

        //  DynwaveData.FadeCtrl = 1   ';                     //斜线模式  0=单斜线方式    1=倾斜轮廓线渐进
        //  DynwaveData.ZeroToDestTime = 2 ';                       //Mode=0，起始到最大值时间    （如果为零，就按照最大幅值来做）
        //  DynwaveData.DestToZeroTime = 2 ';                      // Mode=0，最大值到中止值时间。
        //  DynwaveData.Destination = 0   ';                          //结束位置

        //  DynwaveData.SweepFrequencyMode = 99 ';                   //扫频模式      0=正弦  1=三角  2=方波3=指数      0ff=99
        //  DynwaveData.SweepFrequencyStopHz = 1  ';               //停止频率
        //  DynwaveData.SweepFrequencyTime = 1    ';                 //扫频时间
        //  DynwaveData.SweepFrequencyCount = 1    ';                  //扫频数

        //  DynwaveData.SweepOffsetMode = 99  ';                      //扫中值模式       0=正弦  1=三角  2=方波3=指数   0ff=99
        //  DynwaveData.SweepOffsetAmplitude = 1  ';                     //停止中值
        //  DynwaveData.SweepOffsetFrequency = 1    ';                     //扫中值时间
        //  DynwaveData.SweepOffsetCount = 1    ';                     //扫中值数

        //  DynwaveData.SweepAmplitudeMode = 99 ';                   //扫幅模式     0=正弦  1=三角  2=方波3=指数   0ff=99
        //  DynwaveData.SweepAmplitudeAmplitude = 1   ';              //扫幅幅值
        //  DynwaveData.SweepAmplitudeFrequency = 1    ';              //扫幅频率
        //  DynwaveData.SweepAmplitudeCount = 1   ';                  //扫幅数

        //  DynwaveData.SuperpositionMode = 99  ';                    //超级位置模式0=正弦  1=三角  2=方波  3=指数   4=自定义  无=99
        //  DynwaveData.SuperSensorID = 1  ';                       //自定义传感器通道
        //  DynwaveData.SuperpositionAmplitude = 1   ';              //幅值
        //  DynwaveData.SuperpositionFrequency = 1  ';              //频率
        //  DynwaveData.SuperpositionCount = 1   ';                   //周期数

        //  DynwaveData.BimodalCtrlMode = 99  ';                      //第二控制模式 99=Off    1=平均值保持  2=最小值保持  3= 最大值保持
        //                                                    '            //    4=最大值和最小值都保持  5=最大值最小值差值保持
        //  DynwaveData.BimodalSensorID = 1    ';                      //传感器1   正常有第二传感器
        //  DynwaveData.BimodalValue1 = 1    ';                        //第二传感器最小值、平均值、最大值、差值
        //  DynwaveData.BimodalMaxValue = 1    ';                      //第二传感器最大值
        //  DynwaveData.BimodalScale = 1    ';                         //第二传感器范围

        /// </summary>
        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct DynCycleDataStruct
        {

            /// int
            public int CtrlMode;

            /// int
            public int WaveMode;

            /// int
            public int Modify;

            /// int
            public int PeakCtrl;

            /// int
            public int bak1;

            /// int
            public int RelativeDestination;

            /// float
            public float Offset;

            /// float
            public float Amplitude;

            /// float
            public float Frequency;

            /// float
            public float StartPhase;

            /// float
            public float HaltTimeAtPlusAmplitude;

            /// float
            public float HaltTimeAtMinusAmplitude;

            /// int
            public int Cycles;

            /// int
            public int FadeCtrl;

            /// float
            public float ZeroToDestTime;

            /// float
            public float DestToZeroTime;

            /// float
            public float Destination;

            /// int
            public int SweepFrequencyMode;

            /// float
            public float SweepFrequencyStopHz;

            /// float
            public float SweepFrequencyTime;

            /// int
            public int SweepFrequencyCount;

            /// int
            public int SweepOffsetMode;

            /// float
            public float SweepOffsetAmplitude;

            /// float
            public float SweepOffsetFrequency;

            /// int
            public int SweepOffsetCount;

            /// int
            public int SweepAmplitudeMode;

            /// float
            public float SweepAmplitudeAmplitude;

            /// float
            public float SweepAmplitudeFrequency;

            /// int
            public int SweepAmplitudeCount;

            /// int
            public int SuperpositionMode;

            /// int
            public int SuperSensorID;

            /// float
            public float SuperpositionAmplitude;

            /// float
            public float SuperpositionFrequency;

            /// int
            public int SuperpositionCount;

            /// int
            public int BimodalCtrlMode;

            /// int
            public int BimodalSensorID;

            /// float
            public float BimodalValue1;

            /// float
            public float BimodalValue2;

            /// float
            public float BimodalScale;

            /// int
            public int bak;

            public void Initialize()
            {
                /// int
                CtrlMode = 0;

                /// int
                WaveMode = 0;

                /// int
                Modify = 0;

                /// int
                PeakCtrl = 0;

                /// int
                bak1 = 0;

                /// int
                RelativeDestination = 0;

                /// float
                Offset = 0;

                /// float
                Amplitude = 0;

                /// float
                Frequency = 0;

                /// float
                StartPhase = 0;

                /// float
                HaltTimeAtPlusAmplitude = 0;

                /// float
                HaltTimeAtMinusAmplitude = 0;

                /// int
                Cycles = 0;

                /// int
                FadeCtrl = 0;

                /// float
                ZeroToDestTime = 0;

                /// float
                DestToZeroTime = 0;

                /// float
                Destination = 0;

                /// int
                SweepFrequencyMode = 0;

                /// float
                SweepFrequencyStopHz = 0;

                /// float
                SweepFrequencyTime = 0;

                /// int
                SweepFrequencyCount = 0;

                /// int
                SweepOffsetMode = 0;

                /// float
                SweepOffsetAmplitude = 0;

                /// float
                SweepOffsetFrequency = 0;

                /// int
                SweepOffsetCount = 0;

                /// int
                SweepAmplitudeMode = 0;

                /// float
                SweepAmplitudeAmplitude = 0;

                /// float
                SweepAmplitudeFrequency = 0;

                /// int
                SweepAmplitudeCount = 0;

                /// int
                SuperpositionMode = 0;

                /// int
                SuperSensorID = 0;

                /// float
                SuperpositionAmplitude = 0;

                /// float
                SuperpositionFrequency = 0;

                /// int
                SuperpositionCount = 0;

                /// int
                BimodalCtrlMode = 0;

                /// int
                BimodalSensorID = 0;

                /// float
                BimodalValue1 = 0;

                /// float
                BimodalValue2 = 0;

                /// float
                BimodalScale = 0;

                /// int
                bak = 0;
            }
        }

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct CssMessageStruct
        {

            /// int
            public int Station;

            /// int
            public int SensorID;

            /// int
            public int CurveType;

            /// int
            public int CtrlState;

            /// int
            public int CycleTimes;

            /// int
            public int Err;

            /// int
            public int NowSeg;

            /// int
            public uint MsgPositionCount;

            /// int
            public float MaxMinProtectData;

            /// int[1]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 1, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Free;

            public void Initialize()
            {
                Free = new int[1];
                /// int
                Station = 0;

                /// int
                SensorID = 0;

                /// int
                CurveType = 0;

                /// int
                CtrlState = 0;

                /// int
                CycleTimes = 0;

                /// int
                Err = 0;

                /// int
                NowSeg = 0;

                /// int
                MsgPositionCount = 0;

                /// int
                MaxMinProtectData = 0;
            }



        }
        //sensor(13)  当前通道最大值
        //sensor(14)  当前通道最小值
        //sensor(15)  当前通道控制值的绝对值
        //sensor(16)  Time
        //Sensor(17)  TheoryCmd
        //Sensor(18)  TrueCmd
        //Sensor(19)  DA
        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct DoPEData1
        {

            /// double[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.R8)]
            public double[] Sensor;

            /// int[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] binSensorValue;


            /// ushort[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.U2)]
            public ushort[] BitIn;
            ///// unsigned short
            //public ushort BitIn0;

            ///// unsigned short
            //public ushort BitIn1;

            ///// unsigned short
            //public ushort BitIn2;

            ///// unsigned short
            //public ushort BitIn3;

            ///// unsigned short
            //public ushort BitIn4;

            ///// unsigned short
            //public ushort BitIn5;

            ///// unsigned short
            //public ushort BitIn6;

            ///// unsigned short
            //public ushort BitIn7;

            ///// unsigned short
            //public ushort BitIn8;

            ///// unsigned short
            //public ushort BitIn9;


            /// ushort[10]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 10, ArraySubType = System.Runtime.InteropServices.UnmanagedType.U2)]
            public ushort[] BitOut;
            ///// unsigned short
            //public ushort BitOut0;

            ///// unsigned short
            //public ushort BitOut1;

            ///// unsigned short
            //public ushort BitOut2;

            ///// unsigned short
            //public ushort BitOut3;

            ///// unsigned short
            //public ushort BitOut4;

            ///// unsigned short
            //public ushort BitOut5;

            ///// unsigned short
            //public ushort BitOut6;

            ///// unsigned short
            //public ushort BitOut7;

            ///// unsigned short
            //public ushort BitOut8;

            ///// unsigned short
            //public ushort BitOut9;

            /// unsigned short
            public ushort InSignals;

            /// unsigned short
            public ushort OutSignals;

            /// unsigned short
            public ushort CtrlState1;

            /// unsigned short
            public ushort CtrlState2;

            /// unsigned short
            public ushort UpperLimits;

            /// unsigned short
            public ushort LowerLimits;

            /// unsigned short
            public ushort SysState0;

            /// unsigned short
            public ushort SysState1;

            /// unsigned short
            public ushort SysState2;

            /// unsigned short
            public ushort SysState3;

            /// unsigned short
            public ushort SysState4;

            /// unsigned short
            public ushort SysState5;

            /// double
            public double Test1;

            /// double
            public double Test2;

            /// double
            public double Test3;

            /// unsigned short
            public ushort Keys;

            /// unsigned short
            public ushort NewKeys;

            /// unsigned short
            public ushort GoneKeys;

            /// unsigned short
            public ushort free1;

            /// int
            public int Sync1;

            /// int
            public  uint RTSSCounter;

            public void Initialize()
            {
                Sensor = new double[20];
                binSensorValue = new int[20];
                /// unsigned short
                BitIn = new ushort[10];
                for (int i = 0; i < 10; i++)
                {
                    BitIn[i] = 0;
                }
                BitOut = new ushort[10];
                for (int i = 0; i < 10; i++)
                {
                    BitOut[i] = 0;
                }
                /// unsigned short


                /// unsigned short
                InSignals = 0;

                /// unsigned short
                OutSignals = 0;

                /// unsigned short
                CtrlState1 = 0;

                /// unsigned short
                CtrlState2 = 0;

                /// unsigned short
                UpperLimits = 0;

                /// unsigned short
                LowerLimits = 0;

                /// unsigned short
                SysState0 = 0;

                /// unsigned short
                SysState1 = 0;

                /// unsigned short
                SysState2 = 0;

                /// unsigned short
                SysState3 = 0;

                /// unsigned short
                SysState4 = 0;

                /// unsigned short
                SysState5 = 0;

                /// double
                Test1 = 0;

                /// double
                Test2 = 0;

                /// double
                Test3 = 0;

                /// unsigned short
                Keys = 0;

                /// unsigned short
                NewKeys = 0;

                /// unsigned short
                GoneKeys = 0;

                /// unsigned short
                free1 = 0;

                /// int
                Sync1 = 0;

                /// uint
                RTSSCounter = 0;

            }
        }




        /*
        *******************************************************************
        整个群的数据结构
        *******************************************************************
        */
        //struct StationOfGroupStruct
        //{	
        //    long ControllerComputerID;//控制器计算机号来源   0-N，计算机号
        //    long ControllerStation;//控制器内的站来源   0-N  箱内的站号
        //    long Online;				//有效否  0=无效   1=有效   实时更新
        //    long bak1;				//备用
        //};


        //struct GroupStruct
        //{
        //    long GroupID;              //群号
        //    long StationCount;    //站数量    0=无  
        //    long bak;
        //    long bak1;
        //    StationOfGroupStruct Station[64]; //群内站数量,最大64个


        //};

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct StationOfGroupStruct
        {

            /// int
            public int ControllerComputerID;

            /// int
            public int ControllerStation;

            /// int
            public int Online;

            /// int
            public int bak1;


            public void Initialize()
            {
                ControllerComputerID = 0;
                ControllerStation = 0;
                Online = 0;
                bak1 = 0;
            }
        }

        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct GroupStruct
        {

            /// int
            public int GroupID;

            /// int
            public int StationCount;

            /// int
            public int bak;

            /// int
            public int bak1;

            /// StationOfGroupStruct[64]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 64, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public StationOfGroupStruct[] Station;
            public void Initialize()
            {
                Int32 i;
                Station = new StationOfGroupStruct[64];
                for (i = 0; i < 64; i++)
                {
                    Station[i] = new StationOfGroupStruct();
                }

                for (i = 0; i < 64; i++)
                {
                    Station[i].Initialize();

                }
                GroupID = 0;
                StationCount = 2;
                bak = 0;
                bak1 = 0;

            }
        }


        /*
        *******************************************************************
        本地网络设备的数据结构

        *******************************************************************
        */

        //struct  ComputerNetstruct
        //{

        //    long ComputerID;//计算机号（0-9）服务器=0
        //    long  ServerOrClient;//客户、服务器类型  0=特殊服务器（软件服务器+多个客户端+无控制器） 1=软件客户端  2=控制器  
        //    long  IP1;    //IP号
        //    long  IP2;
        //    long  IP3;
        //    long  IP4;
        //    long  PortID; //端口号	
        //    long Controller;//是否带控制器	0=无  1=有
        //    long  CoreComputerID;//中心计算机ID号
        //    long Free[7];
        //};
        [Serializable]
        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct ComputerNetstruct
        {

            /// int
            public int ComputerID;

            /// int
            public int ServerOrClient;

            /// int
            public int IP1;

            /// int
            public int IP2;

            /// int
            public int IP3;

            /// int
            public int IP4;

            /// int
            public int PortID;

            /// int
            public int Controller;

            /// int
            public int DestComputerID;

            /// int[7]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 7, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Free;
            public void Initialize()
            {
                Free = new int[7];
                ComputerID = 0;
                ServerOrClient = 0;
                IP1 = 127;
                IP2 = 0;
                IP3 = 0;
                IP4 = 1;
                PortID = 5555;
                Controller = 0;
                DestComputerID = 0;
            }
        }
        /*
        *******************************************************************
        整个网络的数据结构
        *******************************************************************
        */
        //struct AllNetSetupStruct
        //{
        //    long  HardwareDeviceCount;			//设备数量(包括控制器，计算机和服务器）
        //    long  GroupCount;					//群数量

        //    ///===================硬件定义========================================
        //    ComputerNetstruct HwDevice[20];		//最多20个设备（包括控制器，计算机和服务器）
        //    ///=================群定义==================================
        //    GroupStruct  Group[20];				//最多20个群

        //    //===================软件cs架构网络定义================================================

        //    long  DebugFlag;                    //调试模式 0=非调试模式  1=调试模式（在一台计算机上模拟多个计算机）
        //    long  Free[9];						//备用	

        //};
        [Serializable]
        [System.Runtime.InteropServices.StructLayoutAttribute(System.Runtime.InteropServices.LayoutKind.Sequential)]
        public struct AllNetSetupStruct
        {

            /// int
            public int HardwareDeviceCount;

            /// int
            public int GroupCount;

            /// ComputerNetstruct[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public ComputerNetstruct[] HwDevice;

            /// GroupStruct[20]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = System.Runtime.InteropServices.UnmanagedType.Struct)]
            public GroupStruct[] Group;

            /// int
            public int DebugFlag;

            /// int[9]
            [System.Runtime.InteropServices.MarshalAsAttribute(System.Runtime.InteropServices.UnmanagedType.ByValArray, SizeConst = 9, ArraySubType = System.Runtime.InteropServices.UnmanagedType.I4)]
            public int[] Free;
            public void Initialize()
            {
                Int32 i;
                HwDevice = new ComputerNetstruct[20];
                for (i = 0; i < 20; i++)
                {
                    HwDevice[i] = new ComputerNetstruct();
                }
                Group = new GroupStruct[20];
                for (i = 0; i < 20; i++)
                {
                    Group[i] = new GroupStruct();
                }
                Free = new int[9];
                for (i = 0; i < 20; i++)
                {
                    HwDevice[i].Initialize();
                }
                for (i = 0; i < 20; i++)
                {
                    Group.Initialize();
                }
                HardwareDeviceCount = 2;
                GroupCount = 1;
                DebugFlag = 0;
            }
        }

        


        /// Return Type: void
        ///a1: int
        ///a2: int
        //public delegate void pInterrupt(int a1, int a2);

        /// Return Type: void
        ///a1: CssMessageStruct*
        //public delegate void pMsgInfo(ref CssMessageStruct a1);


    //[UnmanagedFunctionPointer(CallingConvention.Cdecl)]  
        public delegate void pDataInfoDeleg(
                    ref Css10ArrDataStruct a1,
                    ref Css10Arr1000Struct Curve0,
                    ref Css10Arr1000Struct Curve1,
                    ref Css10Arr1000Struct Curve2,
                    ref Css10Arr1000Struct Curve3,
                    ref Css10Arr1000Struct Curve4,
                    ref Css10Arr1000Struct Curve5,
                    ref Css10Arr1000Struct Curve6,
                    ref Css10Arr1000Struct Curve7,
                    ref Css10Arr1000Struct Curve8,
                    ref Css10Arr1000Struct Curve9,
                    ref Css10Arr1000Struct Curve10,
                    ref Css10Arr1000Struct Curve11,
                    ref Css10Arr1000Struct Curve12,
                    ref Css10Arr1000Struct Curve13,
                    ref Css10Arr1000Struct Curve14,
                    ref Css10Arr1000Struct Curve15,
                    ref Css10Arr1000Struct Curve16,
                    ref Css10Arr1000Struct Curve17,
                    ref Css10Arr1000Struct Curve18,
                    ref MaxMinWin32DataStruct xMaxMinData,
                    int ArrCount);



    


}
