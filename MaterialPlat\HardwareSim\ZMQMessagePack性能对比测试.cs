using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using IHardware;
using MessagePack;
using NetMQ;
using NetMQ.Sockets;
using NUnit.Framework;
using static IHardware.Hw;

namespace HardwareSim.PerformanceTests
{
    /// <summary>
    /// ZeroMQ + MessagePack 性能对比测试
    /// 对比原始CDataBlock与FlatCDataBlock的网络传输性能
    /// </summary>
    [TestFixture]
    public class ZMQMessagePackPerformanceTests
    {
        private const string TestEndpoint = "tcp://localhost:55555";
        private const int TestIterations = 1000; // 测试次数
        private const int WarmupIterations = 100; // 预热次数

        [Test]
        [TestCase(10, 50, 4, Description = "小规模数据")]
        [TestCase(50, 200, 16, Description = "中规模数据")]
        [TestCase(100, 500, 64, Description = "大规模数据")]
        public async Task CompareZMQMessagePackPerformance(int servoAxisCount, int dataCountPerAxis, int sensorCount)
        {
            Console.WriteLine($"\n=== ZeroMQ + MessagePack 性能测试 ===");
            Console.WriteLine($"测试规模: {servoAxisCount}轴 x {dataCountPerAxis}点/轴 x {sensorCount}传感器/点");
            Console.WriteLine($"测试次数: {TestIterations}次 (预热{WarmupIterations}次)");
            Console.WriteLine($"传输协议: ZeroMQ + MessagePack\n");

            // 创建测试数据
            var para = CreateTestParameters(servoAxisCount, dataCountPerAxis, sensorCount);
            var originalData = CreateTestCDataBlock(para);
            var flatData = FlatCDataBlock.FromCDataBlock(originalData);

            // 序列化数据大小对比
            var originalSerialized = MessagePackSerializer.Serialize(originalData);
            var flatSerialized = MessagePackSerializer.Serialize(flatData);

            Console.WriteLine($"序列化大小对比:");
            Console.WriteLine($"  原始数据: {originalSerialized.Length:N0} bytes");
            Console.WriteLine($"  扁平数据: {flatSerialized.Length:N0} bytes");
            Console.WriteLine($"  大小减少: {(1.0 - (double)flatSerialized.Length / originalSerialized.Length) * 100:F2}%\n");

            // 预热JIT编译器
            Console.WriteLine("预热JIT编译器...");
            await PerformZMQTest(originalData, flatData, WarmupIterations, isWarmup: true);

            // 正式性能测试
            Console.WriteLine("开始正式性能测试...\n");
            var results = await PerformZMQTest(originalData, flatData, TestIterations, isWarmup: false);

            // 输出详细结果
            PrintPerformanceResults(results, originalSerialized.Length, flatSerialized.Length);
        }

        private async Task<PerformanceResults> PerformZMQTest(CDataBlock originalData, FlatCDataBlock flatData, int iterations, bool isWarmup)
        {
            var results = new PerformanceResults();

            // 测试原始数据传输
            var originalResults = await TestDataTransmission(originalData, iterations, "原始数据", isWarmup);
            results.OriginalSendTime = originalResults.SendTime;
            results.OriginalReceiveTime = originalResults.ReceiveTime;
            results.OriginalTotalTime = originalResults.TotalTime;

            // 短暂延迟避免端口冲突
            await Task.Delay(100);

            // 测试扁平数据传输
            var flatResults = await TestDataTransmission(flatData, iterations, "扁平数据", isWarmup);
            results.FlatSendTime = flatResults.SendTime;
            results.FlatReceiveTime = flatResults.ReceiveTime;
            results.FlatTotalTime = flatResults.TotalTime;

            return results;
        }

        private async Task<TestResult> TestDataTransmission<T>(T data, int iterations, string testName, bool isWarmup)
        {
            var sendTimes = new List<double>();
            var receiveTimes = new List<double>();
            var totalTimes = new List<double>();

            using var publisher = new PublisherSocket();
            using var subscriber = new SubscriberSocket();

            // 设置套接字选项
            publisher.Options.SendHighWaterMark = 10000;
            subscriber.Options.ReceiveHighWaterMark = 10000;
            subscriber.Options.ReceiveTimeout = TimeSpan.FromSeconds(5);

            publisher.Bind(TestEndpoint);
            subscriber.Connect(TestEndpoint);
            subscriber.Subscribe("test");

            // 等待连接建立
            await Task.Delay(100);

            var receivedCount = 0;
            var receiveTask = Task.Run(() =>
            {
                var sw = Stopwatch.StartNew();
                while (receivedCount < iterations)
                {
                    try
                    {
                        var receiveStart = sw.Elapsed.TotalMilliseconds;
                        var message = subscriber.ReceiveMultipartMessage();
                        if (message.FrameCount >= 2)
                        {
                            var serializedData = message[1].ToByteArray();
                            var deserializedData = MessagePackSerializer.Deserialize<T>(serializedData);
                            var receiveEnd = sw.Elapsed.TotalMilliseconds;
                            
                            if (!isWarmup)
                            {
                                receiveTimes.Add(receiveEnd - receiveStart);
                            }
                            receivedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        if (!isWarmup)
                            Console.WriteLine($"接收异常: {ex.Message}");
                        break;
                    }
                }
            });

            // 发送数据
            await Task.Delay(50); // 确保接收端准备就绪
            var overallStart = Stopwatch.StartNew();

            for (int i = 0; i < iterations; i++)
            {
                var sendStart = Stopwatch.StartNew();
                var serializedData = MessagePackSerializer.Serialize(data);
                publisher.SendMoreFrame("test").SendFrame(serializedData);
                var sendTime = sendStart.Elapsed.TotalMilliseconds;

                if (!isWarmup)
                {
                    sendTimes.Add(sendTime);
                }

                // 控制发送频率，避免过快导致丢包
                if (i % 100 == 0)
                {
                    await Task.Delay(1);
                }
            }

            // 等待接收完成
            await receiveTask;
            var totalTime = overallStart.Elapsed.TotalMilliseconds;

            if (!isWarmup)
            {
                Console.WriteLine($"{testName} - 发送: {sendTimes.Count}, 接收: {receiveTimes.Count}, 成功率: {(double)receiveTimes.Count / iterations * 100:F1}%");
            }

            return new TestResult
            {
                SendTime = sendTimes.Count > 0 ? sendTimes.Average() : 0,
                ReceiveTime = receiveTimes.Count > 0 ? receiveTimes.Average() : 0,
                TotalTime = totalTime
            };
        }

        private void PrintPerformanceResults(PerformanceResults results, int originalSize, int flatSize)
        {
            Console.WriteLine("=== 性能测试结果 ===\n");

            Console.WriteLine("发送性能 (平均每次序列化+发送时间):");
            Console.WriteLine($"  原始数据: {results.OriginalSendTime:F3} ms");
            Console.WriteLine($"  扁平数据: {results.FlatSendTime:F3} ms");
            Console.WriteLine($"  性能提升: {((results.OriginalSendTime - results.FlatSendTime) / results.OriginalSendTime * 100):F2}%\n");

            Console.WriteLine("接收性能 (平均每次接收+反序列化时间):");
            Console.WriteLine($"  原始数据: {results.OriginalReceiveTime:F3} ms");
            Console.WriteLine($"  扁平数据: {results.FlatReceiveTime:F3} ms");
            Console.WriteLine($"  性能提升: {((results.OriginalReceiveTime - results.FlatReceiveTime) / results.OriginalReceiveTime * 100):F2}%\n");

            Console.WriteLine("总体传输性能:");
            Console.WriteLine($"  原始数据总时间: {results.OriginalTotalTime:F3} ms");
            Console.WriteLine($"  扁平数据总时间: {results.FlatTotalTime:F3} ms");
            Console.WriteLine($"  总体性能提升: {((results.OriginalTotalTime - results.FlatTotalTime) / results.OriginalTotalTime * 100):F2}%\n");

            Console.WriteLine("传输效率分析:");
            var originalThroughput = (double)originalSize * TestIterations / (results.OriginalTotalTime / 1000) / 1024 / 1024;
            var flatThroughput = (double)flatSize * TestIterations / (results.FlatTotalTime / 1000) / 1024 / 1024;
            Console.WriteLine($"  原始数据吞吐量: {originalThroughput:F2} MB/s");
            Console.WriteLine($"  扁平数据吞吐量: {flatThroughput:F2} MB/s");
            Console.WriteLine($"  吞吐量提升: {((flatThroughput - originalThroughput) / originalThroughput * 100):F2}%\n");

            Console.WriteLine("网络传输优化总结:");
            Console.WriteLine($"  数据压缩率: {(1.0 - (double)flatSize / originalSize) * 100:F2}%");
            Console.WriteLine($"  发送性能提升: {((results.OriginalSendTime - results.FlatSendTime) / results.OriginalSendTime * 100):F2}%");
            Console.WriteLine($"  接收性能提升: {((results.OriginalReceiveTime - results.FlatReceiveTime) / results.OriginalReceiveTime * 100):F2}%");
            Console.WriteLine($"  整体性能提升: {((results.OriginalTotalTime - results.FlatTotalTime) / results.OriginalTotalTime * 100):F2}%");
        }

        private DataBlockPara CreateTestParameters(int servoAxisCount, int dataCountPerAxis, int sensorCount)
        {
            return new DataBlockPara
            {
                ServoAxisCount = servoAxisCount,
                ServoAxisDataCount = dataCountPerAxis,
                ServoSensorCount = sensorCount,
                TempAxisCount = 4,
                TempAxisDataCount = 50,
                TempSensorCount = 4,
                CreepAxisCount = 2,
                CreepAxisDataCount = 25,
                CreepSensorCount = 2,
                InCount = 16,
                OutCount = 16,
                ADCount = 32
            };
        }

        private CDataBlock CreateTestCDataBlock(DataBlockPara para)
        {
            var dataBlock = new CDataBlock();
            dataBlock.AllocateMemory(para);
            FillSimulatedData(dataBlock, para);
            return dataBlock;
        }

        private void FillSimulatedData(CDataBlock dataBlock, DataBlockPara para)
        {
            var random = new Random(42); // 固定种子确保测试一致性

            // 填充伺服数据
            if (dataBlock.ServoData != null)
            {
                for (int i = 0; i < Math.Min(para.ServoAxisCount, dataBlock.ServoData.Length); i++)
                {
                    if (dataBlock.ServoData[i] != null)
                    {
                        dataBlock.ServoData[i].DataCount = para.ServoAxisDataCount;
                        
                        for (int j = 0; j < Math.Min(para.ServoAxisDataCount, dataBlock.ServoData[i].ChData?.Length ?? 0); j++)
                        {
                            var data = dataBlock.ServoData[i].ChData![j];
                            if (data != null)
                            {
                                data.Command = 100.0 + random.NextDouble() * 50;
                                data.Feedback = data.Command + (random.NextDouble() - 0.5) * 2;
                                data.Output = random.NextDouble() * 100;
                                data.Timer = Environment.TickCount;
                                
                                if (data.Sensor != null)
                                {
                                    for (int k = 0; k < Math.Min(para.ServoSensorCount, data.Sensor.Length); k++)
                                    {
                                        data.Sensor[k] = random.NextDouble() * 1000;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 填充其他数据类型...
            dataBlock.ServoChCount = para.ServoAxisCount;
            dataBlock.TempChCount = para.TempAxisCount;
            dataBlock.CreepChCount = para.CreepAxisCount;
            dataBlock.InCount = para.InCount;
            dataBlock.OutCount = para.OutCount;
            dataBlock.ADCount = para.ADCount;
        }

        private class PerformanceResults
        {
            public double OriginalSendTime { get; set; }
            public double OriginalReceiveTime { get; set; }
            public double OriginalTotalTime { get; set; }
            public double FlatSendTime { get; set; }
            public double FlatReceiveTime { get; set; }
            public double FlatTotalTime { get; set; }
        }

        private class TestResult
        {
            public double SendTime { get; set; }
            public double ReceiveTime { get; set; }
            public double TotalTime { get; set; }
        }
    }
}
