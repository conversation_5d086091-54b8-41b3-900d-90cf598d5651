using System.Diagnostics;
using ZMQPerformanceTest;

namespace ZMQPerformanceTest
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("ZMQ + MessagePack 性能测试工具");
            Console.WriteLine("================================");
            Console.WriteLine();

            bool runTrace = args.Contains("--trace");
            bool runProfile = args.Contains("--profile");
            bool runTraceAnalysis = args.Contains("--trace-analysis");
            bool runMemoryTest = args.Contains("--memory-test");

            if (runTraceAnalysis)
            {
                Console.WriteLine("🔍 启动专业trace分析模式");
                await RunTraceAnalysis();
            }
            else if (runMemoryTest)
            {
                Console.WriteLine("🧠 启动内存压力测试模式");
                await RunMemoryTest();
            }
            else if (runTrace)
            {
                Console.WriteLine("🔍 启动性能追踪模式 (需要 dotnet-trace)");
                await RunWithTrace();
            }
            else if (runProfile)
            {
                Console.WriteLine("📊 启动性能分析模式");
                await RunWithProfiler();
            }
            else
            {
                Console.WriteLine("🚀 启动标准性能测试模式");
                await RunStandardTest();
            }

            Console.WriteLine();
            Console.WriteLine("测试完成! 按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 标准性能测试
        /// </summary>
        static async Task RunStandardTest()
        {
            var benchmark = new ZMQPerformanceBenchmark();
            await benchmark.RunPerformanceComparison();
        }

        /// <summary>
        /// 使用 dotnet-trace 进行性能追踪
        /// </summary>
        static async Task RunWithTrace()
        {
            var processId = Process.GetCurrentProcess().Id;
            Console.WriteLine($"当前进程 ID: {processId}");
            Console.WriteLine("请在另一个终端运行以下命令进行追踪:");
            Console.WriteLine($"dotnet trace collect --process-id {processId} --output zmq-performance.nettrace");
            Console.WriteLine();
            Console.WriteLine("按任意键开始测试 (确保已启动trace收集)...");
            Console.ReadKey();

            // 运行性能测试
            var benchmark = new ZMQPerformanceBenchmark();
            await benchmark.RunPerformanceComparison();

            Console.WriteLine();
            Console.WriteLine("测试完成! 可以停止 dotnet trace 收集");
            Console.WriteLine("使用以下命令分析结果:");
            Console.WriteLine("dotnet trace analyze zmq-performance.nettrace");
        }

        /// <summary>
        /// 内置性能分析
        /// </summary>
        static async Task RunWithProfiler()
        {
            Console.WriteLine("开始内存和CPU性能分析...");
            
            // GC 信息收集
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var initialMemory = GC.GetTotalMemory(false);
            var initialGen0 = GC.CollectionCount(0);
            var initialGen1 = GC.CollectionCount(1);
            var initialGen2 = GC.CollectionCount(2);

            Console.WriteLine($"初始内存使用: {initialMemory / 1024.0 / 1024.0:F2} MB");
            Console.WriteLine($"初始GC统计: Gen0={initialGen0}, Gen1={initialGen1}, Gen2={initialGen2}");
            Console.WriteLine();

            // 运行测试
            var stopwatch = Stopwatch.StartNew();
            var benchmark = new ZMQPerformanceBenchmark();
            await benchmark.RunPerformanceComparison();
            stopwatch.Stop();

            // 收集结束时的信息
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var finalMemory = GC.GetTotalMemory(false);
            var finalGen0 = GC.CollectionCount(0);
            var finalGen1 = GC.CollectionCount(1);
            var finalGen2 = GC.CollectionCount(2);

            Console.WriteLine();
            Console.WriteLine("=== 性能分析结果 ===");
            Console.WriteLine($"总执行时间: {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"最终内存使用: {finalMemory / 1024.0 / 1024.0:F2} MB");
            Console.WriteLine($"内存变化: {(finalMemory - initialMemory) / 1024.0 / 1024.0:+F2;-F2;0} MB");
            Console.WriteLine($"GC触发次数:");
            Console.WriteLine($"  Gen0: {finalGen0 - initialGen0} 次");
            Console.WriteLine($"  Gen1: {finalGen1 - initialGen1} 次");
            Console.WriteLine($"  Gen2: {finalGen2 - initialGen2} 次");
            
            if (finalGen2 - initialGen2 > 0)
            {
                Console.WriteLine("⚠️ 检测到 Gen2 GC，可能存在内存压力");
            }
            
            if ((finalMemory - initialMemory) > 50 * 1024 * 1024) // 50MB
            {
                Console.WriteLine("⚠️ 内存使用量增长较大，可能存在内存泄漏");
            }
        }

        /// <summary>
        /// 专业trace分析模式
        /// </summary>
        static async Task RunTraceAnalysis()
        {
            var traceBenchmark = new TraceAnalysisBenchmark();
            await traceBenchmark.RunTraceAnalysis();
        }

        /// <summary>
        /// 内存压力测试模式
        /// </summary>
        static async Task RunMemoryTest()
        {
            var traceBenchmark = new TraceAnalysisBenchmark();
            await traceBenchmark.RunMemoryPressureTest();
        }
    }
}

/// <summary>
/// 性能分析工具类
/// </summary>
public static class PerformanceAnalyzer
{
    /// <summary>
    /// 分析 dotnet trace 输出的瓶颈
    /// </summary>
    public static void AnalyzeBottlenecks()
    {
        Console.WriteLine("=== 性能瓶颈分析建议 ===");
        Console.WriteLine();
        Console.WriteLine("🔍 主要检查点:");
        Console.WriteLine("1. 序列化/反序列化时间");
        Console.WriteLine("   - MessagePack vs JSON 性能对比");
        Console.WriteLine("   - 大对象图的序列化开销");
        Console.WriteLine();
        Console.WriteLine("2. 网络传输时间");
        Console.WriteLine("   - ZMQ socket 创建/连接开销");
        Console.WriteLine("   - 数据包大小对传输效率的影响");
        Console.WriteLine();
        Console.WriteLine("3. 内存分配模式");
        Console.WriteLine("   - 对象创建数量 (原始 vs Flat)");
        Console.WriteLine("   - GC 压力和频率");
        Console.WriteLine("   - 大对象堆 (LOH) 使用情况");
        Console.WriteLine();
        Console.WriteLine("4. CPU 使用热点");
        Console.WriteLine("   - 序列化器内部实现");
        Console.WriteLine("   - 数组复制和内存操作");
        Console.WriteLine("   - ZMQ 内部处理逻辑");
        Console.WriteLine();
        Console.WriteLine("💡 优化建议:");
        Console.WriteLine("- 如果序列化是瓶颈：考虑更激进的Flat化");
        Console.WriteLine("- 如果传输是瓶颈：考虑压缩或分批传输");
        Console.WriteLine("- 如果内存是瓶颈：考虑对象池和重用策略");
        Console.WriteLine("- 如果GC是瓶颈：使用 ArrayPool 和 span/memory");
    }

    /// <summary>
    /// 生成性能报告
    /// </summary>
    public static void GenerateReport(string testName, TimeSpan duration, long memoryUsed)
    {
        var reportPath = $"performance-report-{DateTime.Now:yyyyMMdd-HHmmss}.md";
        
        var report = $@"# ZMQ性能测试报告

## 测试概要
- 测试名称: {testName}
- 测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
- 执行时长: {duration.TotalMilliseconds:F2} ms
- 内存使用: {memoryUsed / 1024.0 / 1024.0:F2} MB

## 关键发现
1. **数据结构对比**
   - 原始CDataBlock: 复杂对象图，大量小对象
   - FlatCDataBlock: 单一对象，数组优化

2. **性能瓶颈识别**
   - 使用 `dotnet trace` 分析CPU热点
   - 使用 `dotnet counters` 监控GC指标
   - 使用 `PerfView` 深度分析内存分配

3. **优化建议**
   - [ ] 实施对象池减少GC压力
   - [ ] 使用 Span<T> 减少内存分配
   - [ ] 考虑批量传输优化网络效率

## 工具命令参考
```bash
# CPU 性能追踪
dotnet trace collect --process-id {{PID}} --providers Microsoft-DotNETCore-SampleProfiler

# 内存分析
dotnet trace collect --process-id {{PID}} --providers Microsoft-Windows-DotNETRuntime:0x1:4

# 实时计数器监控  
dotnet counters monitor --process-id {{PID}} --counters System.Runtime

# GC 详细分析
dotnet trace collect --process-id {{PID}} --providers Microsoft-Windows-DotNETRuntime:0x1:5
```
";

        File.WriteAllText(reportPath, report);
        Console.WriteLine($"📊 性能报告已生成: {reportPath}");
    }
}
