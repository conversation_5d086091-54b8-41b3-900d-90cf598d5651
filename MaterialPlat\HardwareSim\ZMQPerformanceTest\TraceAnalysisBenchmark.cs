using System.Diagnostics;
using NetMQ;
using NetMQ.Sockets;
using MessagePack;
using IHardware;
using static IHardware.Hw;

namespace ZMQPerformanceTest
{
    /// <summary>
    /// 专门用于dotnet trace分析的性能测试程序
    /// 分别测试原始数据和Flat数据，便于对比分析
    /// </summary>
    public class TraceAnalysisBenchmark
    {
        private const string ZMQ_ADDRESS_ORIGINAL = "tcp://127.0.0.1:5570";
        private const string ZMQ_ADDRESS_FLAT = "tcp://127.0.0.1:5571";
        private const int TRACE_ITERATIONS = 50; // 减少迭代次数，便于trace分析
        
        private CDataBlock _testData;
        private FlatCDataBlock _flatTestData;

        public TraceAnalysisBenchmark()
        {
            InitializeTestData();
        }

        private void InitializeTestData()
        {
            Console.WriteLine("🔧 初始化测试数据...");
            
            var para = new DataBlockPara
            {
                ServoAxisCount = 8,
                ServoAxisDataCount = 500,
                ServoSensorCount = 32,
                TempAxisCount = 4,
                TempAxisDataCount = 100,
                TempSensorCount = 8,
                CreepAxisCount = 2,
                CreepAxisDataCount = 50,
                CreepSensorCount = 4,
                ADCount = 16
            };

            _testData = new CDataBlock(para);
            FillSimulatedData(_testData);
            _flatTestData = FlatCDataBlock.FromCDataBlock(_testData);
            
            Console.WriteLine($"✅ 数据初始化完成: 原始对象数={CountCDataBlockObjects(_testData)}, Flat对象数=1");
        }

        private void FillSimulatedData(CDataBlock dataBlock)
        {
            var random = new Random(12345);

            if (dataBlock.ServoData != null)
            {
                for (int i = 0; i < dataBlock.ServoData.Length; i++)
                {
                    if (dataBlock.ServoData[i] != null)
                    {
                        dataBlock.ServoData[i].DataCount = 20;
                        for (int j = 0; j < Math.Min(20, dataBlock.ServoData[i].ChData?.Length ?? 0); j++)
                        {
                            var data = dataBlock.ServoData[i].ChData![j];
                            if (data != null)
                            {
                                data.Command = 100.0 + random.NextDouble() * 50;
                                data.Feedback = data.Command + (random.NextDouble() - 0.5) * 2;
                                data.Output = random.NextDouble() * 100;
                                data.Timer = Environment.TickCount;

                                if (data.Sensor != null)
                                {
                                    for (int k = 0; k < data.Sensor.Length; k++)
                                    {
                                        data.Sensor[k] = random.NextDouble() * 1000;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 运行专门用于trace分析的测试
        /// </summary>
        public async Task RunTraceAnalysis()
        {
            var processId = Process.GetCurrentProcess().Id;
            Console.WriteLine($"🔍 当前进程 PID: {processId}");
            Console.WriteLine("📊 请在另一个终端运行以下命令进行性能追踪:");
            Console.WriteLine();
            Console.WriteLine("=== CPU 热点分析 ===");
            Console.WriteLine($"dotnet trace collect --process-id {processId} --output cpu-hotspots.nettrace --providers Microsoft-DotNETCore-SampleProfiler");
            Console.WriteLine();
            Console.WriteLine("=== 内存分配分析 ===");
            Console.WriteLine($"dotnet trace collect --process-id {processId} --output memory-allocations.nettrace --providers Microsoft-Windows-DotNETRuntime:0x1:4");
            Console.WriteLine();
            Console.WriteLine("=== GC 行为分析 ===");
            Console.WriteLine($"dotnet trace collect --process-id {processId} --output gc-behavior.nettrace --providers Microsoft-Windows-DotNETRuntime:0x1:5");
            Console.WriteLine();
            Console.WriteLine("按任意键开始测试 (确保已启动trace收集)...");
            Console.ReadKey();

            // 测试阶段1: 原始数据结构
            Console.WriteLine();
            Console.WriteLine("🚀 阶段1: 测试原始CDataBlock (30秒)");
            await TestOriginalDataWithTrace();
            
            Console.WriteLine();
            Console.WriteLine("⏳ 等待5秒，准备下一阶段...");
            await Task.Delay(5000);

            // 测试阶段2: Flat数据结构  
            Console.WriteLine();
            Console.WriteLine("🚀 阶段2: 测试FlatCDataBlock (30秒)");
            await TestFlatDataWithTrace();

            Console.WriteLine();
            Console.WriteLine("✅ 测试完成! 可以停止 dotnet trace 收集");
            Console.WriteLine();
            Console.WriteLine("🔍 分析trace文件:");
            Console.WriteLine("dotnet trace analyze cpu-hotspots.nettrace");
            Console.WriteLine("dotnet trace analyze memory-allocations.nettrace");
            Console.WriteLine("dotnet trace analyze gc-behavior.nettrace");
        }

        /// <summary>
        /// 测试原始数据结构，专注于性能热点
        /// </summary>
        private async Task TestOriginalDataWithTrace()
        {
            Console.WriteLine("📈 开始原始CDataBlock性能测试...");
            
            using (var publisher = new PublisherSocket())
            using (var subscriber = new SubscriberSocket())
            {
                publisher.Bind(ZMQ_ADDRESS_ORIGINAL);
                subscriber.Connect(ZMQ_ADDRESS_ORIGINAL);
                subscriber.Subscribe("");
                
                await Task.Delay(100);

                var totalTime = Stopwatch.StartNew();
                
                for (int i = 0; i < TRACE_ITERATIONS; i++)
                {
                    // 序列化阶段 - 重点分析对象
                    var serializationSw = Stopwatch.StartNew();
                    var serialized = MessagePack.MessagePackSerializer.Serialize(_testData);
                    serializationSw.Stop();

                    // 网络传输阶段
                    var transmissionSw = Stopwatch.StartNew();
                    publisher.SendFrame(serialized);
                    var received = subscriber.ReceiveFrameBytes();
                    transmissionSw.Stop();

                    // 反序列化阶段 - 重点分析对象创建
                    var deserializationSw = Stopwatch.StartNew();
                    var deserialized = MessagePack.MessagePackSerializer.Deserialize<CDataBlock>(received);
                    deserializationSw.Stop();

                    if (i % 10 == 0)
                    {
                        Console.WriteLine($"原始数据 - 迭代 {i}: 序列化={serializationSw.ElapsedTicks}ticks, " +
                                         $"传输={transmissionSw.ElapsedTicks}ticks, " +
                                         $"反序列化={deserializationSw.ElapsedTicks}ticks");
                        
                        // 强制GC，观察GC模式
                        GC.Collect();
                    }

                    // 添加一些CPU密集操作来突出热点
                    await Task.Delay(10);
                }
                
                totalTime.Stop();
                Console.WriteLine($"✅ 原始数据测试完成: {totalTime.ElapsedMilliseconds}ms");
            }
        }

        /// <summary>
        /// 测试Flat数据结构，专注于性能热点
        /// </summary>
        private async Task TestFlatDataWithTrace()
        {
            Console.WriteLine("📈 开始FlatCDataBlock性能测试...");
            
            using (var publisher = new PublisherSocket())
            using (var subscriber = new SubscriberSocket())
            {
                publisher.Bind(ZMQ_ADDRESS_FLAT);
                subscriber.Connect(ZMQ_ADDRESS_FLAT);
                subscriber.Subscribe("");
                
                await Task.Delay(100);

                var totalTime = Stopwatch.StartNew();
                
                for (int i = 0; i < TRACE_ITERATIONS; i++)
                {
                    // 序列化阶段 - 分析Flat结构优势
                    var serializationSw = Stopwatch.StartNew();
                    var serialized = MessagePack.MessagePackSerializer.Serialize(_flatTestData);
                    serializationSw.Stop();

                    // 网络传输阶段
                    var transmissionSw = Stopwatch.StartNew();
                    publisher.SendFrame(serialized);
                    var received = subscriber.ReceiveFrameBytes();
                    transmissionSw.Stop();

                    // 反序列化阶段 - 分析简单对象创建
                    var deserializationSw = Stopwatch.StartNew();
                    var deserialized = MessagePack.MessagePackSerializer.Deserialize<FlatCDataBlock>(received);
                    deserializationSw.Stop();

                    if (i % 10 == 0)
                    {
                        Console.WriteLine($"Flat数据 - 迭代 {i}: 序列化={serializationSw.ElapsedTicks}ticks, " +
                                         $"传输={transmissionSw.ElapsedTicks}ticks, " +
                                         $"反序列化={deserializationSw.ElapsedTicks}ticks");
                        
                        // 观察GC模式差异
                        GC.Collect();
                    }

                    await Task.Delay(10);
                }
                
                totalTime.Stop();
                Console.WriteLine($"✅ Flat数据测试完成: {totalTime.ElapsedMilliseconds}ms");
            }
        }

        /// <summary>
        /// 运行内存压力测试，专门用于内存分析
        /// </summary>
        public async Task RunMemoryPressureTest()
        {
            Console.WriteLine("🧠 运行内存压力测试...");
            
            var initialMemory = GC.GetTotalMemory(true);
            Console.WriteLine($"初始内存: {initialMemory / 1024.0 / 1024.0:F2} MB");

            // 测试原始结构的内存压力
            Console.WriteLine("测试原始结构内存分配...");
            var originalStructures = new List<CDataBlock>();
            for (int i = 0; i < 20; i++)
            {
                var copy = new CDataBlock(new DataBlockPara
                {
                    ServoAxisCount = 8,
                    ServoAxisDataCount = 500,
                    ServoSensorCount = 32
                });
                FillSimulatedData(copy);
                originalStructures.Add(copy);
                
                if (i % 5 == 0)
                {
                    var currentMemory = GC.GetTotalMemory(false);
                    Console.WriteLine($"原始结构 {i}: {currentMemory / 1024.0 / 1024.0:F2} MB");
                }
            }

            await Task.Delay(1000);

            // 测试Flat结构的内存压力
            Console.WriteLine("测试Flat结构内存分配...");
            var flatStructures = new List<FlatCDataBlock>();
            for (int i = 0; i < 20; i++)
            {
                var flatCopy = FlatCDataBlock.FromCDataBlock(_testData);
                flatStructures.Add(flatCopy);
                
                if (i % 5 == 0)
                {
                    var currentMemory = GC.GetTotalMemory(false);
                    Console.WriteLine($"Flat结构 {i}: {currentMemory / 1024.0 / 1024.0:F2} MB");
                }
            }

            var finalMemory = GC.GetTotalMemory(true);
            Console.WriteLine($"最终内存: {finalMemory / 1024.0 / 1024.0:F2} MB");
            Console.WriteLine($"内存增长: {(finalMemory - initialMemory) / 1024.0 / 1024.0:F2} MB");
        }

        private int CountCDataBlockObjects(CDataBlock dataBlock)
        {
            int count = 1; // CDataBlock 本身

            if (dataBlock.ServoData != null)
            {
                count += dataBlock.ServoData.Length; 
                foreach (var servoChannel in dataBlock.ServoData)
                {
                    if (servoChannel?.ChData != null)
                    {
                        count += servoChannel.ChData.Length; 
                        foreach (var data in servoChannel.ChData)
                        {
                            if (data != null)
                            {
                                count += 1; 
                                if (data.Sensor != null) count += 1; 
                                if (data.MaxSensor != null) count += 1; 
                                if (data.MinSensor != null) count += 1; 
                            }
                        }
                    }
                }
            }

            return count;
        }
    }
}
