using System.Diagnostics;
using NetMQ;
using NetMQ.Sockets;
using MessagePack;
using IHardware;
using static IHardware.Hw;

namespace ZMQPerformanceTest
{
    /// <summary>
    /// ZMQ + MessagePack 性能测试：原始数据 vs Flat数据结构
    /// </summary>
    public class ZMQPerformanceBenchmark
    {
        private const string ZMQ_ADDRESS = "tcp://127.0.0.1:5566";
        private const int WARMUP_ITERATIONS = 10;
        private const int TEST_ITERATIONS = 100;
        
        private CDataBlock _testData;
        private FlatCDataBlock _flatTestData;

        public ZMQPerformanceBenchmark()
        {
            InitializeTestData();
        }

        private void InitializeTestData()
        {
            var para = new DataBlockPara
            {
                ServoAxisCount = 8,
                ServoAxisDataCount = 1000,  // 较大数据量测试
                ServoSensorCount = 64,
                TempAxisCount = 4,
                TempAxisDataCount = 200,
                TempSensorCount = 8,
                CreepAxisCount = 2,
                CreepAxisDataCount = 100,
                CreepSensorCount = 4,
                ADCount = 16
            };

            _testData = new CDataBlock(para);
            FillSimulatedData(_testData);
            _flatTestData = FlatCDataBlock.FromCDataBlock(_testData);
        }

        private void FillSimulatedData(CDataBlock dataBlock)
        {
            var random = new Random(12345); // 固定种子确保一致性

            // 填充伺服数据
            for (int i = 0; i < dataBlock.ServoData?.Length; i++)
            {
                if (dataBlock.ServoData[i] != null)
                {
                    dataBlock.ServoData[i].DataCount = 50;
                    for (int j = 0; j < Math.Min(50, dataBlock.ServoData[i].ChData?.Length ?? 0); j++)
                    {
                        var data = dataBlock.ServoData[i].ChData![j];
                        if (data != null)
                        {
                            data.Command = 100.0 + random.NextDouble() * 50;
                            data.Feedback = data.Command + (random.NextDouble() - 0.5) * 2;
                            data.Output = random.NextDouble() * 100;
                            data.Timer = Environment.TickCount;

                            if (data.Sensor != null)
                            {
                                for (int k = 0; k < data.Sensor.Length; k++)
                                {
                                    data.Sensor[k] = random.NextDouble() * 1000;
                                }
                            }
                        }
                    }
                }
            }

            // 填充温度数据
            for (int i = 0; i < dataBlock.TempData?.Length; i++)
            {
                if (dataBlock.TempData[i] != null)
                {
                    dataBlock.TempData[i].DataCount = 20;
                    for (int j = 0; j < Math.Min(20, dataBlock.TempData[i].ChData?.Length ?? 0); j++)
                    {
                        var data = dataBlock.TempData[i].ChData![j];
                        if (data != null)
                        {
                            data.Command = 25.0 + random.NextDouble() * 10;
                            data.Feedback = data.Command + (random.NextDouble() - 0.5) * 0.5;
                            data.Output = random.NextDouble() * 50;
                            data.Timer = Environment.TickCount;

                            if (data.Sensor != null)
                            {
                                for (int k = 0; k < data.Sensor.Length; k++)
                                {
                                    data.Sensor[k] = 20 + random.NextDouble() * 60;
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 运行完整的性能对比测试
        /// </summary>
        public async Task RunPerformanceComparison()
        {
            Console.WriteLine("=== ZMQ + MessagePack 性能对比测试 ===");
            Console.WriteLine($"测试数据规模: {GetDataStatistics()}");
            Console.WriteLine($"预热次数: {WARMUP_ITERATIONS}, 测试次数: {TEST_ITERATIONS}");
            Console.WriteLine();

            // 测试原始数据结构
            var originalResults = await TestOriginalDataStructure();
            
            // 测试Flat数据结构
            var flatResults = await TestFlatDataStructure();

            // 输出对比结果
            PrintComparisonResults(originalResults, flatResults);
        }

        /// <summary>
        /// 测试原始数据结构性能
        /// </summary>
        private async Task<PerformanceResults> TestOriginalDataStructure()
        {
            Console.WriteLine("🔍 测试原始数据结构 (CDataBlock)...");
            
            var results = new PerformanceResults("Original CDataBlock");
            
            using (var publisher = new PublisherSocket())
            using (var subscriber = new SubscriberSocket())
            {
                publisher.Bind(ZMQ_ADDRESS);
                subscriber.Connect(ZMQ_ADDRESS);
                subscriber.Subscribe("");
                
                await Task.Delay(100); // 连接稳定时间

                // 预热
                for (int i = 0; i < WARMUP_ITERATIONS; i++)
                {
                    await SendReceiveOriginalData(publisher, subscriber);
                }

                // 正式测试
                var stopwatch = Stopwatch.StartNew();
                long totalSerializationTime = 0;
                long totalDeserializationTime = 0;
                long totalTransmissionTime = 0;
                long totalMemoryUsed = 0;

                for (int i = 0; i < TEST_ITERATIONS; i++)
                {
                    var iterationResult = await SendReceiveOriginalDataWithMetrics(publisher, subscriber);
                    totalSerializationTime += iterationResult.SerializationTime;
                    totalDeserializationTime += iterationResult.DeserializationTime;
                    totalTransmissionTime += iterationResult.TransmissionTime;
                    totalMemoryUsed += iterationResult.SerializedSize;
                }
                
                stopwatch.Stop();

                results.TotalTime = stopwatch.ElapsedMilliseconds;
                results.AverageSerializationTime = totalSerializationTime / TEST_ITERATIONS;
                results.AverageDeserializationTime = totalDeserializationTime / TEST_ITERATIONS;
                results.AverageTransmissionTime = totalTransmissionTime / TEST_ITERATIONS;
                results.AverageSerializedSize = totalMemoryUsed / TEST_ITERATIONS;
                results.ThroughputMBps = CalculateThroughput(results.AverageSerializedSize, results.AverageTransmissionTime);
            }

            return results;
        }

        /// <summary>
        /// 测试Flat数据结构性能
        /// </summary>
        private async Task<PerformanceResults> TestFlatDataStructure()
        {
            Console.WriteLine("🚀 测试Flat数据结构 (FlatCDataBlock)...");
            
            var results = new PerformanceResults("Flat CDataBlock");
            
            using (var publisher = new PublisherSocket())
            using (var subscriber = new SubscriberSocket())
            {
                publisher.Bind("tcp://127.0.0.1:5567"); // 不同端口避免冲突
                subscriber.Connect("tcp://127.0.0.1:5567");
                subscriber.Subscribe("");
                
                await Task.Delay(100); // 连接稳定时间

                // 预热
                for (int i = 0; i < WARMUP_ITERATIONS; i++)
                {
                    await SendReceiveFlatData(publisher, subscriber);
                }

                // 正式测试
                var stopwatch = Stopwatch.StartNew();
                long totalSerializationTime = 0;
                long totalDeserializationTime = 0;
                long totalTransmissionTime = 0;
                long totalMemoryUsed = 0;

                for (int i = 0; i < TEST_ITERATIONS; i++)
                {
                    var iterationResult = await SendReceiveFlatDataWithMetrics(publisher, subscriber);
                    totalSerializationTime += iterationResult.SerializationTime;
                    totalDeserializationTime += iterationResult.DeserializationTime;
                    totalTransmissionTime += iterationResult.TransmissionTime;
                    totalMemoryUsed += iterationResult.SerializedSize;
                }
                
                stopwatch.Stop();

                results.TotalTime = stopwatch.ElapsedMilliseconds;
                results.AverageSerializationTime = totalSerializationTime / TEST_ITERATIONS;
                results.AverageDeserializationTime = totalDeserializationTime / TEST_ITERATIONS;
                results.AverageTransmissionTime = totalTransmissionTime / TEST_ITERATIONS;
                results.AverageSerializedSize = totalMemoryUsed / TEST_ITERATIONS;
                results.ThroughputMBps = CalculateThroughput(results.AverageSerializedSize, results.AverageTransmissionTime);
            }

            return results;
        }

        private async Task SendReceiveOriginalData(PublisherSocket publisher, SubscriberSocket subscriber)
        {
            var serialized = MessagePack.MessagePackSerializer.Serialize(_testData);
            publisher.SendFrame(serialized);
            
            var received = subscriber.ReceiveFrameBytes();
            var deserialized = MessagePack.MessagePackSerializer.Deserialize<CDataBlock>(received);
        }

        private async Task SendReceiveFlatData(PublisherSocket publisher, SubscriberSocket subscriber)
        {
            var serialized = MessagePack.MessagePackSerializer.Serialize(_flatTestData);
            publisher.SendFrame(serialized);
            
            var received = subscriber.ReceiveFrameBytes();
            var deserialized = MessagePack.MessagePackSerializer.Deserialize<FlatCDataBlock>(received);
        }

        private async Task<IterationMetrics> SendReceiveOriginalDataWithMetrics(PublisherSocket publisher, SubscriberSocket subscriber)
        {
            var metrics = new IterationMetrics();
            
            // 序列化
            var serializationSw = Stopwatch.StartNew();
            var serialized = MessagePack.MessagePackSerializer.Serialize(_testData);
            serializationSw.Stop();
            metrics.SerializationTime = serializationSw.ElapsedTicks;
            metrics.SerializedSize = serialized.Length;

            // 传输
            var transmissionSw = Stopwatch.StartNew();
            publisher.SendFrame(serialized);
            var received = subscriber.ReceiveFrameBytes();
            transmissionSw.Stop();
            metrics.TransmissionTime = transmissionSw.ElapsedTicks;

            // 反序列化
            var deserializationSw = Stopwatch.StartNew();
            var deserialized = MessagePack.MessagePackSerializer.Deserialize<CDataBlock>(received);
            deserializationSw.Stop();
            metrics.DeserializationTime = deserializationSw.ElapsedTicks;

            return metrics;
        }

        private async Task<IterationMetrics> SendReceiveFlatDataWithMetrics(PublisherSocket publisher, SubscriberSocket subscriber)
        {
            var metrics = new IterationMetrics();
            
            // 序列化
            var serializationSw = Stopwatch.StartNew();
            var serialized = MessagePack.MessagePackSerializer.Serialize(_flatTestData);
            serializationSw.Stop();
            metrics.SerializationTime = serializationSw.ElapsedTicks;
            metrics.SerializedSize = serialized.Length;

            // 传输
            var transmissionSw = Stopwatch.StartNew();
            publisher.SendFrame(serialized);
            var received = subscriber.ReceiveFrameBytes();
            transmissionSw.Stop();
            metrics.TransmissionTime = transmissionSw.ElapsedTicks;

            // 反序列化
            var deserializationSw = Stopwatch.StartNew();
            var deserialized = MessagePack.MessagePackSerializer.Deserialize<FlatCDataBlock>(received);
            deserializationSw.Stop();
            metrics.DeserializationTime = deserializationSw.ElapsedTicks;

            return metrics;
        }

        private double CalculateThroughput(long sizeBytes, long timeTicks)
        {
            double timeSeconds = (double)timeTicks / Stopwatch.Frequency;
            double sizeMB = (double)sizeBytes / (1024 * 1024);
            return sizeMB / timeSeconds;
        }

        private string GetDataStatistics()
        {
            return $"原始对象总数: {CountObjects(_testData)}, " +
                   $"Flat对象数: {CountObjects(_flatTestData)}, " +
                   $"减少率: {(1.0 - (double)CountObjects(_flatTestData) / CountObjects(_testData)) * 100:F1}%";
        }

        private int CountObjects(object obj)
        {
            if (obj == null) return 0;
            
            if (obj is CDataBlock cDataBlock)
            {
                return CountCDataBlockObjects(cDataBlock);
            }
            else if (obj is FlatCDataBlock flatCDataBlock)
            {
                return CountFlatCDataBlockObjects(flatCDataBlock);
            }
            
            return 1;
        }

        private int CountCDataBlockObjects(CDataBlock dataBlock)
        {
            int count = 1; // CDataBlock 本身

            // 计算伺服数据对象
            if (dataBlock.ServoData != null)
            {
                count += dataBlock.ServoData.Length; // SingleChDatas 数组
                foreach (var servoChannel in dataBlock.ServoData)
                {
                    if (servoChannel?.ChData != null)
                    {
                        count += servoChannel.ChData.Length; // CData 数组
                        foreach (var data in servoChannel.ChData)
                        {
                            if (data != null)
                            {
                                count += 1; // CData 对象
                                if (data.Sensor != null) count += 1; // Sensor 数组
                                if (data.MaxSensor != null) count += 1; // MaxSensor 数组
                                if (data.MinSensor != null) count += 1; // MinSensor 数组
                            }
                        }
                    }
                }
            }

            // 计算温度数据对象（类似逻辑）
            if (dataBlock.TempData != null)
            {
                count += dataBlock.TempData.Length;
                foreach (var tempChannel in dataBlock.TempData)
                {
                    if (tempChannel?.ChData != null)
                    {
                        count += tempChannel.ChData.Length;
                        foreach (var data in tempChannel.ChData)
                        {
                            if (data != null)
                            {
                                count += 1;
                                if (data.Sensor != null) count += 1;
                                if (data.MaxSensor != null) count += 1;
                                if (data.MinSensor != null) count += 1;
                            }
                        }
                    }
                }
            }

            // 其他数据类似计算...
            return count;
        }

        private int CountFlatCDataBlockObjects(FlatCDataBlock flatDataBlock)
        {
            return 1; // FlatCDataBlock 只有一个主对象
        }

        private void PrintComparisonResults(PerformanceResults originalResults, PerformanceResults flatResults)
        {
            Console.WriteLine();
            Console.WriteLine("========================= 性能对比结果 =========================");
            Console.WriteLine();
            
            // 基本指标对比
            Console.WriteLine("📊 基本性能指标对比:");
            Console.WriteLine($"{"指标",-20} {"原始数据",-15} {"Flat数据",-15} {"改善程度",-15}");
            Console.WriteLine(new string('-', 70));
            
            PrintMetricComparison("序列化时间 (μs)", 
                originalResults.AverageSerializationTime * 1000000.0 / Stopwatch.Frequency,
                flatResults.AverageSerializationTime * 1000000.0 / Stopwatch.Frequency);
            
            PrintMetricComparison("反序列化时间 (μs)", 
                originalResults.AverageDeserializationTime * 1000000.0 / Stopwatch.Frequency,
                flatResults.AverageDeserializationTime * 1000000.0 / Stopwatch.Frequency);
            
            PrintMetricComparison("传输时间 (μs)", 
                originalResults.AverageTransmissionTime * 1000000.0 / Stopwatch.Frequency,
                flatResults.AverageTransmissionTime * 1000000.0 / Stopwatch.Frequency);
            
            PrintMetricComparison("序列化大小 (KB)", 
                originalResults.AverageSerializedSize / 1024.0,
                flatResults.AverageSerializedSize / 1024.0);
            
            PrintMetricComparison("吞吐量 (MB/s)", 
                originalResults.ThroughputMBps,
                flatResults.ThroughputMBps);

            Console.WriteLine();
            Console.WriteLine("🎯 关键性能指标:");
            var totalTimeImprovement = ((double)originalResults.TotalTime - flatResults.TotalTime) / originalResults.TotalTime * 100;
            var sizeReduction = ((double)originalResults.AverageSerializedSize - flatResults.AverageSerializedSize) / originalResults.AverageSerializedSize * 100;
            var throughputImprovement = (flatResults.ThroughputMBps - originalResults.ThroughputMBps) / originalResults.ThroughputMBps * 100;

            Console.WriteLine($"• 总体性能提升: {totalTimeImprovement:+0.1;-0.1;0}%");
            Console.WriteLine($"• 数据大小减少: {sizeReduction:+0.1;-0.1;0}%");
            Console.WriteLine($"• 吞吐量提升: {throughputImprovement:+0.1;-0.1;0}%");

            Console.WriteLine();
            Console.WriteLine("💡 建议:");
            if (flatResults.ThroughputMBps > originalResults.ThroughputMBps)
            {
                Console.WriteLine("✅ Flat数据结构在ZMQ传输中表现更优，建议优先使用");
            }
            else
            {
                Console.WriteLine("⚠️ 需要进一步分析性能瓶颈");
            }
        }

        private void PrintMetricComparison(string metricName, double originalValue, double flatValue)
        {
            string improvement = "";
            if (originalValue > 0)
            {
                double improvementPercent = (originalValue - flatValue) / originalValue * 100;
                improvement = $"{improvementPercent:+0.1;-0.1;0}%";
            }

            Console.WriteLine($"{metricName,-20} {originalValue,-15:F2} {flatValue,-15:F2} {improvement,-15}");
        }

        public class PerformanceResults
        {
            public string TestName { get; set; }
            public long TotalTime { get; set; }
            public long AverageSerializationTime { get; set; }
            public long AverageDeserializationTime { get; set; }
            public long AverageTransmissionTime { get; set; }
            public long AverageSerializedSize { get; set; }
            public double ThroughputMBps { get; set; }

            public PerformanceResults(string testName)
            {
                TestName = testName;
            }
        }

        public class IterationMetrics
        {
            public long SerializationTime { get; set; }
            public long DeserializationTime { get; set; }
            public long TransmissionTime { get; set; }
            public long SerializedSize { get; set; }
        }
    }
}
