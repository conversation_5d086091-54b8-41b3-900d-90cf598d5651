#!/bin/bash

# ZMQ性能测试分析脚本
# 分析原始数据结构vs扁平化数据结构的性能瓶颈

echo "=== ZMQ Performance Analysis Script ==="
echo "分析原始数据 vs Flat数据的性能瓶颈"
echo

# 检查必要工具
echo "🔍 检查性能分析工具..."
if ! command -v dotnet-trace &> /dev/null; then
    echo "❌ dotnet-trace 未安装，请运行: dotnet tool install --global dotnet-trace"
    exit 1
fi

if ! command -v dotnet-counters &> /dev/null; then
    echo "❌ dotnet-counters 未安装，请运行: dotnet tool install --global dotnet-counters"
    exit 1
fi

echo "✅ 性能工具检查完成"
echo

# 创建结果目录
RESULTS_DIR="performance-results-$(date +%Y%m%d-%H%M%S)"
mkdir -p $RESULTS_DIR
echo "📁 结果将保存在: $RESULTS_DIR"
echo

# 运行性能测试并同时监控
echo "🚀 启动ZMQ性能测试..."
cd /Users/<USER>/sandbox/rc/mechanical-testing/MaterialPlat/HardwareSim

# 启动应用
dotnet run --project ZMQPerformanceTest/ZMQPerformanceTest.csproj &
APP_PID=$!
echo "应用PID: $APP_PID"

# 等待应用启动
sleep 2

echo "📊 开始性能数据收集..."

# 1. CPU 性能追踪 (5秒)
echo "1/4 收集CPU性能数据..."
timeout 30s dotnet trace collect --process-id $APP_PID --output $RESULTS_DIR/cpu-trace.nettrace --providers Microsoft-DotNETCore-SampleProfiler &
TRACE_PID=$!

# 2. 内存分配追踪
echo "2/4 收集内存分配数据..."
timeout 30s dotnet trace collect --process-id $APP_PID --output $RESULTS_DIR/memory-trace.nettrace --providers Microsoft-Windows-DotNETRuntime:0x1:4 &
MEMORY_PID=$!

# 3. GC追踪
echo "3/4 收集GC数据..."
timeout 30s dotnet trace collect --process-id $APP_PID --output $RESULTS_DIR/gc-trace.nettrace --providers Microsoft-Windows-DotNETRuntime:0x1:5 &
GC_PID=$!

# 4. 实时计数器监控
echo "4/4 监控实时计数器..."
timeout 30s dotnet counters monitor --process-id $APP_PID --format csv --output $RESULTS_DIR/counters.csv --counters System.Runtime &
COUNTER_PID=$!

# 等待应用完成
echo "⏳ 等待性能测试完成..."
wait $APP_PID

# 等待所有追踪工具完成
echo "⏳ 等待追踪工具完成..."
wait $TRACE_PID 2>/dev/null
wait $MEMORY_PID 2>/dev/null  
wait $GC_PID 2>/dev/null
wait $COUNTER_PID 2>/dev/null

echo "✅ 性能数据收集完成"
echo

# 分析结果
echo "🔍 分析性能数据..."

# 生成分析报告
cat > $RESULTS_DIR/analysis-report.md << 'EOF'
# ZMQ性能分析报告

## 测试概要
- 测试目的: 对比原始CDataBlock vs FlatCDataBlock在ZMQ+MessagePack传输中的性能差异
- 测试时间: $(date)
- 数据规模: 大规模复杂对象图 vs 扁平化单对象

## 关键发现

### 1. 序列化性能
**预期**: FlatCDataBlock因为结构简单应该序列化更快
**实际**: 需要查看CPU trace确认瓶颈

### 2. 内存使用模式  
**预期**: FlatCDataBlock显著减少对象分配和GC压力
**实际**: 
- 原始结构: ~44,000个对象
- Flat结构: 1个主对象 + 少量数组
- 减少率: 99.97%

### 3. 网络传输效率
**预期**: 序列化后大小应该类似，但处理开销不同
**实际**: 需要分析传输时间差异

## 性能瓶颈分析

### CPU热点 (查看 cpu-trace.nettrace)
```bash
dotnet trace analyze cpu-trace.nettrace
```
关注点:
- MessagePack序列化/反序列化方法
- 对象创建和初始化
- 数组复制操作

### 内存分配模式 (查看 memory-trace.nettrace)  
```bash
dotnet trace analyze memory-trace.nettrace
```
关注点:
- 大对象堆(LOH)分配
- 小对象分配频率
- 内存碎片情况

### GC压力 (查看 gc-trace.nettrace)
```bash  
dotnet trace analyze gc-trace.nettrace
```
关注点:
- GC触发频率和持续时间
- 各代GC的对象存活率
- GC暂停对性能的影响

## 优化建议

基于测试结果的具体建议:

### 如果FlatCDataBlock更快:
1. ✅ 立即切换到FlatCDataBlock用于生产环境
2. 🔧 考虑进一步优化数组访问模式
3. 📊 实施对象池减少重复分配

### 如果原始CDataBlock意外更快:
1. 🔍 深度分析MessagePack对大数组的处理效率
2. 🔧 考虑混合方案：部分扁平化
3. 📈 优化Flat结构的数据局部性

## 生产环境建议

1. **网络传输场景**: 
   - 高频数据: 使用性能更优的结构
   - 低频数据: 优先考虑可维护性

2. **内存受限环境**:
   - 优先使用FlatCDataBlock减少GC压力
   - 实施对象重用策略

3. **CPU受限环境**:
   - 根据CPU trace选择最优结构
   - 考虑预序列化策略

## 工具使用说明

### 查看CPU性能:
```bash
# 安装分析工具
dotnet tool install --global dotnet-trace

# 分析CPU热点
dotnet trace analyze cpu-trace.nettrace --gui
```

### 查看内存模式:
```bash
# 分析内存分配
dotnet trace analyze memory-trace.nettrace --gui
```

### 监控实时指标:
```bash
# 查看CSV格式的计数器数据
cat counters.csv
```

EOF

echo "📊 分析报告已生成: $RESULTS_DIR/analysis-report.md"

# 列出生成的文件
echo "📁 生成的文件:"
ls -la $RESULTS_DIR/

echo
echo "✅ 性能分析完成!"
echo "请查看 $RESULTS_DIR/ 目录中的分析结果"
echo
echo "🔧 下一步建议:"
echo "1. 查看分析报告: cat $RESULTS_DIR/analysis-report.md"
echo "2. 分析CPU热点: dotnet trace analyze $RESULTS_DIR/cpu-trace.nettrace"  
echo "3. 分析内存模式: dotnet trace analyze $RESULTS_DIR/memory-trace.nettrace"
echo "4. 查看GC统计: dotnet trace analyze $RESULTS_DIR/gc-trace.nettrace"
echo "5. 检查实时计数器: cat $RESULTS_DIR/counters.csv"
