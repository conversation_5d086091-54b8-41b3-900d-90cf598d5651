// HardwareSim模拟器发送FlatCDataBlock示例

using IHardware;
using MessagePack;
using static IHardware.Hw;

namespace HardwareSim.Example
{
    /// <summary>
    /// 硬件模拟器使用FlatCDataBlock发送数据的示例
    /// </summary>
    public class SimulatorFlatDataSender
    {
        /// <summary>
        /// 原始方式：发送CDataBlock
        /// </summary>
        public byte[] SendOriginalData(CDataBlock dataBlock)
        {
            // 直接序列化原始数据结构
            return MessagePackSerializer.Serialize(dataBlock);
        }

        /// <summary>
        /// 优化方式：发送FlatCDataBlock
        /// </summary>
        public byte[] SendFlatData(CDataBlock dataBlock)
        {
            // 转换为扁平化结构
            var flatBlock = FlatCDataBlock.FromCDataBlock(dataBlock);
            
            // 序列化扁平化结构（更高效）
            return MessagePackSerializer.Serialize(flatBlock);
        }

        /// <summary>
        /// 模拟器主循环示例
        /// </summary>
        public void SimulatorMainLoop()
        {
            // 初始化数据块参数
            var para = new DataBlockPara
            {
                ServoAxisCount = 8,
                ServoAxisDataCount = 500,
                ServoSensorCount = 64,
                TempAxisCount = 4,
                TempAxisDataCount = 100,
                TempSensorCount = 4,
                CreepAxisCount = 2,
                CreepAxisDataCount = 50,
                CreepSensorCount = 32,
                InCount = 64,
                OutCount = 64,
                ADCount = 200,
                ADDataCount = 500
            };

            // 创建数据块
            var dataBlock = new CDataBlock(para);
            
            // 模拟数据填充
            FillSimulatedData(dataBlock);

            // 发送扁平化数据（推荐方式）
            byte[] flatData = SendFlatData(dataBlock);
            
            // 通过网络或其他方式发送 flatData
            Console.WriteLine($"发送扁平化数据包，大小: {flatData.Length} bytes");
            
            // 可以在接收端反序列化
            var receivedFlat = MessagePackSerializer.Deserialize<FlatCDataBlock>(flatData);
            var recoveredOriginal = receivedFlat.ToCDataBlock();
            
            Console.WriteLine("数据传输完成，结构已恢复");
        }

        /// <summary>
        /// 模拟数据填充
        /// </summary>
        private void FillSimulatedData(CDataBlock dataBlock)
        {
            var random = new Random();
            
            // 填充伺服轴数据
            if (dataBlock.ServoData != null)
            {
                for (int i = 0; i < dataBlock.ServoData.Length; i++)
                {
                    if (dataBlock.ServoData[i]?.ChData != null)
                    {
                        dataBlock.ServoData[i].DataCount = 10; // 模拟10个数据点
                        
                        for (int j = 0; j < Math.Min(10, dataBlock.ServoData[i].ChData.Length); j++)
                        {
                            var data = dataBlock.ServoData[i].ChData[j];
                            if (data != null)
                            {
                                // 模拟控制数据
                                data.Command = 100.0 + random.NextDouble() * 50;
                                data.Feedback = data.Command + (random.NextDouble() - 0.5) * 2;
                                data.Output = random.NextDouble() * 100;
                                data.Timer = Environment.TickCount;
                                
                                // 模拟传感器数据
                                if (data.Sensor != null)
                                {
                                    for (int k = 0; k < data.Sensor.Length; k++)
                                    {
                                        data.Sensor[k] = random.NextDouble() * 1000;
                                    }
                                }
                                
                                if (data.MaxSensor != null)
                                {
                                    for (int k = 0; k < data.MaxSensor.Length; k++)
                                    {
                                        data.MaxSensor[k] = data.Sensor?[k] * 1.1 ?? 0;
                                    }
                                }
                                
                                if (data.MinSensor != null)
                                {
                                    for (int k = 0; k < data.MinSensor.Length; k++)
                                    {
                                        data.MinSensor[k] = data.Sensor?[k] * 0.9 ?? 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 填充温度数据
            if (dataBlock.TempData != null)
            {
                for (int i = 0; i < dataBlock.TempData.Length; i++)
                {
                    if (dataBlock.TempData[i]?.ChData != null)
                    {
                        dataBlock.TempData[i].DataCount = 5;
                        
                        for (int j = 0; j < Math.Min(5, dataBlock.TempData[i].ChData.Length); j++)
                        {
                            var data = dataBlock.TempData[i].ChData[j];
                            if (data != null)
                            {
                                data.Command = 25.0 + random.NextDouble() * 50; // 温度控制
                                data.Feedback = data.Command + (random.NextDouble() - 0.5);
                                
                                if (data.Sensor != null)
                                {
                                    for (int k = 0; k < data.Sensor.Length; k++)
                                    {
                                        data.Sensor[k] = 20.0 + random.NextDouble() * 60; // 温度传感器
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 填充IO数据
            if (dataBlock.BitIn != null)
            {
                for (int i = 0; i < dataBlock.BitIn.Length; i++)
                {
                    dataBlock.BitIn[i] = random.Next(2);
                }
            }

            if (dataBlock.BitOut != null)
            {
                for (int i = 0; i < dataBlock.BitOut.Length; i++)
                {
                    dataBlock.BitOut[i] = random.Next(2);
                }
            }

            // 填充AD数据
            if (dataBlock.ADData != null)
            {
                for (int i = 0; i < dataBlock.ADData.Length; i++)
                {
                    if (dataBlock.ADData[i] != null)
                    {
                        dataBlock.ADData[i].DataCount = 20;
                        
                        if (dataBlock.ADData[i].Sensor != null)
                        {
                            for (int j = 0; j < Math.Min(20, dataBlock.ADData[i].Sensor.Length); j++)
                            {
                                dataBlock.ADData[i].Sensor[j] = random.NextDouble() * 4096; // 12位AD
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 性能比较示例
        /// </summary>
        public void PerformanceComparison(CDataBlock dataBlock)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // 测试原始结构序列化
            stopwatch.Restart();
            var originalData = SendOriginalData(dataBlock);
            var originalTime = stopwatch.ElapsedMilliseconds;
            
            // 测试扁平化结构序列化
            stopwatch.Restart();
            var flatData = SendFlatData(dataBlock);
            var flatTime = stopwatch.ElapsedMilliseconds;
            
            stopwatch.Stop();
            
            Console.WriteLine("=== 性能比较结果 ===");
            Console.WriteLine($"原始结构 - 序列化时间: {originalTime}ms, 数据大小: {originalData.Length} bytes");
            Console.WriteLine($"扁平结构 - 序列化时间: {flatTime}ms, 数据大小: {flatData.Length} bytes");
            Console.WriteLine($"性能提升: {((double)originalTime / flatTime):F2}x");
            Console.WriteLine($"大小减少: {((double)originalData.Length / flatData.Length):F2}x");
        }
    }
}
