﻿using System.Reflection;
using NLog;
using NLog.Config;
using NLog.Targets;
using Microsoft.Extensions.Configuration;

namespace Logging;
/// <summary>
/// 提供Logger给系统使用
/// 建议使用 `using static Logging.CCSSLogger;`
/// 来使用Logger
/// 当前logger的配置是输出到控制台和文件
/// 控制台输出的日志级别是Info  文件输出的日志级别是Debug
/// 输出的日志文件是abc.log， 所在路径由启动程序决定
/// 当前logger输出的格式是：
/// 2021-08-05 16:48:01.1234  Logging.CCSSLogger.LogTest:Logging\CCSSLogger.cs:36  this is a test
/// TODO：
///  1. 考虑是否需要将日志输出到数据库（操作日志）
///  2. 太长的日志需要截断或者滚动
///
/// </summary>
public static class CCSSLogger
{
    private static LoggingConfiguration config = new NLog.Config.LoggingConfiguration();
    public static string GetProjectName()
    {
        return Assembly.GetEntryAssembly()?.GetName().Name ?? "UnknownProject";
    }

    public static NLog.Logger Logger;

    static CCSSLogger()
    {
        Logger = InitLoadConfig();
    }

    /// <summary>
    /// 初始化Logger配置,外部也可以调用
    /// 例如静态Dll中也是用了NLog，如果不重新加载会导致日志输出位置不符合平台同一要求
    /// </summary>
    /// <returns></returns>
    public static Logger InitLoadConfig()
    {
        var configuration = new ConfigurationBuilder()
           .AddJsonFile("appsettings.json", optional: true)
           .Build();
        // 清除之前的配置规则
        // 如果不清除加载两次会出现同一条日志打印两次的情况
        config.LoggingRules.Clear();

        string layout = "tid:${threadid} ${longdate} ${callsite} ${callsite-filename}:${callsite-linenumber} ${message}";
        // 获取项目名作为日志文件名
        string projectName = GetProjectName();
        string logFileName = $"{projectName}.log";
        // 目标是console
        ConsoleTarget logconsole = new NLog.Targets.ConsoleTarget("logconsole")
        {
            Layout = layout
        };
        // 目标是一个文件
        FileTarget logfile = new NLog.Targets.FileTarget("logfile")
        {
            FileName = logFileName,
            Layout = layout,
            ArchiveOldFileOnStartup = true,
            ArchiveFileName = $"{projectName}.{{#}}.log",
            ArchiveNumbering = ArchiveNumberingMode.Date,
            ArchiveEvery = FileArchivePeriod.Day,
            MaxArchiveFiles = 7
        };

        // 设置默认target为logfile，并添加规则
        config.AddRule(LogLevel.Error, LogLevel.Fatal, logfile);

        config.AddRule(LogLevel.Error, LogLevel.Fatal, logconsole);

        // 从配置文件中读取日志级别
        string? logLevelStr = configuration["Logging:LogLevel:Default"];
        if (logLevelStr != null)
        {
            LogLevel logLevel = LogLevel.FromString(logLevelStr);
            if (logLevel == null)
            {
                Console.WriteLine("logLevel is null");
                logLevel = LogLevel.Error; // 默认日志级别为 Error
            }

            // 更新日志记录器的日志级别
            foreach (var rule in config.LoggingRules)
            {
                Console.WriteLine(logLevelStr);
                rule.EnableLoggingForLevels(logLevel, LogLevel.Error);
            }
        }
        else
        {
            Console.WriteLine("logLevelStr is null");
        }
        NLog.LogManager.Configuration = config;

        Logger = NLog.LogManager.GetCurrentClassLogger();
        return Logger;
    }

}
