using System.Collections.Concurrent;
using System.ComponentModel;
using System.Diagnostics;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Text.Json;
using System.Text.Json.Serialization;
using FuncLibs;
using MQ;
using ScriptEngine.DaqHandlers;
using ScriptEngine.DBHandler.TableWriters;
using ScriptEngine.InputVar;
using ScriptEngine.InputVar.InputVars;
using static Microsoft.IO.RecyclableMemoryStreamManager;
using MessagePack;

namespace Scripting;

public class DaqHandler : IDisposable
{
    private double epsilon = 0.00000001;
    private double _timeLast = double.NaN;
    private double _moveLast = double.NaN;
    private double _loadLast = double.NaN;
    private double _strainLast = double.NaN;
    private DynamicDataWriter writer;
    private IDisposable? _subscription;//, _subscriptionUI;
    private ISubject<byte[]> _sendUI = new Subject<byte[]>();
    private ITemplate TemplateIns { get; set; }
    [MessagePackObject]
    public record DAQVariable([property: Key("Code")] string Code, [property: Key("Name")]  string Name, [property: Key("Value")]  double Value, [property: Key("Index")] int Index);
    [MessagePackObject]
    public record UICmdParam([property: Key("VarValues")] List<DAQVariable>? VarValues);
    public bool IsSaveBuffer { get; set; }
    public bool IsSaveDb { get; set; }
    public bool IsSendUI { get; set; }

    //时间过滤有关参数
    public bool IsTimeInterval { get; set; }
    public double TimeInterval { get; set; }
    public string IntervalTimeCode { get; set; }

    // 位移过滤有关参数

    public bool IsMoveInterval { get; set; }
    public double MoveInterval { get; set; }
    public string IntervalMoveCode { get; set; }

    // 负荷过滤有关参数
    public bool IsLoadInterval { get; set; }
    public double LoadInterval { get; set; }
    public string IntervalLoadCode { get; set; }
    //是否清零时间戳
    private bool _timeResetZero = false;
    //是否已经下发过清零时间戳的命令
    private bool _timeResetZeroSend = false;


    private string _signal_timeCode = "";
    // 变形过滤有关参数
    public bool IsStrainInterval { get; set; }
    public double StrainInterval { get; set; }
    public string IntervalStrainCode { get; set; }

    public BufferInputVar InputVarBuffer { get; set; }

    public IObservable<Dictionary<string, double>> SignalStream { get; set; }

    public string SimpleCode { get; set; }
    public string SubTaskID { get; set; }
    //是否开启数据有效性过滤
    bool IsValidDataFilterEnabled;
    //数据有效性关闭函数
    Func<bool, bool>? AboutFunc;
    // 轴的数据有效性标志
    string axisSignal ="";

    private int _uiDataFilterInterval = 5; // 每5个点取1个点进行UI推送
    public DaqHandler(DaqHandlerParameters parameters)
    {
        TemplateIns = parameters.Template;
        TimeInterval = parameters.TimeInterval;
        InputVarBuffer = parameters.BufferInputVar;
        IsTimeInterval = parameters.IsTimeInterval;
        SubTaskID = parameters.SubTaskID;
        SimpleCode = parameters.SimpleCode;
        IsSendUI = parameters.IsSendUI;
        IsSaveBuffer = parameters.IsSaveBuffer;
        IsSaveDb = parameters.IsSaveDb;
        IntervalTimeCode = parameters.IntervalTimeCode;
        IsMoveInterval = parameters.IsMoveInterval;
        MoveInterval = parameters.MoveInterval;
        IntervalMoveCode = parameters.IntervalMoveCode;
        IsLoadInterval = parameters.IsLoadInterval;
        LoadInterval = parameters.LoadInterval;
        IntervalLoadCode = parameters.IntervalLoadCode;
        IsStrainInterval = parameters.IsStarinInterval;
        StrainInterval = parameters.StrainInteral;
        IntervalStrainCode = parameters.IntervalStrainCode;
        _timeResetZero =  parameters.timeResetZero;
        _signal_timeCode =  parameters.signal_timeCode;
        IsValidDataFilterEnabled=parameters.IsValidDataFilterEnabled;
        AboutFunc = parameters.AboutFunc;
        axisSignal =parameters.axisSignal;
        if (SimpleCode != null)
        {
            var tableName = SimpleCode + InputVarBuffer.Code;
            try
            {
                writer = (DynamicDataWriter)TemplateIns.CurrentInst.TableWriters[tableName];
            }
            catch (Exception e)
            {
                Logging.CCSSLogger.Logger.Error(tableName + "数据库writer创建失败：" + e.Message);
            }

        }

        // _subscriptionUI = _sendUI.Buffer(TimeSpan.FromSeconds(0.01))
        //     .Where(uiList => uiList.Any())
        //     .Subscribe(uiList =>
        //     {
        //         var uiMsg = uiList.FirstOrDefault();
        //         ISystemBus.SendToUIDataTopic(uiMsg!, TemplateIns.TemplateName!, SimpleCode!, InputVarBuffer.Code!);
        //     });


        // 初始化订阅
        InitializeSubscription();

    }
    //保存数据流
    private ISubject<Dictionary<string, double>> _saveProcessData = new Subject<Dictionary<string, double>>();
    //和存Ui的数据流
    //private ISubject<Dictionary<string, double>> _touiSub = new Subject<Dictionary<string, double>>();
    //订阅流订阅对象
    private IDisposable? _subscriptionsaveProcessData;//, _subscriptiontoui;
    private void InitializeSubscription()
    {

        _subscriptionsaveProcessData = _saveProcessData.Subscribe(vars =>
        {
            // 处理数据          
            ProcessData(vars);
        });

        // _subscriptiontoui = _saveProcessData.Subscribe(vars =>
        // {
        //     // 处理数据          
        //     if (ShouldSendToUI())
        //     {
        //         SendToUI(vars);
        //     }
        // });
        var stopwatch = new Stopwatch();
        int iwatch = 0;
        // 只订阅一次，不在这里处理流的参数
        _subscription = TemplateIns.TemplateObservable
            .Where(x=>ValidDataFilterEnabled(x))
            .Where(x => x.Keys.Any(key =>
                InputVarBuffer.SignalCodes.Contains(key)
                || key == "create_time")
                )
            .Where(dic =>
            TimeResetZero(dic)//清零
            && Filter(dic)//筛选
            ) // 应用过滤器
            .Subscribe(vars =>
            {
                stopwatch.Start();
                iwatch++;
                _saveProcessData.OnNext(vars);
                //_touiSub.OnNext(vars);
                stopwatch.Stop();
                if (iwatch % 100000 == 0)
                {
                    iwatch = 0;
                    Logger.Error($"100000次DAQ转换耗时： {stopwatch.ElapsedMilliseconds}ms");
                    stopwatch.Reset();
                }
                // // 处理数据
                // if (ShouldSendToUI())
                // {
                //     SendToUI(vars);
                // }
                // ProcessData(vars);
            });
    }
    bool IsValidData=false;
    double ValidDataValue=-10 ;
    private bool ValidDataFilterEnabled(Dictionary<string, double> x)
    {
        if (IsValidDataFilterEnabled)
        {
            if (x.TryGetValue(axisSignal, out double Data))
            {

                if (Data < 0)
                {
                    IsValidData = false;
                    //如果最大脉冲的和data < 0.5，则说明此次控制结束，调用结束函数
                    if (Data + ValidDataValue <= 0.5)
                    {
                        //通知子任务可以结束
                        AboutFunc.Invoke(true);
                    }
                }
                else if (Data > 0)
                {
                    //以最大的脉冲控制
                    if (ValidDataValue >= Data)
                    {
                        //通知子任务不能结束
                        AboutFunc.Invoke(false);
                        ValidDataValue = Data;
                    }
                    IsValidData = true;
                }
            }
            return IsValidData;
        }

        return true;
    }

    /// <summary>
    /// 间隔点过滤
    /// </summary>
    /// <param name="dic"></param>
    /// <returns></returns>
    private bool Filter(Dictionary<string, double> dic)
    {
        return filterTime(dic) || filterMove(dic) || filterLoad(dic) || filterStrain(dic);
    }
    /// <summary>
    /// 时间戳清零处理
    /// </summary>
    /// <param name="dic"></param>
    /// <returns></returns>
    private bool TimeResetZero(Dictionary<string, double> dic)
    {
        if (_timeResetZero == true)
        {
            if (dic.TryGetValue(IntervalTimeCode, out double time) && !double.IsNaN(time))
            {
                if (time == 0 || time < _timeLast)
                {
                    _timeResetZero = false;
                    _timeLast = double.NaN;
                    _moveLast = double.NaN;
                    _loadLast = double.NaN;
                    _strainLast = double.NaN;
                    return true;
                }
                else
                {
                    if (_timeResetZeroSend == false)
                    {
                        TemplateIns.SignalVars[_signal_timeCode].ModifyOffsetBy(time);
                        _timeResetZeroSend = true;
                    }
                    _timeLast = time;
                }
            }
            return false;
        }
        else
        {
            return true;
        }
    }

    private bool filterTime(Dictionary<string, double> dic)
    {
        if (!IsTimeInterval)
            return false;
        if (!double.IsNaN(_timeLast))
        {
            if (TimeInterval >= 0 && dic.ContainsKey(IntervalTimeCode) && !double.IsNaN(dic[IntervalTimeCode]))
            {
                if (Math.Abs(dic[IntervalTimeCode] - _timeLast) >=TimeInterval- epsilon)
                {
                    _timeLast = dic[IntervalTimeCode];
                    return true;
                }
                else if (dic[IntervalTimeCode] - _timeLast + epsilon <= 0)
                {
                    // 如果时间戳小于上次的时间戳，说明是清零了
                    _timeLast = dic[IntervalTimeCode];
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
                return false;
        }
        else if (dic.ContainsKey(IntervalTimeCode) && !double.IsNaN(dic[IntervalTimeCode]))
        {
            _timeLast = dic[IntervalTimeCode];
            return true;
        }
        else
            return false;
    }

    private bool filterMove(Dictionary<string, double> dic)
    {
        if (!IsMoveInterval)
            return false;
        if (!double.IsNaN(_moveLast))
        {
            if (MoveInterval >= 0 && dic.ContainsKey(IntervalMoveCode) && !double.IsNaN(dic[IntervalMoveCode]))
            {
                if (Math.Abs(Math.Abs(dic[IntervalMoveCode] - _moveLast) - MoveInterval) >= epsilon)
                {
                    _moveLast = dic[IntervalMoveCode];
                    return true;
                }
                else
                    return false;
            }
            else
                return false;
        }
        else if (dic.ContainsKey(IntervalMoveCode) && !double.IsNaN(dic[IntervalMoveCode]))
        {
            _moveLast = dic[IntervalMoveCode];
            return true;
        }
        else
            return false;
    }

    private bool filterLoad(Dictionary<string, double> dic)
    {
        if (!IsLoadInterval)
            return false;
        if (!double.IsNaN(_loadLast))
        {
            if (LoadInterval >= 0 && dic.ContainsKey(IntervalLoadCode) && !double.IsNaN(dic[IntervalLoadCode]))
            {
                if (Math.Abs(Math.Abs(dic[IntervalLoadCode] - _loadLast) - LoadInterval) >= epsilon)
                {
                    _loadLast = dic[IntervalLoadCode];
                    return true;
                }
                else
                    return false;
            }
            else
                return false;
        }
        else if (dic.ContainsKey(IntervalLoadCode) && !double.IsNaN(dic[IntervalLoadCode]))
        {
            _loadLast = dic[IntervalLoadCode];
            return true;
        }
        else
            return false;
    }

    private bool filterStrain(Dictionary<string, double> dic)
    {
        if (!IsStrainInterval)
            return false;

        if (!double.IsNaN(_strainLast))
        {
            if (StrainInterval >= 0 && dic.ContainsKey(IntervalStrainCode) && !double.IsNaN(dic[IntervalStrainCode]))
            {
                if (Math.Abs(Math.Abs(dic[IntervalStrainCode] - _strainLast) - StrainInterval) >= epsilon)
                {
                    _strainLast = dic[IntervalStrainCode];
                    return true;
                }
                else
                    return false;
            }
            else
                return false;
        }
        else if (dic.ContainsKey(IntervalStrainCode) && !double.IsNaN(dic[IntervalStrainCode]))
        {
            _strainLast = dic[IntervalStrainCode];
            return true;
        }
        else
            return false;
    }

    private int _index = 0;
    private bool ShouldSendToUI()
    {

        if (_index++ % _uiDataFilterInterval == 0)
        {
            _index = 0;
            return true;
        }
        else
        {
            return false;
        }
        
    }
    private void SendToUI(Dictionary<string, double> vars)
    {
        List<DAQVariable> currentDAQVars = vars
            .Select(kvp => new DAQVariable(kvp.Key, kvp.Key, kvp.Value, -1))
            .ToList();

        // 组织DBmessage
        byte[] message = Consts.MessagePackSerializer.Serialize(
            new
            {
                ProcessID = TemplateIns.TemplateName,
                SubTaskID = SubTaskID,
                UICmd = "signalVar",
                UIParams = new UICmdParam(currentDAQVars),
            }
        );

        _sendUI.OnNext(message);

    }
    private void ProcessData(Dictionary<string, double> vars)
    {

       

        int bufferIndex = 0;
        Dictionary<string, double> filteredVars = new Dictionary<string, double>();

        foreach (var item in InputVarBuffer.SignalCodes)
        {
            if (vars.ContainsKey(item))
            {
                filteredVars[item] = vars[item];
            }
            else
            {
                // 这里的处理逻辑是在慢的流到达之前，漫流中的信号变量的值在这里赋值为 double.NaN，且在后面过滤掉该点
                filteredVars[item] = double.NaN;
            }
        }

        if (IsSaveBuffer)
        {
            foreach (var kvp in filteredVars)
            {
                bufferIndex = InputVarBuffer.Value[kvp.Key].AddValue(kvp.Value);
            }
            filteredVars["index"] = bufferIndex;
        }
        else
        {
            filteredVars["index"] = -1;
        }
        filteredVars["create_time"] = vars["create_time"];
         //ToDo：全局监控使用后需更改（暂时想法后需可改成那边用流） 更新daqHandler缓存数据 
        DaqHandlerDataCache.AddOrUpdateDaqHandlerDataCache(TemplateIns.TemplateName, InputVarBuffer.Code, filteredVars);
        if (!IsSaveDb || string.IsNullOrEmpty(SimpleCode))
        {
            return;
        }
        // DAQ => Msg.TopicForVar
        //TODO: 摘除引伸计子任务需要重构，暂时把这个消息发送拿掉，后续需要在摘除引伸计任务进行重构：要么支持messagepack, 要么子任务里自己处理
        //string serializedVars = JsonSerializer.Serialize(filteredVars, _jsonOptions);
        //ISystemBus.SendMsgToTopic(serializedVars, $"DAQ-{TemplateIns.TemplateName}-{InputVarBuffer.Code}-TOPIC_FROM_VAR");

        // DAQ => db
        List<DAQVariable> currentDAQVars = filteredVars
            .Select(kvp => new DAQVariable(kvp.Key, kvp.Key, kvp.Value, bufferIndex))
            .ToList();


        // 存库逻辑
        //var daqRecord = new DaqRecord("", SimpleCode, message, InputVarBuffer.Code, SubTaskID, 0);
        writer?.Add(filteredVars);
   
        
    }

    /// <summary>
    /// 更新采集流的参数
    /// </summary>
    /// <param name="parameters"></param>
    public void Update(DaqHandlerParameters parameters)
    {
        // 更新传入的参数
        TimeInterval = parameters.TimeInterval;
        InputVarBuffer = parameters.BufferInputVar;
        IsTimeInterval = parameters.IsTimeInterval;
        SubTaskID = parameters.SubTaskID;
        SimpleCode = parameters.SimpleCode;
        IsSendUI = parameters.IsSendUI;
        IsSaveBuffer = parameters.IsSaveBuffer;
        IsSaveDb = parameters.IsSaveDb;
        IntervalTimeCode = parameters.IntervalTimeCode;
        IsMoveInterval = parameters.IsMoveInterval;
        MoveInterval = parameters.MoveInterval;
        IntervalMoveCode = parameters.IntervalMoveCode;
        IsLoadInterval = parameters.IsLoadInterval;
        LoadInterval = parameters.LoadInterval;
        IntervalLoadCode = parameters.IntervalLoadCode;
        IsStrainInterval = parameters.IsStarinInterval;
        StrainInterval = parameters.StrainInteral;
        IntervalStrainCode = parameters.IntervalStrainCode;
        IsValidDataFilterEnabled=parameters.IsValidDataFilterEnabled;
        AboutFunc = parameters.AboutFunc;
        axisSignal =parameters.axisSignal;

        // 重新初始化 writer。inputbuffer可能会发生变化,这个时候表名就会变化
        if (!string.IsNullOrEmpty(SimpleCode))
        {
            var tableName = SimpleCode + InputVarBuffer.Code;
            writer = (DynamicDataWriter)TemplateIns.CurrentInst.TableWriters[tableName];
        }

    }

    public void Dispose()
    {
        // 取消订阅
        _subscriptionsaveProcessData?.Dispose();
        //_subscriptiontoui?.Dispose();
        _subscription?.Dispose();
        //_subscriptionUI?.Dispose();
        GC.SuppressFinalize(this);
    }
    /// <summary>
    /// Json序列化时，对double类型的处理
    /// </summary>
    public class DoubleConverter : JsonConverter<double>
    {
        /// <summary>
        /// 重写double类型 WriteJson
        /// </summary>
        public override void Write(Utf8JsonWriter writer, double value, JsonSerializerOptions options)
        {
            // 自定义序列化逻辑
            if (double.IsPositiveInfinity(value))
                writer.WriteStringValue("Infinity");
            else if (double.IsNegativeInfinity(value))
                writer.WriteStringValue("-Infinity");
            else if (double.IsNaN(value))
                writer.WriteNullValue();
            else
                writer.WriteNumberValue(value);
        }

        /// <summary>
        /// 正常读double值
        /// </summary>
        public override double Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return reader.GetDouble();
        }
    }
}
