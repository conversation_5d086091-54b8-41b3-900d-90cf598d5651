using System.Reactive.Linq;
using System.Text.Json;
using System.Linq;
using MQ;
using Scripting;
using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;

namespace ScriptEngine.InstantiatedTemplate.SignalVar;

public abstract class SignalVarsData
{
    /// <summary>
    /// 通道每返回一包数据，都将数据拆到不同的信号变量中
    /// </summary>
    /// <param name="hwKey"></param>
    /// <param name="templateInst"></param>
    /// <returns>每一包返回内容封装到一个Dic中，信号变量Code,本次回调所带回来的数组</returns>
    public static IObservable<Dictionary<string, double[]>> GetDatas(string hwKey, ITemplate templateInst)
    {

        var templateSignalVars = new List<InstantiatedTemplate.SignalVar.SignalVar>(templateInst.SignalVars.Values);

        return SignalExample
            .Examples
            .VarFlatSensorDataDic[hwKey]
            .Select(x =>
            {
                Dictionary<string, double[]> varsDatas = new();
                //不同的DLL只给订阅在自身的信号变量赋值
                foreach (var signalVar in templateSignalVars.Where(singal => singal.HwKey == hwKey))
                {
                    
                    double[] results = signalVar.GetSignalData(x, templateInst) ?? default;
                    varsDatas[signalVar.Code] = results;
                }
                varsDatas["create_time"] = InstantiatedTemplate.SignalVar.SignalVar.GetTime(x);
                return varsDatas;
            });
    }        
    public static IObservable<Dictionary<string, double[]>> GetDatasFlat(string hwKey, ITemplate templateInst,List<MappingAxis?> mappingServoAxes,List<MappingAxis?> mappingTempAxes,List<MappingAxis?> mappingCreepAxes)
    {
        var templateSignalVars = new List<InstantiatedTemplate.SignalVar.SignalVar>(templateInst.SignalVars.Values);

        return SignalExample
            .Examples
            .VarFlatSensorDataDic[hwKey]
            .Select(x =>
            {
                Dictionary<string, double[]> varsDatas = new();
                foreach (var signalVar in templateSignalVars.Where(singal => singal.HwKey == hwKey&& singal.IsVirtual == false))
                {
                    double[] results = signalVar.GetSignalData(x, templateInst);
                    varsDatas[signalVar.Code] = results;
                }
                varsDatas["create_time"] = InstantiatedTemplate.SignalVar.SignalVar.GetTime(x);
                // 获取轴有效数据信号
                for (int i = 0; i < mappingServoAxes.Count; i++)
                {
                    if (mappingServoAxes[i].Hwkey == hwKey)
                    {
                        varsDatas[$"{hwKey}-Servo-{mappingServoAxes[i].RealIndex}-feedback"] =
                        SignalVar.GetFeedback(x.ServoData[mappingServoAxes[i].RealIndex].ChData);
                    }
                }
                 // 获取轴有效数据信号
                for (int i = 0; i < mappingTempAxes.Count; i++)
                {
                    if (mappingTempAxes[i].Hwkey == hwKey)
                    {
                        varsDatas[$"{hwKey}-Temp-{mappingTempAxes[i].RealIndex}-feedback"] =
                        SignalVar.GetFeedback(x.TempData[mappingTempAxes[i].RealIndex].ChData);
                    }
                }
                 // 获取轴有效数据信号
                for (int i = 0; i < mappingCreepAxes.Count; i++)
                {
                    if (mappingCreepAxes[i].Hwkey == hwKey)
                    {
                        varsDatas[$"{hwKey}-Creep-{mappingCreepAxes[i].RealIndex}-feedback"] =
                        SignalVar.GetFeedback(x.CreepData[mappingCreepAxes[i].RealIndex].ChData);
                    }
                }

                return varsDatas;
            });
    }
  


    /// <summary>
    /// 返回的数组中需要带有周期数的有关信息
    /// </summary>
    /// <param name="HwKey"></param>
    /// <param name="templateInst"></param>
    /// <returns></returns>
    public static IObservable<Dictionary<string, SignalCycle[]>> GetDatasCirlce(string? HwKey, ITemplate templateInst)
    {
        if (string.IsNullOrEmpty(HwKey)) return default;
        List<InstantiatedTemplate.SignalVar.SignalVar> templateSignalVars = new List<InstantiatedTemplate.SignalVar.SignalVar>(templateInst.SignalVars.Values);

        return SignalExample
            .Examples
            .VarFlatSensorDataDic[HwKey]
            .Select(x =>
            {
                Dictionary<string, SignalCycle[]> varsDatas = new();
                //不同的DLL只给订阅在自身的信号变量赋值
                foreach (var signalVar in templateSignalVars.Where(singal => singal.HwKey == HwKey))
                {
                    SignalCycle[] results = signalVar.GetDataWithCircle(x, templateInst) ?? new SignalCycle[0];
                    varsDatas[signalVar.Code] = results;
                }
                return varsDatas;
            });
    }

    /// <summary>
    /// 返回的BitIn BitOut数组中,根据信号变量sensorId 对应的下标值
    /// </summary>
    /// <param name="HwKey"></param>
    /// <param name="templateInst"></param>
    /// <returns></returns>
    public static IObservable<Dictionary<string, int>> GetDatas4Bit(string HwKey, ITemplate templateInst)
    {
        List<InstantiatedTemplate.SignalVar.SignalVar> templateSignalVars = 
            new List<InstantiatedTemplate.SignalVar.SignalVar>(
                templateInst.SignalVars
                .Values
                .Where(signal => signal.AxisType == "Input" || signal.AxisType == "Output"));

        return SignalExample
            .Examples
            .VarFlatSensorDataDic[HwKey]
            .Select(x =>
            {
                Dictionary<string, int> varsDatas = new();
                //不同的DLL只给订阅在自身的信号变量赋值
                foreach (var signalVar in templateSignalVars.Where(singal => singal.HwKey == HwKey))
                {
                    var results = signalVar.GetSignalData4Array(x);
                    varsDatas[signalVar.Code] = results;
                }

                return varsDatas;
            });
    }

    /// <summary>
    /// 同步外部信号变量修改
    /// </summary>
    private void SendSignalVarModified()
    {
        // 变量发生变化通知clj,进行信号变量的持久化
        ISystemBus.SendToVarModified(JsonSerializer.Serialize(this), new VarModifiedMsgOptions { SaveDB = true, Type = "signal" });
    }
}