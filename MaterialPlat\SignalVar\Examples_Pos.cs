/// <summary>
/// 位移作为系统变量的定义
/// </summary>
namespace SignalExample_Pos;

using NetMQ.Sockets;
using NetMQ;
using Utils;
using Consts;
using static Utils.UtilsExtensions;
using System.Reactive.Subjects;
using System.Reflection;
using System.Text.Json;
using static Logging.CCSSLogger;

// 定义 SensorDataPos 记录，用于表示传感器数据
public record SensorDataPos(int HwType, int DeviceID, int ADSensorID, double Time, double Position, double DestPosition, int CMDType, int SingleReached, double AllReached, int Tan);

public static class Examples
{
    // 定义了一个 Subject 来发布 SensorDataPos 给多个订阅者
    // private static readonly Subject<SensorDataPos> sensorDataPosSubject = new();


    // public static IObservable<SensorDataPos> VarSensorDataPos => sensorDataPosSubject.AsObservable();

    // 定义用于订阅消息的 SubscriberSocket
    public static SubscriberSocket sub = new();

    // 定义一个字典来存储 SensorDataPos 的 Subject，用于不同的传感器
    private static readonly Dictionary<string, Subject<SensorDataPos>> sensorDataPosSubjectDic = new();

    // 定义一个字典，用于存储可观察的 SensorDataPos 对象
    public static Dictionary<string, IObservable<SensorDataPos>> VarSensorDataPosDic = new();

    // 静态构造函数
    static Examples()
    {
        // 连接到 NetMQ 的套接字
        sub.Connect(SocketsConsts.BUS_XPUB_SOCKET);
        // 订阅硬件位置消息的主题
        sub.Subscribe(TopicConsts.TOPIC_HARDWARE_POS_MSG);

        // 获取程序集路径，用于找到配置文件
        string assemblyPath = Assembly.GetExecutingAssembly().Location;
        string outputPath = Path.GetDirectoryName(assemblyPath)!;
        string subtaskJsonFile = Path.Combine(outputPath, "subtasks.json");

        // 检查配置文件是否存在
        if (File.Exists(subtaskJsonFile))
        {
            // 解析 JSON 文件
            JsonDocument document = JsonDocument.Parse(File.ReadAllText(subtaskJsonFile));
            var root = document.RootElement;
            var hardwareConnectors = root.GetProperty("hardwareConnectors");
            foreach (JsonElement item in hardwareConnectors.EnumerateArray())
            {
                string name = item.GetProperty("name").GetString()!;
                sensorDataPosSubjectDic.Add(name, new Subject<SensorDataPos>());
            }

            // 为 VarSensorDataPosDic 字典赋值
            foreach (var item in sensorDataPosSubjectDic)
            {
                VarSensorDataPosDic[item.Key] = item.Value;
            }
        }
        else
        {
            // 记录配置文件初始化错误
            Logger.Info("Examplesubtasks.json Init wrong");
        }

        // 创建并运行一个新的任务来处理接收到的消息
        Task.Run(() =>
        {
            while (true)
            {
                try
                {
                    // 接收并处理 NetMQ 消息
                    string hwkey = sub.ReceiveFrameString();
                    if (hwkey == TopicConsts.TOPIC_HARDWARE_POS_MSG)
                    {
                        hwkey = sub.ReceiveFrameString();
                    }

                    // 从消息中解析出传感器数据
                    int HwType = BitConverter.ToInt32(sub.ReceiveFrameBytes());
                    int DeviceID = BitConverter.ToInt32(sub.ReceiveFrameBytes());
                    int ADSensorID = BitConverter.ToInt32(sub.ReceiveFrameBytes());
                    double Time= BitConverter.ToDouble(sub.ReceiveFrameBytes());//到达时间
                    double Position= BitConverter.ToDouble(sub.ReceiveFrameBytes());//日标值
                    double DestPosition = BitConverter.ToDouble(sub.ReceiveFrameBytes());//备用
                    int CMDType = BitConverter.ToInt32(sub.ReceiveFrameBytes());
                    int SingleReached = BitConverter.ToInt32(sub.ReceiveFrameBytes());
                    int AllReached = BitConverter.ToInt32(sub.ReceiveFrameBytes());
                    int Tan = BitConverter.ToInt32(sub.ReceiveFrameBytes());


                    // 创建 SensorDataPos 对象
                    SensorDataPos sd = new(HwType, DeviceID, ADSensorID, Time, Position, DestPosition, CMDType, SingleReached, AllReached, Tan);

                    // 将接收到的 SensorDataPos 对象发布给所有订阅者
                    sensorDataPosSubjectDic[hwkey].OnNext(sd);
                }
                catch (Exception e)
                {
                    Logger.Error("Example Pos Receive Error" + e.Message);
                }
              
            }
        }).FailFastOnException(); // 如果发生异常，则立即失败
    }
}
