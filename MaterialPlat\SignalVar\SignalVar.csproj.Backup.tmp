<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <ImplicitUsings>enable</ImplicitUsings>
    <WarningsAsErrors>true</WarningsAsErrors>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Reactive" Version="5.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2-beta2" />
    <PackageReference Include="NetMQ" Version="4.0.1.12" />
    <PackageReference Include="NLog" Version="5.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Logger\Logger.csproj" />
    <ProjectReference Include="..\FuncLibs\FuncLibs.csproj" />
    <ProjectReference Include="..\DBUtils\DBUtils.csproj" />
    <ProjectReference Include="..\MQ\MQ.csproj" />
  </ItemGroup>

</Project>
