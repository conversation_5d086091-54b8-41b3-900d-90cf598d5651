﻿using MQ;
using Scripting;
using SubTaskUtils;
using static Scripting.ITemplate;
using System.Text.Json;
using static Logging.CCSSLogger;
using Consts;

namespace SubTasks.tasks;

/// <summary>
/// 全局执行动作子任务
/// </summary>
public class SubTaskGlobalExecuteAction : ISubTask
{

    private string? _processID;
    private string? _subTaskID;
    private string? _className;
    private ITemplate? _templateInst;
    private SubTaskCmdParams? _subTaskCmdParams;

    /// <summary>
    /// 全局动作code
    /// </summary>
    public string? GlobalActionCode { get; set; }

    public record UIMessageData(bool Isaffirm, List<string>? HostIDs);

    /// <summary>
    /// 打开主机弹窗命令字符串
    /// </summary>
    private const string UICMDDATA = "openHostDialog";

    public MQSubTaskSub[] subs { get; set; }
    public bool Sub_TASK_MGR_CMD { get; set; } = true;
    public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
    public bool Sub_TASK_HARDWARE_CMD { get; set; } = false;
    public bool Sub_TASK_HARDWARE_DATA { get; set; } = false;
    public bool Sub_TOPIC_FROM_UI { get; set; } = true;
    public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = false;
    public bool Sub_TOPIC_NOTIFY { get; set; } = false;
    public bool Sub_SelfTopic { get; set; } = false;

    public SubTaskCmdParams? ImportParams(string paramatersString)
    {
        Logger.Info("全局执行动作子任务 ImportPrams" + " :" + paramatersString);
        _subTaskCmdParams = JsonSerializer.Deserialize<SubTaskCmdParams>(paramatersString)!;
        return _subTaskCmdParams;
    }

    public bool Run(SubTaskCmdParams param)
    {
        _processID = param.ProcessID;
        _subTaskID = param.SubTaskID;
        _className = param.ClassName;
        _templateInst = GetTemplateByName(_className!);

        Logger.Info("全局执行动作子任务 启动:" + param);
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(param.Cmd(), _processID!, _subTaskID!));

        var scheduleElement = param.SubTaskParams.GetProperty("schedule");
        GlobalActionCode = UtilsForSubTasks.ReadVarValue<string>(_templateInst!, scheduleElement.GetProperty("control_input_globalActionCode"));

        // 通知ui弹出主机列表弹窗
        var openHostDialogMsg = new Dictionary<string, string>() { { "globalActionCode", GlobalActionCode } };
        var uicmdParams = new UICmdParams(_processID!, _subTaskID, UICMDDATA, openHostDialogMsg);
        ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(uicmdParams));

        return true;
    }

    public void HandleMsgFromUI(string paramatersString)
    {
        Logger.Info("全局执行动作子任务 收到来自UI的消息:" + paramatersString);

        var uiMsg = JsonSerializer.Deserialize<MsgFromUI>(paramatersString);
        var messageData = JsonSerializer.Deserialize<UIMessageData>(uiMsg!.JsonArgs!);
        // 当页面点击确定的时候,执行全局动作
        if (IsExecuteGlobalAction(messageData!))
        {
            var globalActionParam = new GlobalActionMsg(GlobalActionCode!, messageData!.HostIDs!);
            var globalActionJE = JsonSerializer.SerializeToElement(globalActionParam);
            Logger.Info("全局执行动作" + globalActionJE);
            ISystemBus.SendToTaskUpTopic(CmdConsts.GlobalProcessAction(_className, _processID!, _subTaskID!, globalActionJE));
        }
        Finish();
    }

    /// <summary>
    /// 是否执行全局动作
    /// </summary>
    /// <param name="messageData"></param>
    /// <returns></returns>
    public static bool IsExecuteGlobalAction(UIMessageData messageData)
    {
        return messageData!.Isaffirm == true && messageData.HostIDs != null && messageData.HostIDs.Any();
    }

    public bool Abort(SubTaskCmdParams param)
    {
        string message = UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD, param.ProcessID!, param.SubTaskID!);
        ISystemBus.SendToUIStatusTopic(message);
        Logger.Info("全局执行动作子任务 终止:" + message);

        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool Finish(SubTaskCmdParams param)
    {
        string message = UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD, param.ProcessID!, param.SubTaskID!);
        ISystemBus.SendToUIStatusTopic(message);
        ISystemBus.SendToTaskUpTopic(CmdConsts.SubTaskFinishCmd(param.ClassName!, param.ProcessID!, param.SubTaskID!));
        Logger.Info("全局执行动作子任务 结束:" + message);
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool Finish()
    {
        string message = UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD, _processID!, _subTaskID!);
        ISystemBus.SendToUIStatusTopic(message);
        ISystemBus.SendToTaskUpTopic(CmdConsts.SubTaskFinishCmd(_className!, _processID!, _subTaskID!));
        Logger.Info("全局执行动作子任务 结束:" + message);
        ((ISubTask)this).CleanAllSubs();
        return true;
    }


    public bool Pause(SubTaskCmdParams param)
    {
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_PAUSE_TASK_CMD, param.ProcessID!, param.SubTaskID!));
        return true;
    }

    public bool Resume(SubTaskCmdParams param)
    {
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_RESUME_TASK_CMD, param.ProcessID!, param.SubTaskID!));
        return true;
    }

    public string[] GetSelfTopic()
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromScript(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromVAR(string topic, string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleNotify(string notifyTitle, string msg)
    {
        throw new NotImplementedException();
    }

    public void ImportHwFuncRet(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public bool ProcessData(SubTaskCmdParams param)
    {
        throw new NotImplementedException();
    }

    public bool ReStart(SubTaskCmdParams param)
    {
        throw new NotImplementedException();
    }

    public JsonElement UIParams()
    {
        throw new NotImplementedException();
    }
    public bool Error(SubTaskCmdParams param)
    {
        throw new NotImplementedException();
    }
}