using System.Reactive.Linq;
using System.Text.Json;
using Consts;
using MQ;
using ScriptEngine;
using ScriptEngine.InputVar;
using ScriptEngine.InputVar.InputVars;
using SignalExample;
using SubTaskUtils;
using static Logging.CCSSLogger;
using static Scripting.ITemplate;

namespace SubTasks.tasks
{
    // <summary>
    // Class <c>SubTaskWaitEvent</c>
    // 监听ONSYSTEMMSG子任务
    //8.26 新增子任务  舍弃状态检测子任务 新增监听ONSYSTEMMSG子任务
    //9.26 该子任务接收多任务管理器下发的finish指令
    //  </summary>
    public class SubTaskOnSystemMsg : ISubTask
    {
        //模板id
        private string? _processID;

        //子任务id
        private string? _subtaskID;

        private string? _className;
        private Scripting.ITemplate? _templateInst;
        private bool finishFlag = true;
        private string? _hwKey;

        //蠕变设备状态码
        private Dictionary<int, DeviceStatusCode>? DeviceStatusCodes = new();

        // sys回调的编码和所需执行动作的dic
        private Dictionary<int, string> MsgAction = new();

        // sys回调的编码和变量的dic
        // 变量是一个Text类型，就是为了存储system回调的全部内容，后面客户需要做什么由客户自己编写脚本处理
        // 每个参数之间用“|”隔离  来存储回调的全部内容
        private Dictionary<int, TextInputVar> MsgInputVar = new();
        private IDisposable subscription;

        public MQSubTaskSub[] subs { set; get; }
        public bool Sub_TASK_MGR_CMD { get; set; } = true;
        public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
        public bool Sub_TASK_HARDWARE_CMD { get; set; } = false;
        public bool Sub_TASK_HARDWARE_DATA { get; set; } = false;
        public bool Sub_TOPIC_FROM_UI { get; set; } = false;
        public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = false;
        public bool Sub_SelfTopic { get; set; } = false;
        public bool Sub_TOPIC_NOTIFY { get; set; } = false;

        public SubTaskCmdParams ImportParams(string ParamatersString)
        {
            Logger.Info("ImportPrams" + " :" + ParamatersString);
            SubTaskCmdParams subTaskCmdParams = JsonSerializer.Deserialize<SubTaskCmdParams>(
                ParamatersString
            )!;
            return subTaskCmdParams;
        }

        /**
        子任务业务逻辑入口
        */
        public bool Run(SubTaskCmdParams Params)
        {
            Logger.Info("监听ONSYSTEMMSG子任务 启动:" + Params);
            _processID = Params.ProcessID;
            _subtaskID = Params.SubTaskID;
            _className = Params.ClassName;
            _templateInst = GetTemplateByName(Params.ClassName!);
            _hwKey = UtilsForSubTasks.GetHwkey(Params.SubTaskParams);
            if (_templateInst is null)
            {
                Logger.Error("模板实例为空");
                return false;
            }

            //蠕变设备状态码DeviceStatusCode 代码详解
            DeviceStatusCodes = UtilsForSubTasks.GetDeviceStatusCodes();

            // 序列化 UICmdParams 对象
            ISystemBus.SendToUIStatusTopic(
                UtilsForSubTasks.GenerateStatusUIJson(Params.Cmd(), _processID!, _subtaskID!)
            );
            Logger.Info(
                $"发送监听ONSYSTEMMSG子任务 启动状态: {UtilsForSubTasks.GenerateStatusUIJson(Params.Cmd(), _processID!, _subtaskID!)}"
            );

            //解析加减控件的参数
            JsonElement customParams = Params!
                .SubTaskParams.GetProperty("schedule")
                .GetProperty("customParams")
                .GetProperty("data");

            foreach (var customParam in customParams.EnumerateArray())
            {
                JsonElement variables = customParam.GetProperty("variables");
                int msgNumber = int.Parse(
                    UtilsForSubTasks.ReadVarValue<string>(
                        Params.ClassName!,
                        variables.GetProperty("control_input_MsgNumber")
                    )
                );
                string action = UtilsForSubTasks.ReadVarValue<string>(
                    Params.ClassName!,
                    variables.GetProperty("control_input_msgNumber_action")
                );
                // 获取记录变量的code，是fx变量才有效，这样才能在脚本中使用
                var strVariableCode = string.Empty;
                if (
                    variables
                        .GetProperty("control_input_variable")
                        .GetProperty("type")
                        .GetString()!
                        .Equals("变量")
                )
                {
                    strVariableCode = variables
                        .GetProperty("control_input_variable")
                        .GetProperty("value")
                        .GetString();
                }
                if (MsgAction.ContainsKey(msgNumber))
                {
                    MsgAction[msgNumber] = action;
                }
                else
                {
                    MsgAction.Add(msgNumber, action);
                }
                if (!string.IsNullOrEmpty(strVariableCode))
                {
                    MsgInputVar[msgNumber] = _templateInst!.GetVarByName<TextInputVar>(
                        strVariableCode
                    );
                }
            }
            // JsonDocument document = JsonDocument.Parse(File.ReadAllText($"{AppDomain.CurrentDomain.BaseDirectory}/subTasks.json"));
            // Logger.Error("subtask数据" + JsonSerializer.Serialize(document.RootElement.GetProperty("hardwareConnectors")));
            // List<string> hwKeys = new List<string>();
            // foreach (var hardwareConnector in document.RootElement.GetProperty("hardwareConnectors").EnumerateArray())
            // {
            //     hwKeys.Add(hardwareConnector.GetProperty("name").ToString());
            // }

            IObservable<SignalExample.SensorSys> varSensorSys = SignalExample
                .Examples_Sys
                .VarSysPosDic[_hwKey];
            var stateCache = new Dictionary<string, string>();
            Logger.Error("开始接收状态");
            subscription = varSensorSys.Subscribe(x =>
            {
                Logger.Info($"Sys Received data: {x}");

                //发送消息到总线
                ISystemBus.SendMsgToTopic(
                    JsonSerializer.Serialize(x),
                    $"SYS-{_processID}-onSystemMsg-TOPIC_FROM_VAR"
                );
                //TODO: 20240412 主机的状态通过 蠕变轴获取
                // 蠕变使用该消息回调作为正常的消息通知使用，单独处理
                if (x.HwType == 2)
                {
                    Logger.Info("蠕变收到消息:" + DeviceStatusCodes[int.Parse(x.Text)].describe);
                    MsgInputVar[0].Value=DeviceStatusCodes[int.Parse(x.Text)].describe;
                    // var hostId = _templateInst.ProjectHostId;
                    // var state = DeviceStatusCodes[int.Parse(x.Text)].describe;
                    // if (hostId != null && state != stateCache[hostId])
                    // {
                    //     stateCache[hostId] = state;
                    //     var msg = new { hostId = hostId, state = state };
                    //     ISystemBus.SendToVarModified(
                    //         JsonSerializer.Serialize(msg),
                    //         new VarModifiedMsgOptions { SaveDB = true, Type = "HostState" }
                    //     );
                    //     // 发送主机状态修改信息，给UI更新数据
                    //     ISystemBus.SendToUIHostState(JsonSerializer.Serialize(msg));
                    // }
                }
                else
                {
                    // 不是蠕变轴的情况
                    if (MsgInputVar.ContainsKey(x.MsgNumber))
                    {
                        SaveMsgToInputVar(x);
                    }
                }

                //消息推送前端
                //NoticeUIErrorMsg(x);

                if (MsgAction.ContainsKey(x.MsgNumber))
                {
                    string actionId = MsgAction[x.MsgNumber];
                    if (!string.IsNullOrEmpty(actionId))
                    {
                        UtilsForSubTasks.executeAction(actionId!, _className!, _processID!, _subtaskID!, new
                        {
                            Cmd = "start",
                            InstCode = _templateInst?.CurrentInst.Code,
                            ActionID = actionId
                        });
                    }
                }
            });

            return true;
        }

        /// <summary>
        /// 以  |  为分隔符保存回调的所有内容
        /// </summary>
        /// <param name="x"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void SaveMsgToInputVar(SensorSys x)
        {
            if (MsgInputVar.ContainsKey(x.MsgNumber))
            {
                MsgInputVar[x.MsgNumber].Value =
                    $"{x.MsgNumber}|{x.HwType}|{x.DeviceID}|{x.ADSensorID}|{x.Text}|{x.Time}";
            }
        }

        /// <summary>
        /// 将错误信息发送给UI
        /// </summary>
        /// <param name="sensorSys"></param>
        private void NoticeUIErrorMsg(SignalExample.SensorSys sensorSys)
        {
            //发送消息给UI弹窗
            var uicmdParam = new UICmdParam("reminder", "错误消息编号" + sensorSys.Text);
            // 构建 JSON 字符串
            var jsonStr = JsonSerializer.Serialize(uicmdParam);

            // 通过解析 JSON 字符串创建 JsonDocument 对象
            using (JsonDocument document = JsonDocument.Parse(jsonStr))
            {
                // 获取根节点的 JsonElement
                JsonElement root = document.RootElement;

                // 构建 UICmdParams 对象，并将 UIParams 设置为 JsonElement
                var uicmdParams = new UICmdParams(_processID, _subtaskID, "openDialog", root);

                // 序列化 UICmdParams 对象
                ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(uicmdParams));
                Logger.Info(
                    $"handleMsgFromVAR发送消息msg1:: {JsonSerializer.Serialize(uicmdParams)}"
                );
            }
        }

        /**
        执行action方法
        */
        public void ExecuteAction(String actionCode)
        {
            var actionParam = new ActionMsg(actionCode);
            var jsonStr = JsonSerializer.Serialize(actionParam);
            JsonDocument document = JsonDocument.Parse(jsonStr);
            JsonElement root = document.RootElement;
            Logger.Info("执行动作" + root);
            ISystemBus.SendToTaskUpTopic(
                CmdConsts.ProcessAction(_className, _processID!, _subtaskID!, root)
            );
        }

        /**
        子任务完成
       */
        public bool Finish()
        {
            ISystemBus.SendToUIStatusTopic(
                UtilsForSubTasks.GenerateStatusUIJson(
                    CmdConsts.RCV_FINISH_TASK_CMD,
                    _processID!,
                    _subtaskID!
                )
            );
            Logger.Info(
                "监听ONSYSTEMMSG子任务 完成:"
                    + UtilsForSubTasks.GenerateStatusUIJson(
                        CmdConsts.RCV_FINISH_TASK_CMD,
                        _processID!,
                        _subtaskID!
                    )
            );

            //通知多任务管理器，子任务结束
            ISystemBus.SendToTaskUpTopic(
                CmdConsts.SubTaskFinishCmd(_className, _processID, _subtaskID)
            );
            finishFlag = false;
            ((ISubTask)this).CleanAllSubs();
            return true;
        }

        /**
        子任务完成
        param：
        */
        public bool Finish(SubTaskCmdParams param)
        {
            ISystemBus.SendToUIStatusTopic(
                UtilsForSubTasks.GenerateStatusUIJson(
                    CmdConsts.RCV_FINISH_TASK_CMD,
                    _processID!,
                    _subtaskID!
                )
            );
            subscription.Dispose();
            Logger.Info(
                "监听ONSYSTEMMSG子任务 完成:"
                    + UtilsForSubTasks.GenerateStatusUIJson(
                        CmdConsts.RCV_FINISH_TASK_CMD,
                        _processID!,
                        _subtaskID!
                    )
            );

            //通知多任务管理器，子任务结束
            ISystemBus.SendToTaskUpTopic(
                CmdConsts.SubTaskFinishCmd(param.ClassName!, param.ProcessID!, param.SubTaskID!)
            );
            finishFlag = false;
            ((ISubTask)this).CleanAllSubs();
            return true;
        }

        /**
        终止执行
        */
        public bool Abort(SubTaskCmdParams param)
        {
            ISystemBus.SendToUIStatusTopic(
                UtilsForSubTasks.GenerateStatusUIJson(
                    CmdConsts.RCV_ABORT_TASK_CMD,
                    _processID!,
                    _subtaskID!
                )
            );
            Logger.Info(
                $"发送监听ONSYSTEMMSG子任务 终止状态: {UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD, _processID!, _subtaskID!)}"
            );

            ((ISubTask)this).CleanAllSubs();
            return true;
        }

        /**
        暂停执行
        */
        public bool Pause(SubTaskCmdParams param)
        {
            if (finishFlag)
            {
                ISystemBus.SendToUIStatusTopic(
                    UtilsForSubTasks.GenerateStatusUIJson(
                        CmdConsts.RCV_PAUSE_TASK_CMD,
                        _processID!,
                        _subtaskID!
                    )
                );
                Logger.Info(
                    "监听ONSYSTEMMSG子任务 暂停:"
                        + UtilsForSubTasks.GenerateStatusUIJson(
                            CmdConsts.RCV_PAUSE_TASK_CMD,
                            _processID!,
                            _subtaskID!
                        )
                );
            }
            return true;
        }

        /**
        恢复执行
        */
        public bool Resume(SubTaskCmdParams param)
        {
            if (finishFlag)
            {
                ISystemBus.SendToUIStatusTopic(
                    UtilsForSubTasks.GenerateStatusUIJson(
                        CmdConsts.RCV_RESUME_TASK_CMD,
                        _processID!,
                        _subtaskID!
                    )
                );
                Logger.Info(
                    "监听ONSYSTEMMSG子任务 恢复："
                        + UtilsForSubTasks.GenerateStatusUIJson(
                            CmdConsts.RCV_RESUME_TASK_CMD,
                            _processID!,
                            _subtaskID!
                        )
                );
            }
            return true;
        }

        public string[] GetSelfTopic()
        {
            string[] self_topics = Array.Empty<string>();
            return self_topics;
        }

        public void HandleMsgFromScript(string ParamatersString)
        {
            throw new NotImplementedException();
        }

        public void HandleMsgFromUI(string ParamatersString)
        {
            throw new NotImplementedException();
        }

        public void HandleMsgFromVAR(string topic, string ParamatersString)
        {
            throw new NotImplementedException();
        }

        public void ImportHwFuncRet(string ParamatersString)
        {
            throw new NotImplementedException();
        }

        public bool ProcessData(SubTaskCmdParams Params)
        {
            throw new NotImplementedException();
        }

        public JsonElement UIParams()
        {
            throw new NotImplementedException();
        }

        public bool ReStart(SubTaskCmdParams Params)
        {
            throw new NotImplementedException();
        }

        public bool Error(SubTaskCmdParams Params)
        {
            throw new NotImplementedException();
        }

        public void HandleNotify(string notifyTitle, string msg)
        {
            throw new NotImplementedException();
        }
    }
}
