2025-09-04 10:21:54,915 [XNIO-1 task-7] ERROR io.undertow.request - UT005071: Undertow request failed HttpServerExchange{ GET /api/picture/images/753aaa6a-3936-4728-8429-44b5379f2ffc} 
java.lang.IllegalStateException: UT000196: Session with id NZ05eyeHY3VOwIIiRTFtOeYQyNr_8pNhg4eb8J9T already exists
	at io.undertow.server.session.InMemorySessionManager.createSession(InMemorySessionManager.java:179)
	at io.undertow.util.Sessions.getSession(Sessions.java:60)
	at io.undertow.util.Sessions.getOrCreateSession(Sessions.java:49)
	at ring.adapter.undertow.middleware.session$get_or_create_session.invokeStatic(session.clj:64)
	at ring.adapter.undertow.middleware.session$get_or_create_session.invoke(session.clj:54)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:90)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:178)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 10:25:11,708 [XNIO-1 task-2] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__48032$fn__48033.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__48032.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48207.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 10:25:18,294 [XNIO-1 task-2] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__48032$fn__48033.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__48032.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48207.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 10:48:04,129 [XNIO-1 task-2] ERROR jdbc.audit - 5. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47918.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48212.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 10:48:04,130 [XNIO-1 task-2] ERROR jdbc.sqltiming - 5. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 6 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47918.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_routes$routes$fn__48212.invoke(project_routes.clj:79)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 10:51:54,902 [XNIO-1 task-1] ERROR org.jboss.threads.errors - Thread Thread[XNIO-1 task-1,5,main] threw an uncaught exception 
java.lang.NullPointerException: Cannot invoke "Object.hashCode()" because "key" is null
	at java.base/java.util.concurrent.ConcurrentHashMap.replaceNode(ConcurrentHashMap.java:1111)
	at java.base/java.util.concurrent.ConcurrentHashMap.remove(ConcurrentHashMap.java:1102)
	at io.undertow.server.session.InMemorySessionManager$SessionImpl.invalidate(InMemorySessionManager.java:609)
	at io.undertow.server.session.InMemorySessionManager$SessionImpl$2$1.run(InMemorySessionManager.java:417)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 11:54:23,374 [XNIO-1 task-3] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:52)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:37)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:25)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.instantiation.action$change_state.invokeStatic(action.clj:9)
	at clj_backend.common.instantiation.action$change_state.invoke(action.clj:6)
	at clj_scheduler.context$update_status.invokeStatic(context.clj:1026)
	at clj_scheduler.context$update_status.invoke(context.clj:1014)
	at clj_scheduler.context.multi-task-mgr.stop(context.clj:1084)
	at clj_scheduler.context.multi-task-mgr.abort(context.clj:1120)
	at clj_scheduler.context$mgr_abort.invokeStatic(context.clj:1143)
	at clj_scheduler.context$mgr_abort.invoke(context.clj:1132)
	at clj_scheduler.executer$project_stop$fn__38451.invoke(executer.clj:250)
	at clojure.core$run_BANG_$fn__8922.invoke(core.clj:7907)
	at clojure.core.protocols$iterator_reduce_BANG_.invokeStatic(protocols.clj:42)
	at clojure.core.protocols$iter_reduce.invokeStatic(protocols.clj:52)
	at clojure.core.protocols$fn__8256.invokeStatic(protocols.clj:74)
	at clojure.core.protocols$fn__8256.invoke(protocols.clj:74)
	at clojure.core.protocols$fn__8203$G__8198__8216.invoke(protocols.clj:13)
	at clojure.core$reduce.invokeStatic(core.clj:6965)
	at clojure.core$run_BANG_.invokeStatic(core.clj:7902)
	at clojure.core$run_BANG_.invoke(core.clj:7902)
	at clj_scheduler.executer$project_stop.invokeStatic(executer.clj:249)
	at clj_scheduler.executer$project_stop.invoke(executer.clj:245)
	at clj_backend.modules.project.project_service$close_project.invokeStatic(project_service.clj:391)
	at clj_backend.modules.project.project_service$close_project.invoke(project_service.clj:376)
	at clj_backend.modules.project.project_routes$routes$fn__48217.invoke(project_routes.clj:85)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 11:54:35,505 [XNIO-1 task-3] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:52)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:37)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:25)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.instantiation.action$change_state.invokeStatic(action.clj:9)
	at clj_backend.common.instantiation.action$change_state.invoke(action.clj:6)
	at clj_scheduler.context$update_status.invokeStatic(context.clj:1026)
	at clj_scheduler.context$update_status.invoke(context.clj:1014)
	at clj_scheduler.context.multi-task-mgr.stop(context.clj:1084)
	at clj_scheduler.context.multi-task-mgr.abort(context.clj:1120)
	at clj_scheduler.context$mgr_abort.invokeStatic(context.clj:1143)
	at clj_scheduler.context$mgr_abort.invoke(context.clj:1132)
	at clj_scheduler.executer$project_stop$fn__38451.invoke(executer.clj:250)
	at clojure.core$run_BANG_$fn__8922.invoke(core.clj:7907)
	at clojure.core.protocols$iterator_reduce_BANG_.invokeStatic(protocols.clj:42)
	at clojure.core.protocols$iter_reduce.invokeStatic(protocols.clj:52)
	at clojure.core.protocols$fn__8256.invokeStatic(protocols.clj:74)
	at clojure.core.protocols$fn__8256.invoke(protocols.clj:74)
	at clojure.core.protocols$fn__8203$G__8198__8216.invoke(protocols.clj:13)
	at clojure.core$reduce.invokeStatic(core.clj:6965)
	at clojure.core$run_BANG_.invokeStatic(core.clj:7902)
	at clojure.core$run_BANG_.invoke(core.clj:7902)
	at clj_scheduler.executer$project_stop.invokeStatic(executer.clj:249)
	at clj_scheduler.executer$project_stop.invoke(executer.clj:245)
	at clj_backend.modules.project.project_service$close_project.invokeStatic(project_service.clj:391)
	at clj_backend.modules.project.project_service$close_project.invoke(project_service.clj:376)
	at clj_backend.modules.project.project_routes$routes$fn__48217.invoke(project_routes.clj:85)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 13:37:43,094 [XNIO-1 task-5] ERROR io.undertow.request - UT005071: Undertow request failed HttpServerExchange{ GET /api/picture/images/3db096b7-cbbb-4415-ad14-135216f9bc71} 
java.lang.IllegalStateException: UT000196: Session with id NZ05eyeHY3VOwIIiRTFtOeYQyNr_8pNhg4eb8J9T already exists
	at io.undertow.server.session.InMemorySessionManager.createSession(InMemorySessionManager.java:179)
	at io.undertow.util.Sessions.getSession(Sessions.java:60)
	at io.undertow.util.Sessions.getOrCreateSession(Sessions.java:49)
	at ring.adapter.undertow.middleware.session$get_or_create_session.invokeStatic(session.clj:64)
	at ring.adapter.undertow.middleware.session$get_or_create_session.invoke(session.clj:54)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:90)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:178)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 14:07:43,097 [XNIO-1 task-7] ERROR org.jboss.threads.errors - Thread Thread[XNIO-1 task-7,5,main] threw an uncaught exception 
java.lang.NullPointerException: Cannot invoke "Object.hashCode()" because "key" is null
	at java.base/java.util.concurrent.ConcurrentHashMap.replaceNode(ConcurrentHashMap.java:1111)
	at java.base/java.util.concurrent.ConcurrentHashMap.remove(ConcurrentHashMap.java:1102)
	at io.undertow.server.session.InMemorySessionManager$SessionImpl.invalidate(InMemorySessionManager.java:609)
	at io.undertow.server.session.InMemorySessionManager$SessionImpl$2$1.run(InMemorySessionManager.java:417)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 16:18:40,040 [XNIO-1 task-3] ERROR io.undertow.request - UT005071: Undertow request failed HttpServerExchange{ GET /api/picture/images/3db096b7-cbbb-4415-ad14-135216f9bc71} 
java.lang.IllegalStateException: UT000196: Session with id NZ05eyeHY3VOwIIiRTFtOeYQyNr_8pNhg4eb8J9T already exists
	at io.undertow.server.session.InMemorySessionManager.createSession(InMemorySessionManager.java:179)
	at io.undertow.util.Sessions.getSession(Sessions.java:60)
	at io.undertow.util.Sessions.getOrCreateSession(Sessions.java:49)
	at ring.adapter.undertow.middleware.session$get_or_create_session.invokeStatic(session.clj:64)
	at ring.adapter.undertow.middleware.session$get_or_create_session.invoke(session.clj:54)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:90)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:178)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 16:20:59,147 [XNIO-1 task-1] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:52)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:37)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:25)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invokeStatic(service.clj:536)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invoke(service.clj:522)
	at clj_backend.modules.sys.user.sys_user_service$login.invokeStatic(sys_user_service.clj:96)
	at clj_backend.modules.sys.user.sys_user_service$login.invoke(sys_user_service.clj:91)
	at clj_backend.modules.sys.user.sys_user_routes$routes$fn__43149.invoke(sys_user_routes.clj:56)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 16:48:40,047 [XNIO-1 task-9] ERROR org.jboss.threads.errors - Thread Thread[XNIO-1 task-9,5,main] threw an uncaught exception 
java.lang.NullPointerException: Cannot invoke "Object.hashCode()" because "key" is null
	at java.base/java.util.concurrent.ConcurrentHashMap.replaceNode(ConcurrentHashMap.java:1111)
	at java.base/java.util.concurrent.ConcurrentHashMap.remove(ConcurrentHashMap.java:1102)
	at io.undertow.server.session.InMemorySessionManager$SessionImpl.invalidate(InMemorySessionManager.java:609)
	at io.undertow.server.session.InMemorySessionManager$SessionImpl$2$1.run(InMemorySessionManager.java:417)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-04 17:30:45,419 [XNIO-1 task-4] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:52)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:37)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:25)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invokeStatic(service.clj:536)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invoke(service.clj:522)
	at clj_backend.modules.sys.user.sys_user_service$login.invokeStatic(sys_user_service.clj:96)
	at clj_backend.modules.sys.user.sys_user_service$login.invoke(sys_user_service.clj:91)
	at clj_backend.modules.sys.user.sys_user_routes$routes$fn__43149.invoke(sys_user_routes.clj:56)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
