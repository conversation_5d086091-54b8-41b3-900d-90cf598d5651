25-09-05 09:02:30:429 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 09:02:30:434 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 09:02:30:510 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-05 09:02:30:512 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 09:02:30:566 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 09:02:30:567 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-05 09:02:30:572 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-05 09:02:30:577 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【联机】"}
25-09-05 09:02:30:578 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 09:02:30:633 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-05 09:02:30:639 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 09:02:30:643 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 09:02:30:644 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 09:02:30:716 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-05 09:02:30:717 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 09:02:30:749 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-05 09:02:30:868 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 09:02:30:977 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 09:02:30:978 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 09:02:30:980 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-05 09:02:31:035 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-05 09:02:31:089 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 09:02:32:484 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-05 09:02:32:567 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-05 09:02:32:568 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-05 09:02:37:314 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_28"}
25-09-05 09:02:37:341 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 09:02:39:450 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "9f8539c6-9b9e-4e9f-9550-90041c0e5419", :code 0, :msg "模板生成成功"}
25-09-05 09:02:39:451 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-9f8539c6-9b9e-4e9f-9550-90041c0e5419 中添加消息 {:ProcessId "9f8539c6-9b9e-4e9f-9550-90041c0e5419", :code 0, :msg "模板生成成功"}
25-09-05 09:02:39:452 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "9f8539c6-9b9e-4e9f-9550-90041c0e5419", :code 0, :msg "模板生成成功"}
25-09-05 09:02:40:561 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_28", :CurrentInstCode "sample_c590a6", :SelectedInstCodes []} 
 =============================================================

25-09-05 09:02:41:635 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "6f603b7d-ade2-480a-bf34-740e5a5018b8", :Result true}
25-09-05 09:02:41:636 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-6f603b7d-ade2-480a-bf34-740e5a5018b8 中添加消息 {:ProcessId "project_28", :ScriptId "6f603b7d-ade2-480a-bf34-740e5a5018b8", :Result true}
25-09-05 09:02:41:637 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "6f603b7d-ade2-480a-bf34-740e5a5018b8", :Result true}
25-09-05 09:02:41:649 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "f82e83a0-13c4-45c9-b8f7-22561ce865d7", :Result true}
25-09-05 09:02:41:651 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-f82e83a0-13c4-45c9-b8f7-22561ce865d7 中添加消息 {:ProcessId "project_28", :ScriptId "f82e83a0-13c4-45c9-b8f7-22561ce865d7", :Result true}
25-09-05 09:02:41:651 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "f82e83a0-13c4-45c9-b8f7-22561ce865d7", :Result true}
25-09-05 09:02:41:658 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "1294b9a0-af04-4873-bcac-6337e807bd74", :Result false}
25-09-05 09:02:41:659 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-1294b9a0-af04-4873-bcac-6337e807bd74 中添加消息 {:ProcessId "project_28", :ScriptId "1294b9a0-af04-4873-bcac-6337e807bd74", :Result false}
25-09-05 09:02:41:659 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "1294b9a0-af04-4873-bcac-6337e807bd74", :Result false}
25-09-05 09:02:41:665 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "4e6a73b1-915c-48df-a145-a810d98e7040", :Result true}
25-09-05 09:02:41:666 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-4e6a73b1-915c-48df-a145-a810d98e7040 中添加消息 {:ProcessId "project_28", :ScriptId "4e6a73b1-915c-48df-a145-a810d98e7040", :Result true}
25-09-05 09:02:41:668 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "4e6a73b1-915c-48df-a145-a810d98e7040", :Result true}
25-09-05 09:02:41:668 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "b2186750-48c5-47f5-a728-df9a3cab0bac", :Result true}
25-09-05 09:02:41:669 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b2186750-48c5-47f5-a728-df9a3cab0bac 中添加消息 {:ProcessId "project_28", :ScriptId "b2186750-48c5-47f5-a728-df9a3cab0bac", :Result true}
25-09-05 09:02:41:669 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "b2186750-48c5-47f5-a728-df9a3cab0bac", :Result true}
25-09-05 09:02:41:745 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "60cc5799-0fd9-4c64-a644-99a815f33924", :Result true}
25-09-05 09:02:41:745 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-60cc5799-0fd9-4c64-a644-99a815f33924 中添加消息 {:ProcessId "project_28", :ScriptId "60cc5799-0fd9-4c64-a644-99a815f33924", :Result true}
25-09-05 09:02:41:746 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "60cc5799-0fd9-4c64-a644-99a815f33924", :Result true}
25-09-05 09:02:41:749 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "184af037-8d36-4386-a3aa-608208323b1a", :Result false}
25-09-05 09:02:41:749 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-184af037-8d36-4386-a3aa-608208323b1a 中添加消息 {:ProcessId "project_28", :ScriptId "184af037-8d36-4386-a3aa-608208323b1a", :Result false}
25-09-05 09:02:41:750 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "184af037-8d36-4386-a3aa-608208323b1a", :Result false}
25-09-05 09:02:41:753 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "9c0085f7-e36e-4cf2-a685-5ffada86bcbf", :Result false}
25-09-05 09:02:41:753 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-9c0085f7-e36e-4cf2-a685-5ffada86bcbf 中添加消息 {:ProcessId "project_28", :ScriptId "9c0085f7-e36e-4cf2-a685-5ffada86bcbf", :Result false}
25-09-05 09:02:41:754 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "9c0085f7-e36e-4cf2-a685-5ffada86bcbf", :Result false}
25-09-05 09:02:41:759 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "3d9de2de-b878-404d-b8d0-d7fc8808c0fd", :Result true}
25-09-05 09:02:41:759 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-3d9de2de-b878-404d-b8d0-d7fc8808c0fd 中添加消息 {:ProcessId "project_28", :ScriptId "3d9de2de-b878-404d-b8d0-d7fc8808c0fd", :Result true}
25-09-05 09:02:41:761 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "3d9de2de-b878-404d-b8d0-d7fc8808c0fd", :Result true}
25-09-05 09:02:41:764 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "a9ba5725-a944-467e-b694-95b29f6e8dc7", :Result false}
25-09-05 09:02:41:764 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-a9ba5725-a944-467e-b694-95b29f6e8dc7 中添加消息 {:ProcessId "project_28", :ScriptId "a9ba5725-a944-467e-b694-95b29f6e8dc7", :Result false}
25-09-05 09:02:41:765 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "a9ba5725-a944-467e-b694-95b29f6e8dc7", :Result false}
25-09-05 09:02:41:767 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "31398520-0a00-49c6-8a49-4817dd6c8d32", :Result false}
25-09-05 09:02:41:767 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-31398520-0a00-49c6-8a49-4817dd6c8d32 中添加消息 {:ProcessId "project_28", :ScriptId "31398520-0a00-49c6-8a49-4817dd6c8d32", :Result false}
25-09-05 09:02:41:768 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "31398520-0a00-49c6-8a49-4817dd6c8d32", :Result false}
25-09-05 09:02:41:772 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "1843f8bc-aea5-4b3a-8d43-eb36954bb1a0", :Result false}
25-09-05 09:02:41:772 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-1843f8bc-aea5-4b3a-8d43-eb36954bb1a0 中添加消息 {:ProcessId "project_28", :ScriptId "1843f8bc-aea5-4b3a-8d43-eb36954bb1a0", :Result false}
25-09-05 09:02:41:773 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "1843f8bc-aea5-4b3a-8d43-eb36954bb1a0", :Result false}
25-09-05 09:02:41:781 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "7086fb0d-455d-4f5b-9717-49d7a971cbaa", :Result true}
25-09-05 09:02:41:781 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-7086fb0d-455d-4f5b-9717-49d7a971cbaa 中添加消息 {:ProcessId "project_28", :ScriptId "7086fb0d-455d-4f5b-9717-49d7a971cbaa", :Result true}
25-09-05 09:02:41:781 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "7086fb0d-455d-4f5b-9717-49d7a971cbaa", :Result true}
25-09-05 09:02:41:785 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "4f650489-0b19-4a8b-b7f5-de747d6fc5cc", :Result false}
25-09-05 09:02:41:785 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-4f650489-0b19-4a8b-b7f5-de747d6fc5cc 中添加消息 {:ProcessId "project_28", :ScriptId "4f650489-0b19-4a8b-b7f5-de747d6fc5cc", :Result false}
25-09-05 09:02:41:786 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "4f650489-0b19-4a8b-b7f5-de747d6fc5cc", :Result false}
25-09-05 09:02:41:790 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "86daec14-1e84-4fca-a5db-1ee9ea4aeb59", :Result true}
25-09-05 09:02:41:790 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-86daec14-1e84-4fca-a5db-1ee9ea4aeb59 中添加消息 {:ProcessId "project_28", :ScriptId "86daec14-1e84-4fca-a5db-1ee9ea4aeb59", :Result true}
25-09-05 09:02:41:791 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "86daec14-1e84-4fca-a5db-1ee9ea4aeb59", :Result true}
25-09-05 09:02:41:794 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "db9212db-ed20-496c-a46d-96aa18c4d251", :Result false}
25-09-05 09:02:41:794 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-db9212db-ed20-496c-a46d-96aa18c4d251 中添加消息 {:ProcessId "project_28", :ScriptId "db9212db-ed20-496c-a46d-96aa18c4d251", :Result false}
25-09-05 09:02:41:795 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "db9212db-ed20-496c-a46d-96aa18c4d251", :Result false}
25-09-05 09:02:41:798 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "77afe24b-86a9-489d-942d-b2d915890952", :Result false}
25-09-05 09:02:41:798 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-77afe24b-86a9-489d-942d-b2d915890952 中添加消息 {:ProcessId "project_28", :ScriptId "77afe24b-86a9-489d-942d-b2d915890952", :Result false}
25-09-05 09:02:41:799 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "77afe24b-86a9-489d-942d-b2d915890952", :Result false}
25-09-05 09:02:41:803 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "5f6128a4-2ca1-485e-be69-b5a3aae5ab1f", :Result true}
25-09-05 09:02:41:803 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-5f6128a4-2ca1-485e-be69-b5a3aae5ab1f 中添加消息 {:ProcessId "project_28", :ScriptId "5f6128a4-2ca1-485e-be69-b5a3aae5ab1f", :Result true}
25-09-05 09:02:41:804 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "5f6128a4-2ca1-485e-be69-b5a3aae5ab1f", :Result true}
25-09-05 09:02:41:806 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "aad3d604-6dd7-4901-aaca-45e72aba053b", :Result true}
25-09-05 09:02:41:806 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-aad3d604-6dd7-4901-aaca-45e72aba053b 中添加消息 {:ProcessId "project_28", :ScriptId "aad3d604-6dd7-4901-aaca-45e72aba053b", :Result true}
25-09-05 09:02:41:807 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "aad3d604-6dd7-4901-aaca-45e72aba053b", :Result true}
25-09-05 09:02:41:809 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "9fcad17a-4085-44ba-80ea-1b840e0d2040", :Result true}
25-09-05 09:02:41:810 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-9fcad17a-4085-44ba-80ea-1b840e0d2040 中添加消息 {:ProcessId "project_28", :ScriptId "9fcad17a-4085-44ba-80ea-1b840e0d2040", :Result true}
25-09-05 09:02:41:811 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "9fcad17a-4085-44ba-80ea-1b840e0d2040", :Result true}
25-09-05 09:02:41:814 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "69e20f21-78e8-4ffe-adf2-8107614f0207", :Result true}
25-09-05 09:02:41:814 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-69e20f21-78e8-4ffe-adf2-8107614f0207 中添加消息 {:ProcessId "project_28", :ScriptId "69e20f21-78e8-4ffe-adf2-8107614f0207", :Result true}
25-09-05 09:02:41:814 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "69e20f21-78e8-4ffe-adf2-8107614f0207", :Result true}
25-09-05 09:02:42:558 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "4803b10c-effb-4573-bbc9-c1647d720893", :Result true}
25-09-05 09:02:42:558 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-4803b10c-effb-4573-bbc9-c1647d720893 中添加消息 {:ProcessId "project_28", :ScriptId "4803b10c-effb-4573-bbc9-c1647d720893", :Result true}
25-09-05 09:02:42:559 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "4803b10c-effb-4573-bbc9-c1647d720893", :Result true}
25-09-05 09:02:42:910 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "74af3fa9-0809-4433-b75d-5f2f691c4d53", :Result true}
25-09-05 09:02:42:911 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-74af3fa9-0809-4433-b75d-5f2f691c4d53 中添加消息 {:ProcessId "project_28", :ScriptId "74af3fa9-0809-4433-b75d-5f2f691c4d53", :Result true}
25-09-05 09:02:42:911 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "74af3fa9-0809-4433-b75d-5f2f691c4d53", :Result true}
25-09-05 09:02:42:939 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "6d8316e7-1d65-4f6b-a486-1e7f428f2f49", :Result true}
25-09-05 09:02:42:940 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-6d8316e7-1d65-4f6b-a486-1e7f428f2f49 中添加消息 {:ProcessId "project_28", :ScriptId "6d8316e7-1d65-4f6b-a486-1e7f428f2f49", :Result true}
25-09-05 09:02:42:941 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "6d8316e7-1d65-4f6b-a486-1e7f428f2f49", :Result true}
25-09-05 09:02:42:944 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "abb8878c-58a5-421c-9d29-9b12a4df2f06", :Result true}
25-09-05 09:02:42:945 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-abb8878c-58a5-421c-9d29-9b12a4df2f06 中添加消息 {:ProcessId "project_28", :ScriptId "abb8878c-58a5-421c-9d29-9b12a4df2f06", :Result true}
25-09-05 09:02:42:946 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "abb8878c-58a5-421c-9d29-9b12a4df2f06", :Result true}
25-09-05 09:02:43:962 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "开始流程【试样变化】"}
25-09-05 09:02:43:964 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-15f757e9-db77-4b04-ae63-5bbe94ce3325", :State "running"} 
 =============================================================

25-09-05 09:02:44:052 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_28", :ProcessID "project_28-15f757e9-db77-4b04-ae63-5bbe94ce3325", :SubTaskID "onlyAction-0d71b8ff-ce16-4845-ab02-26e8a0f21ff7", :MsgBody {:Cmd "start", :InstCode "sample_c590a6", :ActionID "d3630a7a-1b95-44e8-8de5-39de95097f7e"}}
25-09-05 09:02:44:054 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "开始流程【计算试样横截面积】"}
25-09-05 09:02:44:055 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-d3630a7a-1b95-44e8-8de5-39de95097f7e", :State "running"} 
 =============================================================

25-09-05 09:02:44:156 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "开始流程【初始化】"}
25-09-05 09:02:44:158 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-aa6903af-b922-43ce-aca5-66a3c5399227", :State "running"} 
 =============================================================

25-09-05 09:02:44:293 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【计算试样横截面积】"}
25-09-05 09:02:44:295 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-d3630a7a-1b95-44e8-8de5-39de95097f7e", :State "finished"} 
 =============================================================

25-09-05 09:02:44:439 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_28", :ScriptId "9a7c588b-3d3f-4fe8-bab9-2006adad1ecc", :Result true}
25-09-05 09:02:44:439 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-9a7c588b-3d3f-4fe8-bab9-2006adad1ecc 中添加消息 {:ProcessId "project_28", :ScriptId "9a7c588b-3d3f-4fe8-bab9-2006adad1ecc", :Result true}
25-09-05 09:02:44:440 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_28", :ScriptId "9a7c588b-3d3f-4fe8-bab9-2006adad1ecc", :Result true}
25-09-05 09:02:44:599 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_28", :ProcessID "project_28-aa6903af-b922-43ce-aca5-66a3c5399227", :SubTaskID "onlyAction-5d80de44-03d4-4252-8de6-37a7dc9122ea", :MsgBody {:Cmd "start", :InstCode "sample_c590a6", :ActionID "0298c64e-2540-4570-a982-d4facc7c8e3d"}}
25-09-05 09:02:44:603 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "开始流程【联机DAQ】"}
25-09-05 09:02:44:604 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-0298c64e-2540-4570-a982-d4facc7c8e3d", :State "running"} 
 =============================================================

25-09-05 09:02:45:040 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_28", :ProcessID "project_28-15f757e9-db77-4b04-ae63-5bbe94ce3325", :SubTaskID "onlyAction-2f8f232f-e1bc-43c4-8b87-ae376e1fcf7e", :MsgBody {:Cmd "start", :InstCode "sample_c590a6", :ActionID "7ba8a333-cc6a-4d8b-a593-8c87e852e343"}}
25-09-05 09:02:45:043 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "开始流程【特定周期重置】"}
25-09-05 09:02:45:044 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-7ba8a333-cc6a-4d8b-a593-8c87e852e343", :State "running"} 
 =============================================================

25-09-05 09:02:45:100 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【试样变化】"}
25-09-05 09:02:45:102 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-15f757e9-db77-4b04-ae63-5bbe94ce3325", :State "finished"} 
 =============================================================

25-09-05 09:02:45:177 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【特定周期重置】"}
25-09-05 09:02:45:179 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-7ba8a333-cc6a-4d8b-a593-8c87e852e343", :State "finished"} 
 =============================================================

25-09-05 09:03:10:287 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "开始流程【关闭项目执行脚本】"}
25-09-05 09:03:10:289 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-5f8092a7-dddd-460b-bd1c-a8fe5a6fa67f", :State "running"} 
 =============================================================

25-09-05 09:03:10:335 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 09:03:10:336 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_28】项目"}
25-09-05 09:03:10:340 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【试样变化】"}
25-09-05 09:03:10:344 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【计算试样横截面积】"}
25-09-05 09:03:10:349 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【初始化】"}
25-09-05 09:03:10:351 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-aa6903af-b922-43ce-aca5-66a3c5399227", :State "finished"} 
 =============================================================

25-09-05 09:03:10:453 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【联机DAQ】"}
25-09-05 09:03:10:453 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-0298c64e-2540-4570-a982-d4facc7c8e3d", :State "finished"} 
 =============================================================

25-09-05 09:03:10:553 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【特定周期重置】"}
25-09-05 09:03:10:555 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 28, :project_name "高周应力疲劳试验模板(1)", :content "结束流程【关闭项目执行脚本】"}
25-09-05 09:03:10:557 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_28", :ProcessId "project_28-5f8092a7-dddd-460b-bd1c-a8fe5a6fa67f", :State "finished"} 
 =============================================================

25-09-05 09:03:12:354 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756952510512_project_28.db 成功
25-09-05 09:03:12:426 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_28 应用性能优化配置
25-09-05 09:03:12:427 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_28 项目库连接
25-09-05 09:04:14:517 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-05 09:04:14:544 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 09:04:16:165 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "6ec9c1c9-0b2c-49c1-81f8-e40513a339e1", :code 0, :msg "模板生成成功"}
25-09-05 09:04:16:166 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-6ec9c1c9-0b2c-49c1-81f8-e40513a339e1 中添加消息 {:ProcessId "6ec9c1c9-0b2c-49c1-81f8-e40513a339e1", :code 0, :msg "模板生成成功"}
25-09-05 09:04:16:167 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "6ec9c1c9-0b2c-49c1-81f8-e40513a339e1", :code 0, :msg "模板生成成功"}
25-09-05 09:04:17:335 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_27", :CurrentInstCode "sample_14785d372", :SelectedInstCodes []} 
 =============================================================

25-09-05 09:04:31:316 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-05 09:04:31:318 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 09:04:31:666 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-05 09:04:31:668 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 09:09:45:176 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_27", :Subtask {:SubTaskId "daq-d5711df9-3137-4a67-9444-060b64552e70", :ActionId "5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :TemplateName "project_27", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "RESTART", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value "signal_time", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_header_buffer", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value false, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value false, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck false}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.2, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "5a5f09ef-c362-437a-9a8a-8e1d86e455d1"} 
 =============================================================

25-09-05 09:36:35:707 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 09:36:35:710 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 09:36:35:774 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-05 09:36:35:776 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 09:36:35:924 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-05 09:36:35:969 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 09:36:35:971 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 09:36:36:051 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 09:36:36:075 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 09:36:36:076 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-05 09:36:36:077 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-05 09:36:36:078 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 09:36:36:079 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-05 09:36:36:083 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 09:36:36:087 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-05 09:36:36:087 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 09:36:36:160 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 09:36:36:160 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 09:36:36:162 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-05 09:36:36:208 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-05 09:36:36:231 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 09:36:37:939 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-05 09:36:38:020 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-05 09:36:38:021 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-05 09:38:08:168 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-05 09:38:09:341 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-05 09:38:09:968 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-05 09:38:10:236 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-05 09:38:11:087 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-05 09:38:11:089 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-05 09:38:13:865 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_27"}
25-09-05 09:38:13:893 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 09:38:18:367 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "2be47fe6-cd1d-4f30-93d4-29086a339a3d", :code 0, :msg "模板生成成功"}
25-09-05 09:38:18:368 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-2be47fe6-cd1d-4f30-93d4-29086a339a3d 中添加消息 {:ProcessId "2be47fe6-cd1d-4f30-93d4-29086a339a3d", :code 0, :msg "模板生成成功"}
25-09-05 09:38:18:369 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "2be47fe6-cd1d-4f30-93d4-29086a339a3d", :code 0, :msg "模板生成成功"}
25-09-05 09:38:19:745 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【打开项目默认执行动作】"}
25-09-05 09:38:19:748 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 09:38:21:117 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-05 09:38:21:119 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 09:38:24:538 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【联机】"}
25-09-05 09:38:24:541 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 09:38:37:727 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-05 09:38:37:733 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-05 09:38:37:886 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "7bdc8214-7c18-4e06-a80c-32decda78587", :Result false}
25-09-05 09:38:37:887 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-7bdc8214-7c18-4e06-a80c-32decda78587 中添加消息 {:ProcessId "project_27", :ScriptId "7bdc8214-7c18-4e06-a80c-32decda78587", :Result false}
25-09-05 09:38:37:887 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "7bdc8214-7c18-4e06-a80c-32decda78587", :Result false}
25-09-05 09:38:40:867 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "dac97e41-cf3c-438c-be01-0c1055a65014", :Result true}
25-09-05 09:38:40:868 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-dac97e41-cf3c-438c-be01-0c1055a65014 中添加消息 {:ProcessId "project_27", :ScriptId "dac97e41-cf3c-438c-be01-0c1055a65014", :Result true}
25-09-05 09:38:40:869 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "dac97e41-cf3c-438c-be01-0c1055a65014", :Result true}
25-09-05 09:38:45:910 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【终止裂纹长度检查】"}
25-09-05 09:38:45:912 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "running"} 
 =============================================================

25-09-05 09:38:45:984 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d", :MsgBody {:Cmd "abort", :InstCode "sample_14785d372", :ActionID "d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-05 09:38:45:986 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 09:38:45:987 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-05 09:38:46:035 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_27", :ProcessID "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-05 09:38:46:038 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【横梁停止】"}
25-09-05 09:38:46:039 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-da399466-da97-449d-b998-0d7a0823cdd0", :State "running"} 
 =============================================================

25-09-05 09:38:46:130 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "b95bdcc8-6724-4148-85dc-20827b7dae40", :Result true}
25-09-05 09:38:46:131 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b95bdcc8-6724-4148-85dc-20827b7dae40 中添加消息 {:ProcessId "project_27", :ScriptId "b95bdcc8-6724-4148-85dc-20827b7dae40", :Result true}
25-09-05 09:38:46:132 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "b95bdcc8-6724-4148-85dc-20827b7dae40", :Result true}
25-09-05 09:38:48:593 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【启动】"}
25-09-05 09:38:48:595 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "running"} 
 =============================================================

25-09-05 09:38:48:666 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-05 09:38:48:668 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-679f70fa-d870-40d9-b121-c759b28044ed", :State "finished"} 
 =============================================================

25-09-05 09:38:50:420 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【裂纹检查新版流程图】"}
25-09-05 09:38:50:423 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-05 09:38:50:502 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "e5981487-a9c8-4253-be8a-165a3f7bbc8d", :Result true}
25-09-05 09:38:50:502 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-e5981487-a9c8-4253-be8a-165a3f7bbc8d 中添加消息 {:ProcessId "project_27", :ScriptId "e5981487-a9c8-4253-be8a-165a3f7bbc8d", :Result true}
25-09-05 09:38:50:503 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "e5981487-a9c8-4253-be8a-165a3f7bbc8d", :Result true}
25-09-05 09:38:50:910 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_27", :ScriptId "432e0d4d-4833-4ff5-9c51-a95945ca430c", :Result true}
25-09-05 09:38:50:910 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-432e0d4d-4833-4ff5-9c51-a95945ca430c 中添加消息 {:ProcessId "project_27", :ScriptId "432e0d4d-4833-4ff5-9c51-a95945ca430c", :Result true}
25-09-05 09:38:50:912 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_27", :ScriptId "432e0d4d-4833-4ff5-9c51-a95945ca430c", :Result true}
25-09-05 09:38:58:925 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【横梁停止】"}
25-09-05 09:38:58:927 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-da399466-da97-449d-b998-0d7a0823cdd0", :State "finished"} 
 =============================================================

25-09-05 09:38:59:119 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【终止裂纹长度检查】"}
25-09-05 09:38:59:121 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "finished"} 
 =============================================================

25-09-05 09:39:30:868 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 09:39:30:870 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-05 09:54:29:190 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 09:54:29:191 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 09:54:29:244 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "开始流程【关闭项目重置参数】"}
25-09-05 09:54:29:245 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 09:54:29:391 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 27, :user_id 1}
25-09-05 09:54:29:425 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 09:54:29:426 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 09:54:29:523 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 09:54:29:553 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 09:54:29:554 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_27】项目"}
25-09-05 09:54:29:554 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-05 09:54:29:555 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 09:54:29:559 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【打开项目默认执行动作】"}
25-09-05 09:54:29:562 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【联机】"}
25-09-05 09:54:29:564 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_27", :ProcessId "project_27-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 09:54:29:634 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 09:54:29:636 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 09:54:29:637 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "保存了项目"}
25-09-05 09:54:29:682 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27_copy.db 成功
25-09-05 09:54:29:707 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 09:54:29:725 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 09:54:29:727 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【终止裂纹长度检查】"}
25-09-05 09:54:29:731 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【横梁停止】"}
25-09-05 09:54:29:735 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【启动】"}
25-09-05 09:54:29:737 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 09:54:29:739 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 27, :project_name "断裂力学k1c试验项目0901", :content "结束流程【关闭项目重置参数】"}
25-09-05 09:54:31:452 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 D:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1756707875343_project_27.db 成功
25-09-05 09:54:31:544 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_27 应用性能优化配置
25-09-05 09:54:31:546 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_27 项目库连接
25-09-05 09:56:00:412 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 导入模板或项目"}
25-09-05 09:56:00:417 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\temp_directory_19917967323\instance 成功
25-09-05 09:56:00:418 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\temp_directory_19917967323 成功
25-09-05 09:56:01:571 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 09:56:01:572 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 09:56:01:778 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 09:56:01:801 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 09:56:03:329 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "20fc4212-9a3c-4c77-9a6f-703a3e967ca0", :code 0, :msg "模板生成成功"}
25-09-05 09:56:03:329 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-20fc4212-9a3c-4c77-9a6f-703a3e967ca0 中添加消息 {:ProcessId "20fc4212-9a3c-4c77-9a6f-703a3e967ca0", :code 0, :msg "模板生成成功"}
25-09-05 09:56:03:330 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "20fc4212-9a3c-4c77-9a6f-703a3e967ca0", :code 0, :msg "模板生成成功"}
25-09-05 09:56:04:557 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/sample-inst/select 
 - 参数: 
 {:ClassName "project_29", :CurrentInstCode "sample_15419d437", :SelectedInstCodes []} 
 =============================================================

25-09-05 09:56:21:564 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 09:56:21:565 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 09:56:21:719 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 09:56:21:720 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 10:09:48:596 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "daq-0ac09652-82d1-4130-a028-2e081713c233", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_29", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "RESTART", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_lwjc_no1_down", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value true, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck true}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-05 10:09:50:582 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "daq-43bef327-78fb-4784-9a26-7efed0ab4432", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_29", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "RESTART", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_lwjc_no2_down", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value true, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck true}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-05 10:10:15:510 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "daq-0ac09652-82d1-4130-a028-2e081713c233", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_29", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "CONTINUE", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_lwjc_no1_down", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value true, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck true}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-05 10:10:22:157 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "daq-43bef327-78fb-4784-9a26-7efed0ab4432", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_29", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "CONTINUE", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_lwjc_no2_down", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value true, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck true}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-05 10:10:27:151 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "daq-32891850-ce61-48d8-98cd-20634cf737c7", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_29", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "CONTINUE", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_lwjc_no3_down", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value false, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-05 10:10:31:888 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "daq-32891850-ce61-48d8-98cd-20634cf737c7", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_29", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "RESTART", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_lwjc_no3_down", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value false, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-05 10:10:40:817 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "daq-32891850-ce61-48d8-98cd-20634cf737c7", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_29", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "RESTART", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_lwjc_no3_down", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value true, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck true}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-05 10:10:42:010 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "daq-32891850-ce61-48d8-98cd-20634cf737c7", :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee", :TemplateName "project_29", :InputVars {:control_input_move_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_move_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferMode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferMode", :Value "RESTART", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_timeResetZero {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_timeResetZero", :Value nil, :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_bufferCode {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_bufferCode", :Value "input_lwjc_no3_down", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_keepStream {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_keepStream", :Value false, :IsConstant false, :Type "Boolean", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_load_interval {:Unit "f7d1bcc0-cec4-4118-bb30-36b2995893d4", :Dimension "56019f36-d734-40c0-8872-73b60304f80a", :Mode "signal_load", :InstName "", :ContactInputCode "", :Code "control_input_load_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_saveDB {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_saveDB", :Value true, :IsConstant false, :Type "Boolean", :ValueType "", :IsOverall false, :IsCheck true}, :control_input_strain_interval {:Unit "e655c1f0-26c0-41aa-9d1d-25c94c059d2e", :Dimension "6420a172-7a93-45c6-a8da-7ccb275a1aad", :Mode "signal_ext", :InstName "", :ContactInputCode "", :Code "control_input_strain_interval", :Value 0, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_time_interval {:Unit "f9b2c0be-a829-4072-878e-f5f592308f79", :Dimension "54891d75-4375-4253-8a5b-068423501a0a", :Mode "signal_time", :InstName "", :ContactInputCode "", :Code "control_input_time_interval", :Value 0.001, :IsConstant false, :Type "Number", :ValueType "string", :IsOverall false, :IsCheck true}}}, :ActionId "d2a28ac5-6be5-4a4c-806f-424fc39131ee"} 
 =============================================================

25-09-05 10:12:52:056 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 保存了【裂纹检查新版流程图】动作"}
25-09-05 11:12:07:511 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e", :ActionId "679f70fa-d870-40d9-b121-c759b28044ed", :TemplateName "project_29", :InputVars {:control_input_action {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_action", :Value "stationOn", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_axle {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_axle", :Value ["Servo" 0], :IsConstant false, :Type "Select", :ValueType "object[]", :IsOverall false, :IsCheck false}}}, :ActionId "679f70fa-d870-40d9-b121-c759b28044ed"} 
 =============================================================

25-09-05 11:17:20:496 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 11:17:20:500 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 11:17:20:547 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目重置参数】"}
25-09-05 11:17:20:548 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 11:17:20:640 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 29, :user_id 1}
25-09-05 11:17:20:699 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 11:17:20:701 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 11:17:20:766 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 11:17:20:875 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 11:17:20:877 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 11:17:20:878 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 11:17:20:934 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 11:17:20:962 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 11:17:20:963 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 11:17:20:964 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_29】项目"}
25-09-05 11:17:20:964 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 11:17:20:967 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 11:17:20:968 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 11:17:20:972 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 11:17:20:976 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 11:17:20:978 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 11:17:22:830 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29.db 成功
25-09-05 11:17:22:916 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 11:17:22:917 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 11:34:06:470 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-05 11:34:06:803 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-05 11:34:06:939 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-05 11:34:06:986 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-05 11:34:12:071 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 11:34:12:099 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 11:34:17:666 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "192c51f6-b6f3-4c9e-9396-78bf6f5e8baa", :code 0, :msg "模板生成成功"}
25-09-05 11:34:17:667 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-192c51f6-b6f3-4c9e-9396-78bf6f5e8baa 中添加消息 {:ProcessId "192c51f6-b6f3-4c9e-9396-78bf6f5e8baa", :code 0, :msg "模板生成成功"}
25-09-05 11:34:17:668 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "192c51f6-b6f3-4c9e-9396-78bf6f5e8baa", :code 0, :msg "模板生成成功"}
25-09-05 11:34:19:048 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 11:34:19:050 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 11:34:20:039 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 11:34:20:041 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 11:34:30:175 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 11:34:30:177 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 11:35:51:954 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 11:35:51:955 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 11:35:52:006 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目重置参数】"}
25-09-05 11:35:52:007 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 11:35:52:118 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 29, :user_id 1}
25-09-05 11:35:52:130 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 11:35:52:133 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 11:35:52:248 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 11:35:52:271 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 11:35:52:271 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_29】项目"}
25-09-05 11:35:52:273 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 11:35:52:276 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 11:35:52:277 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 11:35:52:282 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 11:35:52:283 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 11:35:52:363 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 11:35:52:364 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 11:35:52:365 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 11:35:52:399 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 11:35:52:403 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 11:35:52:420 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 11:35:52:443 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 11:35:54:097 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29.db 成功
25-09-05 11:35:54:179 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 11:35:54:180 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 11:40:48:035 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-05 11:40:48:290 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-05 11:40:48:371 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-05 11:40:48:403 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-05 11:40:48:907 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-05 11:40:48:910 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-05 11:40:55:107 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 11:40:55:135 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 11:41:00:372 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "50a3c834-825d-4969-bb3f-0af2aa1441aa", :code 0, :msg "模板生成成功"}
25-09-05 11:41:00:373 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-50a3c834-825d-4969-bb3f-0af2aa1441aa 中添加消息 {:ProcessId "50a3c834-825d-4969-bb3f-0af2aa1441aa", :code 0, :msg "模板生成成功"}
25-09-05 11:41:00:374 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "50a3c834-825d-4969-bb3f-0af2aa1441aa", :code 0, :msg "模板生成成功"}
25-09-05 11:41:01:821 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 11:41:01:822 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 11:41:03:062 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 11:41:03:064 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 11:42:39:746 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 11:42:39:749 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 11:43:31:340 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-05 11:43:31:347 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 11:43:31:349 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 11:43:31:597 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 11:43:31:599 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 11:43:31:742 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :SubTaskID "online-b1e962d1-dc19-43b3-8067-c7c00caade1a", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "e3c8ca6f-23bf-4fc6-8495-da6d17924867"}}
25-09-05 11:43:31:746 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【Online触发】"}
25-09-05 11:43:31:747 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "running"} 
 =============================================================

25-09-05 11:43:32:054 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【Online触发】"}
25-09-05 11:43:32:057 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "finished"} 
 =============================================================

25-09-05 11:44:03:149 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-05 11:44:03:154 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 11:44:03:155 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 11:44:03:398 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 11:44:03:400 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 11:44:03:535 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :SubTaskID "online-b1e962d1-dc19-43b3-8067-c7c00caade1a", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "e3c8ca6f-23bf-4fc6-8495-da6d17924867"}}
25-09-05 11:44:03:537 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【Online触发】"}
25-09-05 11:44:03:537 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "running"} 
 =============================================================

25-09-05 11:44:11:511 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【Online触发】"}
25-09-05 11:44:11:513 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "finished"} 
 =============================================================

25-09-05 11:44:37:415 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 11:44:37:417 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 11:44:37:523 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目重置参数】"}
25-09-05 11:44:37:524 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 11:44:37:601 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 29, :user_id 1}
25-09-05 11:44:37:610 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 11:44:37:611 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 11:44:37:720 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 11:44:37:721 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_29】项目"}
25-09-05 11:44:37:722 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 11:44:37:723 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 11:44:37:725 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 11:44:37:729 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 11:44:37:730 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 11:44:37:731 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 11:44:37:837 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 11:44:37:839 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 11:44:37:840 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 11:44:37:865 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【Online触发】"}
25-09-05 11:44:37:869 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 11:44:37:873 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 11:44:37:889 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 11:44:37:913 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 11:44:39:560 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29.db 成功
25-09-05 11:44:39:652 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 11:44:39:653 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 11:44:43:267 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 11:44:43:295 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 11:44:44:762 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "36e260fe-b98d-4211-90f2-37f398758588", :code 0, :msg "模板生成成功"}
25-09-05 11:44:44:762 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-36e260fe-b98d-4211-90f2-37f398758588 中添加消息 {:ProcessId "36e260fe-b98d-4211-90f2-37f398758588", :code 0, :msg "模板生成成功"}
25-09-05 11:44:44:765 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "36e260fe-b98d-4211-90f2-37f398758588", :code 0, :msg "模板生成成功"}
25-09-05 11:44:45:935 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 11:44:45:936 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 11:44:46:140 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 11:44:46:141 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 11:44:51:686 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 11:44:51:688 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 11:44:52:300 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :SubTaskID "online-b1e962d1-dc19-43b3-8067-c7c00caade1a", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "e3c8ca6f-23bf-4fc6-8495-da6d17924867"}}
25-09-05 11:44:52:302 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【Online触发】"}
25-09-05 11:44:52:302 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "running"} 
 =============================================================

25-09-05 11:45:06:757 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【Online触发】"}
25-09-05 11:45:06:758 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "finished"} 
 =============================================================

25-09-05 11:51:17:227 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【预制裂纹流程图】"}
25-09-05 11:51:17:231 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16", :State "running"} 
 =============================================================

25-09-05 11:51:17:379 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "0be232e1-35cf-4654-a562-2ddd0d7a4801", :Result false}
25-09-05 11:51:17:380 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-0be232e1-35cf-4654-a562-2ddd0d7a4801 中添加消息 {:ProcessId "project_29", :ScriptId "0be232e1-35cf-4654-a562-2ddd0d7a4801", :Result false}
25-09-05 11:51:17:380 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "0be232e1-35cf-4654-a562-2ddd0d7a4801", :Result false}
25-09-05 11:51:18:839 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【预制裂纹流程图】"}
25-09-05 11:51:18:841 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16", :State "finished"} 
 =============================================================

25-09-05 11:53:53:473 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "3c271e3f-d705-443a-9dfa-1f5764d058fa", :Result "(69,46): error CS0103: 当前上下文中不存在名称“deviceId”\r\n\r\n 1.  using System;\r\n 2.  using System.Text;\r\n 3.  using System.Reflection;\r\n 4.  using System.IO;\r\n 5.  using System.Net;\r\n 6.  using System.Net.Http;\r\n 7.  using System.Collections;\r\n 8.  using System.Collections.Generic;\r\n 9.  using System.Collections.Concurrent;\r\n10.  using System.Text.RegularExpressions;\r\n11.  using System.Threading.Tasks;\r\n12.  using System.Linq;\r\n13.  using SignalExample;\r\n14.  using System.Reactive.Linq;\r\n15.  using System.Reactive.Subjects;\r\n16.  using Scripting;\r\n17.  using ScriptEngine.SampleInst;\r\n18.  using ScriptEngine.InputVar;\r\n19.  using ScriptEngine.InputVar.InputVars;\r\n20.  using ScriptEngine.CcssFunc;\r\n21.  using System.Text.Json;\r\n22.  using ScriptEngine.InstantiatedTemplate.SignalVar;\r\n23.  using Buffers;\r\n24.  using SoftPlantTesion;\r\n25.  using CreepCalc;\r\n26.  using System.Diagnostics;\r\n27.  using MQ;\r\n28.  using ScriptEngine.InstantiatedTemplate.Hardware.Base;\r\n29.  using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;\r\n30.  using SubTaskUtils;\r\n31.  \r\n32.  namespace __ScriptExecution {\r\n33.  \r\n34.  public class __yxypy1b7\r\n35.  { \r\n36.  \r\n37.  \r\n38.  public System.Boolean _compileMethods (ITemplate Model, List<Dictionary<string, double[]>> cycles = null)\r\n39.  {\r\n40.  \r\n41.  Model.RESTART(\"input_real_data_buffer\");\r\n42.  Model.RESTART(\"input_fgz_ck\");\r\n43.  Model.RESTART(\"input_periodic_generation\");\r\n44.  Model.RESTART(\"input_k1cBuffer\");\r\n45.  try {\r\n46.      /**\r\n47.       * 解析控制器状态\r\n48.       */\r\n49.      if(Model.GetVarByName<BooleanInputVar>(\"input_ifopendivice\").Value) \r\n50.      {  \r\n51.          var inputBuffer = Model.GetVarByName<BufferInputVar>(\"input_header_buffer\");\r\n52.          int insignalsBufferLen = inputBuffer.GetTail(\"signal_InSignals\");\r\n53.          \r\n54.          inputBuffer.Value.TryGetValue(\"signal_InSignals\",out var insignalsBuffer);\r\n55.  \r\n56.          // 检查所有buffer长度是否有效\r\n57.          if(insignalsBufferLen == -1 )\r\n58.          {\r\n59.              return default;\r\n60.          }\r\n61.          \r\n62.          int InSignals = (int)insignalsBuffer.GetValue();\r\n63.  \r\n64.          bool stationStatus = (InSignals & (1 << 4)) == 0;   // 第3位：Station启动制动\r\n65.          if(stationStatus!=Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value){\r\n66.            Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = stationStatus;\r\n67.          }\r\n68.      }\r\n69.      int ret1 = Model.station.Ccss_DriveReady(deviceId);\r\n70.      if (ret1 == 0) { \r\n71.        Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = true;\r\n72.      } else {\r\n73.        /* 未就绪 */ \r\n74.        Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = false;\r\n75.        return false;\r\n76.      }\r\n77.  \r\n78.  }\r\n79.  catch(Exception ex) {\r\n80.      FuncLibs.Logger.Error(\"ex \" + ex.ToString());\r\n81.      FuncLibs.Logger.Error(\"ex stack track\" + ex.StackTrace);\r\n82.  }\r\n83.  \r\n84.  return false;\r\n85.  }\r\n86.  \r\n87.  } \r\n88.  }\r\n"}
25-09-05 11:53:53:475 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-3c271e3f-d705-443a-9dfa-1f5764d058fa 中添加消息 {:ProcessId "project_29", :ScriptId "3c271e3f-d705-443a-9dfa-1f5764d058fa", :Result "(69,46): error CS0103: 当前上下文中不存在名称“deviceId”\r\n\r\n 1.  using System;\r\n 2.  using System.Text;\r\n 3.  using System.Reflection;\r\n 4.  using System.IO;\r\n 5.  using System.Net;\r\n 6.  using System.Net.Http;\r\n 7.  using System.Collections;\r\n 8.  using System.Collections.Generic;\r\n 9.  using System.Collections.Concurrent;\r\n10.  using System.Text.RegularExpressions;\r\n11.  using System.Threading.Tasks;\r\n12.  using System.Linq;\r\n13.  using SignalExample;\r\n14.  using System.Reactive.Linq;\r\n15.  using System.Reactive.Subjects;\r\n16.  using Scripting;\r\n17.  using ScriptEngine.SampleInst;\r\n18.  using ScriptEngine.InputVar;\r\n19.  using ScriptEngine.InputVar.InputVars;\r\n20.  using ScriptEngine.CcssFunc;\r\n21.  using System.Text.Json;\r\n22.  using ScriptEngine.InstantiatedTemplate.SignalVar;\r\n23.  using Buffers;\r\n24.  using SoftPlantTesion;\r\n25.  using CreepCalc;\r\n26.  using System.Diagnostics;\r\n27.  using MQ;\r\n28.  using ScriptEngine.InstantiatedTemplate.Hardware.Base;\r\n29.  using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;\r\n30.  using SubTaskUtils;\r\n31.  \r\n32.  namespace __ScriptExecution {\r\n33.  \r\n34.  public class __yxypy1b7\r\n35.  { \r\n36.  \r\n37.  \r\n38.  public System.Boolean _compileMethods (ITemplate Model, List<Dictionary<string, double[]>> cycles = null)\r\n39.  {\r\n40.  \r\n41.  Model.RESTART(\"input_real_data_buffer\");\r\n42.  Model.RESTART(\"input_fgz_ck\");\r\n43.  Model.RESTART(\"input_periodic_generation\");\r\n44.  Model.RESTART(\"input_k1cBuffer\");\r\n45.  try {\r\n46.      /**\r\n47.       * 解析控制器状态\r\n48.       */\r\n49.      if(Model.GetVarByName<BooleanInputVar>(\"input_ifopendivice\").Value) \r\n50.      {  \r\n51.          var inputBuffer = Model.GetVarByName<BufferInputVar>(\"input_header_buffer\");\r\n52.          int insignalsBufferLen = inputBuffer.GetTail(\"signal_InSignals\");\r\n53.          \r\n54.          inputBuffer.Value.TryGetValue(\"signal_InSignals\",out var insignalsBuffer);\r\n55.  \r\n56.          // 检查所有buffer长度是否有效\r\n57.          if(insignalsBufferLen == -1 )\r\n58.          {\r\n59.              return default;\r\n60.          }\r\n61.          \r\n62.          int InSignals = (int)insignalsBuffer.GetValue();\r\n63.  \r\n64.          bool stationStatus = (InSignals & (1 << 4)) == 0;   // 第3位：Station启动制动\r\n65.          if(stationStatus!=Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value){\r\n66.            Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = stationStatus;\r\n67.          }\r\n68.      }\r\n69.      int ret1 = Model.station.Ccss_DriveReady(deviceId);\r\n70.      if (ret1 == 0) { \r\n71.        Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = true;\r\n72.      } else {\r\n73.        /* 未就绪 */ \r\n74.        Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = false;\r\n75.        return false;\r\n76.      }\r\n77.  \r\n78.  }\r\n79.  catch(Exception ex) {\r\n80.      FuncLibs.Logger.Error(\"ex \" + ex.ToString());\r\n81.      FuncLibs.Logger.Error(\"ex stack track\" + ex.StackTrace);\r\n82.  }\r\n83.  \r\n84.  return false;\r\n85.  }\r\n86.  \r\n87.  } \r\n88.  }\r\n"}
25-09-05 11:53:53:476 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "3c271e3f-d705-443a-9dfa-1f5764d058fa", :Result "(69,46): error CS0103: 当前上下文中不存在名称“deviceId”\r\n\r\n 1.  using System;\r\n 2.  using System.Text;\r\n 3.  using System.Reflection;\r\n 4.  using System.IO;\r\n 5.  using System.Net;\r\n 6.  using System.Net.Http;\r\n 7.  using System.Collections;\r\n 8.  using System.Collections.Generic;\r\n 9.  using System.Collections.Concurrent;\r\n10.  using System.Text.RegularExpressions;\r\n11.  using System.Threading.Tasks;\r\n12.  using System.Linq;\r\n13.  using SignalExample;\r\n14.  using System.Reactive.Linq;\r\n15.  using System.Reactive.Subjects;\r\n16.  using Scripting;\r\n17.  using ScriptEngine.SampleInst;\r\n18.  using ScriptEngine.InputVar;\r\n19.  using ScriptEngine.InputVar.InputVars;\r\n20.  using ScriptEngine.CcssFunc;\r\n21.  using System.Text.Json;\r\n22.  using ScriptEngine.InstantiatedTemplate.SignalVar;\r\n23.  using Buffers;\r\n24.  using SoftPlantTesion;\r\n25.  using CreepCalc;\r\n26.  using System.Diagnostics;\r\n27.  using MQ;\r\n28.  using ScriptEngine.InstantiatedTemplate.Hardware.Base;\r\n29.  using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;\r\n30.  using SubTaskUtils;\r\n31.  \r\n32.  namespace __ScriptExecution {\r\n33.  \r\n34.  public class __yxypy1b7\r\n35.  { \r\n36.  \r\n37.  \r\n38.  public System.Boolean _compileMethods (ITemplate Model, List<Dictionary<string, double[]>> cycles = null)\r\n39.  {\r\n40.  \r\n41.  Model.RESTART(\"input_real_data_buffer\");\r\n42.  Model.RESTART(\"input_fgz_ck\");\r\n43.  Model.RESTART(\"input_periodic_generation\");\r\n44.  Model.RESTART(\"input_k1cBuffer\");\r\n45.  try {\r\n46.      /**\r\n47.       * 解析控制器状态\r\n48.       */\r\n49.      if(Model.GetVarByName<BooleanInputVar>(\"input_ifopendivice\").Value) \r\n50.      {  \r\n51.          var inputBuffer = Model.GetVarByName<BufferInputVar>(\"input_header_buffer\");\r\n52.          int insignalsBufferLen = inputBuffer.GetTail(\"signal_InSignals\");\r\n53.          \r\n54.          inputBuffer.Value.TryGetValue(\"signal_InSignals\",out var insignalsBuffer);\r\n55.  \r\n56.          // 检查所有buffer长度是否有效\r\n57.          if(insignalsBufferLen == -1 )\r\n58.          {\r\n59.              return default;\r\n60.          }\r\n61.          \r\n62.          int InSignals = (int)insignalsBuffer.GetValue();\r\n63.  \r\n64.          bool stationStatus = (InSignals & (1 << 4)) == 0;   // 第3位：Station启动制动\r\n65.          if(stationStatus!=Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value){\r\n66.            Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = stationStatus;\r\n67.          }\r\n68.      }\r\n69.      int ret1 = Model.station.Ccss_DriveReady(deviceId);\r\n70.      if (ret1 == 0) { \r\n71.        Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = true;\r\n72.      } else {\r\n73.        /* 未就绪 */ \r\n74.        Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = false;\r\n75.        return false;\r\n76.      }\r\n77.  \r\n78.  }\r\n79.  catch(Exception ex) {\r\n80.      FuncLibs.Logger.Error(\"ex \" + ex.ToString());\r\n81.      FuncLibs.Logger.Error(\"ex stack track\" + ex.StackTrace);\r\n82.  }\r\n83.  \r\n84.  return false;\r\n85.  }\r\n86.  \r\n87.  } \r\n88.  }\r\n"}
25-09-05 11:54:07:731 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "67998ad3-7a4d-4738-bad0-fedfcf650229", :Result "Success"}
25-09-05 11:54:07:732 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-67998ad3-7a4d-4738-bad0-fedfcf650229 中添加消息 {:ProcessId "project_29", :ScriptId "67998ad3-7a4d-4738-bad0-fedfcf650229", :Result "Success"}
25-09-05 11:54:07:732 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "67998ad3-7a4d-4738-bad0-fedfcf650229", :Result "Success"}
25-09-05 11:54:08:966 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  PUT  调用外部接口:  http://localhost:5002/template/subtask 
 - 参数: 
 {:ClassName "project_29", :Subtask {:SubTaskId "waitEvent-50c905cf-82b5-4852-8780-7419cd036bfb", :ActionId "5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :TemplateName "project_29", :InputVars {:control_input_interval {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_interval", :Value "500", :IsConstant false, :Type "Select", :ValueType "string", :IsOverall false, :IsCheck false}, :control_input_script {:Unit "", :Dimension "", :Mode "无", :InstName "", :ContactInputCode "", :Code "control_input_script", :Value "Model.RESTART(\"input_real_data_buffer\");\nModel.RESTART(\"input_fgz_ck\");\nModel.RESTART(\"input_periodic_generation\");\nModel.RESTART(\"input_k1cBuffer\");\ntry {\n    /**\n     * 解析控制器状态\n     */\n    if(Model.GetVarByName<BooleanInputVar>(\"input_ifopendivice\").Value) \n    {  \n        var inputBuffer = Model.GetVarByName<BufferInputVar>(\"input_header_buffer\");\n        int insignalsBufferLen = inputBuffer.GetTail(\"signal_InSignals\");\n        \n        inputBuffer.Value.TryGetValue(\"signal_InSignals\",out var insignalsBuffer);\n\n        // 检查所有buffer长度是否有效\n        if(insignalsBufferLen == -1 )\n        {\n            return default;\n        }\n        \n        int InSignals = (int)insignalsBuffer.GetValue();\n\n        // bool stationStatus = (InSignals & (1 << 4)) == 0;   // 第3位：Station启动制动\n        // if(stationStatus!=Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value){\n        //   Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = stationStatus;\n        // }\n    }\n    int ret1 = Model.station.Ccss_DriveReady(0);\n    if (ret1 == 0) { \n      Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = true;\n    } else {\n      /* 未就绪 */ \n      Model.GetVarByName<BooleanInputVar>(\"input_ifstationon\").Value = false;\n      return false;\n    }\n\n}\ncatch(Exception ex) {\n    FuncLibs.Logger.Error(\"ex \" + ex.ToString());\n    FuncLibs.Logger.Error(\"ex stack track\" + ex.StackTrace);\n}\n\nreturn false;", :IsConstant false, :Type "Text", :ValueType "", :IsOverall false, :IsCheck false}}}, :ActionId "5a5f09ef-c362-437a-9a8a-8e1d86e455d1"} 
 =============================================================

25-09-05 11:54:10:683 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 保存了【联机】动作"}
25-09-05 11:54:10:772 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 执行了【abort】操作"}
25-09-05 11:54:10:776 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 11:54:10:777 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 11:54:11:669 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 保存了【联机】动作"}
25-09-05 11:54:11:787 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 11:54:11:788 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 11:54:11:936 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :SubTaskID "online-b1e962d1-dc19-43b3-8067-c7c00caade1a", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "e3c8ca6f-23bf-4fc6-8495-da6d17924867"}}
25-09-05 11:54:11:939 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【Online触发】"}
25-09-05 11:54:11:940 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "running"} 
 =============================================================

25-09-05 11:54:12:116 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【Online触发】"}
25-09-05 11:54:12:119 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "finished"} 
 =============================================================

25-09-05 11:54:15:275 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 保存了【联机】动作"}
25-09-05 11:54:17:659 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 11:54:17:767 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 11:54:17:768 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 11:54:17:768 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 11:54:17:814 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 11:54:46:074 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "【超级管理员】admin: 另存为了【断裂力学k1c试验项目0905】项目"}
25-09-05 11:54:48:942 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 11:54:48:943 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 11:54:48:947 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 11:54:48:948 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 11:54:48:991 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目重置参数】"}
25-09-05 11:54:48:992 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 11:54:49:033 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 29, :user_id 1}
25-09-05 11:54:49:040 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 11:54:49:041 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_29】项目"}
25-09-05 11:54:49:046 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 11:54:49:052 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 11:54:49:053 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 11:54:49:064 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 11:54:49:069 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 11:54:49:147 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 11:54:49:198 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【Online触发】"}
25-09-05 11:54:49:204 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【预制裂纹流程图】"}
25-09-05 11:54:49:210 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 11:54:49:211 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 11:54:49:256 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 11:54:49:258 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 11:54:49:259 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 11:54:49:277 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 11:54:49:278 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 11:54:49:311 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 11:54:49:340 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 11:54:51:067 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29.db 成功
25-09-05 11:54:51:147 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 11:54:51:148 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 11:54:57:870 DESKTOP-3BSREDP INFO [clj-backend.common.task-manager:13] - Created task: task-1757044497867-263
25-09-05 11:54:57:887 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_30 应用性能优化配置
25-09-05 11:54:57:888 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_30 项目库连接
25-09-05 11:54:57:962 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 30, :project_name "断裂力学k1c试验项目0905", :content "【超级管理员】admin: 导出模板或项目"}
25-09-05 11:54:58:787 DESKTOP-3BSREDP INFO [clj-backend.modules.file.service:86] - Finished writing zip file for task-1757044497867-263 to C:\Users\<USER>\Desktop\断裂力学k1c试验项目0905.pexport
25-09-05 11:59:58:798 DESKTOP-3BSREDP INFO [clj-backend.common.task-manager:31] - Cleaned up and removed task: task-1757044497867-263
25-09-05 12:43:37:229 DESKTOP-3BSREDP INFO [clj-backend.modules.inspection.inspection-record-service:191] - 新生成点检记录:0 条
25-09-05 13:13:37:214 DESKTOP-3BSREDP INFO [clj-backend.modules.inspection.inspection-record-service:236] - 今天有0项设备未进行点检！
25-09-05 13:13:37:216 DESKTOP-3BSREDP INFO [clj-backend.modules.inspection.inspection-record-service:237] - 近期有0项设备未进行点检！
25-09-05 13:39:44:095 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 13:39:44:124 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 13:39:45:436 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "af72b6f7-5be9-4623-9461-e9e0daec2916", :code 0, :msg "模板生成成功"}
25-09-05 13:39:45:438 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-af72b6f7-5be9-4623-9461-e9e0daec2916 中添加消息 {:ProcessId "af72b6f7-5be9-4623-9461-e9e0daec2916", :code 0, :msg "模板生成成功"}
25-09-05 13:39:45:439 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "af72b6f7-5be9-4623-9461-e9e0daec2916", :code 0, :msg "模板生成成功"}
25-09-05 13:39:46:880 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 13:39:46:882 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 13:39:47:103 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 13:39:47:104 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 13:39:52:636 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 13:39:52:638 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 13:39:52:858 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :SubTaskID "online-b1e962d1-dc19-43b3-8067-c7c00caade1a", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "e3c8ca6f-23bf-4fc6-8495-da6d17924867"}}
25-09-05 13:39:52:861 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【Online触发】"}
25-09-05 13:39:52:861 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "running"} 
 =============================================================

25-09-05 13:40:05:739 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【Online触发】"}
25-09-05 13:40:05:741 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-e3c8ca6f-23bf-4fc6-8495-da6d17924867", :State "finished"} 
 =============================================================

25-09-05 13:41:08:187 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 13:41:08:188 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 13:41:08:238 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目重置参数】"}
25-09-05 13:41:08:239 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 13:41:08:311 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 29, :user_id 1}
25-09-05 13:41:08:321 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 13:41:08:322 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 13:41:08:407 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 13:41:08:408 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_29】项目"}
25-09-05 13:41:08:409 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 13:41:08:411 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 13:41:08:412 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 13:41:08:416 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 13:41:08:419 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 13:41:08:441 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 13:41:08:546 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 13:41:08:548 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 13:41:08:549 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 13:41:08:606 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 13:41:08:629 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【Online触发】"}
25-09-05 13:41:08:631 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 13:41:08:634 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 13:41:08:635 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 13:41:10:284 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29.db 成功
25-09-05 13:41:10:364 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 13:41:10:364 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 13:41:39:042 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-05 13:41:39:331 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-05 13:41:39:414 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-05 13:41:39:447 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-05 13:41:42:770 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 13:41:42:800 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 13:41:47:917 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "703955db-5776-43f5-8cfe-3ec9f6c4e0ac", :code 0, :msg "模板生成成功"}
25-09-05 13:41:47:917 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-703955db-5776-43f5-8cfe-3ec9f6c4e0ac 中添加消息 {:ProcessId "703955db-5776-43f5-8cfe-3ec9f6c4e0ac", :code 0, :msg "模板生成成功"}
25-09-05 13:41:47:918 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "703955db-5776-43f5-8cfe-3ec9f6c4e0ac", :code 0, :msg "模板生成成功"}
25-09-05 13:41:49:132 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 13:41:49:134 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 13:41:50:086 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 13:41:50:087 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 13:41:54:758 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 13:41:54:760 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 13:43:19:185 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 13:43:19:187 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 13:43:19:233 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目重置参数】"}
25-09-05 13:43:19:235 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 13:43:19:306 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 29, :user_id 1}
25-09-05 13:43:19:315 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 13:43:19:317 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 13:43:19:447 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 13:43:19:460 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 13:43:19:461 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_29】项目"}
25-09-05 13:43:19:462 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 13:43:19:463 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 13:43:19:469 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 13:43:19:474 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 13:43:19:475 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 13:43:19:561 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 13:43:19:562 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 13:43:19:564 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 13:43:19:578 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 13:43:19:581 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 13:43:19:613 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 13:43:19:639 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 13:43:21:307 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29.db 成功
25-09-05 13:43:21:391 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 13:43:21:393 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 13:43:42:571 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-05 13:43:42:826 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-05 13:43:42:903 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-05 13:43:42:933 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-05 13:43:43:487 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-05 13:43:43:490 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-05 13:43:49:101 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 13:43:49:129 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 13:43:54:102 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "477a6b97-5664-4704-9ba6-5cf8b45c73d4", :code 0, :msg "模板生成成功"}
25-09-05 13:43:54:103 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-477a6b97-5664-4704-9ba6-5cf8b45c73d4 中添加消息 {:ProcessId "477a6b97-5664-4704-9ba6-5cf8b45c73d4", :code 0, :msg "模板生成成功"}
25-09-05 13:43:54:103 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "477a6b97-5664-4704-9ba6-5cf8b45c73d4", :code 0, :msg "模板生成成功"}
25-09-05 13:43:55:648 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 13:43:55:650 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 13:43:56:568 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 13:43:56:569 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 13:44:21:859 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 13:44:21:861 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 13:48:52:261 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 13:48:52:262 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 13:48:52:310 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目重置参数】"}
25-09-05 13:48:52:312 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 13:48:52:381 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 29, :user_id 1}
25-09-05 13:48:52:421 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 13:48:52:422 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 13:48:52:509 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 13:48:52:547 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 13:48:52:548 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_29】项目"}
25-09-05 13:48:52:549 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 13:48:52:551 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 13:48:52:553 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 13:48:52:559 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 13:48:52:560 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 13:48:52:620 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 13:48:52:621 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 13:48:52:623 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 13:48:52:674 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 13:48:52:700 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 13:48:52:781 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 13:48:52:786 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 13:48:54:472 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29.db 成功
25-09-05 13:48:54:554 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 13:48:54:556 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 13:49:43:337 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-05 13:49:43:587 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-05 13:49:43:673 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-05 13:49:43:704 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-05 13:49:47:547 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 13:49:47:578 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 13:49:53:133 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "d7e34ec2-cfcb-4159-83fe-25bc049bdbf6", :code 0, :msg "模板生成成功"}
25-09-05 13:49:53:134 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-d7e34ec2-cfcb-4159-83fe-25bc049bdbf6 中添加消息 {:ProcessId "d7e34ec2-cfcb-4159-83fe-25bc049bdbf6", :code 0, :msg "模板生成成功"}
25-09-05 13:49:53:135 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "d7e34ec2-cfcb-4159-83fe-25bc049bdbf6", :code 0, :msg "模板生成成功"}
25-09-05 13:49:54:308 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 13:49:54:310 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 13:49:55:084 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 13:49:55:085 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 13:50:03:235 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 13:50:03:236 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 13:50:27:137 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【启动】"}
25-09-05 13:50:27:139 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-679f70fa-d870-40d9-b121-c759b28044ed", :State "running"} 
 =============================================================

25-09-05 13:50:27:238 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【启动】"}
25-09-05 13:50:27:240 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-679f70fa-d870-40d9-b121-c759b28044ed", :State "finished"} 
 =============================================================

25-09-05 13:50:44:537 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【裂纹检查新版流程图】"}
25-09-05 13:50:44:539 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-05 13:50:44:646 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "4b95dd6a-e92b-4ae8-a5ab-749db318ba3b", :Result true}
25-09-05 13:50:44:646 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-4b95dd6a-e92b-4ae8-a5ab-749db318ba3b 中添加消息 {:ProcessId "project_29", :ScriptId "4b95dd6a-e92b-4ae8-a5ab-749db318ba3b", :Result true}
25-09-05 13:50:44:647 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "4b95dd6a-e92b-4ae8-a5ab-749db318ba3b", :Result true}
25-09-05 13:50:45:344 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "c94cf48d-ac5e-4fc8-a2b9-9d41bddf0b18", :Result true}
25-09-05 13:50:45:344 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-c94cf48d-ac5e-4fc8-a2b9-9d41bddf0b18 中添加消息 {:ProcessId "project_29", :ScriptId "c94cf48d-ac5e-4fc8-a2b9-9d41bddf0b18", :Result true}
25-09-05 13:50:45:345 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "c94cf48d-ac5e-4fc8-a2b9-9d41bddf0b18", :Result true}
25-09-05 13:53:04:125 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【终止裂纹长度检查】"}
25-09-05 13:53:04:128 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "running"} 
 =============================================================

25-09-05 13:53:04:193 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d", :MsgBody {:Cmd "abort", :InstCode "sample_15419d437", :ActionID "d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-05 13:53:04:195 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 13:53:04:196 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-05 13:53:04:240 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-05 13:53:04:242 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【横梁停止】"}
25-09-05 13:53:04:243 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-da399466-da97-449d-b998-0d7a0823cdd0", :State "running"} 
 =============================================================

25-09-05 13:53:04:328 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "cfa5b1cb-4851-4f76-b6a8-cdf70ccb9acb", :Result true}
25-09-05 13:53:04:329 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-cfa5b1cb-4851-4f76-b6a8-cdf70ccb9acb 中添加消息 {:ProcessId "project_29", :ScriptId "cfa5b1cb-4851-4f76-b6a8-cdf70ccb9acb", :Result true}
25-09-05 13:53:04:330 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "cfa5b1cb-4851-4f76-b6a8-cdf70ccb9acb", :Result true}
25-09-05 13:56:44:009 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目时默认执行动作】"}
25-09-05 13:56:44:010 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "running"} 
 =============================================================

25-09-05 13:56:44:056 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【关闭项目重置参数】"}
25-09-05 13:56:44:120 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "running"} 
 =============================================================

25-09-05 13:56:44:297 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:632] - Handling :finalize-project-quit-save event for project {:event :finalize-project-quit-save, :project_id 29, :user_id 1}
25-09-05 13:56:44:306 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 13:56:44:307 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-a5112cfc-f9ee-4232-86e0-ff1e81e9df45", :State "finished"} 
 =============================================================

25-09-05 13:56:44:387 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-common-service:85] - 项目关闭执行动作---完成
25-09-05 13:56:44:387 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "删除了【关闭项目 project_29】项目"}
25-09-05 13:56:44:388 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 13:56:44:389 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-63526397-7d5a-4d20-8840-4fa2ceee596d", :State "finished"} 
 =============================================================

25-09-05 13:56:44:391 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 13:56:44:395 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【联机】"}
25-09-05 13:56:44:396 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "finished"} 
 =============================================================

25-09-05 13:56:44:421 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 项目数据库 数据库缓存
25-09-05 13:56:44:526 DESKTOP-3BSREDP INFO [clj-backend.modules.sqlite.cache-utils:21] - 成功刷新 系统数据库 数据库缓存
25-09-05 13:56:44:527 DESKTOP-3BSREDP WARN [clj-backend.modules.project.project-service:416] - 项目缓存刷新失败，但继续执行保存操作: {:success false, :message "部分缓存刷新失败", :details {:project true, :project-data false, :system true}}
25-09-05 13:56:44:527 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "保存了项目"}
25-09-05 13:56:44:576 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29_copy.db 成功
25-09-05 13:56:44:599 DESKTOP-3BSREDP INFO [clj-backend.modules.project.project-service:639] - Project event listener started.
25-09-05 13:56:44:612 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【启动】"}
25-09-05 13:56:44:615 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 13:56:44:619 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【终止裂纹长度检查】"}
25-09-05 13:56:44:620 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "finished"} 
 =============================================================

25-09-05 13:56:44:787 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【横梁停止】"}
25-09-05 13:56:44:788 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-da399466-da97-449d-b998-0d7a0823cdd0", :State "finished"} 
 =============================================================

25-09-05 13:56:44:856 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目时默认执行动作】"}
25-09-05 13:56:44:860 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【关闭项目重置参数】"}
25-09-05 13:56:44:893 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "abort", :InstCode "sample_15419d437", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-05 13:56:44:895 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【横梁停止】"}
25-09-05 13:56:50:759 DESKTOP-3BSREDP INFO [clj-backend.common.io-utils:117] - 删除文件 d:\WorkProject\ZJ\MaterialPlat\clj-backend\db\1757037359907_project_29.db 成功
25-09-05 13:56:50:854 DESKTOP-3BSREDP WARN [clj-backend.db.connections:40] - 已为数据库连接 project_29 应用性能优化配置
25-09-05 13:56:50:855 DESKTOP-3BSREDP WARN [clj-backend.db.connections:80] - 创建 project_29 项目库连接
25-09-05 13:58:26:223 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-05 13:58:26:250 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:level "error", :content "调用外部接口出错"}
25-09-05 13:58:26:252 DESKTOP-3BSREDP ERROR [clj-backend.common.biz-error:48] - 发生系统错误. 
错误码: 10001 
错误描述: 调用外部接口出错 
错误数据: #error {
 :cause "Connection refused: connect"
 :via
 [{:type java.net.ConnectException
   :message "Connection refused: connect"
   :at [sun.nio.ch.Net connect0 "Net.java" -2]}]
 :trace
 [[sun.nio.ch.Net connect0 "Net.java" -2]
  [sun.nio.ch.Net connect "Net.java" 579]
  [sun.nio.ch.Net connect "Net.java" 568]
  [sun.nio.ch.NioSocketImpl connect "NioSocketImpl.java" 593]
  [java.net.SocksSocketImpl connect "SocksSocketImpl.java" 327]
  [java.net.Socket connect "Socket.java" 633]
  [org.apache.http.conn.scheme.PlainSocketFactory connectSocket "PlainSocketFactory.java" 120]
  [org.apache.http.impl.conn.DefaultClientConnectionOperator openConnection "DefaultClientConnectionOperator.java" 179]
  [org.apache.http.impl.conn.ManagedClientConnectionImpl open "ManagedClientConnectionImpl.java" 328]
  [org.apache.http.impl.client.DefaultRequestDirector tryConnect "DefaultRequestDirector.java" 612]
  [org.apache.http.impl.client.DefaultRequestDirector execute "DefaultRequestDirector.java" 447]
  [org.apache.http.impl.client.AbstractHttpClient doExecute "AbstractHttpClient.java" 884]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 82]
  [org.apache.http.impl.client.CloseableHttpClient execute "CloseableHttpClient.java" 107]
  [clj_http.core$request invokeStatic "core.clj" 304]
  [clj_http.core$request invoke "core.clj" 208]
  [clojure.lang.Var invoke "Var.java" 386]
  [clj_http.client$wrap_request_timing$fn__23258 invoke "client.clj" 835]
  [clj_http.headers$wrap_header_map$fn__22290 invoke "headers.clj" 143]
  [clj_http.client$wrap_query_params$fn__23155 invoke "client.clj" 661]
  [clj_http.client$wrap_basic_auth$fn__23162 invoke "client.clj" 677]
  [clj_http.client$wrap_oauth$fn__23166 invoke "client.clj" 687]
  [clj_http.client$wrap_user_info$fn__23171 invoke "client.clj" 700]
  [clj_http.client$wrap_url$fn__23244 invoke "client.clj" 801]
  [clj_http.client$wrap_redirects$fn__22931 invoke "client.clj" 267]
  [clj_http.client$wrap_decompression$fn__22956 invoke "client.clj" 339]
  [clj_http.client$wrap_input_coercion$fn__23088 invoke "client.clj" 490]
  [clj_http.client$wrap_additional_header_parsing$fn__23109 invoke "client.clj" 552]
  [clj_http.client$wrap_output_coercion$fn__23079 invoke "client.clj" 468]
  [clj_http.client$wrap_exceptions$fn__22917 invoke "client.clj" 219]
  [clj_http.client$wrap_accept$fn__23123 invoke "client.clj" 592]
  [clj_http.client$wrap_accept_encoding$fn__23129 invoke "client.clj" 609]
  [clj_http.client$wrap_content_type$fn__23118 invoke "client.clj" 584]
  [clj_http.client$wrap_form_params$fn__23218 invoke "client.clj" 761]
  [clj_http.client$wrap_nested_params$fn__23239 invoke "client.clj" 794]
  [clj_http.client$wrap_method$fn__23178 invoke "client.clj" 707]
  [clj_http.cookies$wrap_cookies$fn__21212 invoke "cookies.clj" 124]
  [clj_http.links$wrap_links$fn__22553 invoke "links.clj" 51]
  [clj_http.client$wrap_unknown_host$fn__23248 invoke "client.clj" 810]
  [clj_http.client$post invokeStatic "client.clj" 925]
  [clj_http.client$post doInvoke "client.clj" 921]
  [clojure.lang.RestFn invoke "RestFn.java" 426]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 29]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.common.http_client$post invokeStatic "http_client.clj" 25]
  [clj_backend.common.http_client$post invoke "http_client.clj" 22]
  [clj_backend.modules.hardware.station.service$host_instance_handle invokeStatic "service.clj" 536]
  [clj_backend.modules.hardware.station.service$host_instance_handle invoke "service.clj" 522]
  [clj_backend.modules.sys.user.sys_user_service$login invokeStatic "sys_user_service.clj" 96]
  [clj_backend.modules.sys.user.sys_user_service$login invoke "sys_user_service.clj" 91]
  [clj_backend.modules.sys.user.sys_user_routes$routes$fn__43149 invoke "sys_user_routes.clj" 56]
  [clj_backend.common.trial$trial_middleware$fn__42698 invoke "trial.clj" 73]
  [reitit.ring.coercion$fn__52552$fn__52554$fn__52555 invoke "coercion.cljc" 40]
  [reitit.ring.coercion$fn__52575$fn__52577$fn__52578 invoke "coercion.cljc" 80]
  [reitit.ring.middleware.exception$wrap$fn__49531$fn__49532 invoke "exception.clj" 49]
  [clj_backend.middleware.logger$logger_middleware$fn__52770 invoke "logger.clj" 18]
  [muuntaja.middleware$wrap_format_request$fn__52693 invoke "middleware.clj" 114]
  [muuntaja.middleware$wrap_format_response$fn__52697 invoke "middleware.clj" 132]
  [muuntaja.middleware$wrap_format_negotiate$fn__52690 invoke "middleware.clj" 96]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [reitit.ring$ring_handler$fn__12807 invoke "ring.cljc" 329]
  [clojure.lang.AFn applyToHelper "AFn.java" 154]
  [clojure.lang.AFn applyTo "AFn.java" 144]
  [clojure.lang.AFunction$1 doInvoke "AFunction.java" 33]
  [clojure.lang.RestFn invoke "RestFn.java" 411]
  [clojure.lang.Var invoke "Var.java" 386]
  [ring.middleware.reload$wrap_reload$fn__16585 invoke "reload.clj" 39]
  [selmer.middleware$wrap_error_page$fn__16600 invoke "middleware.clj" 18]
  [prone.middleware$wrap_exceptions$fn__16842 invoke "middleware.clj" 169]
  [ring.middleware.flash$wrap_flash$fn__16963 invoke "flash.clj" 39]
  [ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290 invoke "session.clj" 88]
  [ring.middleware.cors$handle_cors invokeStatic "cors.cljc" 175]
  [ring.middleware.cors$handle_cors invoke "cors.cljc" 167]
  [ring.middleware.cors$wrap_cors$fn__16942 invoke "cors.cljc" 205]
  [ring.middleware.keyword_params$wrap_keyword_params$fn__18384 invoke "keyword_params.clj" 53]
  [ring.middleware.nested_params$wrap_nested_params$fn__18442 invoke "nested_params.clj" 89]
  [ring.middleware.multipart_params$wrap_multipart_params$fn__18740 invoke "multipart_params.clj" 173]
  [ring.middleware.params$wrap_params$fn__18764 invoke "params.clj" 67]
  [ring.middleware.cookies$wrap_cookies$fn__18059 invoke "cookies.clj" 175]
  [ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935 invoke "absolute_redirects.clj" 47]
  [ring.middleware.resource$wrap_resource_prefer_resources$fn__18800 invoke "resource.clj" 25]
  [ring.middleware.content_type$wrap_content_type$fn__18883 invoke "content_type.clj" 34]
  [ring.middleware.default_charset$wrap_default_charset$fn__18907 invoke "default_charset.clj" 31]
  [ring.middleware.not_modified$wrap_not_modified$fn__18864 invoke "not_modified.clj" 61]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.middleware.x_headers$wrap_x_header$fn__18324 invoke "x_headers.clj" 22]
  [ring.adapter.undertow$undertow_handler$fn$reify__58450 handleRequest "undertow.clj" 40]
  [io.undertow.server.session.SessionAttachmentHandler handleRequest "SessionAttachmentHandler.java" 68]
  [io.undertow.server.Connectors executeRootHandler "Connectors.java" 387]
  [io.undertow.server.HttpServerExchange$1 run "HttpServerExchange.java" 852]
  [org.jboss.threads.ContextClassLoaderSavingRunnable run "ContextClassLoaderSavingRunnable.java" 35]
  [org.jboss.threads.EnhancedQueueExecutor safeRun "EnhancedQueueExecutor.java" 2019]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody doRunTask "EnhancedQueueExecutor.java" 1558]
  [org.jboss.threads.EnhancedQueueExecutor$ThreadBody run "EnhancedQueueExecutor.java" 1449]
  [org.xnio.XnioWorker$WorkerThreadFactory$1$1 run "XnioWorker.java" 1282]
  [java.lang.Thread run "Thread.java" 840]]}
25-09-05 14:04:09:483 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/host/instance 
 - 参数: 
 {:Hosts [{:HostId "2d06ec3a-9ecd-422c-a76a-1afc16b47b24", :HostName "1", :ConfigId nil, :StationHardware nil} {:HostId "da28d67a-f0fc-46a6-ab37-d412e6ab9f26", :HostName "2", :ConfigId nil, :StationHardware nil}]} 
 =============================================================

25-09-05 14:04:09:844 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/hardware 
 - 参数: 
 {:HardwareDtos {"hardware_simulator-0000-hw-ccss-id-0000" {:DaChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :AdChannels [{:Sensors ["传感器0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0}], :CreepAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 10, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 11, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 12, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 13, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 15, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 16, :Max 0.0, :Min 0.0} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 14, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :HandBoxChannels [], :Devices ({:Hwkey "hardware_simulator", :SubId 0, :DeviceId "9cdeaaab-9005-4fb9-b11a-279864d5c0cd"}), :ServoAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 3, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 4, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 5, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 6, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 7, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 8, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 9, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 10, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 11, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 12, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 13, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 14, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 15, :Channels [{:Sensors ["Sensor0"], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0} {:Sensors ["传感器1"], :HwKey "hardware_simulator", :RealIndex 1, :Max 0.0, :Min 0.0} {:Sensors ["传感器2"], :HwKey "hardware_simulator", :RealIndex 2, :Max 0.0, :Min 0.0} {:Sensors ["传感器3"], :HwKey "hardware_simulator", :RealIndex 3, :Max 0.0, :Min 0.0} {:Sensors ["传感器4"], :HwKey "hardware_simulator", :RealIndex 4, :Max 0.0, :Min 0.0} {:Sensors ["传感器5"], :HwKey "hardware_simulator", :RealIndex 5, :Max 0.0, :Min 0.0} {:Sensors ["传感器6"], :HwKey "hardware_simulator", :RealIndex 6, :Max 0.0, :Min 0.0} {:Sensors ["传感器7"], :HwKey "hardware_simulator", :RealIndex 7, :Max 0.0, :Min 0.0} {:Sensors ["传感器8"], :HwKey "hardware_simulator", :RealIndex 8, :Max 0.0, :Min 0.0} {:Sensors ["传感器9"], :HwKey "hardware_simulator", :RealIndex 9, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}], :InputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :OutputChannels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 1, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 2, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 3, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 4, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 5, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 6, :Max nil, :Min nil} {:Sensors [], :HwKey "hardware_simulator", :RealIndex 7, :Max nil, :Min nil}], :TempAxes [{:Hwkey "hardware_simulator", :RealIndex 0, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 1, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1} {:Hwkey "hardware_simulator", :RealIndex 2, :Channels [{:Sensors [], :HwKey "hardware_simulator", :RealIndex 0, :Max 0.0, :Min 0.0}], :UpDirectValue 1, :TensileUpOrDown 1, :PressUpOrDown 1}]}}} 
 =============================================================

25-09-05 14:04:09:941 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/api/appserver/config/info 
 - 参数: 
 {:AppServerIp "*************", :AppServerPort 3000, :ComputerId "MP2QAVV0", :AppServerApiPort 3000} 
 =============================================================

25-09-05 14:04:09:981 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "【超级管理员】admin: 登录"}
25-09-05 14:04:10:598 DESKTOP-3BSREDP INFO [clj-backend.modules.standby.service:160] - 运行 sync-to-share-http (HTTP POST)...
25-09-05 14:04:10:600 DESKTOP-3BSREDP WARN [clj-backend.modules.standby.service:170] - 从节点 IP 或端口未配置. 跳过 sync-to-share-http. 
25-09-05 14:04:19:028 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:content "打开项目 project_29"}
25-09-05 14:04:19:056 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:861] - 模板开始实例化
25-09-05 14:04:25:149 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "b8df486a-afe9-43bd-be42-f11f91fb8e60", :code 0, :msg "模板生成成功"}
25-09-05 14:04:25:150 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-b8df486a-afe9-43bd-be42-f11f91fb8e60 中添加消息 {:ProcessId "b8df486a-afe9-43bd-be42-f11f91fb8e60", :code 0, :msg "模板生成成功"}
25-09-05 14:04:25:151 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "b8df486a-afe9-43bd-be42-f11f91fb8e60", :code 0, :msg "模板生成成功"}
25-09-05 14:04:26:483 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【打开项目默认执行动作】"}
25-09-05 14:04:26:484 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "running"} 
 =============================================================

25-09-05 14:04:28:254 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【打开项目默认执行动作】"}
25-09-05 14:04:28:256 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-6a579b71-e5fb-4bef-9569-33de699c61ea", :State "finished"} 
 =============================================================

25-09-05 14:04:31:770 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【联机】"}
25-09-05 14:04:31:772 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :State "running"} 
 =============================================================

25-09-05 14:04:40:424 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【裂纹检查新版流程图】"}
25-09-05 14:04:40:427 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-05 14:04:40:586 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "fe609491-8dea-4126-b306-fc84cbd7e2be", :Result false}
25-09-05 14:04:40:586 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-fe609491-8dea-4126-b306-fc84cbd7e2be 中添加消息 {:ProcessId "project_29", :ScriptId "fe609491-8dea-4126-b306-fc84cbd7e2be", :Result false}
25-09-05 14:04:40:588 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "fe609491-8dea-4126-b306-fc84cbd7e2be", :Result false}
25-09-05 14:04:43:006 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "8ed078b8-a7f0-4157-a64d-6f7fae367ca1", :Result true}
25-09-05 14:04:43:006 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-8ed078b8-a7f0-4157-a64d-6f7fae367ca1 中添加消息 {:ProcessId "project_29", :ScriptId "8ed078b8-a7f0-4157-a64d-6f7fae367ca1", :Result true}
25-09-05 14:04:43:007 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "8ed078b8-a7f0-4157-a64d-6f7fae367ca1", :Result true}
25-09-05 14:04:43:742 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【启动】"}
25-09-05 14:04:43:744 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-679f70fa-d870-40d9-b121-c759b28044ed", :State "running"} 
 =============================================================

25-09-05 14:04:44:206 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【启动】"}
25-09-05 14:04:44:208 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-679f70fa-d870-40d9-b121-c759b28044ed", :State "finished"} 
 =============================================================

25-09-05 14:04:45:393 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【终止裂纹长度检查】"}
25-09-05 14:04:45:394 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "running"} 
 =============================================================

25-09-05 14:04:45:508 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d", :MsgBody {:Cmd "abort", :InstCode "sample_15419d437", :ActionID "d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-05 14:04:45:512 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 14:04:45:513 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-05 14:04:45:582 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-05 14:04:45:585 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【横梁停止】"}
25-09-05 14:04:45:586 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-da399466-da97-449d-b998-0d7a0823cdd0", :State "running"} 
 =============================================================

25-09-05 14:04:45:669 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "4835e366-5969-4f24-bf6b-ffae70c80d3d", :Result true}
25-09-05 14:04:45:669 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-4835e366-5969-4f24-bf6b-ffae70c80d3d 中添加消息 {:ProcessId "project_29", :ScriptId "4835e366-5969-4f24-bf6b-ffae70c80d3d", :Result true}
25-09-05 14:04:45:670 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "4835e366-5969-4f24-bf6b-ffae70c80d3d", :Result true}
25-09-05 14:04:45:884 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【横梁停止】"}
25-09-05 14:04:45:886 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-da399466-da97-449d-b998-0d7a0823cdd0", :State "finished"} 
 =============================================================

25-09-05 14:04:46:295 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【终止裂纹长度检查】"}
25-09-05 14:04:46:297 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "finished"} 
 =============================================================

25-09-05 14:04:48:901 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【裂纹检查新版流程图】"}
25-09-05 14:04:48:904 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-05 14:04:49:023 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "ed62d8ae-95b5-4218-ac3a-63d33713ff52", :Result true}
25-09-05 14:04:49:023 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-ed62d8ae-95b5-4218-ac3a-63d33713ff52 中添加消息 {:ProcessId "project_29", :ScriptId "ed62d8ae-95b5-4218-ac3a-63d33713ff52", :Result true}
25-09-05 14:04:49:024 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "ed62d8ae-95b5-4218-ac3a-63d33713ff52", :Result true}
25-09-05 14:04:49:816 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "770799da-6007-4180-9905-5ff7b80f0869", :Result true}
25-09-05 14:04:49:816 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-770799da-6007-4180-9905-5ff7b80f0869 中添加消息 {:ProcessId "project_29", :ScriptId "770799da-6007-4180-9905-5ff7b80f0869", :Result true}
25-09-05 14:04:49:818 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "770799da-6007-4180-9905-5ff7b80f0869", :Result true}
25-09-05 14:04:52:652 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【终止裂纹长度检查】"}
25-09-05 14:04:52:654 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "running"} 
 =============================================================

25-09-05 14:04:52:746 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d", :MsgBody {:Cmd "abort", :InstCode "sample_15419d437", :ActionID "d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-05 14:04:52:749 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 14:04:52:750 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-05 14:04:52:807 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-05 14:04:52:810 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【横梁停止】"}
25-09-05 14:04:52:810 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-da399466-da97-449d-b998-0d7a0823cdd0", :State "running"} 
 =============================================================

25-09-05 14:04:52:909 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "d5fe81b5-69e6-494e-9acc-6044d4a58edc", :Result true}
25-09-05 14:04:52:909 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-d5fe81b5-69e6-494e-9acc-6044d4a58edc 中添加消息 {:ProcessId "project_29", :ScriptId "d5fe81b5-69e6-494e-9acc-6044d4a58edc", :Result true}
25-09-05 14:04:52:910 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "d5fe81b5-69e6-494e-9acc-6044d4a58edc", :Result true}
25-09-05 14:04:53:046 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【横梁停止】"}
25-09-05 14:04:53:047 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-da399466-da97-449d-b998-0d7a0823cdd0", :State "finished"} 
 =============================================================

25-09-05 14:04:53:141 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【终止裂纹长度检查】"}
25-09-05 14:04:53:143 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "finished"} 
 =============================================================

25-09-05 14:04:55:562 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【裂纹检查新版流程图】"}
25-09-05 14:04:55:564 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-05 14:04:55:656 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "6c8021b7-f9a3-4199-9be5-6160f1724727", :Result true}
25-09-05 14:04:55:656 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-6c8021b7-f9a3-4199-9be5-6160f1724727 中添加消息 {:ProcessId "project_29", :ScriptId "6c8021b7-f9a3-4199-9be5-6160f1724727", :Result true}
25-09-05 14:04:55:657 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "6c8021b7-f9a3-4199-9be5-6160f1724727", :Result true}
25-09-05 14:04:55:759 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "3055bef6-210a-4b24-9a43-fb2608299019", :Result true}
25-09-05 14:04:55:760 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-3055bef6-210a-4b24-9a43-fb2608299019 中添加消息 {:ProcessId "project_29", :ScriptId "3055bef6-210a-4b24-9a43-fb2608299019", :Result true}
25-09-05 14:04:55:760 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "3055bef6-210a-4b24-9a43-fb2608299019", :Result true}
25-09-05 14:05:26:674 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 14:05:26:675 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

25-09-05 14:05:32:862 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【终止裂纹长度检查】"}
25-09-05 14:05:32:863 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "running"} 
 =============================================================

25-09-05 14:05:32:947 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-0fd5b761-7c8e-432f-b8e5-5b70cd023b0d", :MsgBody {:Cmd "abort", :InstCode "sample_15419d437", :ActionID "d2a28ac5-6be5-4a4c-806f-424fc39131ee"}}
25-09-05 14:05:32:949 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 14:05:32:966 DESKTOP-3BSREDP INFO [clj-backend.modules.action.action-service:197] - UI: 子任务执行动作:%s {:Type "process-action", :ClassName "project_29", :ProcessID "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :SubTaskID "onlyAction-7ab30145-6f3a-4ac1-a4c1-6d9ff9ba49cf", :MsgBody {:Cmd "start", :InstCode "sample_15419d437", :ActionID "da399466-da97-449d-b998-0d7a0823cdd0"}}
25-09-05 14:05:32:970 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【横梁停止】"}
25-09-05 14:05:32:971 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-da399466-da97-449d-b998-0d7a0823cdd0", :State "running"} 
 =============================================================

25-09-05 14:05:33:040 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "17f31fef-254c-4ca6-ae91-a22e55666c5f", :Result true}
25-09-05 14:05:33:041 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-17f31fef-254c-4ca6-ae91-a22e55666c5f 中添加消息 {:ProcessId "project_29", :ScriptId "17f31fef-254c-4ca6-ae91-a22e55666c5f", :Result true}
25-09-05 14:05:33:041 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "17f31fef-254c-4ca6-ae91-a22e55666c5f", :Result true}
25-09-05 14:05:33:138 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【横梁停止】"}
25-09-05 14:05:33:139 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-da399466-da97-449d-b998-0d7a0823cdd0", :State "finished"} 
 =============================================================

25-09-05 14:05:33:281 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【终止裂纹长度检查】"}
25-09-05 14:05:33:283 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-b8180c37-afe6-4b02-8fe6-5298a14b54f9", :State "finished"} 
 =============================================================

25-09-05 14:05:34:354 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "开始流程【裂纹检查新版流程图】"}
25-09-05 14:05:34:356 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "running"} 
 =============================================================

25-09-05 14:05:34:461 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "cc706ff0-425c-47e4-bc69-9da6861f0248", :Result true}
25-09-05 14:05:34:461 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-cc706ff0-425c-47e4-bc69-9da6861f0248 中添加消息 {:ProcessId "project_29", :ScriptId "cc706ff0-425c-47e4-bc69-9da6861f0248", :Result true}
25-09-05 14:05:34:462 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "cc706ff0-425c-47e4-bc69-9da6861f0248", :Result true}
25-09-05 14:05:34:747 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:823] - 脚本执行返回结果:{:ProcessId "project_29", :ScriptId "e617b201-4e3c-4b22-b87b-fc0f24787e2c", :Result true}
25-09-05 14:05:34:747 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:817] - 在 script-channel-e617b201-4e3c-4b22-b87b-fc0f24787e2c 中添加消息 {:ProcessId "project_29", :ScriptId "e617b201-4e3c-4b22-b87b-fc0f24787e2c", :Result true}
25-09-05 14:05:34:747 DESKTOP-3BSREDP INFO [clj-backend.modules.script.service:850] - 脚本获取结果: {:ProcessId "project_29", :ScriptId "e617b201-4e3c-4b22-b87b-fc0f24787e2c", :Result true}
25-09-05 14:06:00:968 DESKTOP-3BSREDP INFO [clj-backend.modules.log.log-service:97] - {:project_id 29, :project_name "断裂力学k1c试验项目0901(1)", :content "结束流程【裂纹检查新版流程图】"}
25-09-05 14:06:00:970 DESKTOP-3BSREDP INFO [clj-backend.common.http-client:15] - 
=============================================================
 -  POST  调用外部接口:  http://localhost:5002/template/action/state 
 - 参数: 
 {:ClassName "project_29", :ProcessId "project_29-d2a28ac5-6be5-4a4c-806f-424fc39131ee", :State "finished"} 
 =============================================================

