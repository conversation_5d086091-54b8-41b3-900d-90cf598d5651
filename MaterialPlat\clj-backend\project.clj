(defproject clj-backend "0.1.0-SNAPSHOT"

  :description "中机实验后台"
  :url "http://example.com/FIXME"

  :dependencies [[ch.qos.logback/logback-classic "1.2.3"]
                 [clojure.java-time "1.1.0"]
                 [conman "0.9.5"]
                 [expound "0.9.0"]
                 [funcool/struct "1.4.0"]
                 [json-html "0.4.7"]
                 [ring-cors "0.1.13"]
                 [luminus-migrations "0.7.5"]
                 [luminus-transit "0.1.5"]
                 [luminus-jetty "0.2.3"]
                 [luminus/ring-ttl-session "0.3.3"]
                 [markdown-clj "1.11.3"]
                 [metosin/muuntaja "0.6.8"]
                 [metosin/reitit "0.5.18"]
                 [metosin/ring-http-response "0.9.3"]
                 [nrepl "1.0.0"]
                 [org.clojure/clojure "1.12.0"]
                 [org.clojure/tools.cli "1.0.214"]
                 [org.clojure/tools.logging "1.2.4"]
                 [org.webjars.npm/bulma "0.9.4"]
                 [org.webjars.npm/material-icons "1.10.8"]
                 [org.webjars/webjars-locator "0.45"]
                 [org.webjars/webjars-locator-jboss-vfs "0.1.0"]
                 [org.xerial/sqlite-jdbc "3.36.0.3"]
                 [ring-webjars "0.2.0"]
                 [ring/ring-core "1.7.1"]
                 [ring/ring-defaults "0.3.2"]
                 [selmer "1.12.55"]
                 [io.djy/ezzmq "0.8.2"]
                 [org.clojure/core.match "1.0.0"]
                 [jarohen/chime "0.3.4-SNAPSHOT"]
                 ;; [com.fzakaria/slf4j-timbre "0.3.21"]
                 [com.taoensso/timbre "6.0.1"]
                 [org.clojure/data.xml "0.0.8"]
                 [com.rpl/specter "1.1.4"]
                 [org.clojure/core.async "1.6.673"]
                 [eidolon "0.2.0"]
                 [walmartlabs/system-viz "0.4.0"]
                 [alkie/datascope "0.1.2"]
                 [walmartlabs/datascope "0.1.1"]
                 [mount "0.1.17"]
                 [cprop "0.1.19"]
                 [com.googlecode.log4jdbc/log4jdbc "1.2"]
                 [com.github.houbb/pinyin "0.3.1"]
                 [buddy/buddy-auth "2.2.0"]
                 [clj-http "2.3.0"]
                 [manifold "0.1.9-alpha4"]
                 [aleph "0.4.6"]
                 [io.netty/netty-all "4.1.68.Final"]
                 [org.apache.poi/poi "4.1.2"]
                 [org.apache.poi/poi-ooxml "4.1.2"]
                 [org.apache.poi/poi-ooxml-schemas "4.1.2"]
                 [org.apache.poi/ooxml-schemas "1.4"]
                 [org.apache.poi/poi-scratchpad "4.1.2"]
                 [org.clojure/data.csv "1.1.0"]
                 [compojure "1.6.2"]
                 [dk.ative/docjure "1.14.0"]
                 [digest "1.4.7"]
                 [com.taoensso/nippy "3.2.0"]
                 [babashka/fs "0.5.24"]
                 [lambdaisland/kaocha "1.87.1366"]
                 [net.clojars.zhaoyul/monitor-lib "0.1.2-SNAPSHOT"]
                 ]

  :min-lein-version "2.0.0"
  :source-paths ["src/clj"]
  :test-paths ["test/clj"]
  :resource-paths ["resources"]
  :target-path "target/%s/"
  :main ^:skip-aot clj-backend.core

  :plugins []

  :aliases {"kaocha" ["run" "-m" "kaocha.runner"]
            "test" ["kaocha"]
            "test-watch" ["kaocha" "--watch"]}

  :profiles
  {:uberjar {:omit-source true
             :aot :all
             :uberjar-name "clj-backend.jar"
             :source-paths ["env/prod/clj"]
             :resource-paths ["env/prod/resources"]}

   :dev           [:project/dev :profiles/dev]
   :test          [:project/dev :project/test :profiles/test]

   :project/dev  {:jvm-opts ["-Dconf=dev-config.edn" "-Dclojure.core.async.pool-size=20"]
                  ;; :jvm-opts ["-Dconf=dev-config.edn" ]
                  :dependencies [[org.clojure/tools.namespace "1.3.0"]
                                 [pjstadig/humane-test-output "0.11.0"]
                                 [prone "2021-04-23"]
                                 [ring/ring-devel "1.9.6"]
                                 [ring/ring-mock "0.4.0"]
                                 [lambdaisland/kaocha-cloverage "1.1.89"]
                                 [lambdaisland/kaocha-junit-xml "1.17.101"]]
                  :plugins      [[com.jakemccrary/lein-test-refresh "0.24.1"]
                                 [jonase/eastwood "1.2.4"]]

                  :source-paths ["env/dev/clj"]
                  :resource-paths ["env/dev/resources"]
                  :repl-options {:init-ns user
                                 :timeout 120000}
                  :injections [(require 'pjstadig.humane-test-output)
                               (pjstadig.humane-test-output/activate!)]}
   :project/test {:jvm-opts ["-Dconf=test-config.edn"]
                  :resource-paths ["env/test/resources"]}
   :profiles/dev {}
   :profiles/test {}})
