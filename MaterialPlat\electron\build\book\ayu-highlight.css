/*
Based off of the Ayu theme
Original by <PERSON><PERSON><PERSON> (https://github.com/dempfi/ayu)
*/

.hljs {
  display: block;
  overflow-x: auto;
  background: #191f26;
  color: #e6e1cf;
}

.hljs-comment,
.hljs-quote {
  color: #5c6773;
  font-style: italic;
}

.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-attr,
.hljs-regexp,
.hljs-link,
.hljs-selector-id,
.hljs-selector-class {
  color: #ff7733;
}

.hljs-number,
.hljs-meta,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
  color: #ffee99;
}

.hljs-string,
.hljs-bullet {
  color: #b8cc52;
}

.hljs-title,
.hljs-built_in,
.hljs-section {
  color: #ffb454;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-symbol {
  color: #ff7733;
}

.hljs-name {
    color: #36a3d9;
}

.hljs-tag {
    color: #00568d;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-addition {
  color: #91b362;
}

.hljs-deletion {
  color: #d96c75;
}
