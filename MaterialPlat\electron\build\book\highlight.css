/*
 * An increased contrast highlighting scheme loosely based on the
 * "Base16 Atelier Dune Light" theme by <PERSON>
 * (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/dune)
 * Original Base16 color scheme by <PERSON>
 * (https://github.com/chris<PERSON><PERSON>/base16)
 */

/* Comment */
.hljs-comment,
.hljs-quote {
  color: #575757;
}

/* Red */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-name,
.hljs-regexp,
.hljs-link,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #d70025;
}

/* Orange */
.hljs-number,
.hljs-meta,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
  color: #b21e00;
}

/* Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet {
  color: #008200;
}

/* Blue */
.hljs-title,
.hljs-section {
  color: #0030f2;
}

/* Purple */
.hljs-keyword,
.hljs-selector-tag {
  color: #9d00ec;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #f6f7f6;
  color: #000;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-addition {
  color: #22863a;
  background-color: #f0fff4;
}

.hljs-deletion {
  color: #b31d28;
  background-color: #ffeef0;
}
