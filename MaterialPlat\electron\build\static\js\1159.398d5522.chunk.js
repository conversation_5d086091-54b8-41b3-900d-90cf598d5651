(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[1159],{1159:(e,l,a)=>{"use strict";a.r(l),a.d(l,{default:()=>f});a(65043);var i=a(26815),n=a(79686),o=a.n(n),d=(a(79160),a(56543)),t=a(8361),s=a(55518);const u=e=>{switch(e){case d.P$.\u6620\u50cf\u6570\u636e\u6e90.value:return"object[]";case d.P$.\u6620\u50cf\u8f74\u6570\u636e\u6e90.value:return"double";case d.P$.\u5e26\u591a\u9009\u6846\u7684\u591a\u9009\u9879.value:case d.P$["\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90(\u591a\u9009)"].value:case d.P$["\u53ef\u5206\u6790\u8bd5\u6837(\u591a\u9009)"].value:return"string[]";case d.P$.\u4e8c\u7ef4\u6570\u7ec4\u5217\u6570\u636e\u6e90.value:case d.P$.\u6570\u636e\u6e90.value:case d.P$.\u52a8\u4f5c\u6570\u636e\u6e90.value:case d.P$.\u52a8\u4f5c\u5185\u90e8\u540d\u6570\u636e\u6e90.value:case d.P$.Buffer\u6570\u636e\u6e90.value:case d.P$.\u5e03\u5c14\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case d.P$.\u6570\u5b57\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case d.P$.\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case d.P$.\u7a97\u4f53\u6570\u636e\u6e90.value:case d.P$.\u7279\u6b8a\u52a8\u4f5c\u6570\u636e\u6e90.value:case d.P$.\u97f3\u9891\u6570\u636e\u6e90.value:case d.P$.\u7ed3\u679c\u6570\u636e\u6e90.value:case d.P$["Buffer\u4fe1\u53f7\u53d8\u91cf"].value:case d.P$.\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90.value:default:return"string"}};var r=a(36497),v=a(32513),c=a(96603),h=a(70579);const b=e=>{const{show_type:l="",mode:a=""}=e;return"selector"===l?(0,h.jsx)(r.A,{...e}):"boxSelection"===l?"multiple"===a?(0,h.jsx)(v.A.Group,{...e,className:"checkbox-input-layout"}):(0,h.jsx)(c.Ay.Group,{...e,onChange:l=>{null===e||void 0===e||e.onChange(l.target.value)},style:{display:"flex",flexDirection:"column",gap:8,...null===e||void 0===e?void 0:e.style}}):null},p=e=>{let{variable:l,disabled:a,onChange:n}=e;const{default_val:s,select_tab:r}=l,{getSelectOptions:v}=(0,t.Ay)(),c=e=>{n({...s,...e,value_type:u(null===r||void 0===r?void 0:r.selection)})},{selection:p,show_type:f="selector"}=r,x=v(r),g={value:null===s||void 0===s?void 0:s.value,disabled:a,showSearch:!0,allowClear:!1,className:"input-width",optionFilterProp:"label",options:x,onChange:e=>c({value:e})};switch(p){case d.P$.\u6620\u50cf\u6570\u636e\u6e90.value:{const e=(null===g||void 0===g?void 0:g.options)||[],a=(null===g||void 0===g?void 0:g.value)||[],{hardwareCategory:n,axisSelection:d}=(null===l||void 0===l?void 0:l.select_tab)||{};let t=e,s=a;var y,$,_,m;if(!o()(n))if(t=(null===e||void 0===e||null===(y=e.find((e=>e.value===n)))||void 0===y?void 0:y.children)||[],s=a.slice(1),!o()(d))t=(null===($=t)||void 0===$||null===(_=$.find((e=>e.value===d)))||void 0===_?void 0:_.children)||[],s=a.slice(2);if(Array.isArray(s))s=null===(m=s)||void 0===m?void 0:m.map((e=>Number.isNaN(Number(e))?e:Number(e)));return(0,h.jsx)(i.A,{...g,options:t,value:s,onChange:e=>{let l=e;o()(n)||(l=[n,...e],o()(d)||(l=[n,d,...e])),c({value:l})}})}case d.P$.\u52a8\u4f5c\u6570\u636e\u6e90.value:return(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(b,{show_type:f,...g})});case d.P$.\u6620\u50cf\u8f74\u6570\u636e\u6e90.value:return(0,h.jsx)(b,{show_type:f,...g,value:null===s||void 0===s?void 0:s.valueKey,onChange:e=>{var l,a,i;const n=null===(l=g.options)||void 0===l?void 0:l.find((l=>l.value===e));c({key:null===n||void 0===n?void 0:n.id,name:null===n||void 0===n?void 0:n.name,value:null!==(a=null===n||void 0===n?void 0:n.idx)&&void 0!==a?a:null===n||void 0===n?void 0:n.subId,valueKey:null===n||void 0===n?void 0:n.valueKey,type:null===n||void 0===n?void 0:n.type,hwKey:null===n||void 0===n?void 0:n.hwKey,daqRate:(null===n||void 0===n||null===(i=n.children)||void 0===i?void 0:i.length)>0?null===n||void 0===n?void 0:n.children[0].daqRate:0})}});case d.P$["Buffer\u4fe1\u53f7\u53d8\u91cf"].value:return(0,h.jsx)(b,{show_type:f,...g,value:null===s||void 0===s?void 0:s.buffer_signal,onChange:e=>{var l;const a=null===(l=g.options)||void 0===l?void 0:l.find((l=>l.value===e));c({value:null===a||void 0===a?void 0:a.signal_code,buffer_code:null===a||void 0===a?void 0:a.code,buffer_signal:null===a||void 0===a?void 0:a.buffer_signal})}});case d.P$.\u4e8c\u7ef4\u6570\u7ec4\u5217\u6570\u636e\u6e90.value:return(0,h.jsx)(i.A,{...g,value:null===s||void 0===s?void 0:s.doubleArrayColumnCodeValue,onChange:e=>{var l;return c({value:null!==(l=null===e||void 0===e?void 0:e[1])&&void 0!==l?l:"",doubleArrayColumnCodeValue:e})}});case d.P$.\u6570\u636e\u6e90.value:case d.P$.\u52a8\u4f5c\u5185\u90e8\u540d\u6570\u636e\u6e90.value:case d.P$.Buffer\u6570\u636e\u6e90.value:case d.P$.\u5e03\u5c14\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case d.P$.\u6570\u5b57\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case d.P$.\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case d.P$.\u7a97\u4f53\u6570\u636e\u6e90.value:case d.P$.\u7279\u6b8a\u52a8\u4f5c\u6570\u636e\u6e90.value:case d.P$.\u97f3\u9891\u6570\u636e\u6e90.value:case d.P$.\u7ed3\u679c\u6570\u636e\u6e90.value:case d.P$.\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90.value:case d.P$.\u5217\u8868\u4e2d\u7684\u5355\u9009\u9879.value:return(0,h.jsx)(b,{show_type:f,...g});case d.P$["\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90(\u591a\u9009)"].value:case d.P$["\u53ef\u5206\u6790\u8bd5\u6837(\u591a\u9009)"].value:case d.P$.\u5e26\u591a\u9009\u6846\u7684\u591a\u9009\u9879.value:return(0,h.jsx)(b,{show_type:f,...g,mode:"multiple"});default:return(0,h.jsx)(h.Fragment,{children:"\u672a\u5b9a\u4e49\u7684\u6570\u636e\u6e90"})}},f=e=>{let{variable:l,disabled:a,onChange:i}=e;return(0,h.jsx)(s.A,{variable:l,disabled:a,onChange:i,render:e=>{let{innerDisabled:a}=e;return(0,h.jsx)(p,{variable:l,disabled:a,onChange:e=>{i({...l,default_val:e})}})}})}},55518:(e,l,a)=>{"use strict";a.d(l,{A:()=>N});var i=a(65043),n=a(74117),o=a(56543),d=a(81143),t=a(68374),s=a(18650);const u=0,r="left";var v=a(70579);const c=d.Ay.div`
    width: ${(0,t.D0)("20px")};
    height: ${(0,t.D0)("20px")};
    background-size: ${(0,t.D0)("20px")} ${(0,t.D0)("20px")};
    background-image: url(${e=>{let{isConstant:l}=e;return l?s.fd:s.Mo}});
`,h=e=>{let{variable:l,onChange:a,disabled:i}=e;const{default_val:n,is_fx:o}=l;return!o||i?(0,v.jsx)(v.Fragment,{}):(0,v.jsx)(c,{isConstant:n.isConstant===u,onClick:()=>{a({...l,default_val:{...n,isConstant:0===(null===n||void 0===n?void 0:n.isConstant)?1:0}})}})};var b=a(95206),p=a(34154),f=a(67208),x=a(16090),g=a(36497),y=a(29977);const $=e=>{var l;let{disabled:a,variable:n,handleChange:o}=e;const d=(0,y.A)(),t=(0,i.useMemo)((()=>(null===d||void 0===d?void 0:d.filter((e=>e.variable_type===n.variable_type&&e.id!==n.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[d,n]);return(0,v.jsx)(g.A,{showSearch:!0,optionFilterProp:"labelName",disabled:a,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===n||void 0===n||null===(l=n.default_val)||void 0===l?void 0:l.variable_id,options:t,onChange:(e,l)=>o(l)})},_=e=>{let{disabled:l,content:a,buttonType:n,actionId:d,script:t}=e;const[s,u]=(0,i.useState)(!1),{startAction:r}=(0,x.A)(),c=()=>{n!==p.NR.\u52a8\u4f5c?n!==p.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{u(!0),await(0,f.O5k)({script:t,result_type:o.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{u(!1)}})():(async()=>{try{d&&(u(!0),await r({action_id:d}))}catch(e){console.log("err when handleSubmitAction",e)}finally{u(!1)}})()};return(0,v.jsx)(b.Ay,{loading:s,disabled:l,className:"button-width",onClick:()=>c(),children:a})},m=d.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:l}=e;return l?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,t.D0)("80px")};
        pointer-events: auto;
    }
`,w=e=>{let{disabled:l,variable:a,render:i,onChange:n,buttonShow:o}=e;const{button_variable_tab:d,default_val:t}=a;return(0,v.jsx)(m,{isLeft:(null===d||void 0===d?void 0:d.position)===r,children:1===t.isConstant?(0,v.jsx)($,{disabled:l,variable:a,handleChange:e=>{n({...a,default_val:{...t,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,v.jsxs)(v.Fragment,{children:[o&&(null===d||void 0===d?void 0:d.isEnable)&&(0,v.jsx)(_,{...d,disabled:l}),i()]})})};var P=a(12624),C=a(32513);const j=e=>{let{variable:l,disabled:a=!1,onChange:i,usableShowType:n="checkbox"}=e;return null!==l&&void 0!==l&&l.is_enable?"switch"===n?(0,v.jsx)(P.A,{disabled:a,checked:null===l||void 0===l?void 0:l.is_feature,onChange:e=>{i({...l,is_feature:e})}}):(0,v.jsx)(C.A,{disabled:a,checked:null===l||void 0===l?void 0:l.is_feature,onChange:e=>{i({...l,is_feature:e.target.checked})}}):(0,v.jsx)(v.Fragment,{})},A=d.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,N=e=>{let{variable:l,disabled:a=!1,onChange:i,render:d,usableShow:t=!0,buttonShow:s=!0,fxShow:u=!0,nameShow:r=!0,usableShowType:c}=e;const{t:b}=(0,n.Bd)(),p=a||(l.variable_type===o.ps.\u5e03\u5c14\u578b?(null===l||void 0===l?void 0:l.is_enable)&&(null===l||void 0===l?void 0:l.is_feature):(null===l||void 0===l?void 0:l.is_enable)&&!(null!==l&&void 0!==l&&l.is_feature));return(0,v.jsxs)(A,{children:[(t||r)&&(0,v.jsx)("div",{className:"input-render-left",children:(0,v.jsxs)("div",{children:[t&&(0,v.jsx)(j,{variable:l,disabled:a,onChange:i,usableShowType:c}),r&&(0,v.jsx)("div",{className:"variable_name",children:b(l.name)})]})}),(0,v.jsxs)("div",{className:"input-render-right",children:[u&&(0,v.jsx)(h,{variable:l,onChange:i,disabled:p}),(0,v.jsx)(w,{disabled:p,variable:l,onChange:i,buttonShow:s,render:()=>d({innerDisabled:p})})]})]})}},79160:(e,l,a)=>{var i=a(16913),n=a(22761);e.exports=function(e){return"number"==typeof e||n(e)&&"[object Number]"==i(e)}},79686:e=>{e.exports=function(e){return null==e}}}]);
//# sourceMappingURL=1159.398d5522.chunk.js.map