"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[120],{20120:(e,t,i)=>{i.r(t),i.d(t,{default:()=>r});i(65043);var s=i(55008),n=i(18650),a=i(56543);const l=i(81143).Ay.div`
    padding: 10px;
    display: flex;
    justify-content: center;
    height: 12vw;
    .layout {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

    }
    .description {
        display: flex;
        justify-content: center;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
`;var c=i(70579);const r=e=>{let{variable:t}=e;const{src:i,showName:r,name:d}=null===t||void 0===t?void 0:t.picture_tab;return(0,c.jsx)(l,{children:(0,c.jsxs)("div",{className:"layout",children:[(0,c.jsx)(s.A,{alt:d,src:i.includes(a.mF)||i.startsWith("data:")?i:`${a.mF}${i}`,height:"8vw",width:"8vw",fallback:n.Np}),r&&(0,c.jsx)("div",{className:"description",title:d,children:d})]})})}}}]);
//# sourceMappingURL=120.2ecda320.chunk.js.map