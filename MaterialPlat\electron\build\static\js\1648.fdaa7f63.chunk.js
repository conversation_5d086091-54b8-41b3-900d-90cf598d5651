"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[1648],{1648:(e,t,n)=>{n.d(t,{_G:()=>h,be:()=>a,gB:()=>p,gl:()=>D,m$:()=>u});var r=n(65043),o=n(23585),i=n(41024);function a(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function l(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);return o&&(e[r]=o),e}),Array(e.length))}function s(e){return null!==e&&e>=0}const c={scaleX:1,scaleY:1},u=e=>{var t;let{rects:n,activeNodeRect:r,activeIndex:o,overIndex:i,index:a}=e;const l=null!=(t=n[o])?t:r;if(!l)return null;const s=function(e,t,n){const r=e[t],o=e[t-1],i=e[t+1];if(!r||!o&&!i)return 0;if(n<t)return o?r.left-(o.left+o.width):i.left-(r.left+r.width);return i?i.left-(r.left+r.width):r.left-(o.left+o.width)}(n,a,o);if(a===o){const e=n[i];return e?{x:o<i?e.left+e.width-(l.left+l.width):e.left-l.left,y:0,...c}:null}return a>o&&a<=i?{x:-l.width-s,y:0,...c}:a<o&&a>=i?{x:l.width+s,y:0,...c}:{x:0,y:0,...c}};const d=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=a(t,r,n),l=t[o],s=i[o];return s&&l?{x:s.left-l.left,y:s.top-l.top,scaleX:s.width/l.width,scaleY:s.height/l.height}:null},f={scaleX:1,scaleY:1},h=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:i,overIndex:a}=e;const l=null!=(t=i[n])?t:r;if(!l)return null;if(o===n){const e=i[a];return e?{x:0,y:n<a?e.top+e.height-(l.top+l.height):e.top-l.top,...f}:null}const s=function(e,t,n){const r=e[t],o=e[t-1],i=e[t+1];if(!r)return 0;if(n<t)return o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0;return i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0}(i,o,n);return o>n&&o<=a?{x:0,y:-l.height-s,...f}:o<n&&o>=a?{x:0,y:l.height+s,...f}:{x:0,y:0,...f}};const v="Sortable",g=r.createContext({activeIndex:-1,containerId:v,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:d,disabled:{draggable:!1,droppable:!1}});function p(e){let{children:t,id:n,items:a,strategy:s=d,disabled:c=!1}=e;const{active:u,dragOverlay:f,droppableRects:h,over:p,measureDroppableContainers:b}=(0,o.fF)(),m=(0,i.YG)(v,n),y=Boolean(null!==f.rect),w=(0,r.useMemo)((()=>a.map((e=>"object"===typeof e&&"id"in e?e.id:e))),[a]),x=null!=u,C=u?w.indexOf(u.id):-1,D=p?w.indexOf(p.id):-1,R=(0,r.useRef)(w),E=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(w,R.current),S=-1!==D&&-1===C||E,M=function(e){return"boolean"===typeof e?{draggable:e,droppable:e}:e}(c);(0,i.Es)((()=>{E&&x&&b(w)}),[E,w,x,b]),(0,r.useEffect)((()=>{R.current=w}),[w]);const k=(0,r.useMemo)((()=>({activeIndex:C,containerId:m,disabled:M,disableTransforms:S,items:w,overIndex:D,useDragOverlay:y,sortedRects:l(w,h),strategy:s})),[C,m,M.draggable,M.droppable,S,w,D,h,y,s]);return r.createElement(g.Provider,{value:k},t)}const b=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return a(n,r,o).indexOf(t)},m=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:a,previousItems:l,previousContainerId:s,transition:c}=e;return!(!c||!r)&&((l===i||o!==a)&&(!!n||a!==o&&t===s))},y={duration:200,easing:"ease"},w="transform",x=i.Ks.Transition.toString({property:w,duration:0,easing:"linear"}),C={roleDescription:"sortable"};function D(e){let{animateLayoutChanges:t=m,attributes:n,disabled:a,data:l,getNewIndex:c=b,id:u,strategy:d,resizeObserverConfig:f,transition:h=y}=e;const{items:v,containerId:p,activeIndex:D,disabled:R,disableTransforms:E,sortedRects:S,overIndex:M,useDragOverlay:k,strategy:N}=(0,r.useContext)(g),I=function(e,t){var n,r;if("boolean"===typeof e)return{draggable:e,droppable:!1};return{draggable:null!=(n=null==e?void 0:e.draggable)?n:t.draggable,droppable:null!=(r=null==e?void 0:e.droppable)?r:t.droppable}}(a,R),T=v.indexOf(u),O=(0,r.useMemo)((()=>({sortable:{containerId:p,index:T,items:v},...l})),[p,l,T,v]),L=(0,r.useMemo)((()=>v.slice(v.indexOf(u))),[v,u]),{rect:A,node:z,isOver:B,setNodeRef:Y}=(0,o.zM)({id:u,data:O,disabled:I.droppable,resizeObserverConfig:{updateMeasurementsFor:L,...f}}),{active:W,activatorEvent:K,activeNodeRect:j,attributes:F,setNodeRef:P,listeners:U,isDragging:X,over:_,setActivatorNodeRef:G,transform:q}=(0,o.PM)({id:u,data:O,attributes:{...C,...n},disabled:I.draggable}),H=(0,i.jn)(Y,P),Q=Boolean(W),J=Q&&!E&&s(D)&&s(M),V=!k&&X,Z=V&&J?q:null,$=J?null!=Z?Z:(null!=d?d:N)({rects:S,activeNodeRect:j,activeIndex:D,overIndex:M,index:T}):null,ee=s(D)&&s(M)?c({id:u,items:v,activeIndex:D,overIndex:M}):T,te=null==W?void 0:W.id,ne=(0,r.useRef)({activeId:te,items:v,newIndex:ee,containerId:p}),re=v!==ne.current.items,oe=t({active:W,containerId:p,isDragging:X,isSorting:Q,id:u,index:T,items:v,newIndex:ne.current.newIndex,previousItems:ne.current.items,previousContainerId:ne.current.containerId,transition:h,wasDragging:null!=ne.current.activeId}),ie=function(e){let{disabled:t,index:n,node:a,rect:l}=e;const[s,c]=(0,r.useState)(null),u=(0,r.useRef)(n);return(0,i.Es)((()=>{if(!t&&n!==u.current&&a.current){const e=l.current;if(e){const t=(0,o.Sj)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&c(n)}}n!==u.current&&(u.current=n)}),[t,n,a,l]),(0,r.useEffect)((()=>{s&&c(null)}),[s]),s}({disabled:!oe,index:T,node:z,rect:A});return(0,r.useEffect)((()=>{Q&&ne.current.newIndex!==ee&&(ne.current.newIndex=ee),p!==ne.current.containerId&&(ne.current.containerId=p),v!==ne.current.items&&(ne.current.items=v)}),[Q,ee,p,v]),(0,r.useEffect)((()=>{if(te===ne.current.activeId)return;if(te&&!ne.current.activeId)return void(ne.current.activeId=te);const e=setTimeout((()=>{ne.current.activeId=te}),50);return()=>clearTimeout(e)}),[te]),{active:W,activeIndex:D,attributes:F,data:O,rect:A,index:T,newIndex:ee,items:v,isOver:B,isSorting:Q,isDragging:X,listeners:U,node:z,overIndex:M,over:_,setNodeRef:H,setActivatorNodeRef:G,setDroppableNodeRef:Y,setDraggableNodeRef:P,transform:null!=ie?ie:$,transition:function(){if(ie||re&&ne.current.newIndex===T)return x;if(V&&!(0,i.kx)(K)||!h)return;if(Q||oe)return i.Ks.Transition.toString({...h,property:w});return}()}}o.vL.Down,o.vL.Right,o.vL.Up,o.vL.Left},23585:(e,t,n)=>{n.d(t,{Mp:()=>Fe,vL:()=>V,AN:()=>le,fp:()=>C,Sj:()=>I,fF:()=>Ge,PM:()=>_e,zM:()=>Qe,MS:()=>g,FR:()=>p});var r=n(65043),o=n(97950),i=n(41024);const a={display:"none"};function l(e){let{id:t,value:n}=e;return r.createElement("div",{id:t,style:a},n)}function s(e){let{id:t,announcement:n,ariaLiveType:o="assertive"}=e;return r.createElement("div",{id:t,style:{position:"fixed",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":o,"aria-atomic":!0},n)}const c=(0,r.createContext)(null);const u={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},d={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function f(e){let{announcements:t=d,container:n,hiddenTextDescribedById:a,screenReaderInstructions:f=u}=e;const{announce:h,announcement:v}=function(){const[e,t]=(0,r.useState)("");return{announce:(0,r.useCallback)((e=>{null!=e&&t(e)}),[]),announcement:e}}(),g=(0,i.YG)("DndLiveRegion"),[p,b]=(0,r.useState)(!1);if((0,r.useEffect)((()=>{b(!0)}),[]),function(e){const t=(0,r.useContext)(c);(0,r.useEffect)((()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)}),[e,t])}((0,r.useMemo)((()=>({onDragStart(e){let{active:n}=e;h(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&h(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;h(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;h(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;h(t.onDragCancel({active:n,over:r}))}})),[h,t])),!p)return null;const m=r.createElement(r.Fragment,null,r.createElement(l,{id:a,value:f.draggable}),r.createElement(s,{id:g,announcement:v}));return n?(0,o.createPortal)(m,n):m}var h;function v(){}function g(e,t){return(0,r.useMemo)((()=>({sensor:e,options:null!=t?t:{}})),[e,t])}function p(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>[...t].filter((e=>null!=e))),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(h||(h={}));const b=Object.freeze({x:0,y:0});function m(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function y(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function w(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function x(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}const C=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=x(t,t.left,t.top),i=[];for(const a of r){const{id:e}=a,t=n.get(e);if(t){const n=m(x(t),o);i.push({id:e,data:{droppableContainer:a,value:n}})}}return i.sort(y)};function D(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),a=o-r,l=i-n;if(r<o&&n<i){const n=t.width*t.height,r=e.width*e.height,o=a*l;return Number((o/(n+r-o)).toFixed(4))}return 0}const R=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const i of r){const{id:e}=i,r=n.get(e);if(r){const n=D(r,t);n>0&&o.push({id:e,data:{droppableContainer:i,value:n}})}}return o.sort(w)};function E(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:b}function S(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const M=S(1);function k(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}const N={ignoreTransform:!1};function I(e,t){void 0===t&&(t=N);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=(0,i.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=k(t);if(!r)return e;const{scaleX:o,scaleY:i,x:a,y:l}=r,s=e.left-a-(1-o)*parseFloat(n),c=e.top-l-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),u=o?e.width/o:e.width,d=i?e.height/i:e.height;return{width:u,height:d,top:c,right:s+u,bottom:c+d,left:s}}(n,t,r))}const{top:r,left:o,width:a,height:l,bottom:s,right:c}=n;return{top:r,left:o,width:a,height:l,bottom:s,right:c}}function T(e){return I(e,{ignoreTransform:!0})}function O(e,t){const n=[];return e?function r(o){if(null!=t&&n.length>=t)return n;if(!o)return n;if((0,i.wz)(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!(0,i.sb)(o)||(0,i.xZ)(o))return n;if(n.includes(o))return n;const a=(0,i.zk)(e).getComputedStyle(o);return o!==e&&function(e,t){void 0===t&&(t=(0,i.zk)(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const r=t[e];return"string"===typeof r&&n.test(r)}))}(o,a)&&n.push(o),function(e,t){return void 0===t&&(t=(0,i.zk)(e).getComputedStyle(e)),"fixed"===t.position}(o,a)?n:r(o.parentNode)}(e):n}function L(e){const[t]=O(e,1);return null!=t?t:null}function A(e){return i.Sw&&e?(0,i.l6)(e)?e:(0,i.Ll)(e)?(0,i.wz)(e)||e===(0,i.TW)(e).scrollingElement?window:(0,i.sb)(e)?e:null:null:null}function z(e){return(0,i.l6)(e)?e.scrollX:e.scrollLeft}function B(e){return(0,i.l6)(e)?e.scrollY:e.scrollTop}function Y(e){return{x:z(e),y:B(e)}}var W;function K(e){return!(!i.Sw||!e)&&e===document.scrollingElement}function j(e){const t={x:0,y:0},n=K(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(W||(W={}));const F={x:.2,y:.2};function P(e,t,n,r,o){let{top:i,left:a,right:l,bottom:s}=n;void 0===r&&(r=10),void 0===o&&(o=F);const{isTop:c,isBottom:u,isLeft:d,isRight:f}=j(e),h={x:0,y:0},v={x:0,y:0},g=t.height*o.y,p=t.width*o.x;return!c&&i<=t.top+g?(h.y=W.Backward,v.y=r*Math.abs((t.top+g-i)/g)):!u&&s>=t.bottom-g&&(h.y=W.Forward,v.y=r*Math.abs((t.bottom-g-s)/g)),!f&&l>=t.right-p?(h.x=W.Forward,v.x=r*Math.abs((t.right-p-l)/p)):!d&&a<=t.left+p&&(h.x=W.Backward,v.x=r*Math.abs((t.left+p-a)/p)),{direction:h,speed:v}}function U(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function X(e){return e.reduce(((e,t)=>(0,i.WQ)(e,Y(t))),b)}function _(e,t){if(void 0===t&&(t=I),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);L(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const G=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+z(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+B(t)),0)}]];class q{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=O(t),r=X(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[o,i,a]of G)for(const e of i)Object.defineProperty(this,e,{get:()=>{const t=a(n),i=r[o]-t;return this.rect[e]+i},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class H{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Q(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"===typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var J,V;function Z(e){e.preventDefault()}function $(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(J||(J={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter"}(V||(V={}));const ee={start:[V.Space,V.Enter],cancel:[V.Esc],end:[V.Space,V.Enter]},te=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case V.Right:return{...n,x:n.x+25};case V.Left:return{...n,x:n.x-25};case V.Down:return{...n,y:n.y+25};case V.Up:return{...n,y:n.y-25}}};class ne{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new H((0,i.TW)(t)),this.windowListeners=new H((0,i.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(J.Resize,this.handleCancel),this.windowListeners.add(J.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(J.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&_(n),t(b)}handleKeyDown(e){if((0,i.kx)(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:o=ee,coordinateGetter:a=te,scrollBehavior:l="smooth"}=r,{code:s}=e;if(o.end.includes(s))return void this.handleEnd(e);if(o.cancel.includes(s))return void this.handleCancel(e);const{collisionRect:c}=n.current,u=c?{x:c.left,y:c.top}:b;this.referenceCoordinates||(this.referenceCoordinates=u);const d=a(e,{active:t,context:n.current,currentCoordinates:u});if(d){const t=(0,i.Re)(d,u),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code,{isTop:i,isRight:a,isLeft:s,isBottom:c,maxScroll:u,minScroll:f}=j(n),h=U(n),v={x:Math.min(o===V.Right?h.right-h.width/2:h.right,Math.max(o===V.Right?h.left:h.left+h.width/2,d.x)),y:Math.min(o===V.Down?h.bottom-h.height/2:h.bottom,Math.max(o===V.Down?h.top:h.top+h.height/2,d.y))},g=o===V.Right&&!a||o===V.Left&&!s,p=o===V.Down&&!c||o===V.Up&&!i;if(g&&v.x!==d.x){const e=n.scrollLeft+t.x,i=o===V.Right&&e<=u.x||o===V.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:l});r.x=i?n.scrollLeft-e:o===V.Right?n.scrollLeft-u.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:l});break}if(p&&v.y!==d.y){const e=n.scrollTop+t.y,i=o===V.Down&&e<=u.y||o===V.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:l});r.y=i?n.scrollTop-e:o===V.Down?n.scrollTop-u.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:l});break}}this.handleMove(e,(0,i.WQ)((0,i.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function re(e){return Boolean(e&&"distance"in e)}function oe(e){return Boolean(e&&"delay"in e)}ne.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=ee,onActivation:o}=t,{active:i}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==o||o({event:e.nativeEvent}),!0)}return!1}}];class ie{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=(0,i.zk)(e);return e instanceof t?e:(0,i.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:a}=o;this.props=e,this.events=t,this.document=(0,i.TW)(a),this.documentListeners=new H(this.document),this.listeners=new H(n),this.windowListeners=new H((0,i.zk)(a)),this.initialCoordinates=null!=(r=(0,i.e_)(o))?r:b,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(J.Resize,this.handleCancel),this.windowListeners.add(J.DragStart,Z),this.windowListeners.add(J.VisibilityChange,this.handleCancel),this.windowListeners.add(J.ContextMenu,Z),this.documentListeners.add(J.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(oe(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(re(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(J.Click,$,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(J.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this,{onMove:a,options:{activationConstraint:l}}=o;if(!r)return;const s=null!=(t=(0,i.e_)(e))?t:b,c=(0,i.Re)(r,s);if(!n&&l){if(re(l)){if(null!=l.tolerance&&Q(c,l.tolerance))return this.handleCancel();if(Q(c,l.distance))return this.handleStart()}return oe(l)&&Q(c,l.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),a(s)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===V.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const ae={move:{name:"pointermove"},end:{name:"pointerup"}};class le extends ie{constructor(e){const{event:t}=e,n=(0,i.TW)(t.target);super(e,ae,n)}}le.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button)&&(null==r||r({event:n}),!0)}}];const se={move:{name:"mousemove"},end:{name:"mouseup"}};var ce;!function(e){e[e.RightClick=2]="RightClick"}(ce||(ce={}));(class extends ie{constructor(e){super(e,se,(0,i.TW)(e.event.target))}}).activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==ce.RightClick&&(null==r||r({event:n}),!0)}}];const ue={move:{name:"touchmove"},end:{name:"touchend"}};var de,fe;function he(e){let{acceleration:t,activator:n=de.Pointer,canScroll:o,draggingRect:a,enabled:l,interval:s=5,order:c=fe.TreeOrder,pointerCoordinates:u,scrollableAncestors:d,scrollableAncestorRects:f,delta:h,threshold:v}=e;const g=function(e){let{delta:t,disabled:n}=e;const r=(0,i.ZC)(t);return(0,i.KG)((e=>{if(n||!r||!e)return ve;const o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[W.Backward]:e.x[W.Backward]||-1===o.x,[W.Forward]:e.x[W.Forward]||1===o.x},y:{[W.Backward]:e.y[W.Backward]||-1===o.y,[W.Forward]:e.y[W.Forward]||1===o.y}}}),[n,t,r])}({delta:h,disabled:!l}),[p,b]=(0,i.$$)(),m=(0,r.useRef)({x:0,y:0}),y=(0,r.useRef)({x:0,y:0}),w=(0,r.useMemo)((()=>{switch(n){case de.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case de.DraggableRect:return a}}),[n,a,u]),x=(0,r.useRef)(null),C=(0,r.useCallback)((()=>{const e=x.current;if(!e)return;const t=m.current.x*y.current.x,n=m.current.y*y.current.y;e.scrollBy(t,n)}),[]),D=(0,r.useMemo)((()=>c===fe.TreeOrder?[...d].reverse():d),[c,d]);(0,r.useEffect)((()=>{if(l&&d.length&&w){for(const e of D){if(!1===(null==o?void 0:o(e)))continue;const n=d.indexOf(e),r=f[n];if(!r)continue;const{direction:i,speed:a}=P(e,r,w,t,v);for(const e of["x","y"])g[e][i[e]]||(a[e]=0,i[e]=0);if(a.x>0||a.y>0)return b(),x.current=e,p(C,s),m.current=a,void(y.current=i)}m.current={x:0,y:0},y.current={x:0,y:0},b()}else b()}),[t,C,o,b,l,s,JSON.stringify(w),JSON.stringify(g),p,d,D,f,JSON.stringify(v)])}(class extends ie{constructor(e){super(e,ue)}static setup(){return window.addEventListener(ue.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ue.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return!(o.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(de||(de={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(fe||(fe={}));const ve={x:{[W.Backward]:!1,[W.Forward]:!1},y:{[W.Backward]:!1,[W.Forward]:!1}};var ge,pe;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(ge||(ge={})),function(e){e.Optimized="optimized"}(pe||(pe={}));const be=new Map;function me(e,t){return(0,i.KG)((n=>e?n||("function"===typeof t?t(e):e):null),[t,e])}function ye(e){let{callback:t,disabled:n}=e;const o=(0,i._q)(t),a=(0,r.useMemo)((()=>{if(n||"undefined"===typeof window||"undefined"===typeof window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(o)}),[n]);return(0,r.useEffect)((()=>()=>null==a?void 0:a.disconnect()),[a]),a}function we(e){return new q(I(e),e)}function xe(e,t,n){void 0===t&&(t=we);const[o,a]=(0,r.useReducer)((function(r){if(!e)return null;var o;if(!1===e.isConnected)return null!=(o=null!=r?r:n)?o:null;const i=t(e);if(JSON.stringify(r)===JSON.stringify(i))return r;return i}),null),l=function(e){let{callback:t,disabled:n}=e;const o=(0,i._q)(t),a=(0,r.useMemo)((()=>{if(n||"undefined"===typeof window||"undefined"===typeof window.MutationObserver)return;const{MutationObserver:e}=window;return new e(o)}),[o,n]);return(0,r.useEffect)((()=>()=>null==a?void 0:a.disconnect()),[a]),a}({callback(t){if(e)for(const n of t){const{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){a();break}}}}),s=ye({callback:a});return(0,i.Es)((()=>{a(),e?(null==s||s.observe(e),null==l||l.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==l||l.disconnect())}),[e]),o}const Ce=[];function De(e,t){void 0===t&&(t=[]);const n=(0,r.useRef)(null);return(0,r.useEffect)((()=>{n.current=null}),t),(0,r.useEffect)((()=>{const t=e!==b;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)}),[e]),n.current?(0,i.Re)(e,n.current):b}function Re(e){return(0,r.useMemo)((()=>e?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null),[e])}const Ee=[];function Se(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return(0,i.sb)(t)?t:e}const Me=[{sensor:le,options:{}},{sensor:ne,options:{}}],ke={current:{}},Ne={draggable:{measure:T},droppable:{measure:T,strategy:ge.WhileDragging,frequency:pe.Optimized},dragOverlay:{measure:I}};class Ie extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const Te={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new Ie,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:v},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Ne,measureDroppableContainers:v,windowRect:null,measuringScheduled:!1},Oe={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:v,draggableNodes:new Map,over:null,measureDroppableContainers:v},Le=(0,r.createContext)(Oe),Ae=(0,r.createContext)(Te);function ze(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new Ie}}}function Be(e,t){switch(t.type){case h.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case h.DragMove:return e.draggable.active?{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}}:e;case h.DragEnd:case h.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case h.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new Ie(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case h.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const a=new Ie(e.droppable.containers);return a.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:a}}}case h.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new Ie(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function Ye(e){let{disabled:t}=e;const{active:n,activatorEvent:o,draggableNodes:a}=(0,r.useContext)(Le),l=(0,i.ZC)(o),s=(0,i.ZC)(null==n?void 0:n.id);return(0,r.useEffect)((()=>{if(!t&&!o&&l&&null!=s){if(!(0,i.kx)(l))return;if(document.activeElement===l.target)return;const e=a.get(s);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=(0,i.ag)(e);if(t){t.focus();break}}}))}}),[o,t,a,s,l]),null}function We(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}const Ke=(0,r.createContext)({...b,scaleX:1,scaleY:1});var je;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(je||(je={}));const Fe=(0,r.memo)((function(e){var t,n,a,l;let{id:s,accessibility:u,autoScroll:d=!0,children:v,sensors:g=Me,collisionDetection:p=R,measuring:m,modifiers:y,...w}=e;const x=(0,r.useReducer)(Be,void 0,ze),[C,D]=x,[S,k]=function(){const[e]=(0,r.useState)((()=>new Set)),t=(0,r.useCallback)((t=>(e.add(t),()=>e.delete(t))),[e]);return[(0,r.useCallback)((t=>{let{type:n,event:r}=t;e.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)}))}),[e]),t]}(),[N,T]=(0,r.useState)(je.Uninitialized),z=N===je.Initialized,{draggable:{active:B,nodes:W,translate:j},droppable:{containers:F}}=C,P=B?W.get(B):null,U=(0,r.useRef)({initial:null,translated:null}),_=(0,r.useMemo)((()=>{var e;return null!=B?{id:B,data:null!=(e=null==P?void 0:P.data)?e:ke,rect:U}:null}),[B,P]),G=(0,r.useRef)(null),[H,Q]=(0,r.useState)(null),[J,V]=(0,r.useState)(null),Z=(0,i.YN)(w,Object.values(w)),$=(0,i.YG)("DndDescribedBy",s),ee=(0,r.useMemo)((()=>F.getEnabled()),[F]),te=(ne=m,(0,r.useMemo)((()=>({draggable:{...Ne.draggable,...null==ne?void 0:ne.draggable},droppable:{...Ne.droppable,...null==ne?void 0:ne.droppable},dragOverlay:{...Ne.dragOverlay,...null==ne?void 0:ne.dragOverlay}})),[null==ne?void 0:ne.draggable,null==ne?void 0:ne.droppable,null==ne?void 0:ne.dragOverlay]));var ne;const{droppableRects:re,measureDroppableContainers:oe,measuringScheduled:ie}=function(e,t){let{dragging:n,dependencies:o,config:a}=t;const[l,s]=(0,r.useState)(null),{frequency:c,measure:u,strategy:d}=a,f=(0,r.useRef)(e),h=function(){switch(d){case ge.Always:return!1;case ge.BeforeDragging:return n;default:return!n}}(),v=(0,i.YN)(h),g=(0,r.useCallback)((function(e){void 0===e&&(e=[]),v.current||s((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[v]),p=(0,r.useRef)(null),b=(0,i.KG)((t=>{if(h&&!n)return be;if(!t||t===be||f.current!==e||null!=l){const t=new Map;for(let n of e){if(!n)continue;if(l&&l.length>0&&!l.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current,r=e?new q(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t}),[e,l,n,h,u]);return(0,r.useEffect)((()=>{f.current=e}),[e]),(0,r.useEffect)((()=>{h||g()}),[n,h]),(0,r.useEffect)((()=>{l&&l.length>0&&s(null)}),[JSON.stringify(l)]),(0,r.useEffect)((()=>{h||"number"!==typeof c||null!==p.current||(p.current=setTimeout((()=>{g(),p.current=null}),c))}),[c,h,g,...o]),{droppableRects:b,measureDroppableContainers:g,measuringScheduled:null!=l}}(ee,{dragging:z,dependencies:[j.x,j.y],config:te.droppable}),ae=function(e,t){const n=null!==t?e.get(t):void 0,r=n?n.node.current:null;return(0,i.KG)((e=>{var n;return null===t?null:null!=(n=null!=r?r:e)?n:null}),[r,t])}(W,B),le=(0,r.useMemo)((()=>J?(0,i.e_)(J):null),[J]),se=function(){const e=!1===(null==H?void 0:H.autoScrollEnabled),t="object"===typeof d?!1===d.enabled:!1===d,n=z&&!e&&!t;if("object"===typeof d)return{...d,enabled:n};return{enabled:n}}(),ce=function(e,t){return me(e,t)}(ae,te.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:o,config:a=!0}=e;const l=(0,r.useRef)(!1),{x:s,y:c}="boolean"===typeof a?{x:a,y:a}:a;(0,i.Es)((()=>{if(!s&&!c||!t)return void(l.current=!1);if(l.current||!o)return;const e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;const r=E(n(e),o);if(s||(r.x=0),c||(r.y=0),l.current=!0,Math.abs(r.x)>0||Math.abs(r.y)>0){const t=L(e);t&&t.scrollBy({top:r.y,left:r.x})}}),[t,s,c,o,n])}({activeNode:B?W.get(B):null,config:se.layoutShiftCompensation,initialRect:ce,measure:te.draggable.measure});const ue=xe(ae,te.draggable.measure,ce),de=xe(ae?ae.parentElement:null),fe=(0,r.useRef)({activatorEvent:null,active:null,activeNode:ae,collisionRect:null,collisions:null,droppableRects:re,draggableNodes:W,draggingNode:null,draggingNodeRect:null,droppableContainers:F,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ve=F.getNodeFor(null==(t=fe.current.over)?void 0:t.id),pe=function(e){let{measure:t}=e;const[n,o]=(0,r.useState)(null),a=ye({callback:(0,r.useCallback)((e=>{for(const{target:n}of e)if((0,i.sb)(n)){o((e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r}));break}}),[t])}),l=(0,r.useCallback)((e=>{const n=Se(e);null==a||a.disconnect(),n&&(null==a||a.observe(n)),o(n?t(n):null)}),[t,a]),[s,c]=(0,i.lk)(l);return(0,r.useMemo)((()=>({nodeRef:s,rect:n,setRef:c})),[n,s,c])}({measure:te.dragOverlay.measure}),we=null!=(n=pe.nodeRef.current)?n:ae,Ie=z?null!=(a=pe.rect)?a:ue:null,Te=Boolean(pe.nodeRef.current&&pe.rect),Oe=E(Fe=Te?null:ue,me(Fe));var Fe;const Pe=Re(we?(0,i.zk)(we):null),Ue=function(e){const t=(0,r.useRef)(e),n=(0,i.KG)((n=>e?n&&n!==Ce&&e&&t.current&&e.parentNode===t.current.parentNode?n:O(e):Ce),[e]);return(0,r.useEffect)((()=>{t.current=e}),[e]),n}(z?null!=ve?ve:ae:null),Xe=function(e,t){void 0===t&&(t=I);const[n]=e,o=Re(n?(0,i.zk)(n):null),[a,l]=(0,r.useReducer)((function(){return e.length?e.map((e=>K(e)?o:new q(t(e),e))):Ee}),Ee),s=ye({callback:l});return e.length>0&&a===Ee&&l(),(0,i.Es)((()=>{e.length?e.forEach((e=>null==s?void 0:s.observe(e))):(null==s||s.disconnect(),l())}),[e]),a}(Ue),_e=We(y,{transform:{x:j.x-Oe.x,y:j.y-Oe.y,scaleX:1,scaleY:1},activatorEvent:J,active:_,activeNodeRect:ue,containerNodeRect:de,draggingNodeRect:Ie,over:fe.current.over,overlayNodeRect:pe.rect,scrollableAncestors:Ue,scrollableAncestorRects:Xe,windowRect:Pe}),Ge=le?(0,i.WQ)(le,j):null,qe=function(e){const[t,n]=(0,r.useState)(null),o=(0,r.useRef)(e),a=(0,r.useCallback)((e=>{const t=A(e.target);t&&n((e=>e?(e.set(t,Y(t)),new Map(e)):null))}),[]);return(0,r.useEffect)((()=>{const t=o.current;if(e!==t){r(t);const i=e.map((e=>{const t=A(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,Y(t)]):null})).filter((e=>null!=e));n(i.length?new Map(i):null),o.current=e}return()=>{r(e),r(t)};function r(e){e.forEach((e=>{const t=A(e);null==t||t.removeEventListener("scroll",a)}))}}),[a,e]),(0,r.useMemo)((()=>e.length?t?Array.from(t.values()).reduce(((e,t)=>(0,i.WQ)(e,t)),b):X(e):b),[e,t])}(Ue),He=De(qe),Qe=De(qe,[ue]),Je=(0,i.WQ)(_e,He),Ve=Ie?M(Ie,_e):null,Ze=_&&Ve?p({active:_,collisionRect:Ve,droppableRects:re,droppableContainers:ee,pointerCoordinates:Ge}):null,$e=function(e,t){if(!e||0===e.length)return null;const[n]=e;return t?n[t]:n}(Ze,"id"),[et,tt]=(0,r.useState)(null),nt=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(Te?_e:(0,i.WQ)(_e,Qe),null!=(l=null==et?void 0:et.rect)?l:null,ue),rt=(0,r.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(null==G.current)return;const i=W.get(G.current);if(!i)return;const a=e.nativeEvent,l=new n({active:G.current,activeNode:i,event:a,options:r,context:fe,onStart(e){const t=G.current;if(null==t)return;const n=W.get(t);if(!n)return;const{onDragStart:r}=Z.current,i={active:{id:t,data:n.data,rect:U}};(0,o.unstable_batchedUpdates)((()=>{null==r||r(i),T(je.Initializing),D({type:h.DragStart,initialCoordinates:e,active:t}),S({type:"onDragStart",event:i})}))},onMove(e){D({type:h.DragMove,coordinates:e})},onEnd:s(h.DragEnd),onCancel:s(h.DragCancel)});function s(e){return async function(){const{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=fe.current;let l=null;if(t&&i){const{cancelDrop:o}=Z.current;if(l={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===h.DragEnd&&"function"===typeof o){await Promise.resolve(o(l))&&(e=h.DragCancel)}}G.current=null,(0,o.unstable_batchedUpdates)((()=>{D({type:e}),T(je.Uninitialized),tt(null),Q(null),V(null);const t=e===h.DragEnd?"onDragEnd":"onDragCancel";if(l){const e=Z.current[t];null==e||e(l),S({type:t,event:l})}}))}}(0,o.unstable_batchedUpdates)((()=>{Q(l),V(e.nativeEvent)}))}),[W]),ot=(0,r.useCallback)(((e,t)=>(n,r)=>{const o=n.nativeEvent,i=W.get(r);if(null!==G.current||!i||o.dndKit||o.defaultPrevented)return;const a={active:i};!0===e(n,t.options,a)&&(o.dndKit={capturedBy:t.sensor},G.current=r,rt(n,t))}),[W,rt]),it=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:r}=n;return[...e,...r.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})))]}),[])),[e,t])}(g,ot);!function(e){(0,r.useEffect)((()=>{if(!i.Sw)return;const t=e.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const e of t)null==e||e()}}),e.map((e=>{let{sensor:t}=e;return t})))}(g),(0,i.Es)((()=>{ue&&N===je.Initializing&&T(je.Initialized)}),[ue,N]),(0,r.useEffect)((()=>{const{onDragMove:e}=Z.current,{active:t,activatorEvent:n,collisions:r,over:i}=fe.current;if(!t||!n)return;const a={active:t,activatorEvent:n,collisions:r,delta:{x:Je.x,y:Je.y},over:i};(0,o.unstable_batchedUpdates)((()=>{null==e||e(a),S({type:"onDragMove",event:a})}))}),[Je.x,Je.y]),(0,r.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:i}=fe.current;if(!e||null==G.current||!t||!i)return;const{onDragOver:a}=Z.current,l=r.get($e),s=l&&l.rect.current?{id:l.id,rect:l.rect.current,data:l.data,disabled:l.disabled}:null,c={active:e,activatorEvent:t,collisions:n,delta:{x:i.x,y:i.y},over:s};(0,o.unstable_batchedUpdates)((()=>{tt(s),null==a||a(c),S({type:"onDragOver",event:c})}))}),[$e]),(0,i.Es)((()=>{fe.current={activatorEvent:J,active:_,activeNode:ae,collisionRect:Ve,collisions:Ze,droppableRects:re,draggableNodes:W,draggingNode:we,draggingNodeRect:Ie,droppableContainers:F,over:et,scrollableAncestors:Ue,scrollAdjustedTranslate:Je},U.current={initial:Ie,translated:Ve}}),[_,ae,Ze,Ve,W,we,Ie,re,F,et,Ue,Je]),he({...se,delta:j,draggingRect:Ve,pointerCoordinates:Ge,scrollableAncestors:Ue,scrollableAncestorRects:Xe});const at=(0,r.useMemo)((()=>({active:_,activeNode:ae,activeNodeRect:ue,activatorEvent:J,collisions:Ze,containerNodeRect:de,dragOverlay:pe,draggableNodes:W,droppableContainers:F,droppableRects:re,over:et,measureDroppableContainers:oe,scrollableAncestors:Ue,scrollableAncestorRects:Xe,measuringConfiguration:te,measuringScheduled:ie,windowRect:Pe})),[_,ae,ue,J,Ze,de,pe,W,F,re,et,oe,Ue,Xe,te,ie,Pe]),lt=(0,r.useMemo)((()=>({activatorEvent:J,activators:it,active:_,activeNodeRect:ue,ariaDescribedById:{draggable:$},dispatch:D,draggableNodes:W,over:et,measureDroppableContainers:oe})),[J,it,_,ue,D,$,W,et,oe]);return r.createElement(c.Provider,{value:k},r.createElement(Le.Provider,{value:lt},r.createElement(Ae.Provider,{value:at},r.createElement(Ke.Provider,{value:nt},v)),r.createElement(Ye,{disabled:!1===(null==u?void 0:u.restoreFocus)})),r.createElement(f,{...u,hiddenTextDescribedById:$}))})),Pe=(0,r.createContext)(null),Ue="button",Xe="Droppable";function _e(e){let{id:t,data:n,disabled:o=!1,attributes:a}=e;const l=(0,i.YG)(Xe),{activators:s,activatorEvent:c,active:u,activeNodeRect:d,ariaDescribedById:f,draggableNodes:h,over:v}=(0,r.useContext)(Le),{role:g=Ue,roleDescription:p="draggable",tabIndex:b=0}=null!=a?a:{},m=(null==u?void 0:u.id)===t,y=(0,r.useContext)(m?Ke:Pe),[w,x]=(0,i.lk)(),[C,D]=(0,i.lk)(),R=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:r,handler:o}=n;return e[r]=e=>{o(e,t)},e}),{})),[e,t])}(s,t),E=(0,i.YN)(n);(0,i.Es)((()=>(h.set(t,{id:t,key:l,node:w,activatorNode:C,data:E}),()=>{const e=h.get(t);e&&e.key===l&&h.delete(t)})),[h,t]);return{active:u,activatorEvent:c,activeNodeRect:d,attributes:(0,r.useMemo)((()=>({role:g,tabIndex:b,"aria-disabled":o,"aria-pressed":!(!m||g!==Ue)||void 0,"aria-roledescription":p,"aria-describedby":f.draggable})),[o,g,b,m,p,f.draggable]),isDragging:m,listeners:o?void 0:R,node:w,over:v,setNodeRef:x,setActivatorNodeRef:D,transform:y}}function Ge(){return(0,r.useContext)(Ae)}const qe="Droppable",He={timeout:25};function Qe(e){let{data:t,disabled:n=!1,id:o,resizeObserverConfig:a}=e;const l=(0,i.YG)(qe),{active:s,dispatch:c,over:u,measureDroppableContainers:d}=(0,r.useContext)(Le),f=(0,r.useRef)({disabled:n}),v=(0,r.useRef)(!1),g=(0,r.useRef)(null),p=(0,r.useRef)(null),{disabled:b,updateMeasurementsFor:m,timeout:y}={...He,...a},w=(0,i.YN)(null!=m?m:o),x=ye({callback:(0,r.useCallback)((()=>{v.current?(null!=p.current&&clearTimeout(p.current),p.current=setTimeout((()=>{d(Array.isArray(w.current)?w.current:[w.current]),p.current=null}),y)):v.current=!0}),[y]),disabled:b||!s}),C=(0,r.useCallback)(((e,t)=>{x&&(t&&(x.unobserve(t),v.current=!1),e&&x.observe(e))}),[x]),[D,R]=(0,i.lk)(C),E=(0,i.YN)(t);return(0,r.useEffect)((()=>{x&&D.current&&(x.disconnect(),v.current=!1,x.observe(D.current))}),[D,x]),(0,i.Es)((()=>(c({type:h.RegisterDroppable,element:{id:o,key:l,disabled:n,node:D,rect:g,data:E}}),()=>c({type:h.UnregisterDroppable,key:l,id:o}))),[o]),(0,r.useEffect)((()=>{n!==f.current.disabled&&(c({type:h.SetDroppableDisabled,id:o,key:l,disabled:n}),f.current.disabled=n)}),[o,l,n,c]),{active:s,rect:g,isOver:(null==u?void 0:u.id)===o,node:D,over:u,setNodeRef:R}}},41024:(e,t,n)=>{n.d(t,{$$:()=>g,Es:()=>h,KG:()=>b,Ks:()=>M,Ll:()=>l,Re:()=>R,Sw:()=>i,TW:()=>f,WQ:()=>D,YG:()=>x,YN:()=>p,ZC:()=>y,_q:()=>v,ag:()=>N,e_:()=>S,jn:()=>o,kx:()=>E,l6:()=>a,lk:()=>m,sb:()=>u,wz:()=>c,xZ:()=>d,zk:()=>s});var r=n(65043);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}const i="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;function a(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function l(e){return"nodeType"in e}function s(e){var t,n;return e?a(e)?e:l(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function c(e){const{Document:t}=s(e);return e instanceof t}function u(e){return!a(e)&&e instanceof s(e).HTMLElement}function d(e){return e instanceof s(e).SVGElement}function f(e){return e?a(e)?e.document:l(e)?c(e)?e:u(e)||d(e)?e.ownerDocument:document:document:document}const h=i?r.useLayoutEffect:r.useEffect;function v(e){const t=(0,r.useRef)(e);return h((()=>{t.current=e})),(0,r.useCallback)((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function g(){const e=(0,r.useRef)(null);return[(0,r.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]),(0,r.useCallback)((()=>{null!==e.current&&(clearInterval(e.current),e.current=null)}),[])]}function p(e,t){void 0===t&&(t=[e]);const n=(0,r.useRef)(e);return h((()=>{n.current!==e&&(n.current=e)}),t),n}function b(e,t){const n=(0,r.useRef)();return(0,r.useMemo)((()=>{const t=e(n.current);return n.current=t,t}),[...t])}function m(e){const t=v(e),n=(0,r.useRef)(null),o=(0,r.useCallback)((e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e}),[]);return[n,o]}function y(e){const t=(0,r.useRef)();return(0,r.useEffect)((()=>{t.current=e}),[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)((()=>{if(t)return t;const n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n}),[e,t])}function C(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[o,i]of r){const n=t[o];null!=n&&(t[o]=n+e*i)}return t}),{...t})}}const D=C(1),R=C(-1);function E(e){if(!e)return!1;const{KeyboardEvent:t}=s(e.target);return t&&e instanceof t}function S(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=s(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const M=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[M.Translate.toString(e),M.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),k="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function N(e){return e.matches(k)?e:e.querySelector(k)}}}]);
//# sourceMappingURL=1648.fdaa7f63.chunk.js.map