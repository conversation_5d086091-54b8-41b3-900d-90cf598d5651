"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[1785,3997],{22854:(e,t,n)=>{n.r(t),n.d(t,{default:()=>b});var o=n(65043),a=n(80231),r=n(93950),d=n.n(r),i=n(80077),c=n(12097),l=n(58168);const s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M820 436h-40c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8zm32-104H732V120c0-4.4-3.6-8-8-8H300c-4.4 0-8 3.6-8 8v212H172c-44.2 0-80 35.8-80 80v328c0 17.7 14.3 32 32 32h168v132c0 4.4 3.6 8 8 8h424c4.4 0 8-3.6 8-8V772h168c17.7 0 32-14.3 32-32V412c0-44.2-35.8-80-80-80zM360 180h304v152H360V180zm304 664H360V568h304v276zm200-140H732V500H292v204H160V412c0-6.6 5.4-12 12-12h680c6.6 0 12 5.4 12 12v292z"}}]},name:"printer",theme:"outlined"};var u=n(22172),f=function(e,t){return o.createElement(u.A,(0,l.A)({},e,{ref:t,icon:s}))};const p=o.forwardRef(f);n(92676);var v=n(81143);n(68374);const m=v.Ay.div`
    display: flex;
    justify-content:center;
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-color: #fff;
    padding: 10px;    

   
    .pdf-layout {

        display: flex;
    }
    .func-layout {
        padding: 10px;
        display: flex;
        align-items: flex-start;
        ${e=>e.isOpenExperiment&&"\n            pointer-events: none;\n        "}
    }
`,h=v.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }

`;var x=n(70579);const g=e=>{let{domId:t,layoutConfig:n}=e;return(0,x.jsx)(h,{children:(0,x.jsx)(a.A,{domId:t,layoutConfig:n})})},y=(e,t)=>{var n;let{id:a,data:r,layoutConfig:l,onPrintCallback:s}=e;const u=(0,o.useRef)(),f=(0,i.d4)((e=>e.subTask.openExperiment)),v=(0,i.d4)((e=>e.template.exportList)),{pdf_config:h,export_name:y}=null!==(n=null===v||void 0===v?void 0:v.find((e=>"pdf"===(null===e||void 0===e?void 0:e.export_type)&&1===(null===e||void 0===e?void 0:e.default_flag))))&&void 0!==n?n:{pdf_config:[],export_name:""},b=(0,o.useCallback)(d()((e=>w(e)),1500),[]),w=()=>{u.current&&u.current.downloadPdf()};return(0,o.useImperativeHandle)(t,(()=>({downloadPdf:w}))),(0,x.jsxs)(m,{isOpenExperiment:f,children:[(0,x.jsx)("div",{className:"func-layout",children:(0,x.jsx)(p,{onClick:b})}),(0,x.jsx)(c.A,{parentId:a,ref:u,layoutData:h,config:{export_name:y},onPrintCallback:s}),a&&(0,x.jsx)(g,{domId:a,layoutConfig:l})]})},b=(0,o.forwardRef)(y)},39282:(e,t,n)=>{n.r(t),n.d(t,{default:()=>v});var o=n(65043),a=n(22854),r=n(80077),d=n(45333),i=n(50540),c=n(65913),l=n(74117),s=n(81143);n(18650),n(68374);const u=s.Ay.div`
    position: absolute;
    left: -9999px;

`;var f=n(70579);const p=()=>{var e;const{t:t}=(0,l.Bd)(),n=(0,r.wA)(),s=(0,o.useRef)(),[p,v]=(0,o.useState)(!1),[m,h]=(0,o.useState)(),{subTaskShortcut:x}=(0,r.d4)((e=>e.subTask));(0,o.useEffect)((()=>{var e,t;if(null!==(e=null===x||void 0===x||null===(t=x.UIParams)||void 0===t?void 0:t.shortcutCode)&&void 0!==e&&e){const{shortcutCode:e}=x.UIParams;e===d.tI.\u6253\u5370\u62a5\u544a&&(g(),v(!0),h("\u6253\u5370\u4e2d"),y()),n({type:i.MY,param:null})}}),[null===x||void 0===x||null===(e=x.UIParams)||void 0===e?void 0:e.shortcutCode]);const g=()=>{if(!document.getElementById("print-iframe")){const e=document.createElement("iframe");e.id="print-iframe",e.style.position="absolute",e.style.width="0px",e.style.height="0px",e.style.border="none",document.body.appendChild(e)}},y=()=>{setTimeout((()=>{s.current&&s.current.downloadPdf()}),2e3)};return(0,f.jsxs)(u,{children:[m&&(0,f.jsx)(c.A,{text:t(m)}),p&&(0,f.jsx)(a.default,{ref:s,onPrintCallback:e=>{let{isComplete:t=!1,page:n=0,total:o=0}=e;t?setTimeout((()=>{v(!1),h("")}),3e3):h(`${n}/${o}\u9875`)}})]})},v=(0,o.memo)(p)},46959:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(58168),a=n(65043);const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"};var d=n(22172),i=function(e,t){return a.createElement(d.A,(0,o.A)({},e,{ref:t,icon:r}))};const c=a.forwardRef(i)},69312:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(58168),a=n(65043);const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"};var d=n(22172),i=function(e,t){return a.createElement(d.A,(0,o.A)({},e,{ref:t,icon:r}))};const c=a.forwardRef(i)}}]);
//# sourceMappingURL=1785.90f302a0.chunk.js.map