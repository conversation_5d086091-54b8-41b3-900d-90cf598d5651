{"version": 3, "file": "static/js/1937.d828dd22.chunk.js", "mappings": "4XAIO,MAAMA,EAAuBC,EAAAA,GAAOC,GAAG;;;;;;;;iBCY9C,MAoDA,EApDoBC,IAEb,IAFc,KACjBC,EAAI,KAAEC,EAAI,KAAEC,EAAI,QAAEC,GACrBJ,EACG,MAAM,EAAEK,IAAMC,EAAAA,EAAAA,MACRC,GAAsBC,EAAAA,EAAAA,MACrBC,EAAOC,IAAYC,EAAAA,EAAAA,aAE1BC,EAAAA,EAAAA,YAAU,KACFX,GACAS,EAAST,EACb,GACD,CAACA,IAMJ,OACIY,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACIF,EAAAA,EAAAA,KAACG,EAAAA,EAAM,CACHd,KAAMA,EACNe,MAAOZ,EAAE,gBACTa,MAAM,OACNC,SAAUA,IAAMf,GAAQ,GACxBgB,OAAQ,KAAKL,UAEbM,EAAAA,EAAAA,MAACxB,EAAoB,CAAAkB,SAAA,EACjBM,EAAAA,EAAAA,MAAA,OAAAN,SAAA,EACIF,EAAAA,EAAAA,KAAA,QAAAE,SAAOV,EAAE,8BAAe,UAExBQ,EAAAA,EAAAA,KAACS,EAAAA,EAAM,CACHC,QAAShB,EACTE,MAAOA,EACPe,MAAO,CAAEN,MAAO,QAChBO,WAAY,CAAEC,MAAO,OAAQjB,MAAO,QACpCkB,SAAWC,GAAMlB,EAASkB,SAGlCf,EAAAA,EAAAA,KAAA,OAAKgB,UAAU,gBAAed,UAC1BM,EAAAA,EAAAA,MAACS,EAAAA,EAAK,CAACC,UAAU,WAAUhB,SAAA,EACvBF,EAAAA,EAAAA,KAACmB,EAAAA,EAAO,CAACC,OAAK,EAACC,QAASA,KA1B5C/B,EAAKM,EA0BwD,EAAAM,SAAEV,EAAE,mBAC7CQ,EAAAA,EAAAA,KAACmB,EAAAA,EAAO,CAACC,OAAK,EAACC,QAASA,IAAM9B,GAAQ,GAAOW,SAAEV,EAAE,6BAOlE,EC7DE8B,EAAiBrC,EAAAA,GAAOC,GAAG;;;;;;;kBAOtBqC,EAAAA,GAAMC;EAEXC,EAAuBxC,EAAAA,GAAOC,GAAG;;;;;;;;;ECYxCwC,EAAwBvC,IAEvB,IAFwB,MAC3BwC,EAAK,aAAEC,EAAY,aAAEC,EAAY,eAAEC,EAAc,mBAAEC,EAAkB,gBAAEC,GAC1E7C,EACG,MAAM,EAAEK,EAAC,KAAEyC,IAASxC,EAAAA,EAAAA,MACpB,OACIO,EAAAA,EAAAA,KAACyB,EAAoB,CAAAvB,UACjBM,EAAAA,EAAAA,MAAC0B,EAAAA,EAAW,CACRP,MAAOA,EACPE,aAAcA,EAAa3B,SAAA,EAE3BF,EAAAA,EAAAA,KAAA,OACIgB,UAAU,iBACVK,QAASA,KACLO,GAAa,EAAK,EACpB1B,SAEDV,EAAE,mBAEPQ,EAAAA,EAAAA,KAAA,OACIgB,UAAW,mBAAkBgB,EAAkB,WAAa,IAC5DX,QAASA,KACLU,GAAoB,EACtB7B,SAEgCV,EAAhCsC,EAAkC,6CAAf,oDAIV,EA+R/B,EA3RcK,IAEP,IAADC,EAAAC,EAAA,IAFS,GACXC,EAAE,KAAElD,EAAI,UAAEmD,GAAY,EAAK,KAAEC,EAAI,aAAEX,GACtCM,EACG,MAAM,EAAE3C,EAAC,KAAEyC,IAASxC,EAAAA,EAAAA,MAEdgD,GAAYC,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,SAASH,YAChDI,GAAaH,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,SAASC,aACjDC,GAAYJ,EAAAA,EAAAA,KAAYC,GAASA,EAAMI,QAAQD,YAC/CE,GAAgBN,EAAAA,EAAAA,KAAYC,GAASA,EAAMM,QAAQD,gBACnDE,GAAeR,EAAAA,EAAAA,KAAYC,GAASA,EAAMM,QAAQC,eAClDxD,GAAsBC,EAAAA,EAAAA,MACtB,oBAAEwD,IAAwBC,EAAAA,EAAAA,MAC1B,YACFC,EAAW,mBACXC,EAAkB,WAClBC,EACAC,aAAcC,IACdC,EAAAA,EAAAA,MACE,WAAEC,IAAeC,EAAAA,EAAAA,MAEhBC,EAAWC,IAAgBhE,EAAAA,EAAAA,aAC3BiE,EAAUC,IAAelE,EAAAA,EAAAA,UAAS,KAClCmE,EAASC,IAAcpE,EAAAA,EAAAA,WAAS,IAChCqE,EAAUC,IAAetE,EAAAA,EAAAA,WAAS,IAClCgC,EAAgBuC,IAAqBvE,EAAAA,EAAAA,WAASyC,KAAsBkB,IACpEa,EAAaC,IAAkBzE,EAAAA,EAAAA,WAAS,IACxC0E,EAAYC,IAAiB3E,EAAAA,EAAAA,YAC9B4E,GAAYC,EAAAA,EAAAA,WAElB5E,EAAAA,EAAAA,YAAU,KACN,GAAQ,OAAJyC,QAAI,IAAJA,GAAAA,EAAMoC,UAAW,CACjB,MAAMC,GAAaC,EAAAA,EAAAA,IAASjC,EAAY,YAAiB,OAAJL,QAAI,IAAJA,OAAI,EAAJA,EAAMoC,WAC3DH,EAAcI,EAClB,CACA,MAAO,KACHJ,GAAe,CAClB,GACF,CAAC5B,KAEJ9C,EAAAA,EAAAA,YAAU,KACDwC,GACD8B,IAAoBZ,EACxB,GACD,CAACA,KAEJ1D,EAAAA,EAAAA,YAAU,MACDiD,GAAiBE,GAAgBpB,GAClCiD,GACJ,GACD,CAAC/B,EACAE,EACApB,IAEJ,MAAMiD,GAAYC,EAAAA,EAAAA,aACdC,KAAS,KACLC,IAAkB,GACnB,KACH,KAGJnF,EAAAA,EAAAA,YAAU,KACNoF,KACO,KACHnB,EAAY,IACZF,GAAc,IAEnB,CAAC1E,EACA0D,EACAhB,EACAkB,EACAP,KAGJ1C,EAAAA,EAAAA,YAAU,KAAO,IAADqF,EACZ,IAAKpC,GAA2B,OAAVwB,QAAU,IAAVA,GAAuB,QAAbY,EAAVZ,EAAYa,mBAAW,IAAAD,GAAvBA,EAAyBE,WAAY,CAAC,IAADC,EAAAC,EACvD,MAAMC,EAA2B,OAAnB/F,QAAmB,IAAnBA,GAA+E,QAA5D6F,EAAnB7F,EAAqBgG,MAAKC,IAAC,IAAAC,EAAA,OAAK,OAADD,QAAC,IAADA,OAAC,EAADA,EAAGE,SAAmB,OAAVrB,QAAU,IAAVA,GAAuB,QAAboB,EAAVpB,EAAYa,mBAAW,IAAAO,OAAb,EAAVA,EAAyBN,WAAW,eAAAC,GAAa,QAAbC,EAA/ED,EAAiFO,mBAAW,IAAAN,OAAzE,EAAnBA,EAA8F5F,MAC5GmG,EAAyBN,EAC7B,IACD,CAAW,OAAVjB,QAAU,IAAVA,GAAuB,QAAbpC,EAAVoC,EAAYa,mBAAW,IAAAjD,OAAb,EAAVA,EAAyBkD,WACzB5F,EACAsD,IAGJ,MAAM+C,EAA2BC,UAC7B,IACSlE,GACDmE,YAAW,KAAO,IAADC,EACI,QAAjBA,EAAAxB,EAAUyB,eAAO,IAAAD,GAAjBA,EAAmBE,OAAOX,EAAM,GACjC,IAEX,CAAE,MAAOY,GACLC,QAAQC,IAAIF,EAChB,GAWElB,GAAOa,UACT,GAAIlE,EACA0E,UAGJ,GAAIpH,EAAJ,CACI,MAAMqH,QAAYpD,EAAYjE,GAC1BqH,EACA3C,EAAa2C,GAEbzC,EAAYxE,EAAE,wCAGtB,MAEA,GAAIsD,EAAW,CACX,MAAM4D,EAAQjE,EAAUiD,MAAKC,GAAKA,EAAEgB,cAAgB7D,EAAU+C,OAC1Da,IAAU1D,GACVoB,GAAY,GACZF,GAAW,GACX0C,GAASF,IACFjD,EACP+C,KAEAxC,EAAYxE,EAAE,kCAEtB,GAGEgH,GAAcR,UAChB,IACI,MAAMxC,QAAqBF,IAC3Be,GAAkB,GAClBL,EAAY,IACZF,EAAaN,GACbU,GAAW,GACXE,GAAY,EAChB,CAAE,MAAOiC,GACc,oBAAfA,EAAMQ,OACN7C,EAAYxE,EAAE6G,EAAMS,UACpBzC,GAAkB,GAE1B,GAGEuC,GAAWZ,UACb,GAAIU,EAAO,CACP,MAAMD,QAAYpD,EAAYqD,GAC1BD,EACA3C,EAAa2C,GAEbzC,EAAYxE,EAAE,wCAEtB,GAkCEuH,GAAmB9B,KAASe,MAAOgB,EAAOrE,KAC5CQ,EAAoB,CAChB0C,KAAMmB,EAAMnB,KACZC,YAAa,CACTlG,MAAOqH,OAAOtE,EAAMuE,iBAGtBC,EAAAA,EAAAA,KAAoB,CACtBC,WAAY,CAAC,CACT9E,GAAI0E,EAAM1E,GACVwD,YAAa,IACNkB,EAAMlB,YACTlG,MAAOqH,OAAOtE,EAAMuE,YAG9B,GACH,MAEGG,IAAwBrC,EAAAA,EAAAA,aAAY+B,GAAkB,IAUtD7B,GAAmBA,KACrB,GAAIR,EAAUyB,SAAWzB,EAAUyB,QAAQmB,QAAS,CACjC5C,EAAUyB,QAAQmB,QAAQC,iBAAiB,SAGnDC,SAAQd,IACPA,EAAMe,YACNf,EAAMe,UAAY,KACtB,GAER,MACInB,QAAQD,MAAM,gDAElB9C,IACAc,GAAkB,EAAM,EAW5B,OACI7D,EAAAA,EAAAA,MAACc,EAAc,CAAApB,SAAA,CACV6D,GACK/D,EAAAA,EAAAA,KAAA,OAAAE,SAAM6D,KAEJ/D,EAAAA,EAAAA,KAAC0H,IAAW,CACRC,IAAKjD,EACLkD,IAAK/D,EACLxD,MAAM,MACNwH,OAAO,MACPC,OA3CDC,KACf7D,GAAW,EAAK,EA2CA8D,QAxCAC,KAChB/D,GAAW,EAAM,EAwCDC,SAAUA,EACVF,QAASA,EACTiE,WA5EGlC,UACnB,IAAgB,IAAZ/B,IAAsBjB,EAAe,CAAC,IAADmF,EACrC,MAAMnB,EAAQtH,EAAoBgG,MAAKC,IAAC,IAAAyC,EAAA,OAAIzC,EAAEE,QAAmB,OAAVrB,QAAU,IAAVA,GAAuB,QAAb4D,EAAV5D,EAAYa,mBAAW,IAAA+C,OAAb,EAAVA,EAAyB9C,WAAW,IACvF0B,IAAiC,QAAxBmB,EAACnB,EAAMlB,YAAYlG,aAAK,IAAAuI,EAAAA,EAAI,GAAGE,QAAQ,KAAOpB,OAAOtE,EAAMuE,QAAQmB,QAAQ,IACpFhB,GAAsBL,EAAOrE,EAErC,MA0EI3C,EAAAA,EAAAA,KAAC0B,EAAqB,CAClBC,MAAOW,EACPT,aAAcA,EACdD,aAzGSA,KACjB2C,GAAe,EAAK,EAyGZzC,eAAgBA,EAChBC,mBA/BeA,KACnBD,EACAoD,KAEAsB,IACJ,EA2BQxE,kBAAmBgB,IAEtBsB,IAEItE,EAAAA,EAAAA,KAACsI,EAAW,CACRjJ,KAAMiF,EACN/E,QAASgF,EACTnF,KAAgB,OAAVoF,QAAU,IAAVA,GAAuB,QAAbnC,EAAVmC,EAAYa,mBAAW,IAAAhD,OAAb,EAAVA,EAAyBiD,WAC/BhG,KAhHA0G,UAEb,GAAIxB,EAAY,OACY+D,EAAAA,EAAAA,KAAW,CAC/B3D,UAAqB,OAAVJ,QAAU,IAAVA,OAAU,EAAVA,EAAYI,UACvB4D,UAAqB,OAAVhE,QAAU,IAAVA,OAAU,EAAVA,EAAYgE,UACvBC,YAAuB,OAAVjE,QAAU,IAAVA,OAAU,EAAVA,EAAYiE,YACzBC,YAAuB,OAAVlE,QAAU,IAAVA,OAAU,EAAVA,EAAYpE,MACzBiF,YAAa,CAAEC,WAAY1F,OAG3B+D,IACAY,GAAe,GAEvB,OAsGiB,C,gDC/UzB,IAAIoE,EAAY1B,OAAO2B,OACnB,SAAkBhJ,GACd,MAAwB,kBAAVA,GAAsBA,IAAUA,CAClD,EAUJ,SAASiJ,EAAeC,EAAWC,GAC/B,GAAID,EAAUE,SAAWD,EAAWC,OAChC,OAAO,EAEX,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUE,OAAQC,IAClC,GAdSC,EAcIJ,EAAUG,GAdPE,EAcWJ,EAAWE,KAbtCC,IAAUC,GAGVR,EAAUO,IAAUP,EAAUQ,IAW1B,OAAO,EAfnB,IAAiBD,EAAOC,EAkBpB,OAAO,CACX,CAyBA,QAvBA,SAAoBC,EAAUC,GAE1B,IAAIC,OADY,IAAZD,IAAsBA,EAAUR,GAEpC,IACIU,EADAC,EAAW,GAEXC,GAAa,EAejB,OAdA,WAEI,IADA,IAAIC,EAAU,GACLC,EAAK,EAAGA,EAAKC,UAAUZ,OAAQW,IACpCD,EAAQC,GAAMC,UAAUD,GAE5B,OAAIF,GAAcH,IAAaO,MAAQR,EAAQK,EAASF,KAGxDD,EAAaH,EAASU,MAAMD,KAAMH,GAClCD,GAAa,EACbH,EAAWO,KACXL,EAAWE,GALAH,CAOf,CAEJ,C", "sources": ["pages/layout/video/components/configModal/style.js", "pages/layout/video/components/configModal/index.js", "pages/layout/video/style.js", "pages/layout/video/index.js", "../node_modules/memoize-one/dist/memoize-one.esm.js"], "names": ["ConfigModalContainer", "styled", "div", "_ref", "data", "open", "onOk", "<PERSON><PERSON><PERSON>", "t", "useTranslation", "inputVariableNumber", "useNumberInputVariable", "value", "setValue", "useState", "useEffect", "_jsx", "_Fragment", "children", "VModal", "title", "width", "onCancel", "footer", "_jsxs", "Select", "options", "style", "fieldNames", "label", "onChange", "e", "className", "Space", "direction", "VButton", "block", "onClick", "VideoContainer", "COLOR", "splitBack", "ContextMenuContainer", "ContextMenuRightClick", "domId", "handelConfig", "layoutConfig", "isCameraStream", "handelCameraStream", "isSubTaskSample", "i18n", "ContextMenu", "_ref2", "_configData$data_sour3", "_configData$data_sour5", "id", "isHistory", "item", "videoList", "useSelector", "state", "template", "widgetData", "optSample", "project", "subTaskSample", "subTask", "isFinishMain", "updateInputVariable", "useInputVariables", "getVideoUrl", "getUserMediaStream", "stopCamera", "cameraStream", "cameraVideoStream", "useVideo", "initWidget", "useWidget", "playerUrl", "setPlayerUrl", "mediaErr", "setMediaErr", "playing", "setPlaying", "controls", "setControls", "setIsCameraStream", "configModal", "setConfigModal", "configData", "setConfigData", "playerRef", "useRef", "widget_id", "findWidget", "findItem", "handelEnd", "useCallback", "debounce", "handelStopCamera", "init", "_configData$data_sour", "data_source", "input_code", "_inputVariableNumber$", "_inputVariableNumber$2", "index", "find", "f", "_configData$data_sour2", "code", "default_val", "getCreateTimeOriginIndex", "async", "setTimeout", "_playerRef$current", "current", "seekTo", "error", "console", "log", "startCamera", "res", "video", "sample_code", "getVideo", "name", "message", "subBatchInputVar", "input", "Number", "played", "batchUpdateInputVar", "input_vars", "subCacheBatchInputVar", "wrapper", "querySelectorAll", "for<PERSON>ach", "srcObject", "ReactPlayer", "ref", "url", "height", "onPlay", "handlePlay", "onPause", "handlePause", "onProgress", "_input$default_val$va", "_configData$data_sour4", "toFixed", "ConfigModal", "saveWidget", "parent_id", "widget_type", "widget_name", "safeIsNaN", "isNaN", "areInputsEqual", "newInputs", "lastInputs", "length", "i", "first", "second", "resultFn", "isEqual", "lastThis", "lastResult", "lastArgs", "calledOnce", "newArgs", "_i", "arguments", "this", "apply"], "sourceRoot": ""}