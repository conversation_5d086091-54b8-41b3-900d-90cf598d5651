{"version": 3, "file": "static/js/3056.8d45dfbf.chunk.js", "mappings": "qWAYA,MAqEA,EArEeA,IAqBR,IApBHC,QACIC,MAAM,UACFC,EAAS,WACTC,EAAU,MACVC,EAAK,YACLC,EAAW,UACXC,EAAS,cACTC,EAAa,YACbC,GACA,CAAC,EACLC,UAAU,MACNC,EAAK,QACLC,EAAO,SACPC,GACA,CAAC,EACLC,OAAO,OACHC,GACA,CAAC,GACL,CAAC,EAAC,kBACNC,GACHhB,EACG,MAAM,oBAAEiB,IAAwBC,EAAAA,EAAAA,MAC1B,QAAEC,IAAYC,EAAAA,EAAAA,KAEdC,GAAWC,EAAAA,EAAAA,GAA4B,OAALX,QAAK,IAALA,OAAK,EAALA,EAAOY,KCxBb,eAAC,cACnCC,GACHC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAC,MAAM,CAGRG,KAAM,GAENL,KAAM,GAENM,eAAgB,GAEhBC,YAAa,GAEbC,GAAI,GAEJP,cAA4B,OAAbA,QAAa,IAAbA,EAAAA,EAAiB,SAEhCQ,YAAa,CAETrB,MAAO,EAEPsB,WAAY,EAEZC,WAAY,SAEZC,SAAU,KAEVC,KAAM,MAGVC,UAAW,GAEXC,SAAU,GAEVC,IAAK,GAELC,UAAW,EAEXC,WAAY,EAEZC,WAAY,EAEZC,MAAO,EAGPC,WAAY,CAERC,OAAQ,CAEJC,cAAe,MAEfC,WAAY,OAEZC,WAAY,KAEZC,YAAa,KAEbC,kBAAmB,EAEnBC,kBAAmB,EAEnBC,cAAe,EAEfC,UAAW,EAEXC,WAAY,EAEZC,WAAY,EAEZC,WAAY,EAEZC,WAAY,EAEZC,WAAY,GAGhBC,QAAS,CAELC,YAAa,SAEbD,QAAS,SAETE,kBAAkB,EAElBC,aAAc,IAGlBC,qBAAsB,CAElBC,kBAAmB,EAEnBC,gBAAiB,OAGrB7B,KAAM,CAEFD,SAAU,SAEVC,KAAM,SAENyB,kBAAkB,EAElBC,aAAc,KAKtBI,mBAAoB,CAEhBC,eAAgB,QAChBC,OAAQ,CAEJ,EAEA,GAGJC,WAAY,GAEZC,SAAU,GAEVC,SAAU,GAEVC,gBAAgB,GAIpBC,oBAAqB,CAEjBC,UAAU,EAEVC,QAAS,GAETC,SAAU,OAEVC,SAAU,GAEVC,SAAU,OAEVvC,IAAK,GAELwC,WAAY,SAEZC,OAAQ,IAIZC,WAAY,CAERP,UAAU,EAEVC,QAAS,GAETC,SAAU,OAEVC,SAAU,GAEVC,SAAU,GAEVI,OAAQ,aAER3C,IAAK,GAEL4C,KAAM,SAENC,OAAQ,QAIZC,YAAa,CAETC,cAAe,GAEflD,KAAM,GAENmD,UAAW,GAEXC,WAAY,GAEZC,KAAM,GAENC,QAAS,IAIbC,SAAU,CAENhB,QAAS,GAET9B,OAAQ,SAER+C,YAAaC,EAAAA,GAAYC,KAEzBC,YAAY,GAIhBC,WAAY,CAERC,UAAW,cAEXC,SAAU,GAEVC,YAAaC,EAAAA,GAA8BC,OAE3CC,MAAO,GAEPzD,OAAQ,GAER0D,QAAS,IAIbC,oBAAqB,CAEjBC,UAAW,EAEXC,YAAa,EAEbC,gBAAgB,EAEhBC,mBAAmB,EAEnBC,WAAW,EAEXC,cAAe,CACX,CAEIC,MAAO,UAEP5B,KAAM,OAEN6B,QAAS,KAIjBC,iBAAkB,CACd,CAEIF,MAAO,UAEP5B,KAAM,OAEN6B,QAAS,KAIjBE,WAAY,CACR,CAAC,MAKTC,iBAAkB,CAEdC,QAAS,cAIbC,YAAa,CAETlC,KAAM,aAENmC,YAAa,WAEbC,aAAc,GAEdhG,KAAM,GAENiG,aAAc,GAEdC,kBAAmB,GAEnBV,MAAO,GAEPW,UAAW,GAEXC,QAAS,GAETC,QAAQ,GAIZC,WAAY,CAERC,YAAa,aAEbC,KAAM,EAENC,YAAa,SAEbL,QAAS,IAIbM,UAAW,CAEPpF,OAAQ,GAER8B,QAAS,GAETuD,SAAU,GAEVC,KAAM,IAIVC,YAAa,CAETC,IAAK,GAELC,UAAU,EAEVC,KAAM,GAEN3G,KAAM,IAIV4G,gBAAiB,CAEbC,KAAM,IAIVC,iBAAkB,CAEdC,UAAW,EAEXC,QAAS,IAIbC,sBAAuB,CAEnBC,eAAgB,GAEhBC,OAAQ,GAEf,CD3TwDC,CAAuB,CAAExH,cAAeR,KAEvFiI,GAAyBC,EAAAA,EAAAA,UAAQ,SAAY7H,EAAUO,KAAW,OAALvB,QAAK,IAALA,EAAAA,EAASgB,EAASO,QAAS,CAACP,EAAUhB,IAEnGkF,GAAY4D,EAAAA,EAAAA,GAAmC,OAAPvI,QAAO,IAAPA,OAAO,EAAPA,EAASW,MAAM,GACvD6H,GAAUD,EAAAA,EAAAA,GAAoC,OAARtI,QAAQ,IAARA,OAAQ,EAARA,EAAUU,MAAM,GAoB5D,OACI8H,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SAEQhE,IACI8D,EAAAA,EAAAA,KAACG,EAAAA,EAAW,CACR9I,SAAUuI,EACVpI,UAAWuI,EACXK,SAzBHC,UACb,GAAU,OAAL/I,QAAK,IAALA,IAAAA,EAAOY,KAER,YADAoI,EAAAA,GAAQC,MAAM,kCAKlB,MAAMC,EAAU,IACTC,EAAGlI,KAAMP,EAASO,YAEPmI,EAAAA,EAAAA,KAAeF,KAG7B5I,EAAoB,CAAEM,KAAMsI,EAAQtI,MAAQsI,GAC5C1I,EAAQJ,GACZ,EAWgBT,YAAaA,EACbC,UAAWA,EACXC,cAAeA,EACfC,YAAaA,KAI1B,EE7EEuJ,EAAmBhK,IAAA,IAAC,MAAEK,EAAQ,IAAIL,EAAA,MAAM,CACjDE,KAAM,CACFC,UAAW,OACX8J,WAAY,OACZ5J,QACAD,WAAY,MACZ2H,KAAM,SACNmC,aAAa,EACbC,UAAU,GAEdzJ,SAAU,CACNC,MAAO,KACPC,QAAS,KACTC,SAAU,MAEdC,MAAO,CACHC,OAAQ,MAEf,ECJKqJ,GAAUC,EAAAA,EAAAA,OAAK,IAAM,0DAEdC,EAAYC,EAAAA,GAAOC,GAAG;aACtBxK,IAAA,IAAC,UAAEG,GAAWH,EAAA,OAAc,OAATG,QAAS,IAATA,EAAAA,EAAa,MAAM;cACrCsK,IAAA,IAAC,WAAER,GAAYQ,EAAA,OAAe,OAAVR,QAAU,IAAVA,EAAAA,EAAc,MAAM;;;;EAiGtD,EA1F0BS,IAGnB,IAADC,EAAAC,EAAAC,EAAAC,EAAA,IAHqB,KACvBC,EAAI,GAAEhJ,EAAE,aAAEiJ,EAAY,kBACtBhK,EAAoBiK,EAAAA,GAAoBC,aAAE,aAAEC,EAAe,kCAC9DT,EACG,MAAM,iBAAEU,IAAqBC,EAAAA,EAAAA,MAEtBC,EAAMC,IAAWC,EAAAA,EAAAA,WAAS,IAC1BvL,EAAQwL,IAAaD,EAAAA,EAAAA,aAG5BE,EAAAA,EAAAA,YAAU,KACND,EAAUzB,EAAiB,CAAE3J,MAAO8K,IAAgB,GACrD,CAACA,KAGJO,EAAAA,EAAAA,YAAU,KACN,IACI,GAAQ,OAAJX,QAAI,IAAJA,GAAAA,EAAMY,YAAa,CACnB,MAAM,YAAEC,GAAgBC,KAAKC,MAAU,OAAJf,QAAI,IAAJA,OAAI,EAAJA,EAAMY,aACpCI,IAAQH,EAAa3L,IACtBwL,EAAUG,EAElB,MACIH,EAAUzB,EAAiB,CAAE3J,MAAO8K,IAE5C,CAAE,MAAOvB,GACLoC,QAAQC,IAAI,MAAOrC,EACvB,IACD,CAAK,OAAJmB,QAAI,IAAJA,OAAI,EAAJA,EAAMY,cAEV,MAaMpG,GAAY4D,EAAAA,EAAAA,GAAkC,OAANlJ,QAAM,IAANA,GAAgB,QAAV0K,EAAN1K,EAAQS,gBAAQ,IAAAiK,GAAS,QAATC,EAAhBD,EAAkB/J,eAAO,IAAAgK,OAAnB,EAANA,EAA2BrJ,MAAM,GAE/E,OACI2K,EAAAA,EAAAA,MAAC5B,EAAS,CACNvI,GAAIA,EACJ5B,UAAiB,OAANF,QAAM,IAANA,GAAY,QAAN4K,EAAN5K,EAAQC,YAAI,IAAA2K,OAAN,EAANA,EAAc1K,UACzB8J,WAAkB,OAANhK,QAAM,IAANA,GAAY,QAAN6K,EAAN7K,EAAQC,YAAI,IAAA4K,OAAN,EAANA,EAAcb,WAC1BkC,MAAO,CACHC,QAAS7G,EAAY,QAAU,QACjCgE,SAAA,CAGEtJ,IAAUoJ,EAAAA,EAAAA,KAACgD,EAAM,CAACpM,OAAQA,EAAQe,kBAAmBA,KAGzDqI,EAAAA,EAAAA,KAACiD,EAAAA,SAAQ,CAACC,UAAUlD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,IAAMC,SAElB+B,IACIjC,EAAAA,EAAAA,KAACe,EAAO,CACJkB,KAAMA,EACNkB,QAjCRA,KACZjB,GAAQ,GAGRH,EAAiB,CACbqB,OAAQzB,EACR0B,QAAS,IACF3B,EACHY,YAAaE,KAAKc,UAAU,CAAEf,YAAa3L,MAEjD,EAwBkBA,OAAQA,EACRwL,UAAWA,EACXzK,kBAAmBA,OAMnCqI,EAAAA,EAAAA,KAACuD,EAAAA,EAAW,CACRC,MAAO9K,EACPiJ,aAAcA,EAAazB,UAE3BF,EAAAA,EAAAA,KAAA,OACIyD,UAAU,iBACVC,QAASA,IAAMxB,GAAQ,GAAMhC,SAGzB,eAAK4B,UAKT,C,2HCxGpB,MAAM6B,GAAoB3C,EAAAA,EAAAA,OAAK,IAAM,kCAExBjJ,EAAaA,KACtB,MAAM,YAAE6L,IAAgBC,EAAAA,EAAAA,KAoBlBC,EAAkBzD,UAChB0D,SACMH,EAAY,CAAEG,UAAWC,OAAOD,IAC1C,EAIEE,EAAkB5D,UAChB1E,SACMuI,EAAAA,EAAAA,KAAa,CACfvI,SACAwI,YAAa,QAErB,EAGJ,MAAO,CACHrM,QAlCaL,IACb,IACI,GAAIA,EAAO,CACP,MAAM,UAAEsM,EAAS,aAAEK,EAAY,OAAEzI,GAAWlE,EACvB,WAAjB2M,GACAN,EAAgBC,GAEC,WAAjBK,GACAH,EAAgBtI,EAExB,CACJ,CAAE,MAAO4E,GACLoC,QAAQC,IAAI,QAASrC,EACzB,GAsBH,EAqDL,EAzC0B5J,IAEnB,IAFoB,GACvB+B,EAAE,MAAEpB,EAAK,SAAE8I,GACdzJ,EACG,MAAM,EAAE0N,IAAMC,EAAAA,EAAAA,OACPrC,EAAMC,IAAWC,EAAAA,EAAAA,WAAS,GAE3BoC,EAAmBA,KACrBrC,GAAQ,EAAK,EAGjB,OACIW,EAAAA,EAAAA,MAAA,OAAA3C,SAAA,CAEQ5I,GACI0I,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACI2C,EAAAA,EAAAA,MAAC2B,EAAAA,EAAK,CAAAtE,SAAA,EACFF,EAAAA,EAAAA,KAACyE,EAAAA,GAAM,CAACf,QAASA,IAAMa,IAAmBrE,SAAEmE,EAAE,mBAC9CrE,EAAAA,EAAAA,KAACyE,EAAAA,GAAM,CAACf,QAASA,IAAMtD,IAAWF,SAAEmE,EAAE,wBAI9CrE,EAAAA,EAAAA,KAACyE,EAAAA,GAAM,CAACf,QAASA,IAAMa,IAAmBrE,SAAEmE,EAAE,2CAGtDrE,EAAAA,EAAAA,KAACiD,EAAAA,SAAQ,CAACC,UAAUlD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,IAAMC,SAElB+B,IACIjC,EAAAA,EAAAA,KAAC2D,EAAiB,CACd1B,KAAMA,EACNC,QAASA,EACTzK,MAAOH,EACPoN,SAAUtE,QAMxB,C,uGCtFd,MAyEA,EAzEuB4B,KACnB,MAAM2C,GAAWC,EAAAA,EAAAA,OACX,WAAEC,IAAeC,EAAAA,EAAAA,KAuBjBC,EAAgB1E,UAAgC,IAAzB,OAAE+C,EAAM,QAAEC,GAASjC,EAE5C,MAAM4D,EAAY,IACX5B,EACHlD,SAAU+E,EAAU7B,EAAOlD,SAAUmD,KAGlC6B,SAAoBC,EAAAA,EAAAA,KAAe,CAAEC,WAAY,CAAO,OAANhC,QAAM,IAANA,OAAM,EAANA,EAAQiC,mBAE3DC,EAAAA,EAAAA,KAAU,CACZC,QAAS,CACL,IAAKL,EAAY9B,QAAQoC,EAAAA,EAAAA,IAAoBR,EAAiB,OAAN5B,QAAM,IAANA,OAAM,EAANA,EAAQiC,eAIxEV,EAAS,CAAE7I,KAAM2J,EAAAA,GAAgCC,MAAOR,EAAWG,WAAY,EAG7EJ,EAAYA,CAACU,EAAKtC,IACbsC,EAAIC,KAAIlE,GACPA,EAAKhJ,KAAO2K,EAAQ3K,GACb2K,EAGP3B,EAAKxB,UAAYwB,EAAKxB,SAAS7H,OAAS,EACjC,IACAqJ,EACHxB,SAAU+E,EAAUvD,EAAKxB,SAAUmD,IAIpC3B,IAITmE,EAAaxF,UAAgC,IAAzB,OAAE+C,EAAM,QAAEC,GAAShC,EACzC,MAAM2D,EAAY,IACX5B,EACHlD,SAAU+E,EAAU7B,EAAOlD,SAAUmD,UAEnCwB,EAAWG,EAAU,EAG/B,MAAO,CACHjD,iBA5DqB1B,UAGlB,IAHyB,OAC5B+C,EAAM,QACNC,GACH1M,EAEc,OAANyM,QAAM,IAANA,GAAAA,EAAQiC,WAMT1C,QAAQC,IAAI,sCACNmC,EAAc,CAAE3B,SAAQC,cAL9BV,QAAQC,IAAI,qDACNiD,EAAW,CAAEzC,SAAQC,YAK/B,EAgDH,C", "sources": ["module/layout/controlComp/lib/AtomInputVariable/render/index.js", "module/variableInput/constants/variableInfo.js", "module/layout/controlComp/lib/AtomInputVariable/constants.js", "module/layout/controlComp/lib/AtomInputVariable/index.js", "components/formItems/SetActionOrScript/index.js", "hooks/useSplitLayout.js"], "names": ["_ref", "config", "attr", "compWidth", "labelWidth", "label", "labelItalic", "labelBold", "contentItalic", "contentBold", "variable", "value", "visible", "disabled", "event", "change", "inputVariableType", "updateInputVariable", "useInputVariables", "onEvent", "useTrigger", "valueVar", "useInputVariableByCode", "code", "variable_type", "arguments", "length", "undefined", "name", "group_category", "description", "id", "default_val", "isConstant", "value_type", "unitType", "unit", "created<PERSON>y", "f1_index", "pic", "is_enable", "is_feature", "is_overall", "is_fx", "number_tab", "format", "numberRequire", "formatType", "afterPoint", "beforePoint", "significantDigits", "amendmentInterval", "pointPosition", "roundMode", "threshold1", "threshold2", "roundType1", "roundType2", "roundType3", "channel", "channelType", "isUserConversion", "lockChannels", "multipleMeasurements", "measurementCounts", "measurementType", "reasonable_val_tab", "reasonableType", "values", "defaultVal", "minParam", "max<PERSON>ara<PERSON>", "isToResultList", "button_variable_tab", "isEnable", "content", "position", "actionId", "function", "buttonType", "script", "button_tab", "source", "type", "method", "program_tab", "numericFormat", "isVisible", "isDisabled", "mode", "is<PERSON><PERSON><PERSON>", "text_tab", "return_type", "SCRIPT_TYPE", "BOOL", "canUseText", "select_tab", "selection", "group_id", "<PERSON><PERSON><PERSON><PERSON>", "MAPPING_SELECT_INPUTVAR_LAYER", "轴", "items", "comment", "two_digit_array_tab", "rowCounts", "columnCount", "rowHeaderPlace", "columnHeaderPlace", "isRowType", "rowDefinition", "title", "options", "columnDefinition", "columnData", "custom_array_tab", "useType", "control_tab", "dialog_type", "control_name", "default_name", "related_variables", "variables", "signals", "is_daq", "buffer_tab", "buffer_type", "size", "size_expand", "label_tab", "fontSize", "fore", "picture_tab", "src", "showName", "path", "related_var_tab", "vars", "double_array_tab", "rowNumber", "columns", "double_array_list_tab", "dataSourceCode", "number", "getVariableDefaultInfo", "applyLabelNameValueVar", "useMemo", "useInputVariableValueByCode", "isAbled", "_jsx", "_Fragment", "children", "InputRender", "onChange", "async", "message", "error", "newVari", "v", "updateInputVar", "getDefaultConfig", "compHeight", "isShowColon", "readonly", "Setting", "lazy", "Container", "styled", "div", "_ref2", "_ref3", "_config$variable", "_config$variable$visi", "_config$attr", "_config$attr2", "item", "layoutConfig", "INPUT_VARIABLE_TYPE", "文本", "atomTypeName", "updateLayoutItem", "useSplitLayout", "open", "<PERSON><PERSON><PERSON>", "useState", "setConfig", "useEffect", "data_source", "comp_config", "JSON", "parse", "isEqual", "console", "log", "_jsxs", "style", "display", "Render", "Suspense", "fallback", "onClose", "layout", "newItem", "stringify", "ContextMenu", "domId", "className", "onClick", "EventEditorDialog", "startAction", "useAction", "handleRunAction", "action_id", "String", "handleRunScript", "submitScript", "result_type", "execute_type", "t", "useTranslation", "handleOpenDialog", "Space", "<PERSON><PERSON>", "callback", "dispatch", "useDispatch", "saveLayout", "useTemplateLayout", "handleTabEdit", "newLayout", "recursion", "binderData", "getBatchBinder", "binder_ids", "binder_id", "actionTab", "binders", "handleTabLayoutData", "SPLIT_CHANGE_CHANGED_BINDER_ID", "param", "arr", "map", "handleEdit"], "sourceRoot": ""}