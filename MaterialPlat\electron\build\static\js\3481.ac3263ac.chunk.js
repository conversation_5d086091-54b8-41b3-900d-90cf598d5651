"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3481],{43481:(e,i,t)=>{t.r(i),t.d(i,{default:()=>L});var l=t(65043),n=t(16569),o=t(80077),a=t(14463),d=t(65694),s=t(56543),r=t(9339),u=t(43880),c=t(8237),v=t(80231),m=t(84),h=t(45303),g=t(67299),b=t(74117),p=t(36950),x=t(75440),f=t(16133),_=t(4494),y=t(79889),w=t.n(y),j=t(10866),k=t(30212),A=t(84674),D=t(68945),N=t(81143),R=t(68374);const $=N.Ay.div`
    background: ${R.o$.splitBack};
    border-radius: 8px;
    padding: 10px;
    height: 100%;
    width: 100%;
    .hidden-element {
        visibility: hidden;
        position: absolute;
        top: 0;
        width: 100%;
    }
    .table-first {
       background: rgba(66,111,255,0.1);
    }

    .statistic-table-layout table {
        font-size: ${e=>e.isFission?"12px":"auto"};
        min-width: auto !important;
        colgroup col:last-child {
            display: none;
        }
    }

    .ant-table-wrapper {
        font-size:${(0,R.D0)("14px")};
        .ant-table {
            .ant-table-header {
                font-size: ${e=>e.isFission?"10px":"auto"};
            }
        }

        .ant-table-wrapper .ant-table-thead >tr>th
        .ant-table-cell-scrollbar {
            background: rgba(66,111,255,0.1);
            box-shadow:none
         }
        .ant-table-thead {
            >tr >th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
                display: none;
            }
            >tr >th:first-child {
                background: rgba(215, 226, 255, 1);
            }
            >tr >th  {
                padding: 0 ${(0,R.D0)("4px")};
                overflow: hidden;
                background: rgba(66,111,255,0.1);
            }
        }
        .ant-table-body {
            >tr >td  {
                padding: 0 ${(0,R.D0)("4px")};
                background: rgba(66,111,255,0.1);
            }
        }
    }
    .columns-style {
        background: rgba(66,111,255,0.1);
    }
    .row-style {
        background: rgba(66,111,255,0.1)
    }
    .unit-name {
        opacity: 0.6;
    }
`,C=N.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .disabled {
        cursor: no-drop;
    }
    .unique-content {
        padding: 2px;
    }

`,S=N.Ay.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-width: ${e=>e.min_width-16}px;
    max-width: ${e=>e.max_width}px;
    .line-layout {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
    .omit { 
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
`,M=N.Ay.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-width: ${e=>e.min_width}px;
    max-width: ${e=>e.max_width}px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;

`,F=N.Ay.div`
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 25px;
    min-width: ${e=>e.min_width}px;
    max-width: ${e=>e.max_width}px;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
`;var H=t(70579);const I=e=>{const i=e.reduce(((e,i)=>e+i),0)/e.length,t=e.reduce(((e,t)=>e+(t-i)**2),0)/(e.length-1);return Number.isNaN(t)?0:t},z=e=>{var i,t,l,n;let{setting:o,res:a,isFission:d,setHighlight:s,currentResult:r}=e;const u=null!==(i=null===o||void 0===o?void 0:o.is_unit)&&void 0!==i&&i,c=null!==(t=null===o||void 0===o?void 0:o.is_name)&&void 0!==t&&t,v=null!==(l=null===o||void 0===o?void 0:o.is_abbr)&&void 0!==l&&l&&(null===a||void 0===a?void 0:a.abbreviation),m=null!==(n=null===o||void 0===o?void 0:o.is_line)&&void 0!==n&&n,{t:h}=(0,b.Bd)(),g=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return(0,H.jsxs)(H.Fragment,{children:[m&&e?c&&v?(0,H.jsxs)("div",{className:"line-layout",children:[(0,H.jsx)("div",{className:"omit",children:h(null===a||void 0===a?void 0:a.variable_name)}),(0,H.jsx)("div",{children:"("}),(0,H.jsx)(D.A,{text:null===a||void 0===a?void 0:a.abbreviation,variables:null===a||void 0===a?void 0:a.variables}),(0,H.jsx)("div",{children:")"})]}):c?(0,H.jsx)("div",{className:"omit",children:h(null===a||void 0===a?void 0:a.variable_name)}):v?(0,H.jsx)(D.A,{text:null===a||void 0===a?void 0:a.abbreviation,variables:null===a||void 0===a?void 0:a.variables}):"":(0,H.jsxs)(H.Fragment,{children:[c&&(0,H.jsx)("div",{className:"omit",children:h(a.variable_name)}),v&&(0,H.jsx)("div",{className:"omit",children:(0,H.jsx)(D.A,{text:null===a||void 0===a?void 0:a.abbreviation,variables:null===a||void 0===a?void 0:a.variables})})]}),u&&(0,H.jsx)("div",{className:"unit-name omit",children:h(a.unit_name)}),e?null:(0,H.jsx)("div",{className:"omit",children:h(null===a||void 0===a?void 0:a.description)})]})};return(0,H.jsx)(S,{...o,onMouseOver:()=>{d||(r.current=a)},children:(0,H.jsx)(A.A,{title:g(!1),children:g()})})},B=e=>{let{setting:i,samples:t,currentResult:l}=e;const{t:n}=(0,b.Bd)(),o=(0,H.jsxs)("div",{children:[(0,H.jsx)("div",{className:"omit",children:n("\u7ed3\u679c\u6587\u4ef6")}),(0,H.jsxs)("div",{className:"omit",children:["n =",null===t||void 0===t?void 0:t.length]})]});return(0,H.jsx)(S,{...i,onMouseOver:()=>{l.current={}},children:(0,H.jsx)(A.A,{title:o,children:o})})},E=e=>{let{format_type:i,format_info:t,value:l,dimension_id:n,unit_id:o}=e;return(0,p.jq)(i,(0,p.tJ)(l,n,o),(0,p._q)(i,t))},q=e=>{let{setting:i,col:t,row:n,sampleData:o,historyData:a,currentResult:d}=e;const s=((e,i,t,l)=>{var n,o;let{code:a,result_variable_id:d,...s}=e;const{format_info:r="",format_type:u="",dimension_id:c="",unit_id:v=""}=s||{format_info:"",format_type:""},m=null===t||void 0===t||null===(n=t.filter((e=>(null===e||void 0===e?void 0:e.parameter_id)===d)))||void 0===n?void 0:n.map((e=>(null===e||void 0===e?void 0:e.value)&&(0,p.tJ)((0,p.tJ)(null===e||void 0===e?void 0:e.value,e.dimension_id,null===e||void 0===e?void 0:e.units_id),e.dimension_id,v,e.units_id))),h=null===l||void 0===l||null===(o=l.filter((e=>(null===e||void 0===e?void 0:e.code)===a&&"NaN"!==e.value)))||void 0===o?void 0:o.map((e=>null===e||void 0===e?void 0:e.value)),g=h&&(null===h||void 0===h?void 0:h.length)>0,b=m&&m.length>0;if(!g&&!b)return"";switch(null===i||void 0===i?void 0:i.name){case"\u5e73\u5747\u503c":return b?((null===m||void 0===m?void 0:m.reduce(((e,i)=>e+i)))/(null===m||void 0===m?void 0:m.length)).toFixed(2):E({format_type:u,format_info:r,dimension_id:c,unit_id:v,value:(null===h||void 0===h?void 0:h.reduce(((e,i)=>e+i)))/(null===h||void 0===h?void 0:h.length)});case"\u6700\u5927\u503c":return b?Math.max(...m):E({format_type:u,format_info:r,dimension_id:c,unit_id:v,value:Math.max(...h)});case"\u6700\u5c0f\u503c":return b?Math.min(...m):E({format_type:u,format_info:r,dimension_id:c,unit_id:v,value:Math.min(...h)});case"\u65b9\u5dee\u503c":return b?I(m).toFixed(2):E({format_type:u,format_info:r,dimension_id:c,unit_id:v,value:I(h)});default:return""}})(t,n,o,a),r=(0,l.useMemo)((()=>{var e,l,n,o,a,d,r,u;let c=null!==(e=null===i||void 0===i?void 0:i.min_width)&&void 0!==e?e:0,v=i.is_name?t.variable_name:"";const m=i.is_abbr?null===(l=t.abbreviation)||void 0===l?void 0:l.replace(/{lo|}|{hi|}/g,""):"",h=i.is_unit?t.unit_name:"";i.is_line&&(v=`${v}${m}`);if(Math.max(null!==(n=null===(o=v)||void 0===o?void 0:o.length)&&void 0!==n?n:0,null!==(a=null===m||void 0===m?void 0:m.length)&&void 0!==a?a:0,null!==(d=null===h||void 0===h?void 0:h.length)&&void 0!==d?d:0)>(null!==(r=null===(u=String(s))||void 0===u?void 0:u.length)&&void 0!==r?r:0)&&!["NaN","0"].includes(String(null!==s&&void 0!==s?s:""))){const e=[v,m,h].reduce(((e,i)=>{const t=String(i);return t.length>e.length?t:e}),"");let t=(0,p.WI)(e);t=t<c?c:t,c=t>i.max_width?i.max_width:t}return c}),[i,t,s]);return(0,H.jsx)(M,{...i,min_width:r,onMouseOver:()=>{d.current=t},children:(0,H.jsx)(A.A,{title:s,children:s})})},O=e=>{let{samples:i,isFission:t,setHighlight:l,highlight:n,setting:o,colData:a=[],resultHistoryData:d,currentResult:s}=e;const r=((e,i)=>{const t=new Set(null===e||void 0===e?void 0:e.map((e=>null===e||void 0===e?void 0:e.code)));return Object.keys(i).filter((e=>t.has(e))).map((e=>i[e])).flat()})(i,d),u=null===i||void 0===i?void 0:i.flatMap((e=>null===e||void 0===e?void 0:e.data));return{columns:[{title:(0,H.jsx)(B,{samples:i,setting:o,currentResult:s}),dataIndex:"name",align:"center",className:"table-first",render:(e,i)=>(0,H.jsx)(F,{...o,onMouseOver:()=>{s.current={}},children:e})},...a.map((e=>({title:(0,H.jsx)(z,{setting:o,res:e,isFission:t,setHighlight:l,currentResult:s}),dataIndex:"name",align:"center",key:e.result_variable_id,className:n===e.result_variable_id?"columns-style":"",render:(i,n)=>(0,H.jsxs)(H.Fragment,{children:[(0,H.jsx)("div",{style:{visibility:"hidden",height:0,padding:"0 2px"},children:(0,H.jsx)(z,{setting:o,res:e,isFission:t,setHighlight:l,currentResult:s})}),(0,H.jsx)(q,{setting:o,col:e,row:n,sampleData:u,historyData:r,currentResult:s})]})})))]}},T=e=>{var i;let{domId:t,layoutConfig:l,handleResult:n}=e;const{openDialog:d}=(0,m.A)(),{t:s}=(0,b.Bd)(),r=(0,o.d4)((e=>e.global.roleHiddenDomClass)),u=(0,o.wA)(),{subContextMenuId:p}=(0,g.A)(),x=null===t||void 0===t||null===(i=t.split("edit-"))||void 0===i?void 0:i.at(-1);return(0,H.jsx)(C,{children:(0,H.jsxs)(v.A,{domId:t,layoutConfig:l,children:[(0,H.jsx)("div",{className:`unique-content ${r}`,onClick:()=>n(!0),children:s("\u7f16\u8f91\u7ed3\u679c")}),(0,H.jsx)("div",{className:"unique-content",onClick:()=>{d({type:h.pn}),p(x),u({type:a.Bz,param:c.pz.TABLE_STATISTIC_CONTROL})},children:s("\u6570\u636e\u7edf\u8ba1\u8bbe\u7f6e\u8868")})]})})},L=e=>{let{item:{widget_id:i,data:t},title:a,id:c,isRightClick:v=!0,isFission:m=!1,layoutConfig:h,isPdfPrint:g=!1}=e;const y=(0,o.wA)(),{t:A}=(0,b.Bd)(),D=(0,o.d4)((e=>e.project.highlight)),N=(0,o.d4)((e=>e.project.sampleData)),R=(0,o.d4)((e=>e.project.resultHistoryData)),C=(0,o.d4)((e=>e.template.resultData)),S=(0,o.d4)((e=>e.template.widgetData)),M=(0,o.d4)((e=>e.template.tableConfigData)),{getSamples:F}=(0,j.A)(),{getsStatisticTableData:I}=(0,_.A)(),{initResultData:z}=(0,f.A)(),[B,E]=(0,l.useState)([]),[q,L]=(0,l.useState)(!1),[J,P]=(0,l.useState)([]),K=(0,l.useRef)(),W=(0,l.useRef)(),Y=(0,l.useRef)(),G=(0,l.useRef)(),[Q,U]=(0,l.useState)({}),[V,X]=(0,l.useState)("100%");(0,l.useEffect)((()=>{const e=document.getElementById(c);return e&&(Y.current=new ResizeObserver(w()(Z,800)),Y.current.observe(e)),()=>{Y.current&&(Y.current.unobserve(e),Y.current=null)}}),[]);const Z=()=>{const e=document.getElementById(c);var i,t,l,n;e&&X((null===e||void 0===e?void 0:e.offsetHeight)-(null===W||void 0===W||null===(i=W.current)||void 0===i?void 0:i.offsetHeight)-(null===K||void 0===K||null===(t=K.current)||void 0===t||null===(l=t.nativeElement)||void 0===l||null===(n=l.querySelector("thead"))||void 0===n?void 0:n.offsetHeight)-30)};(0,l.useEffect)((()=>{Z()}),[J]),(0,l.useEffect)((()=>{if(m&&t)ie(null!==t&&void 0!==t?t:{});else{const t=(0,p.Rm)(S,"widget_id",i);var e;if(t)ie(null!==(e=I(null===t||void 0===t?void 0:t.data_source))&&void 0!==e?e:{})}Z()}),[C,S,N,m,N,M,t,D,R]);const ee=e=>{y({type:d.q5,param:e})},ie=e=>{let{tableData:i=[],setting:t={},colData:l=[]}=e;E(i),U(t);const{columns:n}=O({samples:g&&t.isPdfSelect?F(!0).filter((e=>{var i;return null===(i=t.sampleList)||void 0===i?void 0:i.includes(e.key)})):F(!0).filter((e=>e.status!==s.$y.READY)),highlight:D,colData:l,setting:t,setHighlight:ee,currentResult:G,resultHistoryData:R});P(n)},te=function(){L(arguments.length>0&&void 0!==arguments[0]&&arguments[0]),z()};return(0,H.jsxs)($,{isFission:m,...Q,children:[(0,H.jsx)(u.A,{title:a,ref:W}),(0,H.jsx)(r.A,{rowKey:"id",ref:K,size:"small",className:"statistic-table-layout",columns:J,dataSource:B.length>0?B:[{}],bordered:!0,tableLayout:"fixed",pagination:!1,scroll:(0,l.useMemo)((()=>({y:V,x:"max-content"})),[V])}),q&&G.current&&(0,H.jsx)(x.A,{title:A("\u7f16\u8f91\u7ed3\u679c"),open:q,onCancel:()=>te(),footer:null,children:(0,H.jsx)(k.A,{resultIsModal:q,result_variable_id:G.current.result_variable_id,handleCancel:()=>te(!1),isEdit:!0})}),v&&(0,H.jsx)(T,{domId:c,layoutConfig:h,handleResult:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];null!==G&&void 0!==G&&G.current&&"format_type"in G.current?te(e):n.Ay.error(A("\u4e0d\u662f\u7ed3\u679c\u53d8\u91cf"))}})]})}}}]);
//# sourceMappingURL=3481.ac3263ac.chunk.js.map