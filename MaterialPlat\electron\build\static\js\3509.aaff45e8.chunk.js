/*! For license information please see 3509.aaff45e8.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3509],{3509:(i,e,d)=>{d.r(e),d.d(e,{default:()=>S});var t=d(65043),l=d(80077),n=d(5520),o=d(63390),a=d(58168);const s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M603.3 327.5l-246 178a7.95 7.95 0 000 12.9l246 178c5.3 3.8 12.7 0 12.7-6.5V643c0-10.2-4.9-19.9-13.2-25.9L457.4 512l145.4-105.2c8.3-6 13.2-15.6 13.2-25.9V334c0-6.5-7.4-10.3-12.7-6.5z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"left-circle",theme:"outlined"};var u=d(22172),r=function(i,e){return t.createElement(u.A,(0,a.A)({},i,{ref:e,icon:s}))};const g=t.forwardRef(r);const c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M666.7 505.5l-246-178A8 8 0 00408 334v46.9c0 10.2 4.9 19.9 13.2 25.9L566.6 512 421.2 617.2c-8.3 6-13.2 15.6-13.2 25.9V690c0 6.5 7.4 10.3 12.7 6.5l246-178c4.4-3.2 4.4-9.8 0-13z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"right-circle",theme:"outlined"};var v=function(i,e){return t.createElement(u.A,(0,a.A)({},i,{ref:e,icon:c}))};const m=t.forwardRef(v);var h=d(74117),p=d(34458),f=d(36950),x=d(4554),_=d(18650),w=d(94817),b=d(84),j=d(45303),y=d(67299),$=d(21043),D=d(21256),N=d(30092),k=d(71424),A=d(81143),C=d(68374);const z=A.Ay.div`
    display: flex;
    justify-content: center;
    height: 100%;
    overflow: hidden;
    width: 100%;
    .guide-container {
        width: 100%;
        .guide-list {
            background: ${C.o$.splitBack};
            height: 100%;
            display: flex;
            flex-direction: column;
            
            .guide-list-guide {
                height: ${(0,C.D0)("36px")};
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: ${(0,C.D0)("10px")};
                height: 6vh;
                .guide-name {
                    font-size: ${(0,C.D0)("20px")};
                    white-space: nowrap;
                    font-weight: 600;
                    color: #2D4586;
                    display: flex;
                    align-items: center;
                    
                }
                .guide-list-layout {
                    display:flex;
                    position: relative;
                    width: 75%;
                    .list-layout {
                        overflow: auto;
                        display:flex;
                        align-items: center;
                        justify-content: flex-start;
                        height: 2vw;
                        width: calc(88% - 1vw);
                    }
                    .guide-more {
                        right: 0;
                        position: absolute;
                        cursor: pointer;
                        
                        .guide-button {
                            font-size: ${(0,C.D0)("36px")};
                        }
                        >img {
                            width: ${(0,C.D0)("32px")};
                            height: ${(0,C.D0)("32px")};
                        }
                    }
                }
                .guide {
                    cursor: pointer;
                    display:flex;
                    align-items: center;
                    margin-right: ${(0,C.D0)("10px")};
                    .guide-button {
                        font-size: ${(0,C.D0)("36px")};
                    }
                    .general {
                        width: ${(0,C.D0)("30px")};
                        height: ${(0,C.D0)("22px")};
                    }
                    .extend {
                        width: ${(0,C.D0)("26px")};
                        height: ${(0,C.D0)("24px")};
                    }
                }
            }

            .guide-list-item {
                padding-right: ${(0,C.D0)("10px")};
                //height: 70%;
                flex: 1;
                overflow-y: auto;
                .layout-name {
                    display: flex ;
                    .icon-button {
                        height: 4vh;
                        width: ${(0,C.D0)("10px")};
                        border-top-right-radius: ${(0,C.D0)("4px")};
                        border-bottom-right-radius: ${(0,C.D0)("4px")};
                    }
                    .button-opt {
                        background: linear-gradient(180deg, #94BFF5 0%, #467AF2 45%, #7EA1FF 100%);
                    }
                    .name {
                        margin-left: 10px;
                        width: 100%;
                        height: 4vh;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: ${(0,C.D0)("6px")};
                        cursor: pointer;
                        font-size: ${(0,C.D0)("14px")};

                        .name_img_container{
                            display: flex;
                            align-items: center;
                            
                            .image-layout {
                                margin: 0 5px;
                                display: flex;
                                align-items: center;
                                width: ${(0,C.D0)("30px")};
                                justify-content: center;
                                img {
                                    width: ${(0,C.D0)("30px")};
                                    height: ${(0,C.D0)("30px")};
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                }
                            }
                        }
                    }
                    .opt {
                        background: linear-gradient(180deg, #94BFF5 0%, #467AF2 45%, #5D89FF 100%);
                        border-radius: 4px;
                        color: #ffffff;
                    }
                }
                .icon {
                    height: ${(0,C.D0)("50px")};
                    line-height: ${(0,C.D0)("50px")};
                    padding-left: ${(0,C.D0)("20px")}; 
                    cursor: pointer;
                }
            }
        }
        .guide-layout {
            height: 100%;
            width: 100%;

            > .guide-param-layout {
                display: flex;
                height: calc(100% - ${(0,C.D0)("60px")});
                border-bottom: 1px solid #C0CFFF;
            }

            // 底栏
            >.guide-footer {
                background: ${C.o$.splitBack};
                height: ${(0,C.D0)("60px")};
                display: flex;
                align-items: center;
                justify-content: center;

                button {
                    margin-left: ${(0,C.D0)("16px")};
                }
            }
        }
    }
`;var F=d(70579);const M=i=>{let{item:e,isOpt:d,isConfirmed:t,onClick:l}=i;const{t:n}=(0,h.Bd)();return(0,k.A)(null===e||void 0===e?void 0:e.visible_bind_code,!0)?(0,F.jsxs)("div",{className:"layout-name",onClick:()=>l(e,!0),children:[(0,F.jsx)("div",{className:d?"icon-button button-opt":"icon-button"}),(0,F.jsxs)("div",{className:d?"name opt":"name",children:[(0,F.jsxs)("div",{className:"name_img_container",title:null===e||void 0===e?void 0:e.description,children:[(0,F.jsx)("div",{className:"image-layout",children:1===(null===e||void 0===e?void 0:e.is_show_img)&&e.img&&(0,F.jsx)("img",{src:e.img,alt:""})}),n(e.dialog_name)]}),t&&(0,F.jsx)(o.A,{})]})]},e.dialog_id):(0,F.jsx)(F.Fragment,{})},S=i=>{let{id:e,layoutConfig:d,item:o}=i;const a=(0,t.useRef)({}),{t:s}=(0,h.Bd)(),{openDialog:u}=(0,b.A)(),{subMenu:r,subContextMenuId:c}=(0,y.A)(),{editWidget:v}=(0,D.A)(),k=(0,l.d4)((i=>i.template.dialogs)),A=(0,l.d4)((i=>i.template.guides)),C=(0,l.d4)((i=>i.template.widgetData)),S=(0,l.d4)((i=>i.inputVariable.inputVariableMap)),[B,E]=(0,t.useState)(),[I,R]=(0,t.useState)(),V=`confirmed-${(0,p.HN)()}-${e}`,[O,L]=(0,t.useState)(sessionStorage.getItem(V)?JSON.parse(sessionStorage.getItem(V)):[]),Y=(0,t.useRef)(!0),Z=(0,t.useMemo)((()=>{var i,e,d;const t=null!==(i=null===(e=(0,f.Rm)(C,"widget_id",null===o||void 0===o?void 0:o.widget_id))||void 0===e||null===(d=e.data_source)||void 0===d?void 0:d.guide_ids)&&void 0!==i?i:[];return A.filter((i=>t.includes(i.guide_id)))}),[o,C,A]),H=(0,t.useMemo)((()=>Z.find((i=>i.guide_id===B))),[Z,B]),J=(0,t.useMemo)((()=>{var i,e,d;return null!==(i=null===H||void 0===H||null===(e=H.dialog_ids)||void 0===e||null===(d=e.map((i=>null===k||void 0===k?void 0:k.find((e=>e.dialog_id===i)))))||void 0===d?void 0:d.filter(Boolean))&&void 0!==i?i:[]}),[k,H]);(0,t.useEffect)((()=>{if("14"===o.widget_id&&Z&&0===Z.length&&Y.current){const i=(0,f.Rm)(C,"widget_id",null===o||void 0===o?void 0:o.widget_id);v({...i,data_source:{guide_ids:A.map((i=>i.guide_id))}}),Y.current=!1}}),[Z]),(0,t.useEffect)((()=>{var i,e,d;const t=null===(i=(0,p.l4)(o.widget_id))||void 0===i||null===(e=i.data)||void 0===e?void 0:e.guide_id;E(Z.some((i=>i.guide_id===t))?t:null===Z||void 0===Z||null===(d=Z[0])||void 0===d?void 0:d.guide_id)}),[Z]),(0,t.useEffect)((()=>{var i,e,d,t,l;const n=null===(i=(0,p.l4)(o.widget_id))||void 0===i||null===(e=i.data)||void 0===e?void 0:e.dialog_id;console.log(n);const a=J.some((i=>i.dialog_id===n))?n:null===J||void 0===J||null===(d=J[0])||void 0===d?void 0:d.dialog_id,s=(null===J||void 0===J?void 0:J.find((i=>i.dialog_id===a)))||{};if(!1===(null===S||void 0===S||null===(t=S.get(null===s||void 0===s?void 0:s.visible_bind_code))||void 0===t||null===(l=t.default_val)||void 0===l?void 0:l.value)){const i=G(0,"next");R(i),q(i)}else R(a),q(a)}),[J]);const W=async i=>{R(i.dialog_id),(0,p.ZY)({widget_id:o.widget_id,data:{dialog_id:i.dialog_id}}),q(i.dialog_id)},G=(i,e)=>{var d,t,l,n;let o=i,a=!1===(null===(d=S.get(null===J||void 0===J||null===(l=J[o])||void 0===l?void 0:l.visible_bind_code))||void 0===d||null===(t=d.default_val)||void 0===t?void 0:t.value);for(;a;){let d;if(d="last"===e?0===o?J.length-1:o-1:o===J.length-1?0:o+1,o=d,s=o,u=void 0,r=void 0,g=void 0,a=!1===(null===(u=S.get(null===J||void 0===J||null===(g=J[s])||void 0===g?void 0:g.visible_bind_code))||void 0===u||null===(r=u.default_val)||void 0===r?void 0:r.value),o===i)return}var s,u,r,g;return(null===J||void 0===J||null===(n=J[o])||void 0===n?void 0:n.dialog_id)||""},U=(i,e)=>{var d,t,l,n,a;let s=i,u=!1===(null===(d=S.get(null===J||void 0===J||null===(l=J[s])||void 0===l?void 0:l.visible_bind_code))||void 0===d||null===(t=d.default_val)||void 0===t?void 0:t.value);for(;u;){let d;if(d="last"===e?0===s?J.length-1:s-1:s===J.length-1?0:s+1,s=d,r=s,g=void 0,c=void 0,v=void 0,u=!1===(null===(g=S.get(null===J||void 0===J||null===(v=J[r])||void 0===v?void 0:v.visible_bind_code))||void 0===g||null===(c=g.default_val)||void 0===c?void 0:c.value),s===i)return}var r,g,c,v;const m=null===J||void 0===J||null===(n=J[s])||void 0===n?void 0:n.dialog_id;R(m),q(m),(0,p.ZY)({widget_id:o.widget_id,data:{dialog_id:null===J||void 0===J||null===(a=J[s])||void 0===a?void 0:a.dialog_id}})},q=i=>{if(i&&!O.includes(i)){const e=[...O,i];L(e),sessionStorage.setItem(V,JSON.stringify(e))}},K=[{key:"1",disabled:!1,label:(0,F.jsx)("a",{onClick:()=>{c(o.id),u({type:j.gi})},children:s("\u5411\u5bfc")})}];return(0,F.jsx)(z,{children:(0,F.jsx)("div",{className:"guide-container",onMouseUp:i=>r(null),children:(0,F.jsxs)(w.A,{sizes:[18,82],minSize:window.innerWidth/6,children:[(0,F.jsxs)("div",{className:"guide-list",children:[(0,F.jsxs)("div",{className:"guide-list-guide",children:[(0,F.jsx)("div",{className:"guide-name",children:s("\u53c2\u6570\u914d\u7f6e")}),(0,F.jsxs)("div",{className:"guide-list-layout",children:[(0,F.jsx)("div",{className:"list-layout",children:Z.map((i=>(0,F.jsx)("div",{title:s(i.guide_name),className:"guide "+(i.guide_id===B?"selected":""),onClick:()=>(i=>{E(i.guide_id),(0,p.ZY)({widget_id:o.widget_id,data:{guide_id:i.guide_id}}),a.current[i.guide_id]&&a.current[i.guide_id].scrollIntoView({behavior:"smooth",inline:"center"})})(i),ref:e=>{a.current[i.guide_id]=e},children:(0,F.jsx)("img",{src:i.guide_id===B?_.E8:_.Hl,className:i.guide_name===$.B2.GENERAL?"general":"extend",alt:""})},i.guide_id)))}),(0,F.jsx)("div",{className:"guide-more",children:(0,F.jsx)(n.A,{menu:{items:K},trigger:"click",placement:"bottomRight",children:(0,F.jsx)("img",{src:_.mj,alt:""})})})]})]}),(0,F.jsx)("div",{className:"guide-list-item",children:null===J||void 0===J?void 0:J.map((i=>(0,F.jsx)(M,{item:i,isOpt:i.dialog_id===I,isConfirmed:null===O||void 0===O?void 0:O.some((e=>e===i.dialog_id)),onClick:W})))})]}),(0,F.jsxs)("div",{className:"guide-layout",children:[(0,F.jsx)("div",{className:"guide-param-layout",children:(0,F.jsx)(N.default,{id:e,layoutConfig:d,dialogId:I,showImg:!0},I)}),(0,F.jsxs)("div",{className:"guide-footer",children:[(0,F.jsx)(x.A,{icon:(0,F.jsx)(g,{}),onClick:()=>(()=>{const i=J.findIndex((i=>i.dialog_id===I)),e=0===i?J.length-1:i-1;U(e,"last")})(),children:s("\u8fd4\u56de")}),(0,F.jsxs)(x.A,{onClick:()=>(()=>{const i=J.findIndex((i=>i.dialog_id===I)),e=i===J.length-1?0:i+1;U(e,"next")})(),children:[s("\u524d\u8fdb"),(0,F.jsx)(m,{})]})]})]})]})})})}}}]);
//# sourceMappingURL=3509.aaff45e8.chunk.js.map