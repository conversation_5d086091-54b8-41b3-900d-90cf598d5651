"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4068],{64068:(i,o,e)=>{e.r(o),e.d(o,{default:()=>L});var n=e(65043),l=e(80077),d=e(19853),s=e.n(d),r=e(36950),u=e(21256),v=e(68406),t=e(51238),a=e(754),x=e(72238),c=e(66966);const y=i=>{let o="";return a.A.getState().global.unitList.forEach((e=>{e.units.find((o=>o.code===i))&&(o=e.units.find((o=>o.code===i)).id)})),o};function p(i){if(null===i||void 0===i)return i;if(Array.isArray(i))return i.map((i=>p(i)));if("object"===typeof i){const o={};for(const[e,n]of Object.entries(i)){o[e.replace(/_([a-z])/g,((i,o)=>o.toUpperCase()))]=p(n)}return o}return i}function A(i){var o,e,n,l,d,s,r,u,v,a,A,m,T,L,f,h,k,C,b,z,S,_,w,E,R,G,U,N,Z,j,I,O,Y,D,M,$,q,F,B,J,P,W,H,K,Q,V,X,ii,oi,ei,ni,li,di,si,ri,ui,vi,ti,ai,xi,ci,yi,pi,Ai,gi,mi,Ti;const Li=p(i),fi="array"===(null===(o=Li.base)||void 0===o?void 0:o.sourceType)?x.xO.\u5355\u6570\u636e\u6e90:x.xO.\u591a\u6570\u636e\u6e90,hi=(null===(e=Li.base)||void 0===e?void 0:e.sourceInputCode)||"",ki=(0,c.L)({sourceType:fi,sourceInputCode:hi}),Ci={base:{isName:(null===(n=Li.base)||void 0===n?void 0:n.isName)||!1,name:(null===(l=Li.base)||void 0===l?void 0:l.name)||"",sourceType:fi,sourceInputCode:hi,updateFreq:(null===(d=Li.base)||void 0===d?void 0:d.updateFreq)||180,crossInputCode:(null===(s=Li.base)||void 0===s?void 0:s.crossInputCode)||""},curveGroup:{yAxis:{isEnable:!1,index:0,name:"Y1\u8f74\u66f2\u7ebf\u7ec4",xSignal:"",xUnit:"",ySignal:[],curves:{}},y2Axis:{isEnable:!1,index:1,name:"Y2\u8f74\u66f2\u7ebf\u7ec4",xSignal:"time",xUnit:"",ySignal:[],curves:{}}},xAxis:{name:(null===(r=Li.xAxis)||void 0===r?void 0:r.name)||"",unit:y(null===(u=Li.xAxis)||void 0===u?void 0:u.unit)||"",proportionType:(null===(v=Li.xAxis)||void 0===v?void 0:v.proportionType)||"not",lowLimit:(null===(a=Li.xAxis)||void 0===a?void 0:a.lowLimit)||0,upLimit:(null===(A=Li.xAxis)||void 0===A?void 0:A.upLimit)||10,lastRange:(null===(m=Li.xAxis)||void 0===m?void 0:m.lastRange)||10,isLog:"log"===(null===(T=Li.xAxis)||void 0===T?void 0:T.intervalType),type:(null===(L=Li.xAxis)||void 0===L?void 0:L.type)||"solid",thickness:(null===(f=Li.xAxis)||void 0===f?void 0:f.thickness)||2,color:(null===(h=Li.xAxis)||void 0===h?void 0:h.color)||"#000000",isGrid:(null===(k=Li.xAxis)||void 0===k?void 0:k.isGrid)||!1,gridType:(null===(C=Li.xAxis)||void 0===C?void 0:C.gridType)||"solid",gridThickness:(null===(b=Li.xAxis)||void 0===b?void 0:b.gridThickness)||1,gridColor:(null===(z=Li.xAxis)||void 0===z?void 0:z.gridColor)||"#000000",isZeroLine:(null===(S=Li.xAxis)||void 0===S?void 0:S.isZeroLine)||!1,zeroLineType:(null===(_=Li.xAxis)||void 0===_?void 0:_.zeroLineType)||"solid",zeroLineThickness:(null===(w=Li.xAxis)||void 0===w?void 0:w.zeroLineThickness)||1,zeroLineColor:(null===(E=Li.xAxis)||void 0===E?void 0:E.zeroLineColor)||"#000000"},yAxis:{name:(null===(R=Li.yAxis)||void 0===R?void 0:R.name)||"",proportionType:(null===(G=Li.yAxis)||void 0===G?void 0:G.proportionType)||"not",lowLimit:(null===(U=Li.yAxis)||void 0===U?void 0:U.lowLimit)||0,upLimit:(null===(N=Li.yAxis)||void 0===N?void 0:N.upLimit)||10,lastRange:(null===(Z=Li.yAxis)||void 0===Z?void 0:Z.lastRange)||10,isLog:"log"===(null===(j=Li.yAxis)||void 0===j?void 0:j.intervalType),type:(null===(I=Li.yAxis)||void 0===I?void 0:I.type)||"solid",thickness:(null===(O=Li.yAxis)||void 0===O?void 0:O.thickness)||2,color:(null===(Y=Li.yAxis)||void 0===Y?void 0:Y.color)||"#000000",isGrid:(null===(D=Li.yAxis)||void 0===D?void 0:D.isGrid)||!1,gridType:(null===(M=Li.yAxis)||void 0===M?void 0:M.gridType)||"solid",gridThickness:(null===($=Li.yAxis)||void 0===$?void 0:$.gridThickness)||1,gridColor:(null===(q=Li.yAxis)||void 0===q?void 0:q.gridColor)||"#000000",isZeroLine:(null===(F=Li.yAxis)||void 0===F?void 0:F.isZeroLine)||!1,zeroLineType:(null===(B=Li.yAxis)||void 0===B?void 0:B.zeroLineType)||"solid",zeroLineThickness:(null===(J=Li.yAxis)||void 0===J?void 0:J.zeroLineThickness)||1,zeroLineColor:(null===(P=Li.yAxis)||void 0===P?void 0:P.zeroLineColor)||"#000000"},y2Axis:{name:(null===(W=Li.y2Axis)||void 0===W?void 0:W.name)||"",proportionType:(null===(H=Li.y2Axis)||void 0===H?void 0:H.proportionType)||"not",lowLimit:(null===(K=Li.y2Axis)||void 0===K?void 0:K.lowLimit)||0,upLimit:(null===(Q=Li.y2Axis)||void 0===Q?void 0:Q.upLimit)||10,lastRange:(null===(V=Li.y2Axis)||void 0===V?void 0:V.lastRange)||10,isLog:"log"===(null===(X=Li.y2Axis)||void 0===X?void 0:X.intervalType),type:(null===(ii=Li.y2Axis)||void 0===ii?void 0:ii.type)||"solid",thickness:(null===(oi=Li.y2Axis)||void 0===oi?void 0:oi.thickness)||2,color:(null===(ei=Li.y2Axis)||void 0===ei?void 0:ei.color)||"#000000",isGrid:(null===(ni=Li.y2Axis)||void 0===ni?void 0:ni.isGrid)||!1,gridType:(null===(li=Li.y2Axis)||void 0===li?void 0:li.gridType)||"solid",gridThickness:(null===(di=Li.y2Axis)||void 0===di?void 0:di.gridThickness)||1,gridColor:(null===(si=Li.y2Axis)||void 0===si?void 0:si.gridColor)||"#000000",isZeroLine:(null===(ri=Li.y2Axis)||void 0===ri?void 0:ri.isZeroLine)||!1,zeroLineType:(null===(ui=Li.y2Axis)||void 0===ui?void 0:ui.zeroLineType)||"solid",zeroLineThickness:(null===(vi=Li.y2Axis)||void 0===vi?void 0:vi.zeroLineThickness)||1,zeroLineColor:(null===(ti=Li.y2Axis)||void 0===ti?void 0:ti.zeroLineColor)||"#000000"},auxiliary:Li.auxiliary||[],legend:{open:(null===(ai=Li.tag)||void 0===ai?void 0:ai.isLegend)||!0},pointTag:{open:(null===(xi=Li.tag)||void 0===xi?void 0:xi.isTag)||!1},chunkTag:{open:(null===(ci=Li.tag)||void 0===ci?void 0:ci.isChunkTag)||!1,list:(null===(yi=Li.tag)||void 0===yi?void 0:yi.chunkTags)||[]},tag:{isChunkTag:(null===(pi=Li.tag)||void 0===pi?void 0:pi.isChunkTag)||!1,chunkTags:(null===(Ai=Li.tag)||void 0===Ai?void 0:Ai.chunkTags)||[]},breakPoint:{},marker:Li.marker||[],defineAxis:{isDefineAxis:(null===(gi=Li.defineAxis)||void 0===gi?void 0:gi.isDefineAxis)||!1,inputCode:(null===(mi=Li.defineAxis)||void 0===mi?void 0:mi.inputCode)||"",source:(null===(Ti=Li.defineAxis)||void 0===Ti?void 0:Ti.source.map((i=>{var o,e;return{id:(0,t.A)(),label:null!==(o=null===i||void 0===i?void 0:i.label)&&void 0!==o?o:"",value:null===i||void 0===i?void 0:i.value,ySignal:null!==i&&void 0!==i&&i.yAxis?[i.yAxis]:[],yUnit:"",yName:"",xSignal:null!==(e=null===i||void 0===i?void 0:i.xAxis)&&void 0!==e?e:"",xUnit:"",xName:""}})))||[]}};return Li.curve&&Array.isArray(Li.curve)&&Li.curve.forEach(((i,o)=>{if(0===o)Ci.curveGroup.yAxis={isEnable:i.isEnable||!1,name:i.name||"Y1\u8f74\u66f2\u7ebf\u7ec4",xSignal:i.xAxis||"",xUnit:"",ySignal:i.yAxis||[],curves:g(i.styles||[[]],i.name,Li.yAxis.unit,ki)};else if(1===o){var e,n,l,d;const o=new Array(null!==(e=null===Li||void 0===Li||null===(n=Li.curve)||void 0===n||null===(l=n[0])||void 0===l||null===(d=l.styles)||void 0===d?void 0:d.length)&&void 0!==e?e:1).fill(0).map(((o,e)=>{var n,l;return null!==(n=null===i||void 0===i||null===(l=i.styles)||void 0===l?void 0:l[e])&&void 0!==n?n:[]}));Ci.curveGroup.y2Axis={isEnable:i.isEnable||!1,index:i.index||1,name:i.name||"Y2\u8f74\u66f2\u7ebf\u7ec4",xSignal:i.xAxis||"",xUnit:"",ySignal:i.yAxis||[],curves:g(o,i.name,Li.y2Axis.unit,ki)}}})),Ci}function g(i,o,e,n){if(!Array.isArray(i))return{};const l={};return i.forEach(((i,d)=>{Array.isArray(i)?l[d]={lines:i.map(((i,l)=>{var s,r;return{isLine:i.isLine||!0,lineType:i.lineType||"solid",lineThickness:i.lineThickness||1,isSign:i.isSign||!1,signType:i.signType||"o",signEach:i.signEach||!1,color:"transparent"===i.color?"#000000":i.color||"#000000",code:i.code||"",isApply:i.isApply||!1,yUnit:y(e),id:i.id||(0,t.A)(),name:i.name||`${o}-\u4e8c\u7ef4\u6570\u7ec4[${d}]-${null!==(s=null===(r=n.find((o=>o.code===i.code)))||void 0===r?void 0:r.name)&&void 0!==s?s:i.code}`,pointTags:[]}}))}:l[d]={lines:[]}})),l}var m=e(41234),T=e(70579);const L=i=>{var o;let{item:e,id:d,layoutConfig:t,isRightClick:a=!0}=i;const x=null!==e&&void 0!==e&&e.widget_data_source?JSON.parse(null===e||void 0===e?void 0:e.widget_data_source):null,c=(0,l.d4)((0,n.useCallback)((i=>i.template.arrayCurveConfigList.find((i=>i.curve_id===x))),[x])),y=(0,l.d4)((i=>i.template.widgetData)),p=(0,n.useMemo)((()=>(0,r.Rm)(y,"widget_id",null===e||void 0===e?void 0:e.widget_id)),[e,y]),{editWidget:g}=(0,u.A)(),[L,f]=(0,n.useState)();(0,n.useEffect)((()=>{var i;if(null!==p&&void 0!==p&&p.data_source&&null!==p&&void 0!==p&&null!==(i=p.data_source)&&void 0!==i&&i.curveGroup)s()(L,null===p||void 0===p?void 0:p.data_source)||f(null===p||void 0===p?void 0:p.data_source);else{if(c){const i=A(c);return console.log("\u914d\u7f6e",c,i),void h(i)}f(v.c)}}),[p,c]);const h=i=>{f(i),g({...p,data_source:i})},k=(0,n.useRef)(),C=(0,n.useMemo)((()=>{if(!L)return null;const{compStatus:i,...o}=L;return s()(k.current,o)?k.current:(k.current=o,o)}),[L]),b=(0,n.useRef)(),z=(0,n.useMemo)((()=>{if(!L)return null;const{compStatus:i}=L;return s()(b.current,i)?b.current:(b.current=i,i)}),[L]);return(0,T.jsx)(m.A,{id:d,layoutConfig:t,compName:null!==(o=null===e||void 0===e?void 0:e.name)&&void 0!==o?o:"",config:C,compStatus:z,updateConfig:h,isBufferCurve:!1,isRightClick:a})}}}]);
//# sourceMappingURL=4068.465493b4.chunk.js.map