"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4154],{4494:(i,d,l)=>{l.d(d,{A:()=>v});var e=l(80077),t=l(56543),n=l(8237),o=l(74117),a=l(10866),r=l(36950);const v=()=>{const{t:i}=(0,o.Bd)(),{getSample:d}=(0,a.A)(),l=(0,e.d4)((i=>i.project.sampleData)),v=(0,e.d4)((i=>i.project.optSample)),u=(0,e.d4)((i=>i.project.resultHistoryData)),s=(0,e.d4)((i=>i.template.resultData)),c=(0,e.d4)((i=>i.template.tableConfigData)),_=(0,e.d4)((i=>i.template.resultTestData)),m=(0,e.d4)((i=>i.global.unitList)),p=i=>{var l,e,n;const o=null===c||void 0===c?void 0:c.find((d=>(null===d||void 0===d?void 0:d.id)===i)),a=null===(l=d(v))||void 0===l?void 0:l.data,r=(null===o||void 0===o?void 0:o.param)||[],u=(null===o||void 0===o?void 0:o.setting)||{min_width:100,max_width:200,is_name:!0},p=(null===o||void 0===o?void 0:o.statistics)||[],f=null===o||void 0===o||null===(e=o.sample_param)||void 0===e?void 0:e.map((i=>i.parameter_id)),b=null===a||void 0===a?void 0:a.filter((i=>(null===f||void 0===f?void 0:f.includes(i.parameter_id))&&!i.hidden_flag));var h,g;return{data:null===(h=b,g=null===o||void 0===o?void 0:o.type,n=h&&h.length>0?[...null===s||void 0===s?void 0:s.filter((i=>{var d;return null===i||void 0===i||null===(d=i.display_modes)||void 0===d?void 0:d.includes(g)})).map((i=>{var d,l,e;return{...i,unit_name:null===m||void 0===m||null===(d=m.find((d=>(null===d||void 0===d?void 0:d.id)===(null===i||void 0===i?void 0:i.dimension_id))))||void 0===d||null===(l=d.units)||void 0===l||null===(e=l.find((d=>(null===d||void 0===d?void 0:d.id)===(null===i||void 0===i?void 0:i.unit_id))))||void 0===e?void 0:e.name}})),...null===h||void 0===h?void 0:h.map((i=>{var d,l,e,n;return{result_variable_id:i.parameter_id,variable_name:i.parameter_name,type:t.l4.RESULT_TYPE,code:null===i||void 0===i?void 0:i.code,abbreviation:null!==(d=i.abbreviation)&&void 0!==d?d:"",unit_id:i.units_id,dimension_id:i.dimension_id,unit_name:null===m||void 0===m||null===(l=m.find((d=>(null===d||void 0===d?void 0:d.id)===(null===i||void 0===i?void 0:i.dimension_id))))||void 0===l||null===(e=l.units)||void 0===e||null===(n=e.find((d=>(null===d||void 0===d?void 0:d.id)===(null===i||void 0===i?void 0:i.units_id))))||void 0===n?void 0:n.name,value:i.value}}))]:s)||void 0===n?void 0:n.filter((i=>(null===r||void 0===r?void 0:r.includes(i.result_variable_id))&&([t.l4.RESULT_TABLE,t.l4.LABEL].includes(i.type)||(null===_||void 0===_?void 0:_.map((i=>i.id)).includes(i.result_variable_id))))).sort(((i,d)=>(null===r||void 0===r?void 0:r.indexOf(null===i||void 0===i?void 0:i.result_variable_id))-(null===r||void 0===r?void 0:r.indexOf(null===d||void 0===d?void 0:d.result_variable_id)))),setting:u,statistics:p}};return{getColData:p,getTableData:i=>{const d=null===l||void 0===l?void 0:l.flatMap((i=>{const{children:d,name:l}=i;return d.map((i=>({...i,parentName:l})))})).filter((i=>!i.disabled&&i.status!==t.$y.READY)),{data:e,setting:n}=p(i);return{tableData:d.map((i=>{var d;const l={sample_color:i.color,sample_code:i.code,sample_name:i.name,sample_key:i.key},t=null!==(d=null===u||void 0===u?void 0:u[i.code])&&void 0!==d?d:[];return e.reduce(((d,l)=>{var e;const n=l.unit_id||l.units_id;if("display_modes"in l){var o,a;const{format_type:i,format_info:e,code:n,dimension_id:v,unit_id:u}=l,s=null!==(o=null===t||void 0===t?void 0:t.find((i=>i.code===n)))&&void 0!==o?o:{};let c=null===s||void 0===s?void 0:s.value;return"number"===typeof c&&(c=(0,r.jq)(i,(0,r.tJ)(c,v,u),(0,r._q)(i,e))),{...d,...l,result_variable_id:l.result_variable_id,[l.code]:c,errors:[...null!==(a=null===d||void 0===d?void 0:d.errors)&&void 0!==a?a:[],s.error&&{error:s.error,msg:s.errorMessage,code:s.code}].filter(Boolean)}}const v=null!==(e=i.data.find((i=>i.parameter_id===l.result_variable_id)))&&void 0!==e?e:{},u=(0,r.tJ)(null===v||void 0===v?void 0:v.value,v.dimension_id,null===v||void 0===v?void 0:v.units_id);return{...d,...l,result_variable_id:l.result_variable_id,[l.code]:(0,r.tJ)(u,null===v||void 0===v?void 0:v.dimension_id,n,null===v||void 0===v?void 0:v.units_id)}}),l)})),colData:e,setting:n}},getsStatisticTableData:d=>{var l;const{statistics:e,setting:t,data:o}=null!==(l=p(d))&&void 0!==l?l:{};return{colData:null!==o&&void 0!==o?o:[],setting:t,tableData:(0,n.Qu)({t:i}).filter((i=>null===e||void 0===e?void 0:e.includes(i.id))).sort(((i,d)=>(null===e||void 0===e?void 0:e.indexOf(i.id))-(null===e||void 0===e?void 0:e.indexOf(d.id))))}}}}},43880:(i,d,l)=>{l.d(d,{A:()=>u});var e=l(65043),t=l(74117),n=l(81143),o=l(68374);const a=n.Ay.div`
    .title-layout {
        display: flex;
        align-items: center;
        margin-bottom: ${(0,o.D0)("10px")};
        .pillar {
            width: ${(0,o.D0)("8px")};
            height: ${(0,o.D0)("20px")};
            background: #0091FF;
            border-radius: 2px;
        }
        .title {
            margin-left: 0.5vw;
            font-size: 14px;
            font-weight: 600;
            color: #333333;
            line-height: 20px;
        }
    }
`;n.Ay.div`
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction:column ;
    .circle {
        border-radius: 50%;
        width: 1.5vh;
        height: 1.5vh;
    }
`;var r=l(70579);const v=(i,d)=>{let{title:l}=i;const{t:e}=(0,t.Bd)();return(0,r.jsx)(a,{children:(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"title-layout",ref:d,children:[(0,r.jsx)("div",{className:"pillar"}),(0,r.jsx)("div",{className:"title",children:e(l)})]})})})},u=(0,e.forwardRef)(v)},84674:(i,d,l)=>{l.d(d,{A:()=>v});var e=l(65043),t=l(96651),n=l(81143);l(68374);const o=n.Ay.div`

  
`;l(4554);var a=l(70579);const r=(i,d)=>{const{children:l,title:e}=i;return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(o,{children:(0,a.jsx)(t.A,{...i,destroyTooltipOnHide:{keepParent:!1},ref:d,children:l})})})},v=(0,e.forwardRef)(r)}}]);
//# sourceMappingURL=4154.6549a318.chunk.js.map