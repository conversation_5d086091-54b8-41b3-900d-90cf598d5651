"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4219],{94219:(t,e,i)=>{i.r(e),i.d(e,{AElement:()=>Rt,AnimateColorElement:()=>Nt,AnimateElement:()=>Ot,AnimateTransformElement:()=>Dt,BoundingBox:()=>ot,CB1:()=>O,CB2:()=>N,CB3:()=>D,CB4:()=>z,Canvg:()=>fe,CircleElement:()=>vt,ClipPathElement:()=>ee,DefsElement:()=>Ct,DescElement:()=>oe,Document:()=>ce,Element:()=>it,EllipseElement:()=>xt,FeColorMatrixElement:()=>Jt,FeCompositeElement:()=>ne,FeDropShadowElement:()=>re,FeG<PERSON>sianBlurElement:()=>ae,FeMorphologyElement:()=>se,FilterElement:()=>ie,Font:()=>ht,FontElement:()=>zt,FontFaceElement:()=>Bt,GElement:()=>Tt,GlyphElement:()=>ct,GradientElement:()=>Vt,ImageElement:()=>Yt,LineElement:()=>bt,LinearGradientElement:()=>Et,MarkerElement:()=>At,MaskElement:()=>Kt,Matrix:()=>Z,MissingGlyphElement:()=>Lt,Mouse:()=>X,PSEUDO_ZERO:()=>V,Parser:()=>q,PathElement:()=>gt,PathParser:()=>lt,PatternElement:()=>wt,Point:()=>_,PolygonElement:()=>Pt,PolylineElement:()=>St,Property:()=>R,QB1:()=>B,QB2:()=>L,QB3:()=>I,RadialGradientElement:()=>Mt,RectElement:()=>mt,RenderedElement:()=>ut,Rotate:()=>Q,SVGElement:()=>yt,SVGFontLoader:()=>Ht,Scale:()=>$,Screen:()=>U,Skew:()=>J,SkewX:()=>K,SkewY:()=>tt,StopElement:()=>kt,StyleElement:()=>Wt,SymbolElement:()=>Ut,TRefElement:()=>It,TSpanElement:()=>pt,TextElement:()=>dt,TextPathElement:()=>Xt,TitleElement:()=>he,Transform:()=>et,Translate:()=>G,UnknownElement:()=>rt,UseElement:()=>qt,ViewPort:()=>F,compressSpaces:()=>u,default:()=>fe,getSelectorSpecificity:()=>T,normalizeAttributeName:()=>f,normalizeColor:()=>m,parseExternalUrl:()=>y,presets:()=>l,toNumbers:()=>d,trimLeft:()=>g,trimRight:()=>c,vectorMagnitude:()=>E,vectorsAngle:()=>k,vectorsRatio:()=>M});var r=i(10467),s=i(64467),n=i(8073),a=i(34566),h=i(26),o=i(64917);var l=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>(0,r.A)((function*(){var e=yield fetch(t),i=yield e.blob();return yield createImageBitmap(i)}))()};return"undefined"===typeof DOMParser&&"undefined"!==typeof t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:i,fetch:r}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:r,createCanvas:i.createCanvas,createImage:i.loadImage}}});function u(t){return t.replace(/(?!\u3000)\s+/gm," ")}function g(t){return t.replace(/^[\n \t]+/,"")}function c(t){return t.replace(/[\n \t]+$/,"")}function d(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var p=/^[A-Z-]+$/;function f(t){return p.test(t)?t.toLowerCase():t}function y(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function m(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,((t,i)=>e--&&i?String(Math.round(parseFloat(t))):t))}var v=/(\[[^\]]+\])/g,x=/(#[^\s+>~.[:]+)/g,b=/(\.[^\s+>~.[:]+)/g,S=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,P=/(:[\w-]+\([^)]*\))/gi,w=/(:[^\s+>~.[:]+)/g,A=/([^\s+>~.[:]+)/g;function C(t,e){var i=e.exec(t);return i?[t.replace(e," "),i.length]:[t,0]}function T(t){var e=[0,0,0],i=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),r=0;return[i,r]=C(i,v),e[1]+=r,[i,r]=C(i,x),e[0]+=r,[i,r]=C(i,b),e[1]+=r,[i,r]=C(i,S),e[2]+=r,[i,r]=C(i,P),e[1]+=r,[i,r]=C(i,w),e[1]+=r,i=i.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[i,r]=C(i,A),e[2]+=r,e.join("")}var V=1e-8;function E(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function M(t,e){return(t[0]*e[0]+t[1]*e[1])/(E(t)*E(e))}function k(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(M(t,e))}function O(t){return t*t*t}function N(t){return 3*t*t*(1-t)}function D(t){return 3*t*(1-t)*(1-t)}function z(t){return(1-t)*(1-t)*(1-t)}function B(t){return t*t}function L(t){return 2*t*(1-t)}function I(t){return(1-t)*(1-t)}class R{constructor(t,e,i){this.document=t,this.name=e,this.value=i,this.isNormalizedColor=!1}static empty(t){return new R(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:i}=this;return u(this.getString()).trim().split(t).map((t=>new R(e,i,t)))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&"undefined"!==typeof e}isString(t){var{value:e}=this,i="string"===typeof e;return i&&t?t.test(e):i}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return"undefined"===typeof t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return"undefined"===typeof t?0:parseFloat(t);var{value:e}=this,i=parseFloat(e);return this.isString(/%$/)&&(i/=100),i}getString(t){return"undefined"===typeof t||this.hasValue()?"undefined"===typeof this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=m(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[i,r]="boolean"===typeof t?[void 0,t]:[t],{viewPort:s}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(s.computeSize("x"),s.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(s.computeSize("x"),s.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*s.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*s.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&r:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*s.computeSize(i);default:var n=this.getNumber();return e&&n<1?n*s.computeSize(i):n}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var i=this.getDefinition();if(!i)return null;if("function"===typeof i.createGradient)return i.createGradient(this.document.ctx,t,e);if("function"===typeof i.createPattern){if(i.getHrefAttribute().hasValue()){var r=i.getAttribute("patternTransform");i=i.getHrefAttribute().getDefinition(),r.hasValue()&&i.getAttribute("patternTransform",!0).setValue(r.value)}return i.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?R.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),i=e.length,r=0,s=0;s<i&&(","===e[s]&&r++,3!==r);s++);if(t.hasValue()&&this.isString()&&3!==r){var n=new a(e);n.ok&&(n.alpha=t.getNumber(),e=n.toRGBA())}return new R(this.document,this.name,e)}}R.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class F{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"===typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class _{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[i=e,r=e]=d(t);return new _(i,r)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[i=e,r=i]=d(t);return new _(i,r)}static parsePath(t){for(var e=d(t),i=e.length,r=[],s=0;s<i;s+=2)r.push(new _(e[s],e[s+1]));return r}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:i}=this,r=e*t[0]+i*t[2]+t[4],s=e*t[1]+i*t[3]+t[5];this.x=r,this.y=s}}class X{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:i}=this,r=t.ctx.canvas;r.onclick=e,r.onmousemove=i,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:i}=this,{style:r}=t.ctx.canvas;r&&(r.cursor=""),e.forEach(((t,e)=>{for(var{run:r}=t,s=i[e];s;)r(s),s=s.parent})),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:i,eventElements:r}=this;i.forEach(((i,s)=>{var{x:n,y:a}=i;!r[s]&&e.isPointInPath&&e.isPointInPath(n,a)&&(r[s]=t)}))}}checkBoundingBox(t,e){if(this.working&&e){var{events:i,eventElements:r}=this;i.forEach(((i,s)=>{var{x:n,y:a}=i;!r[s]&&e.isPointInBox(n,a)&&(r[s]=t)}))}}mapXY(t,e){for(var{window:i,ctx:r}=this.screen,s=new _(t,e),n=r.canvas;n;)s.x-=n.offsetLeft,s.y-=n.offsetTop,n=n.offsetParent;return i.scrollX&&(s.x+=i.scrollX),i.scrollY&&(s.y+=i.scrollY),s}onClick(t){var{x:e,y:i}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:i,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:i}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:i,run(t){t.onMouseMove&&t.onMouseMove()}})}}var j="undefined"!==typeof window?window:null,Y="undefined"!==typeof fetch?fetch.bind(void 0):null;class U{constructor(t){var{fetch:e=Y,window:i=j}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new F,this.mouse=new X(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=i,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every((t=>t()));return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:i,aspectRatio:r,width:s,desiredWidth:n,height:a,desiredHeight:h,minX:o=0,minY:l=0,refX:g,refY:c,clip:d=!1,clipX:p=0,clipY:f=0}=t,y=u(r).replace(/^defer\s/,""),[m,v]=y.split(" "),x=m||"xMidYMid",b=v||"meet",S=s/n,P=a/h,w=Math.min(S,P),A=Math.max(S,P),C=n,T=h;"meet"===b&&(C*=w,T*=w),"slice"===b&&(C*=A,T*=A);var V=new R(e,"refX",g),E=new R(e,"refY",c),M=V.hasValue()&&E.hasValue();if(M&&i.translate(-w*V.getPixels("x"),-w*E.getPixels("y")),d){var k=w*p,O=w*f;i.beginPath(),i.moveTo(k,O),i.lineTo(s,O),i.lineTo(s,a),i.lineTo(k,a),i.closePath(),i.clip()}if(!M){var N="meet"===b&&w===P,D="slice"===b&&A===P,z="meet"===b&&w===S,B="slice"===b&&A===S;x.startsWith("xMid")&&(N||D)&&i.translate(s/2-C/2,0),x.endsWith("YMid")&&(z||B)&&i.translate(0,a/2-T/2),x.startsWith("xMax")&&(N||D)&&i.translate(s-C,0),x.endsWith("YMax")&&(z||B)&&i.translate(0,a-T)}switch(!0){case"none"===x:i.scale(S,P);break;case"meet"===b:i.scale(w,w);break;case"slice"===b:i.scale(A,A)}i.translate(-o,-l)}start(t){var{enableRedraw:e=!1,ignoreMouse:i=!1,ignoreAnimation:r=!1,ignoreDimensions:s=!1,ignoreClear:a=!1,forceRedraw:h,scaleWidth:o,scaleHeight:l,offsetX:u,offsetY:g}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:c,mouse:d}=this,p=1e3/c;if(this.frameDuration=p,this.readyPromise=new Promise((t=>{this.resolveReady=t})),this.isReady()&&this.render(t,s,a,o,l,u,g),e){var f=Date.now(),y=f,m=0,v=()=>{f=Date.now(),(m=f-y)>=p&&(y=f-m%p,this.shouldUpdate(r,h)&&(this.render(t,s,a,o,l,u,g),d.runEvents())),this.intervalId=n(v)};i||d.start(),this.intervalId=n(v)}}stop(){this.intervalId&&(n.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:i}=this;if(this.animations.reduce(((t,e)=>e.update(i)||t),!1))return!0}return!("function"!==typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,i,r,s,n,a){var{CLIENT_WIDTH:h,CLIENT_HEIGHT:o,viewPort:l,ctx:u,isFirstRender:g}=this,c=u.canvas;l.clear(),c.width&&c.height?l.setCurrent(c.width,c.height):l.setCurrent(h,o);var p=t.getStyle("width"),f=t.getStyle("height");!e&&(g||"number"!==typeof r&&"number"!==typeof s)&&(p.hasValue()&&(c.width=p.getPixels("x"),c.style&&(c.style.width="".concat(c.width,"px"))),f.hasValue()&&(c.height=f.getPixels("y"),c.style&&(c.style.height="".concat(c.height,"px"))));var y=c.clientWidth||c.width,m=c.clientHeight||c.height;if(e&&p.hasValue()&&f.hasValue()&&(y=p.getPixels("x"),m=f.getPixels("y")),l.setCurrent(y,m),"number"===typeof n&&t.getAttribute("x",!0).setValue(n),"number"===typeof a&&t.getAttribute("y",!0).setValue(a),"number"===typeof r||"number"===typeof s){var v=d(t.getAttribute("viewBox").getString()),x=0,b=0;if("number"===typeof r){var S=t.getStyle("width");S.hasValue()?x=S.getPixels("x")/r:isNaN(v[2])||(x=v[2]/r)}if("number"===typeof s){var P=t.getStyle("height");P.hasValue()?b=P.getPixels("y")/s:isNaN(v[3])||(b=v[3]/s)}x||(x=b),b||(b=x),t.getAttribute("width",!0).setValue(r),t.getAttribute("height",!0).setValue(s);var w=t.getStyle("transform",!0,!0);w.setValue("".concat(w.getString()," scale(").concat(1/x,", ").concat(1/b,")"))}i||u.clearRect(0,0,y,m),t.render(u),g&&(this.isFirstRender=!1)}}U.defaultWindow=j,U.defaultFetch=Y;var{defaultFetch:H}=U,W="undefined"!==typeof DOMParser?DOMParser:null;class q{constructor(){var{fetch:t=H,DOMParser:e=W}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return(0,r.A)((function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)}))()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(i){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return(0,r.A)((function*(){var i=yield e.fetch(t),r=yield i.text();return e.parseFromString(r)}))()}}class G{constructor(t,e){this.type="translate",this.point=null,this.point=_.parse(e)}apply(t){var{x:e,y:i}=this.point;t.translate(e||0,i||0)}unapply(t){var{x:e,y:i}=this.point;t.translate(-1*e||0,-1*i||0)}applyToPoint(t){var{x:e,y:i}=this.point;t.applyTransform([1,0,0,1,e||0,i||0])}}class Q{constructor(t,e,i){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var r=d(e);this.angle=new R(t,"angle",r[0]),this.originX=i[0],this.originY=i[1],this.cx=r[1]||0,this.cy=r[2]||0}apply(t){var{cx:e,cy:i,originX:r,originY:s,angle:n}=this,a=e+r.getPixels("x"),h=i+s.getPixels("y");t.translate(a,h),t.rotate(n.getRadians()),t.translate(-a,-h)}unapply(t){var{cx:e,cy:i,originX:r,originY:s,angle:n}=this,a=e+r.getPixels("x"),h=i+s.getPixels("y");t.translate(a,h),t.rotate(-1*n.getRadians()),t.translate(-a,-h)}applyToPoint(t){var{cx:e,cy:i,angle:r}=this,s=r.getRadians();t.applyTransform([1,0,0,1,e||0,i||0]),t.applyTransform([Math.cos(s),Math.sin(s),-Math.sin(s),Math.cos(s),0,0]),t.applyTransform([1,0,0,1,-e||0,-i||0])}}class ${constructor(t,e,i){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var r=_.parseScale(e);0!==r.x&&0!==r.y||(r.x=V,r.y=V),this.scale=r,this.originX=i[0],this.originY=i[1]}apply(t){var{scale:{x:e,y:i},originX:r,originY:s}=this,n=r.getPixels("x"),a=s.getPixels("y");t.translate(n,a),t.scale(e,i||e),t.translate(-n,-a)}unapply(t){var{scale:{x:e,y:i},originX:r,originY:s}=this,n=r.getPixels("x"),a=s.getPixels("y");t.translate(n,a),t.scale(1/e,1/i||e),t.translate(-n,-a)}applyToPoint(t){var{x:e,y:i}=this.scale;t.applyTransform([e||0,0,0,i||0,0,0])}}class Z{constructor(t,e,i){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=d(e),this.originX=i[0],this.originY=i[1]}apply(t){var{originX:e,originY:i,matrix:r}=this,s=e.getPixels("x"),n=i.getPixels("y");t.translate(s,n),t.transform(r[0],r[1],r[2],r[3],r[4],r[5]),t.translate(-s,-n)}unapply(t){var{originX:e,originY:i,matrix:r}=this,s=r[0],n=r[2],a=r[4],h=r[1],o=r[3],l=r[5],u=1/(s*(1*o-0*l)-n*(1*h-0*l)+a*(0*h-0*o)),g=e.getPixels("x"),c=i.getPixels("y");t.translate(g,c),t.transform(u*(1*o-0*l),u*(0*l-1*h),u*(0*a-1*n),u*(1*s-0*a),u*(n*l-a*o),u*(a*h-s*l)),t.translate(-g,-c)}applyToPoint(t){t.applyTransform(this.matrix)}}class J extends Z{constructor(t,e,i){super(t,e,i),this.type="skew",this.angle=null,this.angle=new R(t,"angle",e)}}class K extends J{constructor(t,e,i){super(t,e,i),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class tt extends J{constructor(t,e,i){super(t,e,i),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class et{constructor(t,e,i){this.document=t,this.transforms=[];var r=function(t){return u(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e);r.forEach((t=>{if("none"!==t){var[e,r]=function(t){var[e,i]=t.split("(");return[e.trim(),i.trim().replace(")","")]}(t),s=et.transformTypes[e];"undefined"!==typeof s&&this.transforms.push(new s(this.document,r,i))}}))}static fromElement(t,e){var i=e.getStyle("transform",!1,!0),[r,s=r]=e.getStyle("transform-origin",!1,!0).split(),n=[r,s];return i.hasValue()?new et(t,i.getString(),n):null}apply(t){for(var{transforms:e}=this,i=e.length,r=0;r<i;r++)e[r].apply(t)}unapply(t){for(var{transforms:e}=this,i=e.length-1;i>=0;i--)e[i].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,i=e.length,r=0;r<i;r++)e[r].applyToPoint(t)}}et.transformTypes={translate:G,rotate:Q,scale:$,matrix:Z,skewX:K,skewY:tt};class it{constructor(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=i,this.attributes={},this.styles={},this.stylesSpecificity={},this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach((e=>{var i=f(e.nodeName);this.attributes[i]=new R(t,i,e.value)})),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue())this.getAttribute("style").getString().split(";").map((t=>t.trim())).forEach((e=>{if(e){var[i,r]=e.split(":").map((t=>t.trim()));this.styles[i]=new R(t,i,r)}}));var{definitions:r}=t,s=this.getAttribute("id");s.hasValue()&&(r[s.getString()]||(r[s.getString()]=this)),Array.from(e.childNodes).forEach((e=>{if(1===e.nodeType)this.addChild(e);else if(i&&(3===e.nodeType||4===e.nodeType)){var r=t.createTextNode(e);r.getText().length>0&&this.addChild(r)}}))}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.attributes[t];if(!i&&e){var r=new R(this.document,t,"");return this.attributes[t]=r,r}return i||R.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return R.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=this.styles[t];if(r)return r;var s=this.getAttribute(t);if(null!==s&&void 0!==s&&s.hasValue())return this.styles[t]=s,s;if(!i){var{parent:n}=this;if(n){var a=n.getStyle(t);if(null!==a&&void 0!==a&&a.hasValue())return a}}if(e){var h=new R(this.document,t,"");return this.styles[t]=h,h}return r||R.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var i=this.getStyle("filter").getDefinition();i&&(this.applyEffects(t),i.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=et.fromElement(this.document,this);e&&e.apply(t);var i=this.getStyle("clip-path",!1,!0);if(i.hasValue()){var r=i.getDefinition();r&&r.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach((e=>{e.render(t)}))}addChild(t){var e=t instanceof it?t:this.document.createElement(t);e.parent=this,it.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:i}=this;if("function"===typeof i.matches)return i.matches(t);var r=null===(e=i.getAttribute)||void 0===e?void 0:e.call(i,"class");return!(!r||""===r)&&r.split(" ").some((e=>".".concat(e)===t))}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var i in t)if(!i.startsWith("@")&&this.matchesSelector(i)){var r=t[i],s=e[i];if(r)for(var n in r){var a=this.stylesSpecificity[n];"undefined"===typeof a&&(a="000"),s>=a&&(this.styles[n]=r[n],this.stylesSpecificity[n]=s)}}}removeStyles(t,e){return e.reduce(((e,i)=>{var r=t.getStyle(i);if(!r.hasValue())return e;var s=r.getString();return r.setValue(""),[...e,[i,s]]}),[])}restoreStyles(t,e){e.forEach((e=>{var[i,r]=e;t.getStyle(i,!0).setValue(r)}))}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}it.ignoreChildTypes=["title"];class rt extends it{constructor(t,e,i){super(t,e,i)}}function st(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function nt(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function at(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class ht{constructor(t,e,i,r,s,n){var a=n?"string"===typeof n?ht.parse(n):n:{};this.fontFamily=s||a.fontFamily,this.fontSize=r||a.fontSize,this.fontStyle=t||a.fontStyle,this.fontWeight=i||a.fontWeight,this.fontVariant=e||a.fontVariant}static parse(){var t=arguments.length>1?arguments[1]:void 0,e="",i="",r="",s="",n="",a=u(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),h={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return a.forEach((t=>{switch(!0){case!h.fontStyle&&ht.styles.includes(t):"inherit"!==t&&(e=t),h.fontStyle=!0;break;case!h.fontVariant&&ht.variants.includes(t):"inherit"!==t&&(i=t),h.fontStyle=!0,h.fontVariant=!0;break;case!h.fontWeight&&ht.weights.includes(t):"inherit"!==t&&(r=t),h.fontStyle=!0,h.fontVariant=!0,h.fontWeight=!0;break;case!h.fontSize:"inherit"!==t&&([s]=t.split("/")),h.fontStyle=!0,h.fontVariant=!0,h.fontWeight=!0,h.fontSize=!0;break;default:"inherit"!==t&&(n+=t)}})),new ht(e,i,r,s,n,t)}toString(){return[nt(this.fontStyle),this.fontVariant,at(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"===typeof process?t:t.trim().split(",").map(st).join(","))].join(" ").trim();var t}}ht.styles="normal|italic|oblique|inherit",ht.variants="normal|small-caps|inherit",ht.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class ot{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=i,this.y2=r,this.addPoint(t,e),this.addPoint(i,r)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){"undefined"!==typeof t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),"undefined"!==typeof e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:i,x2:r,y2:s}=t;this.addPoint(e,i),this.addPoint(r,s)}}sumCubic(t,e,i,r,s){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*i+3*(1-t)*Math.pow(t,2)*r+Math.pow(t,3)*s}bezierCurveAdd(t,e,i,r,s){var n=6*e-12*i+6*r,a=-3*e+9*i-9*r+3*s,h=3*i-3*e;if(0!==a){var o=Math.pow(n,2)-4*h*a;if(!(o<0)){var l=(-n+Math.sqrt(o))/(2*a);0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,i,r,s)):this.addY(this.sumCubic(l,e,i,r,s)));var u=(-n-Math.sqrt(o))/(2*a);0<u&&u<1&&(t?this.addX(this.sumCubic(u,e,i,r,s)):this.addY(this.sumCubic(u,e,i,r,s)))}}else{if(0===n)return;var g=-h/n;0<g&&g<1&&(t?this.addX(this.sumCubic(g,e,i,r,s)):this.addY(this.sumCubic(g,e,i,r,s)))}}addBezierCurve(t,e,i,r,s,n,a,h){this.addPoint(t,e),this.addPoint(a,h),this.bezierCurveAdd(!0,t,i,s,a),this.bezierCurveAdd(!1,e,r,n,h)}addQuadraticCurve(t,e,i,r,s,n){var a=t+2/3*(i-t),h=e+2/3*(r-e),o=a+1/3*(s-t),l=h+1/3*(n-e);this.addBezierCurve(t,e,a,o,h,l,s,n)}isPointInBox(t,e){var{x1:i,y1:r,x2:s,y2:n}=this;return i<=t&&t<=s&&r<=e&&e<=n}}class lt extends h.LQ{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new _(0,0),this.control=new _(0,0),this.current=new _(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",i=new _(this.command[t],this.command[e]);return this.makeAbsolute(i)}getAsControlPoint(t,e){var i=this.getPoint(t,e);return this.control=i,i}getAsCurrentPoint(t,e){var i=this.getPoint(t,e);return this.current=i,i}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==h.LQ.CURVE_TO&&t!==h.LQ.SMOOTH_CURVE_TO&&t!==h.LQ.QUAD_TO&&t!==h.LQ.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:i},control:{x:r,y:s}}=this;return new _(2*e-r,2*i-s)}makeAbsolute(t){if(this.command.relative){var{x:e,y:i}=this.current;t.x+=e,t.y+=i}return t}addMarker(t,e,i){var{points:r,angles:s}=this;i&&s.length>0&&!s[s.length-1]&&(s[s.length-1]=r[r.length-1].angleTo(i)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,i=0;i<e;i++)if(!t[i])for(var r=i+1;r<e;r++)if(t[r]){t[i]=t[r];break}return t}}class ut extends it{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var i=e.getStyle("opacity",!1,!0);i.hasValue(!0)&&(t*=i.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var i=this.getStyle("fill"),r=this.getStyle("fill-opacity"),s=this.getStyle("stroke"),n=this.getStyle("stroke-opacity");if(i.isUrlDefinition()){var a=i.getFillStyleDefinition(this,r);a&&(t.fillStyle=a)}else if(i.hasValue()){"currentColor"===i.getString()&&i.setValue(this.getStyle("color").getColor());var h=i.getColor();"inherit"!==h&&(t.fillStyle="none"===h?"rgba(0,0,0,0)":h)}if(r.hasValue()){var o=new R(this.document,"fill",t.fillStyle).addOpacity(r).getColor();t.fillStyle=o}if(s.isUrlDefinition()){var l=s.getFillStyleDefinition(this,n);l&&(t.strokeStyle=l)}else if(s.hasValue()){"currentColor"===s.getString()&&s.setValue(this.getStyle("color").getColor());var u=s.getString();"inherit"!==u&&(t.strokeStyle="none"===u?"rgba(0,0,0,0)":u)}if(n.hasValue()){var g=new R(this.document,"stroke",t.strokeStyle).addOpacity(n).getString();t.strokeStyle=g}var c=this.getStyle("stroke-width");if(c.hasValue()){var p=c.getPixels();t.lineWidth=p||V}var f=this.getStyle("stroke-linecap"),y=this.getStyle("stroke-linejoin"),m=this.getStyle("stroke-miterlimit"),v=this.getStyle("stroke-dasharray"),x=this.getStyle("stroke-dashoffset");if(f.hasValue()&&(t.lineCap=f.getString()),y.hasValue()&&(t.lineJoin=y.getString()),m.hasValue()&&(t.miterLimit=m.getNumber()),v.hasValue()&&"none"!==v.getString()){var b=d(v.getString());"undefined"!==typeof t.setLineDash?t.setLineDash(b):"undefined"!==typeof t.webkitLineDash?t.webkitLineDash=b:"undefined"===typeof t.mozDash||1===b.length&&0===b[0]||(t.mozDash=b);var S=x.getPixels();"undefined"!==typeof t.lineDashOffset?t.lineDashOffset=S:"undefined"!==typeof t.webkitLineDashOffset?t.webkitLineDashOffset=S:"undefined"!==typeof t.mozDashOffset&&(t.mozDashOffset=S)}}if(this.modifiedEmSizeStack=!1,"undefined"!==typeof t.font){var P=this.getStyle("font"),w=this.getStyle("font-style"),A=this.getStyle("font-variant"),C=this.getStyle("font-weight"),T=this.getStyle("font-size"),E=this.getStyle("font-family"),M=new ht(w.getString(),A.getString(),C.getString(),T.hasValue()?"".concat(T.getPixels(!0),"px"):"",E.getString(),ht.parse(P.getString(),t.font));w.setValue(M.fontStyle),A.setValue(M.fontVariant),C.setValue(M.fontWeight),T.setValue(M.fontSize),E.setValue(M.fontFamily),t.font=M.toString(),T.isPixels()&&(this.document.emSize=T.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class gt extends ut{constructor(t,e,i){super(t,e,i),this.type="path",this.pathParser=null,this.pathParser=new lt(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,i=new ot;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case lt.MOVE_TO:this.pathM(t,i);break;case lt.LINE_TO:this.pathL(t,i);break;case lt.HORIZ_LINE_TO:this.pathH(t,i);break;case lt.VERT_LINE_TO:this.pathV(t,i);break;case lt.CURVE_TO:this.pathC(t,i);break;case lt.SMOOTH_CURVE_TO:this.pathS(t,i);break;case lt.QUAD_TO:this.pathQ(t,i);break;case lt.SMOOTH_QUAD_TO:this.pathT(t,i);break;case lt.ARC:this.pathA(t,i);break;case lt.CLOSE_PATH:this.pathZ(t,i)}return i}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),i=t.getMarkerAngles();return e.map(((t,e)=>[t,i[e]]))}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var i=this.getMarkers();if(i){var r=i.length-1,s=this.getStyle("marker-start"),n=this.getStyle("marker-mid"),a=this.getStyle("marker-end");if(s.isUrlDefinition()){var h=s.getDefinition(),[o,l]=i[0];h.render(t,o,l)}if(n.isUrlDefinition())for(var u=n.getDefinition(),g=1;g<r;g++){var[c,d]=i[g];u.render(t,c,d)}if(a.isUrlDefinition()){var p=a.getDefinition(),[f,y]=i[r];p.render(t,f,y)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:i}=this,{point:r}=gt.pathM(i),{x:s,y:n}=r;i.addMarker(r),e.addPoint(s,n),t&&t.moveTo(s,n)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:i}=this,{current:r,point:s}=gt.pathL(i),{x:n,y:a}=s;i.addMarker(s,r),e.addPoint(n,a),t&&t.lineTo(n,a)}static pathH(t){var{current:e,command:i}=t,r=new _((i.relative?e.x:0)+i.x,e.y);return t.current=r,{current:e,point:r}}pathH(t,e){var{pathParser:i}=this,{current:r,point:s}=gt.pathH(i),{x:n,y:a}=s;i.addMarker(s,r),e.addPoint(n,a),t&&t.lineTo(n,a)}static pathV(t){var{current:e,command:i}=t,r=new _(e.x,(i.relative?e.y:0)+i.y);return t.current=r,{current:e,point:r}}pathV(t,e){var{pathParser:i}=this,{current:r,point:s}=gt.pathV(i),{x:n,y:a}=s;i.addMarker(s,r),e.addPoint(n,a),t&&t.lineTo(n,a)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:i}=this,{current:r,point:s,controlPoint:n,currentPoint:a}=gt.pathC(i);i.addMarker(a,n,s),e.addBezierCurve(r.x,r.y,s.x,s.y,n.x,n.y,a.x,a.y),t&&t.bezierCurveTo(s.x,s.y,n.x,n.y,a.x,a.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:i}=this,{current:r,point:s,controlPoint:n,currentPoint:a}=gt.pathS(i);i.addMarker(a,n,s),e.addBezierCurve(r.x,r.y,s.x,s.y,n.x,n.y,a.x,a.y),t&&t.bezierCurveTo(s.x,s.y,n.x,n.y,a.x,a.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:i}=this,{current:r,controlPoint:s,currentPoint:n}=gt.pathQ(i);i.addMarker(n,s,s),e.addQuadraticCurve(r.x,r.y,s.x,s.y,n.x,n.y),t&&t.quadraticCurveTo(s.x,s.y,n.x,n.y)}static pathT(t){var{current:e}=t,i=t.getReflectedControlPoint();return t.control=i,{current:e,controlPoint:i,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:i}=this,{current:r,controlPoint:s,currentPoint:n}=gt.pathT(i);i.addMarker(n,s,s),e.addQuadraticCurve(r.x,r.y,s.x,s.y,n.x,n.y),t&&t.quadraticCurveTo(s.x,s.y,n.x,n.y)}static pathA(t){var{current:e,command:i}=t,{rX:r,rY:s,xRot:n,lArcFlag:a,sweepFlag:h}=i,o=n*(Math.PI/180),l=t.getAsCurrentPoint(),u=new _(Math.cos(o)*(e.x-l.x)/2+Math.sin(o)*(e.y-l.y)/2,-Math.sin(o)*(e.x-l.x)/2+Math.cos(o)*(e.y-l.y)/2),g=Math.pow(u.x,2)/Math.pow(r,2)+Math.pow(u.y,2)/Math.pow(s,2);g>1&&(r*=Math.sqrt(g),s*=Math.sqrt(g));var c=(a===h?-1:1)*Math.sqrt((Math.pow(r,2)*Math.pow(s,2)-Math.pow(r,2)*Math.pow(u.y,2)-Math.pow(s,2)*Math.pow(u.x,2))/(Math.pow(r,2)*Math.pow(u.y,2)+Math.pow(s,2)*Math.pow(u.x,2)));isNaN(c)&&(c=0);var d=new _(c*r*u.y/s,c*-s*u.x/r),p=new _((e.x+l.x)/2+Math.cos(o)*d.x-Math.sin(o)*d.y,(e.y+l.y)/2+Math.sin(o)*d.x+Math.cos(o)*d.y),f=k([1,0],[(u.x-d.x)/r,(u.y-d.y)/s]),y=[(u.x-d.x)/r,(u.y-d.y)/s],m=[(-u.x-d.x)/r,(-u.y-d.y)/s],v=k(y,m);return M(y,m)<=-1&&(v=Math.PI),M(y,m)>=1&&(v=0),{currentPoint:l,rX:r,rY:s,sweepFlag:h,xAxisRotation:o,centp:p,a1:f,ad:v}}pathA(t,e){var{pathParser:i}=this,{currentPoint:r,rX:s,rY:n,sweepFlag:a,xAxisRotation:h,centp:o,a1:l,ad:u}=gt.pathA(i),g=1-a?1:-1,c=l+g*(u/2),d=new _(o.x+s*Math.cos(c),o.y+n*Math.sin(c));if(i.addMarkerAngle(d,c-g*Math.PI/2),i.addMarkerAngle(r,c-g*Math.PI),e.addPoint(r.x,r.y),t&&!isNaN(l)&&!isNaN(u)){var p=s>n?s:n,f=s>n?1:s/n,y=s>n?n/s:1;t.translate(o.x,o.y),t.rotate(h),t.scale(f,y),t.arc(0,0,p,l,l+u,Boolean(1-a)),t.scale(1/f,1/y),t.rotate(-h),t.translate(-o.x,-o.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){gt.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class ct extends gt{constructor(t,e,i){super(t,e,i),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class dt extends ut{constructor(t,e,i){super(t,e,new.target===dt||i),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var i=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();i&&(t.textBaseline=i)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach(((i,r)=>{var s=this.getChildBoundingBox(t,this,this,r);e?e.addBoundingBox(s):e=s})),e}getFontSize(){var{document:t,parent:e}=this,i=ht.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(i)}getTElementBoundingBox(t){var e=this.getFontSize();return new ot(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,i){var r=e[i],s=null;if(t.isArabic){var n=e.length,a=e[i-1],h=e[i+1],o="isolated";if((0===i||" "===a)&&i<n-1&&" "!==h&&(o="terminal"),i>0&&" "!==a&&i<n-1&&" "!==h&&(o="medial"),i>0&&" "!==a&&(i===n-1||" "===h)&&(o="initial"),"undefined"!==typeof t.glyphs[r]){var l=t.glyphs[r];s=l instanceof ct?l:l[o]}}else s=t.glyphs[r];return s||(s=t.missingGlyph),s}getText(){return""}getTextFromNode(t){var e=t||this.node,i=Array.from(e.parentNode.childNodes),r=i.indexOf(e),s=i.length-1,n=u(e.textContent||"");return 0===r&&(n=g(n)),r===s&&(n=c(n)),n}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach(((e,i)=>{this.renderChild(t,this,this,i)}));var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:i}=this,r=this.getText(),s=i.getStyle("font-family").getDefinition();if(s)for(var{unitsPerEm:n}=s.fontFace,a=ht.parse(e.ctx.font),h=i.getStyle("font-size").getNumber(a.fontSize),o=i.getStyle("font-style").getString(a.fontStyle),l=h/n,u=s.isRTL?r.split("").reverse().join(""):r,g=d(i.getAttribute("dx").getString()),c=u.length,p=0;p<c;p++){var f=this.getGlyph(s,u,p);t.translate(this.x,this.y),t.scale(l,-l);var y=t.lineWidth;t.lineWidth=t.lineWidth*n/h,"italic"===o&&t.transform(1,0,.4,1,0,0),f.render(t),"italic"===o&&t.transform(1,0,-.4,1,0,0),t.lineWidth=y,t.scale(1/l,-1/l),t.translate(-this.x,-this.y),this.x+=h*(f.horizAdvX||s.horizAdvX)/n,"undefined"===typeof g[p]||isNaN(g[p])||(this.x+=g[p])}else{var{x:m,y:v}=this;t.fillStyle&&t.fillText(r,m,v),t.strokeStyle&&t.strokeText(r,m,v)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),i=0;i="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var r=this.textChunkStart;r<this.leafTexts.length;r++)this.leafTexts[r].x+=i;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach(((e,i)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,i)})),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,i,r){var s=i.children[r];s.children.length>0?s.children.forEach(((i,r)=>{e.adjustChildCoordinatesRecursiveCore(t,e,s,r)})):this.adjustChildCoordinates(t,e,i,r)}adjustChildCoordinates(t,e,i,r){var s=i.children[r];if("function"!==typeof s.measureText)return s;t.save(),s.setContext(t,!0);var n=s.getAttribute("x"),a=s.getAttribute("y"),h=s.getAttribute("dx"),o=s.getAttribute("dy"),l=s.getStyle("font-family").getDefinition(),u=Boolean(l)&&l.isRTL;0===r&&(n.hasValue()||n.setValue(s.getInheritedAttribute("x")),a.hasValue()||a.setValue(s.getInheritedAttribute("y")),h.hasValue()||h.setValue(s.getInheritedAttribute("dx")),o.hasValue()||o.setValue(s.getInheritedAttribute("dy")));var g=s.measureText(t);return u&&(e.x-=g),n.hasValue()?(e.applyAnchoring(),s.x=n.getPixels("x"),h.hasValue()&&(s.x+=h.getPixels("x"))):(h.hasValue()&&(e.x+=h.getPixels("x")),s.x=e.x),e.x=s.x,u||(e.x+=g),a.hasValue()?(s.y=a.getPixels("y"),o.hasValue()&&(s.y+=o.getPixels("y"))):(o.hasValue()&&(e.y+=o.getPixels("y")),s.y=e.y),e.y=s.y,e.leafTexts.push(s),e.minX=Math.min(e.minX,s.x,s.x+g),e.maxX=Math.max(e.maxX,s.x,s.x+g),s.clearContext(t),t.restore(),s}getChildBoundingBox(t,e,i,r){var s=i.children[r];if("function"!==typeof s.getBoundingBox)return null;var n=s.getBoundingBox(t);return n?(s.children.forEach(((i,r)=>{var a=e.getChildBoundingBox(t,e,s,r);n.addBoundingBox(a)})),n):null}renderChild(t,e,i,r){var s=i.children[r];s.render(t),s.children.forEach(((i,r)=>{e.renderChild(t,e,s,r)}))}measureText(t){var{measureCache:e}=this;if(~e)return e;var i=this.getText(),r=this.measureTargetText(t,i);return this.measureCache=r,r}measureTargetText(t,e){if(!e.length)return 0;var{parent:i}=this,r=i.getStyle("font-family").getDefinition();if(r){for(var s=this.getFontSize(),n=r.isRTL?e.split("").reverse().join(""):e,a=d(i.getAttribute("dx").getString()),h=n.length,o=0,l=0;l<h;l++){o+=(this.getGlyph(r,n,l).horizAdvX||r.horizAdvX)*s/r.fontFace.unitsPerEm,"undefined"===typeof a[l]||isNaN(a[l])||(o+=a[l])}return o}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:u}=t.measureText(e);return this.clearContext(t),t.restore(),u}getInheritedAttribute(t){for(var e=this;e instanceof dt&&e.isFirstChild();){var i=e.parent.getAttribute(t);if(i.hasValue(!0))return i.getValue("0");e=e.parent}return null}}class pt extends dt{constructor(t,e,i){super(t,e,new.target===pt||i),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class ft extends pt{constructor(){super(...arguments),this.type="textNode"}}class yt extends ut{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:i}=this,{screen:r,window:s}=i,n=t.canvas;if(r.setDefaults(t),n.style&&"undefined"!==typeof t.font&&s&&"undefined"!==typeof s.getComputedStyle){t.font=s.getComputedStyle(n).getPropertyValue("font");var a=new R(i,"fontSize",ht.parse(t.font).fontSize);a.hasValue()&&(i.rootEmSize=a.getPixels("y"),i.emSize=i.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:h,height:o}=r.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var l=this.getAttribute("refX"),u=this.getAttribute("refY"),g=this.getAttribute("viewBox"),c=g.hasValue()?d(g.getString()):null,p=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),f=0,y=0,m=0,v=0;c&&(f=c[0],y=c[1]),this.root||(h=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y"),"marker"===this.type&&(m=f,v=y,f=0,y=0)),r.viewPort.setCurrent(h,o),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),c&&(h=c[2],o=c[3]),i.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:r.viewPort.width,desiredWidth:h,height:r.viewPort.height,desiredHeight:o,minX:f,minY:y,refX:l.getValue(),refY:u.getValue(),clip:p,clipX:m,clipY:v}),c&&(r.viewPort.removeCurrent(),r.viewPort.setCurrent(h,o))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=this.getAttribute("width",!0),s=this.getAttribute("height",!0),n=this.getAttribute("viewBox"),a=this.getAttribute("style"),h=r.getNumber(0),o=s.getNumber(0);if(i)if("string"===typeof i)this.getAttribute("preserveAspectRatio",!0).setValue(i);else{var l=this.getAttribute("preserveAspectRatio");l.hasValue()&&l.setValue(l.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(r.setValue(t),s.setValue(e),n.hasValue()||n.setValue("0 0 ".concat(h||t," ").concat(o||e)),a.hasValue()){var u=this.getStyle("width"),g=this.getStyle("height");u.hasValue()&&u.setValue("".concat(t,"px")),g.hasValue()&&g.setValue("".concat(e,"px"))}}}class mt extends gt{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),i=this.getAttribute("y").getPixels("y"),r=this.getStyle("width",!1,!0).getPixels("x"),s=this.getStyle("height",!1,!0).getPixels("y"),n=this.getAttribute("rx"),a=this.getAttribute("ry"),h=n.getPixels("x"),o=a.getPixels("y");if(n.hasValue()&&!a.hasValue()&&(o=h),a.hasValue()&&!n.hasValue()&&(h=o),h=Math.min(h,r/2),o=Math.min(o,s/2),t){var l=(Math.sqrt(2)-1)/3*4;t.beginPath(),s>0&&r>0&&(t.moveTo(e+h,i),t.lineTo(e+r-h,i),t.bezierCurveTo(e+r-h+l*h,i,e+r,i+o-l*o,e+r,i+o),t.lineTo(e+r,i+s-o),t.bezierCurveTo(e+r,i+s-o+l*o,e+r-h+l*h,i+s,e+r-h,i+s),t.lineTo(e+h,i+s),t.bezierCurveTo(e+h-l*h,i+s,e,i+s-o+l*o,e,i+s-o),t.lineTo(e,i+o),t.bezierCurveTo(e,i+o-l*o,e+h-l*h,i,e+h,i),t.closePath())}return new ot(e,i,e+r,i+s)}getMarkers(){return null}}class vt extends gt{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),i=this.getAttribute("cy").getPixels("y"),r=this.getAttribute("r").getPixels();return t&&r>0&&(t.beginPath(),t.arc(e,i,r,0,2*Math.PI,!1),t.closePath()),new ot(e-r,i-r,e+r,i+r)}getMarkers(){return null}}class xt extends gt{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,i=this.getAttribute("rx").getPixels("x"),r=this.getAttribute("ry").getPixels("y"),s=this.getAttribute("cx").getPixels("x"),n=this.getAttribute("cy").getPixels("y");return t&&i>0&&r>0&&(t.beginPath(),t.moveTo(s+i,n),t.bezierCurveTo(s+i,n+e*r,s+e*i,n+r,s,n+r),t.bezierCurveTo(s-e*i,n+r,s-i,n+e*r,s-i,n),t.bezierCurveTo(s-i,n-e*r,s-e*i,n-r,s,n-r),t.bezierCurveTo(s+e*i,n-r,s+i,n-e*r,s+i,n),t.closePath()),new ot(s-i,n-r,s+i,n+r)}getMarkers(){return null}}class bt extends gt{constructor(){super(...arguments),this.type="line"}getPoints(){return[new _(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new _(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:i},{x:r,y:s}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,i),t.lineTo(r,s)),new ot(e,i,r,s)}getMarkers(){var[t,e]=this.getPoints(),i=t.angleTo(e);return[[t,i],[e,i]]}}class St extends gt{constructor(t,e,i){super(t,e,i),this.type="polyline",this.points=[],this.points=_.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:i,y:r}]=e,s=new ot(i,r);return t&&(t.beginPath(),t.moveTo(i,r)),e.forEach((e=>{var{x:i,y:r}=e;s.addPoint(i,r),t&&t.lineTo(i,r)})),s}getMarkers(){var{points:t}=this,e=t.length-1,i=[];return t.forEach(((r,s)=>{s!==e&&i.push([r,r.angleTo(t[s+1])])})),i.length>0&&i.push([t[t.length-1],i[i.length-1][1]]),i}}class Pt extends St{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:i,y:r}]=this.points;return t&&(t.lineTo(i,r),t.closePath()),e}}class wt extends it{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,i){var r=this.getStyle("width").getPixels("x",!0),s=this.getStyle("height").getPixels("y",!0),n=new yt(this.document,null);n.attributes.viewBox=new R(this.document,"viewBox",this.getAttribute("viewBox").getValue()),n.attributes.width=new R(this.document,"width","".concat(r,"px")),n.attributes.height=new R(this.document,"height","".concat(s,"px")),n.attributes.transform=new R(this.document,"transform",this.getAttribute("patternTransform").getValue()),n.children=this.children;var a=this.document.createCanvas(r,s),h=a.getContext("2d"),o=this.getAttribute("x"),l=this.getAttribute("y");o.hasValue()&&l.hasValue()&&h.translate(o.getPixels("x",!0),l.getPixels("y",!0)),i.hasValue()?this.styles["fill-opacity"]=i:Reflect.deleteProperty(this.styles,"fill-opacity");for(var u=-1;u<=1;u++)for(var g=-1;g<=1;g++)h.save(),n.attributes.x=new R(this.document,"x",u*a.width),n.attributes.y=new R(this.document,"y",g*a.height),n.render(h),h.restore();return t.createPattern(a,"repeat")}}class At extends it{constructor(){super(...arguments),this.type="marker"}render(t,e,i){if(e){var{x:r,y:s}=e,n=this.getAttribute("orient").getString("auto"),a=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(r,s),"auto"===n&&t.rotate(i),"strokeWidth"===a&&t.scale(t.lineWidth,t.lineWidth),t.save();var h=new yt(this.document,null);h.type=this.type,h.attributes.viewBox=new R(this.document,"viewBox",this.getAttribute("viewBox").getValue()),h.attributes.refX=new R(this.document,"refX",this.getAttribute("refX").getValue()),h.attributes.refY=new R(this.document,"refY",this.getAttribute("refY").getValue()),h.attributes.width=new R(this.document,"width",this.getAttribute("markerWidth").getValue()),h.attributes.height=new R(this.document,"height",this.getAttribute("markerHeight").getValue()),h.attributes.overflow=new R(this.document,"overflow",this.getAttribute("overflow").getValue()),h.attributes.fill=new R(this.document,"fill",this.getAttribute("fill").getColor("black")),h.attributes.stroke=new R(this.document,"stroke",this.getAttribute("stroke").getValue("none")),h.children=this.children,h.render(t),t.restore(),"strokeWidth"===a&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===n&&t.rotate(-i),t.translate(-r,-s)}}}class Ct extends it{constructor(){super(...arguments),this.type="defs"}render(){}}class Tt extends ut{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new ot;return this.children.forEach((i=>{e.addBoundingBox(i.getBoundingBox(t))})),e}}class Vt extends it{constructor(t,e,i){super(t,e,i),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:r,children:s}=this;s.forEach((t=>{"stop"===t.type&&r.push(t)}))}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,i){var r=this;this.getHrefAttribute().hasValue()&&(r=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(r));var{stops:s}=r,n=this.getGradient(t,e);if(!n)return this.addParentOpacity(i,s[s.length-1].color);if(s.forEach((t=>{n.addColorStop(t.offset,this.addParentOpacity(i,t.color))})),this.getAttribute("gradientTransform").hasValue()){var{document:a}=this,{MAX_VIRTUAL_PIXELS:h,viewPort:o}=a.screen,[l]=o.viewPorts,u=new mt(a,null);u.attributes.x=new R(a,"x",-h/3),u.attributes.y=new R(a,"y",-h/3),u.attributes.width=new R(a,"width",h),u.attributes.height=new R(a,"height",h);var g=new Tt(a,null);g.attributes.transform=new R(a,"transform",this.getAttribute("gradientTransform").getValue()),g.children=[u];var c=new yt(a,null);c.attributes.x=new R(a,"x",0),c.attributes.y=new R(a,"y",0),c.attributes.width=new R(a,"width",l.width),c.attributes.height=new R(a,"height",l.height),c.children=[g];var d=a.createCanvas(l.width,l.height),p=d.getContext("2d");return p.fillStyle=n,c.render(p),p.createPattern(d,"no-repeat")}return n}inheritStopContainer(t){this.attributesToInherit.forEach((e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())}))}addParentOpacity(t,e){return t.hasValue()?new R(this.document,"color",e).addOpacity(t).getColor():e}}class Et extends Vt{constructor(t,e,i){super(t,e,i),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var i="objectBoundingBox"===this.getGradientUnits(),r=i?e.getBoundingBox(t):null;if(i&&!r)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var s=i?r.x+r.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),n=i?r.y+r.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),a=i?r.x+r.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),h=i?r.y+r.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return s===a&&n===h?null:t.createLinearGradient(s,n,a,h)}}class Mt extends Vt{constructor(t,e,i){super(t,e,i),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var i="objectBoundingBox"===this.getGradientUnits(),r=e.getBoundingBox(t);if(i&&!r)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var s=i?r.x+r.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),n=i?r.y+r.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),a=s,h=n;this.getAttribute("fx").hasValue()&&(a=i?r.x+r.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(h=i?r.y+r.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var o=i?(r.width+r.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),l=this.getAttribute("fr").getPixels();return t.createRadialGradient(a,h,l,s,n,o)}}class kt extends it{constructor(t,e,i){super(t,e,i),this.type="stop";var r=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),s=this.getStyle("stop-opacity"),n=this.getStyle("stop-color",!0);""===n.getString()&&n.setValue("#000"),s.hasValue()&&(n=n.addOpacity(s)),this.offset=r,this.color=n.getColor()}}class Ot extends it{constructor(t,e,i){super(t,e,i),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new R(t,"values",null);var r=this.getAttribute("values");r.hasValue()&&this.values.setValue(r.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:i,to:r}=this.getProgress(),s=i.getNumber()+(r.getNumber()-i.getNumber())*e;return"%"===t&&(s*=100),"".concat(s).concat(t)}update(t){var{parent:e}=this,i=this.getProperty();if(this.initialValue||(this.initialValue=i.getString(),this.initialUnits=i.getUnits()),this.duration>this.maxDuration){var r=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==r||this.frozen){if("remove"===r&&!this.removed)return this.removed=!0,i.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=i.getString();return!1}this.duration+=t;var s=!1;if(this.begin<this.duration){var n=this.calcValue(),a=this.getAttribute("type");if(a.hasValue()){var h=a.getString();n="".concat(h,"(").concat(n,")")}i.setValue(n),s=!0}return s}getProgress(){var{document:t,values:e}=this,i={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var r=i.progress*(e.getValue().length-1),s=Math.floor(r),n=Math.ceil(r);i.from=new R(t,"from",parseFloat(e.getValue()[s])),i.to=new R(t,"to",parseFloat(e.getValue()[n])),i.progress=(r-s)/(n-s)}else i.from=this.from,i.to=this.to;return i}}class Nt extends Ot{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:i}=this.getProgress(),r=new a(e.getColor()),s=new a(i.getColor());if(r.ok&&s.ok){var n=r.r+(s.r-r.r)*t,h=r.g+(s.g-r.g)*t,o=r.b+(s.b-r.b)*t;return"rgb(".concat(Math.floor(n),", ").concat(Math.floor(h),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class Dt extends Ot{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:i}=this.getProgress(),r=d(e.getString()),s=d(i.getString()),n=r.map(((e,i)=>e+(s[i]-e)*t)).join(" ");return n}}class zt extends it{constructor(t,e,i){super(t,e,i),this.type="font",this.glyphs={},this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:r}=t,{children:s}=this;for(var n of s)switch(n.type){case"font-face":this.fontFace=n;var a=n.getStyle("font-family");a.hasValue()&&(r[a.getString()]=this);break;case"missing-glyph":this.missingGlyph=n;break;case"glyph":var h=n;h.arabicForm?(this.isRTL=!0,this.isArabic=!0,"undefined"===typeof this.glyphs[h.unicode]&&(this.glyphs[h.unicode]={}),this.glyphs[h.unicode][h.arabicForm]=h):this.glyphs[h.unicode]=h}}render(){}}class Bt extends it{constructor(t,e,i){super(t,e,i),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class Lt extends gt{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class It extends dt{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class Rt extends dt{constructor(t,e,i){super(t,e,i),this.type="a";var{childNodes:r}=e,s=r[0],n=r.length>0&&Array.from(r).every((t=>3===t.nodeType));this.hasText=n,this.text=n?this.getTextFromNode(s):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:i,y:r}=this,{mouse:s}=e.screen,n=new R(e,"fontSize",ht.parse(e.ctx.font).fontSize);s.isWorking()&&s.checkBoundingBox(this,new ot(i,r-n.getPixels("y"),i+this.measureText(t),r))}else if(this.children.length>0){var a=new Tt(this.document,null);a.children=this.children,a.parent=this,a.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function Ft(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,r)}return i}function _t(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Ft(Object(i),!0).forEach((function(e){(0,s.A)(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Ft(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}class Xt extends dt{constructor(t,e,i){super(t,e,i),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var r=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(r)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach((e=>{var{type:i,points:r}=e;switch(i){case lt.LINE_TO:t&&t.lineTo(r[0],r[1]);break;case lt.MOVE_TO:t&&t.moveTo(r[0],r[1]);break;case lt.CURVE_TO:t&&t.bezierCurveTo(r[0],r[1],r[2],r[3],r[4],r[5]);break;case lt.QUAD_TO:t&&t.quadraticCurveTo(r[0],r[1],r[2],r[3]);break;case lt.ARC:var[s,n,a,h,o,l,u,g]=r,c=a>h?a:h,d=a>h?1:a/h,p=a>h?h/a:1;t&&(t.translate(s,n),t.rotate(u),t.scale(d,p),t.arc(0,0,c,o,o+l,Boolean(1-g)),t.scale(1/d,1/p),t.rotate(-u),t.translate(-s,-n));break;case lt.CLOSE_PATH:t&&t.closePath()}}))}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),i=this.getFontSize(),{glyphInfo:r}=this,s=t.fillStyle;"underline"===e&&t.beginPath(),r.forEach(((r,s)=>{var{p0:n,p1:a,rotation:h,text:o}=r;t.save(),t.translate(n.x,n.y),t.rotate(h),t.fillStyle&&t.fillText(o,0,0),t.strokeStyle&&t.strokeText(o,0,0),t.restore(),"underline"===e&&(0===s&&t.moveTo(n.x,n.y+i/8),t.lineTo(a.x,a.y+i/5))})),"underline"===e&&(t.lineWidth=i/20,t.strokeStyle=s,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,i,r,s,n,a,h,o){var l=n,u=this.measureText(t,h);" "===h&&"justify"===e&&i<r&&(u+=(r-i)/s),o>-1&&(l+=this.getLetterSpacingAt(o));var g=this.textHeight/20,c=this.getEquidistantPointOnPath(l,g,0),d=this.getEquidistantPointOnPath(l+u,g,0),p={p0:c,p1:d},f=c&&d?Math.atan2(d.y-c.y,d.x-c.x):0;if(a){var y=Math.cos(Math.PI/2+f)*a,m=Math.cos(-f)*a;p.p0=_t(_t({},c),{},{x:c.x+y,y:c.y+m}),p.p1=_t(_t({},d),{},{x:d.x+y,y:d.y+m})}return{offset:l+=u,segment:p,rotation:f}}measureText(t,e){var{measuresCache:i}=this,r=e||this.getText();if(i.has(r))return i.get(r);var s=this.measureTargetText(t,r);return i.set(r,s),s}setTextData(t){if(!this.glyphInfo){var e=this.getText(),i=e.split(""),r=e.split(" ").length-1,s=this.parent.getAttribute("dx").split().map((t=>t.getPixels("x"))),n=this.parent.getAttribute("dy").getPixels("y"),a=this.parent.getStyle("text-anchor").getString("start"),h=this.getStyle("letter-spacing"),o=this.parent.getStyle("letter-spacing"),l=0;h.hasValue()&&"inherit"!==h.getValue()?h.hasValue()&&"initial"!==h.getValue()&&"unset"!==h.getValue()&&(l=h.getPixels()):l=o.getPixels();var u=[],g=e.length;this.letterSpacingCache=u;for(var c=0;c<g;c++)u.push("undefined"!==typeof s[c]?s[c]:l);var d=u.reduce(((t,e,i)=>0===i?0:t+e||0),0),p=this.measureText(t),f=Math.max(p+d,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var y=this.getPathLength(),m=this.getStyle("startOffset").getNumber(0)*y,v=0;"middle"!==a&&"center"!==a||(v=-f/2),"end"!==a&&"right"!==a||(v=-f),v+=m,i.forEach(((e,s)=>{var{offset:h,segment:o,rotation:l}=this.findSegmentToFitChar(t,a,f,y,r,v,n,e,s);v=h,o.p0&&o.p1&&this.glyphInfo.push({text:i[s],p0:o.p0,p1:o.p1,rotation:l})}))}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:i}=t;for(i.reset();!i.isEnd();){var{current:r}=i,s=r?r.x:0,n=r?r.y:0,a=i.next(),h=a.type,o=[];switch(a.type){case lt.MOVE_TO:this.pathM(i,o);break;case lt.LINE_TO:h=this.pathL(i,o);break;case lt.HORIZ_LINE_TO:h=this.pathH(i,o);break;case lt.VERT_LINE_TO:h=this.pathV(i,o);break;case lt.CURVE_TO:this.pathC(i,o);break;case lt.SMOOTH_CURVE_TO:h=this.pathS(i,o);break;case lt.QUAD_TO:this.pathQ(i,o);break;case lt.SMOOTH_QUAD_TO:h=this.pathT(i,o);break;case lt.ARC:o=this.pathA(i);break;case lt.CLOSE_PATH:gt.pathZ(i)}a.type!==lt.CLOSE_PATH?e.push({type:h,points:o,start:{x:s,y:n},pathLength:this.calcLength(s,n,h,o)}):e.push({type:lt.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:i,y:r}=gt.pathM(t).point;e.push(i,r)}pathL(t,e){var{x:i,y:r}=gt.pathL(t).point;return e.push(i,r),lt.LINE_TO}pathH(t,e){var{x:i,y:r}=gt.pathH(t).point;return e.push(i,r),lt.LINE_TO}pathV(t,e){var{x:i,y:r}=gt.pathV(t).point;return e.push(i,r),lt.LINE_TO}pathC(t,e){var{point:i,controlPoint:r,currentPoint:s}=gt.pathC(t);e.push(i.x,i.y,r.x,r.y,s.x,s.y)}pathS(t,e){var{point:i,controlPoint:r,currentPoint:s}=gt.pathS(t);return e.push(i.x,i.y,r.x,r.y,s.x,s.y),lt.CURVE_TO}pathQ(t,e){var{controlPoint:i,currentPoint:r}=gt.pathQ(t);e.push(i.x,i.y,r.x,r.y)}pathT(t,e){var{controlPoint:i,currentPoint:r}=gt.pathT(t);return e.push(i.x,i.y,r.x,r.y),lt.QUAD_TO}pathA(t){var{rX:e,rY:i,sweepFlag:r,xAxisRotation:s,centp:n,a1:a,ad:h}=gt.pathA(t);return 0===r&&h>0&&(h-=2*Math.PI),1===r&&h<0&&(h+=2*Math.PI),[n.x,n.y,e,i,a,h,s,r]}calcLength(t,e,i,r){var s=0,n=null,a=null,h=0;switch(i){case lt.LINE_TO:return this.getLineLength(t,e,r[0],r[1]);case lt.CURVE_TO:for(s=0,n=this.getPointOnCubicBezier(0,t,e,r[0],r[1],r[2],r[3],r[4],r[5]),h=.01;h<=1;h+=.01)a=this.getPointOnCubicBezier(h,t,e,r[0],r[1],r[2],r[3],r[4],r[5]),s+=this.getLineLength(n.x,n.y,a.x,a.y),n=a;return s;case lt.QUAD_TO:for(s=0,n=this.getPointOnQuadraticBezier(0,t,e,r[0],r[1],r[2],r[3]),h=.01;h<=1;h+=.01)a=this.getPointOnQuadraticBezier(h,t,e,r[0],r[1],r[2],r[3]),s+=this.getLineLength(n.x,n.y,a.x,a.y),n=a;return s;case lt.ARC:s=0;var o=r[4],l=r[5],u=r[4]+l,g=Math.PI/180;if(Math.abs(o-u)<g&&(g=Math.abs(o-u)),n=this.getPointOnEllipticalArc(r[0],r[1],r[2],r[3],o,0),l<0)for(h=o-g;h>u;h-=g)a=this.getPointOnEllipticalArc(r[0],r[1],r[2],r[3],h,0),s+=this.getLineLength(n.x,n.y,a.x,a.y),n=a;else for(h=o+g;h<u;h+=g)a=this.getPointOnEllipticalArc(r[0],r[1],r[2],r[3],h,0),s+=this.getLineLength(n.x,n.y,a.x,a.y),n=a;return a=this.getPointOnEllipticalArc(r[0],r[1],r[2],r[3],u,0),s+=this.getLineLength(n.x,n.y,a.x,a.y)}return 0}getPointOnLine(t,e,i,r,s){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:i,h=(s-i)/(r-e+V),o=Math.sqrt(t*t/(1+h*h));r<e&&(o*=-1);var l=h*o,u=null;if(r===e)u={x:n,y:a+l};else if((a-i)/(n-e+V)===h)u={x:n+o,y:a+l};else{var g,c,d=this.getLineLength(e,i,r,s);if(d<V)return null;var p=(n-e)*(r-e)+(a-i)*(s-i);g=e+(p/=d*d)*(r-e),c=i+p*(s-i);var f=this.getLineLength(n,a,g,c),y=Math.sqrt(t*t-f*f);o=Math.sqrt(y*y/(1+h*h)),r<e&&(o*=-1),u={x:g+o,y:c+(l=h*o)}}return u}getPointOnPath(t){var e=this.getPathLength(),i=0,r=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:s}=this;for(var n of s){if(!n||!(n.pathLength<5e-5||i+n.pathLength+5e-5<t)){var a=t-i,h=0;switch(n.type){case lt.LINE_TO:r=this.getPointOnLine(a,n.start.x,n.start.y,n.points[0],n.points[1],n.start.x,n.start.y);break;case lt.ARC:var o=n.points[4],l=n.points[5],u=n.points[4]+l;if(h=o+a/n.pathLength*l,l<0&&h<u||l>=0&&h>u)break;r=this.getPointOnEllipticalArc(n.points[0],n.points[1],n.points[2],n.points[3],h,n.points[6]);break;case lt.CURVE_TO:(h=a/n.pathLength)>1&&(h=1),r=this.getPointOnCubicBezier(h,n.start.x,n.start.y,n.points[0],n.points[1],n.points[2],n.points[3],n.points[4],n.points[5]);break;case lt.QUAD_TO:(h=a/n.pathLength)>1&&(h=1),r=this.getPointOnQuadraticBezier(h,n.start.x,n.start.y,n.points[0],n.points[1],n.points[2],n.points[3])}if(r)return r;break}i+=n.pathLength}return null}getLineLength(t,e,i,r){return Math.sqrt((i-t)*(i-t)+(r-e)*(r-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce(((t,e)=>e.pathLength>0?t+e.pathLength:t),0)),this.pathLength}getPointOnCubicBezier(t,e,i,r,s,n,a,h,o){return{x:h*O(t)+n*N(t)+r*D(t)+e*z(t),y:o*O(t)+a*N(t)+s*D(t)+i*z(t)}}getPointOnQuadraticBezier(t,e,i,r,s,n,a){return{x:n*B(t)+r*L(t)+e*I(t),y:a*B(t)+s*L(t)+i*I(t)}}getPointOnEllipticalArc(t,e,i,r,s,n){var a=Math.cos(n),h=Math.sin(n),o=i*Math.cos(s),l=r*Math.sin(s);return{x:t+(o*a-l*h),y:e+(o*h+l*a)}}buildEquidistantCache(t,e){var i=this.getPathLength(),r=e||.25,s=t||i/100;if(!this.equidistantCache||this.equidistantCache.step!==s||this.equidistantCache.precision!==r){this.equidistantCache={step:s,precision:r,points:[]};for(var n=0,a=0;a<=i;a+=r){var h=this.getPointOnPath(a),o=this.getPointOnPath(a+r);h&&o&&((n+=this.getLineLength(h.x,h.y,o.x,o.y))>=s&&(this.equidistantCache.points.push({x:h.x,y:h.y,distance:a}),n-=s))}}}getEquidistantPointOnPath(t,e,i){if(this.buildEquidistantCache(e,i),t<0||t-this.getPathLength()>5e-5)return null;var r=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[r]||null}}var jt=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class Yt extends ut{constructor(t,e,i){super(t,e,i),this.type="image",this.loaded=!1;var r=this.getHrefAttribute().getString();if(r){var s=r.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(r);t.images.push(this),s?this.loadSvg(r):this.loadImage(r),this.isSvg=s}}loadImage(t){var e=this;return(0,r.A)((function*(){try{var i=yield e.document.createImage(t);e.image=i}catch(r){console.error('Error while loading image "'.concat(t,'":'),r)}e.loaded=!0}))()}loadSvg(t){var e=this;return(0,r.A)((function*(){var i=jt.exec(t);if(i){var r=i[5];"base64"===i[4]?e.image=atob(r):e.image=decodeURIComponent(r)}else try{var s=yield e.document.fetch(t),n=yield s.text();e.image=n}catch(a){console.error('Error while loading image "'.concat(t,'":'),a)}e.loaded=!0}))()}renderChildren(t){var{document:e,image:i,loaded:r}=this,s=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),h=this.getStyle("height").getPixels("y");if(r&&i&&a&&h){if(t.save(),t.translate(s,n),this.isSvg){var o=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:a,scaleHeight:h});o.document.documentElement.parent=this,o.render()}else{var l=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a,desiredWidth:l.width,height:h,desiredHeight:l.height}),this.loaded&&("undefined"===typeof l.complete||l.complete)&&t.drawImage(l,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),i=this.getStyle("width").getPixels("x"),r=this.getStyle("height").getPixels("y");return new ot(t,e,t+i,e+r)}}class Ut extends ut{constructor(){super(...arguments),this.type="symbol"}render(t){}}class Ht{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var i=this;return(0,r.A)((function*(){try{var{document:r}=i,s=(yield r.canvg.parser.load(e)).getElementsByTagName("font");Array.from(s).forEach((e=>{var i=r.createElement(e);r.definitions[t]=i}))}catch(n){console.error('Error while loading font "'.concat(e,'":'),n)}i.loaded=!0}))()}}class Wt extends it{constructor(t,e,i){super(t,e,i),this.type="style",u(Array.from(e.childNodes).map((t=>t.textContent)).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach((e=>{var i=e.trim();if(i){var r=i.split("{"),s=r[0].split(","),n=r[1].split(";");s.forEach((e=>{var i=e.trim();if(i){var r=t.styles[i]||{};if(n.forEach((e=>{var i=e.indexOf(":"),s=e.substr(0,i).trim(),n=e.substr(i+1,e.length-i).trim();s&&n&&(r[s]=new R(t,s,n))})),t.styles[i]=r,t.stylesSpecificity[i]=T(i),"@font-face"===i){var s=r["font-family"].getString().replace(/"|'/g,"");r.src.getString().split(",").forEach((e=>{if(e.indexOf('format("svg")')>0){var i=y(e);i&&new Ht(t).load(s,i)}}))}}}))}}))}}Wt.parseExternalUrl=y;class qt extends ut{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),i=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),i.hasValue()&&t.translate(0,i.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:i}=this;if(i){var r=i;if("symbol"===i.type&&((r=new yt(e,null)).attributes.viewBox=new R(e,"viewBox",i.getAttribute("viewBox").getString()),r.attributes.preserveAspectRatio=new R(e,"preserveAspectRatio",i.getAttribute("preserveAspectRatio").getString()),r.attributes.overflow=new R(e,"overflow",i.getAttribute("overflow").getString()),r.children=i.children,i.styles.opacity=new R(e,"opacity",this.calculateOpacity())),"svg"===r.type){var s=this.getStyle("width",!1,!0),n=this.getStyle("height",!1,!0);s.hasValue()&&(r.attributes.width=new R(e,"width",s.getString())),n.hasValue()&&(r.attributes.height=new R(e,"height",n.getString()))}var a=r.parent;r.parent=this,r.render(t),r.parent=a}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return et.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function Gt(t,e,i,r,s,n){return t[i*r*4+4*e+n]}function Qt(t,e,i,r,s,n,a){t[i*r*4+4*e+n]=a}function $t(t,e,i){return t[e]*i}function Zt(t,e,i,r){return e+Math.cos(t)*i+Math.sin(t)*r}class Jt extends it{constructor(t,e,i){super(t,e,i),this.type="feColorMatrix";var r=d(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var s=r[0];r=[.213+.787*s,.715-.715*s,.072-.072*s,0,0,.213-.213*s,.715+.285*s,.072-.072*s,0,0,.213-.213*s,.715-.715*s,.072+.928*s,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var n=r[0]*Math.PI/180;r=[Zt(n,.213,.787,-.213),Zt(n,.715,-.715,-.715),Zt(n,.072,-.072,.928),0,0,Zt(n,.213,-.213,.143),Zt(n,.715,.285,.14),Zt(n,.072,-.072,-.283),0,0,Zt(n,.213,-.213,-.787),Zt(n,.715,-.715,.715),Zt(n,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=r,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,i,r,s){for(var{includeOpacity:n,matrix:a}=this,h=t.getImageData(0,0,r,s),o=0;o<s;o++)for(var l=0;l<r;l++){var u=Gt(h.data,l,o,r,0,0),g=Gt(h.data,l,o,r,0,1),c=Gt(h.data,l,o,r,0,2),d=Gt(h.data,l,o,r,0,3),p=$t(a,0,u)+$t(a,1,g)+$t(a,2,c)+$t(a,3,d)+$t(a,4,1),f=$t(a,5,u)+$t(a,6,g)+$t(a,7,c)+$t(a,8,d)+$t(a,9,1),y=$t(a,10,u)+$t(a,11,g)+$t(a,12,c)+$t(a,13,d)+$t(a,14,1),m=$t(a,15,u)+$t(a,16,g)+$t(a,17,c)+$t(a,18,d)+$t(a,19,1);n&&(p=0,f=0,y=0,m*=d/255),Qt(h.data,l,o,r,0,0,p),Qt(h.data,l,o,r,0,1,f),Qt(h.data,l,o,r,0,2,y),Qt(h.data,l,o,r,0,3,m)}t.clearRect(0,0,r,s),t.putImageData(h,0,0)}}class Kt extends it{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:i}=this,r=this.getAttribute("x").getPixels("x"),s=this.getAttribute("y").getPixels("y"),n=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");if(!n&&!a){var h=new ot;this.children.forEach((e=>{h.addBoundingBox(e.getBoundingBox(t))})),r=Math.floor(h.x1),s=Math.floor(h.y1),n=Math.floor(h.width),a=Math.floor(h.height)}var o=this.removeStyles(e,Kt.ignoreStyles),l=i.createCanvas(r+n,s+a),u=l.getContext("2d");i.screen.setDefaults(u),this.renderChildren(u),new Jt(i,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(u,0,0,r+n,s+a);var g=i.createCanvas(r+n,s+a),c=g.getContext("2d");i.screen.setDefaults(c),e.render(c),c.globalCompositeOperation="destination-in",c.fillStyle=u.createPattern(l,"no-repeat"),c.fillRect(0,0,r+n,s+a),t.fillStyle=c.createPattern(g,"no-repeat"),t.fillRect(0,0,r+n,s+a),this.restoreStyles(e,o)}render(t){}}Kt.ignoreStyles=["mask","transform","clip-path"];var te=()=>{};class ee extends it{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,i=Reflect.getPrototypeOf(t),{beginPath:r,closePath:s}=t;i&&(i.beginPath=te,i.closePath=te),Reflect.apply(r,t,[]),this.children.forEach((r=>{if("undefined"!==typeof r.path){var n="undefined"!==typeof r.elementTransform?r.elementTransform():null;n||(n=et.fromElement(e,r)),n&&n.apply(t),r.path(t),i&&(i.closePath=s),n&&n.unapply(t)}})),Reflect.apply(s,t,[]),t.clip(),i&&(i.beginPath=r,i.closePath=s)}render(t){}}class ie extends it{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:i,children:r}=this,s=e.getBoundingBox(t);if(s){var n=0,a=0;r.forEach((t=>{var e=t.extraFilterDistance||0;n=Math.max(n,e),a=Math.max(a,e)}));var h=Math.floor(s.width),o=Math.floor(s.height),l=h+2*n,u=o+2*a;if(!(l<1||u<1)){var g=Math.floor(s.x),c=Math.floor(s.y),d=this.removeStyles(e,ie.ignoreStyles),p=i.createCanvas(l,u),f=p.getContext("2d");i.screen.setDefaults(f),f.translate(-g+n,-c+a),e.render(f),r.forEach((t=>{"function"===typeof t.apply&&t.apply(f,0,0,l,u)})),t.drawImage(p,0,0,l,u,g-n,c-a,l,u),this.restoreStyles(e,d)}}}render(t){}}ie.ignoreStyles=["filter","transform","clip-path"];class re extends it{constructor(t,e,i){super(t,e,i),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,i,r,s){}}class se extends it{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,i,r,s){}}class ne extends it{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,i,r,s){}}class ae extends it{constructor(t,e,i){super(t,e,i),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,i,r,s){var{document:n,blurRadius:a}=this,h=n.window?n.window.document.body:null,l=t.canvas;l.id=n.getUniqueId(),h&&(l.style.display="none",h.appendChild(l)),(0,o.dD)(l,e,i,r,s,a),h&&h.removeChild(l)}}class he extends it{constructor(){super(...arguments),this.type="title"}}class oe extends it{constructor(){super(...arguments),this.type="desc"}}var le={svg:yt,rect:mt,circle:vt,ellipse:xt,line:bt,polyline:St,polygon:Pt,path:gt,pattern:wt,marker:At,defs:Ct,linearGradient:Et,radialGradient:Mt,stop:kt,animate:Ot,animateColor:Nt,animateTransform:Dt,font:zt,"font-face":Bt,"missing-glyph":Lt,glyph:ct,text:dt,tspan:pt,tref:It,a:Rt,textPath:Xt,image:Yt,g:Tt,symbol:Ut,style:Wt,use:qt,mask:Kt,clipPath:ee,filter:ie,feDropShadow:re,feMorphology:se,feComposite:ne,feColorMatrix:Jt,feGaussianBlur:ae,title:he,desc:oe};function ue(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,r)}return i}function ge(){return ge=(0,r.A)((function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=document.createElement("img");return e&&(i.crossOrigin="Anonymous"),new Promise(((e,r)=>{i.onload=()=>{e(i)},i.onerror=(t,e,i,s,n)=>{r(n)},i.src=t}))})),ge.apply(this,arguments)}class ce{constructor(t){var{rootEmSize:e=12,emSize:i=12,createCanvas:r=ce.createCanvas,createImage:s=ce.createImage,anonymousCrossOrigin:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions={},this.styles={},this.stylesSpecificity={},this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=i,this.createCanvas=r,this.createImage=this.bindCreateImage(s,n),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"===typeof e?(i,r)=>t(i,"boolean"===typeof r?r:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every((t=>t.loaded))}isFontsLoaded(){return this.fonts.every((t=>t.loaded))}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),i=ce.elementTypes[e];return"undefined"!==typeof i?new i(this,t):new rt(this,t)}createTextNode(t){return new ft(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ue(Object(i),!0).forEach((function(e){(0,s.A)(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ue(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}({document:this},t))}}function de(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,r)}return i}function pe(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?de(Object(i),!0).forEach((function(e){(0,s.A)(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):de(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}ce.createCanvas=function(t,e){var i=document.createElement("canvas");return i.width=t,i.height=e,i},ce.createImage=function(t){return ge.apply(this,arguments)},ce.elementTypes=le;class fe{constructor(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new q(i),this.screen=new U(t,i),this.options=i;var r=new ce(this,i),s=r.createDocumentElement(e);this.document=r,this.documentElement=s}static from(t,e){var i=arguments;return(0,r.A)((function*(){var r=i.length>2&&void 0!==i[2]?i[2]:{},s=new q(r),n=yield s.parse(e);return new fe(t,n,r)}))()}static fromString(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=new q(i).parseFromString(e);return new fe(t,r,i)}fork(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return fe.from(t,e,pe(pe({},this.options),i))}forkString(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return fe.fromString(t,e,pe(pe({},this.options),i))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return(0,r.A)((function*(){var i=t.length>0&&void 0!==t[0]?t[0]:{};e.start(pe({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},i)),yield e.ready(),e.stop()}))()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:i,options:r}=this;i.start(e,pe(pe({enableRedraw:!0},r),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,i)}}}}]);