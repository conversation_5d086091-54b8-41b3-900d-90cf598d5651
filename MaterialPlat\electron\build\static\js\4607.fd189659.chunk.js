"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4607],{14607:(i,e,l)=>{l.d(e,{A:()=>W});var a=l(65043),d=l(97326),n=l(16569),t=l(83720),o=l(36497),r=l(6051),s=l(95206),v=l(74117),u=l(80077),c=l(93950),p=l.n(c),g=l(56434),x=l.n(g),b=l(35666),h=l(22),_=l(18650),m=l(75440),f=l(54962),y=l(72295),j=l(9339),A=l(36950),L=l(56543),k=l(10202),S=l(46085),w=l(32099);const N=()=>(0,w.Mz)([i=>i.inputVariable.inputVariableMap,i=>i.inputVariable.allCodeList],((i,e)=>e.map((e=>i.get(e))))),C=()=>{const i=(0,a.useMemo)(N,[]);return(0,u.d4)((e=>i(e)))};var I=l(45591),O=l(70579);const T=i=>{let{isActive:e}=i;return(0,O.jsx)(I.A,{rotate:e?180:0,style:{color:"rgba(0,0,0,0.25)"}})};var R=l(81143),M=l(68374);const E=R.Ay.div`

`,$=(0,R.Ay)(E)`
    width: 100%;
    height: 100%;
     .changliang {
        width: ${(0,M.D0)("24px")};
        height: ${(0,M.D0)("24px")};
        background-image: url(${_.fd});
        background-size: ${(0,M.D0)("24px")} ${(0,M.D0)("24px")};
    }

    .bianliang {
        width: ${(0,M.D0)("24px")};
        height: ${(0,M.D0)("24px")};
        background-image: url(${_.Mo});
        background-size: ${(0,M.D0)("24px")} ${(0,M.D0)("24px")};
    }

    .btn-layout {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        >img {
            width: ${(0,M.D0)("24px")};
            height: ${(0,M.D0)("24px")};
        }
        >div {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(0,0,0,0.45);
            margin-top: 2px;
            line-height: 14px;
            
        }
    }
    .disabled {
        cursor: no-drop;
    }
    .require {
        color: red;
        margin-right: 3px
    }
    .title {
        padding: 10px;
    }
    .submit {
        padding:  0 10px;
    }
    >.form {
        width: 100%;
        //不使用height，自动撑开，在父组件中滚动
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        margin-bottom: 1%;

        .row {
            padding:  0 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-bottom: 6px;
            .content {
                .table-title-layout {
                    display: flex;
                    justify-content: end;
                    img {
                        cursor: pointer;
                    }
                }
                overflow: auto;
                .info {
                    padding: 5px;
                    cursor: pointer;
                    &:hover {
                    background: rgba(215, 226, 255, 0.2);
                    }
                }
                .select {
                    padding: 5px;
                    cursor: pointer;
                    background: rgba(215, 226, 255, 1);
                }
            }

            >.label {
                min-width: 120px;
                &::after {
                    content: ':'
                }
            }

            >.value {
                display: flex; 
                align-items: center;
                >.kong {
                    width: 20px;
                    height: 20px; 
                    margin-right: 10px;
                    visibility:hidden;
                }
                >.changliang {
                    width: 20px;
                    height: 20px;
                    background-image: url(${_.fd});
                    background-size: 20px 20px;
                    margin-right: 10px;
                }

                >.bianliang {
                    width: 20px;
                    height: 20px;
                    background-image: url(${_.Mo});
                    background-size: 20px 20px;
                    margin-right: 10px;
                }
                .input-value {
                    display: flex;

                    input {
                        width: 80px;
                    }

                    .ant-select {
                        width: 95px;
                        margin-left: 5px;
                    }
                }

                input {
                    width: 180px;
                    height: 24px;
                }

                .ant-select {
                    width: 180px;
                    height: 27px;
                }
                
                textarea {
                    width: 180px;
                }
            }

            input {
                height: 24px;
            }

            .ant-select-selector {
                height: 24px;

                .ant-select-selection-item {
                    line-height: 24px;
                }
            }
        }

        .ant-collapse {
            width: 100%;
            height: 100%;
        }

        .ant-collapse-header {
            background: #FFFFFF;
            box-shadow: 0 2px 5px 0 rgb(0 22 121 / 12%);
            border-radius: 6px;
        }

        .ant-collapse-header {
            border-radius: 6px !important;
        }
        .ant-collapse-borderless {
            background-color: rgba(0, 0, 0, 0.0);
        }

        .ant-collapse-content {
            .ant-collapse-content-box {
                padding: 12px 4px;
            }
        }

        .ant-collapse-item {
            border-bottom: 0;
        }
    }
`,D=R.Ay.div`
    padding: 20px ;
    height: 50vh;
    display: flex;
    flex-direction: column;
    .daq-layout {
        display: flex;
    }
    .dialog-content {
        display: flex;
        .label-content {
            display: flex;
            word-break:keep-all; 
            align-items: center;
            .name {
                margin-right: 10px;
            }
        }
    }

    .transfer-content {
        display: flex;
        padding: 10px 0 ;
        height: 48.5vh;
            .layout-right {
                display: flex;
                flex-direction: column;
                justify-content: center;
                width: 20%;
                margin-left: 10px;
            }
        }
`,Y=R.Ay.div`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0;
`,U=(0,M.D0)("15px"),V=(0,M.D0)("60px"),B=R.Ay.div`
    display: flex;
    height: 100%;
    /* padding:  */
    >.left {
        width: 200px;
        height: 100%;
        background: ${M.o$.white};
        margin-right: ${U};

        >.group-item {
            height: 100%;

            >.group-title {
                display: flex;
                height: ${V};
                padding: 0 ${U};
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid ${M.o$.borderGray};
            }
            >.group-body {
                height: calc(100% - ${V});
                overflow-y: scroll;
                // 抄写继承子任务样式
                >.row {
                    .info {
                        padding: 5px;
                        cursor: pointer;
                        &:hover {
                            background: rgba(215, 226, 255, 0.2);
                        }
                    }
                    .select {
                        padding: 5px;
                        cursor: pointer;
                        background: rgba(215, 226, 255, 1);
                    }
                }
            }
        }

    }

    >.right {
        flex-grow: 1;
        background: ${M.o$.white};
        padding: ${U};
    }
`,F=R.Ay.div`
    background: ${M.o$.white};
    padding: ${(0,M.D0)("20px")} ${(0,M.D0)("30px")};
`,G=i=>{let{i:e,index:l,handleIcon:a,handelFx:d,handelConstantChange:n,handelAdd:t,handelEdit:s,handelSubtract:u,inputVariables:c,panelView:p}=i;const{t:g}=(0,v.Bd)();return(0,O.jsxs)("div",{className:"group-item",children:[(0,O.jsxs)("div",{className:"group-title",children:[(0,O.jsx)("div",{children:g(e.title)}),(null===e||void 0===e?void 0:e.type)===b.dk.CONTROL&&(0,O.jsxs)(r.A,{children:[(null===e||void 0===e?void 0:e.is_fx)&&(0,O.jsx)("div",{className:a(e),onClick:i=>d(i,e,l)}),null!==e&&void 0!==e&&e.is_constant?(0,O.jsx)(o.A,{showSearch:!0,optionFilterProp:"name",style:{width:"10vw"},fieldNames:{label:"name",value:"id"},value:null===e||void 0===e?void 0:e.constant_id,onChange:i=>n(i,e,l),onClick:i=>i.stopPropagation(),options:null===c||void 0===c?void 0:c.filter((i=>"Control"===i.variable_type&&i.type===L.Ih.GENERAL&&i.control_tab.dialog_type===e.dialog_type))}):(0,O.jsxs)(O.Fragment,{children:[(0,O.jsx)("div",{onClick:i=>t(i,e),className:"btn-layout",children:(0,O.jsx)("img",{src:_.d7,alt:""})}),e.dialog_type===b.YM.VARIABLE_LIST&&(0,O.jsx)("div",{onClick:i=>s(i,e),className:"btn-layout",children:(0,O.jsx)("img",{src:_.G8,alt:""})}),(0,O.jsx)("div",{onClick:i=>u(i,e),className:"btn-layout",children:(0,O.jsx)("img",{src:_.pU,alt:""})})]})]})]}),(0,O.jsx)("div",{className:"group-body",children:!(null!==e&&void 0!==e&&e.is_constant)&&p(e,l)})]})},P=i=>{const{groups:e,getKey:l}=i;return(0,O.jsxs)(B,{children:[(0,O.jsx)("div",{className:"left",children:(null===e||void 0===e?void 0:e.find((i=>"\u4f20\u611f\u5668\u7a97\u53e3"===i.title)))&&(0,O.jsx)(G,{i:null===e||void 0===e?void 0:e.find((i=>"\u4f20\u611f\u5668\u7a97\u53e3"===i.title)),index:0,...i})}),(0,O.jsx)("div",{className:"right",children:null===e||void 0===e?void 0:e.filter((i=>"\u8fdb\u5ea6\u8868"!==i.title&&"\u4f20\u611f\u5668\u7a97\u53e3"!==i.title)).map(((e,a)=>(0,O.jsx)(G,{i:e,index:a,...i},l(e))))})]})},z=i=>{const{groups:e,getKey:l}=i;return(0,O.jsxs)(F,{children:[(0,O.jsx)("div",{className:"left",children:(null===e||void 0===e?void 0:e.length)>0&&(0,O.jsx)(G,{i:e[0],index:0,...i})}),(0,O.jsx)("div",{className:"right",children:null===e||void 0===e?void 0:e.slice(1).filter((i=>"\u8fdb\u5ea6\u8868"!==i.title)).map(((e,a)=>(0,O.jsx)(G,{i:e,index:a,...i},l(e))))})]})},K=i=>{const{groups:e,subTaskRenderKey:l,getKey:a,handleIcon:d,handelFx:n,handelConstantChange:t,handelAdd:o,handelEdit:r,handelSubtract:s,panelView:u}=i,{t:c}=(0,v.Bd)();return(0,O.jsx)("div",{children:(e=>{switch(e){case L.Xj.\u9650\u4f4d\u68c0\u6d4b:case L.Xj.\u4e0b\u4f4d\u673a\u9650\u4f4d\u68c0\u6d4b:return(0,O.jsx)(P,{...i});case L.Xj.\u659c\u6ce2:default:return(0,O.jsx)(z,{...i})}})(l)})},{Panel:q}=d.A,W=i=>{const{unitList:e,setSelectedImg:l,paramsData:c,callback:g,action_id:w,subTaskRenderKey:N,disabled:I}=i,{t:R}=(0,v.Bd)(),{subTaskCorrelationVariables:M}=(0,y.A)(),E=C(),U=(0,u.d4)((i=>i.template.signalList)),V=(0,u.d4)((i=>i.template.resultData)),[B,F]=(0,a.useState)(!1),[G,P]=(0,a.useState)(!1),[z,W]=(0,a.useState)("id"),[X,H]=(0,a.useState)("name"),[J,Q]=(0,a.useState)(""),[Z,ii]=(0,a.useState)([]),[ei,li]=(0,a.useState)(void 0),[ai,di]=(0,a.useState)({}),[ni,ti]=(0,a.useState)([]),[oi,ri]=(0,a.useState)([]),[si,vi]=(0,a.useState)(""),[ui,ci]=(0,a.useState)(),[pi,gi]=(0,a.useState)([]);(0,a.useEffect)((()=>{var i,e;ii(null===c||void 0===c?void 0:c.groups),gi(null!==(i=null===c||void 0===c||null===(e=c.groups)||void 0===e?void 0:e.map((i=>Ti(i))))&&void 0!==i?i:[])}),[c]),(0,a.useEffect)((()=>{var i;xi(null===Z||void 0===Z||null===(i=Z.filter((i=>(null===i||void 0===i?void 0:i.type)!==b.dk.CONTROL)))||void 0===i?void 0:i.flatMap((i=>null===i||void 0===i?void 0:i.variables)))}),[Z]);const xi=async i=>{if(i&&i.length>0){const e=await M(i,c.id,w||null);ci(e)}},bi=(i,e)=>{if(!I)if(i.stopPropagation(),ei){const i=Z.map((i=>i.id)),l=Z.filter((i=>i.parent_id!==ei.parent_id));if(e.control_type===b.ze.NOT_CUSTOM){if(e.dialog_type===b.YM.VARIABLE){const a=l.map((i=>e.id===i.id?{...i,variables:e.variables.filter((i=>i.id!==ei.id))}:i)).sort(((e,l)=>i.indexOf(e.id)-i.indexOf(l.id)));ii(a),g&&g(a)}if(e.dialog_type===b.YM.SIGNAL){const a=l.map((i=>e.id===i.id?{...i,signals:e.signals.filter((i=>i.signal_variable_id!==ei.signal_variable_id))}:i)).sort(((e,l)=>i.indexOf(e.id)-i.indexOf(l.id)));ii(a),g&&g(a)}if(e.dialog_type===b.YM.RESULT){const a=l.map((i=>e.id===i.id?{...i,results:e.results.filter((i=>i.result_variable_id!==ei.result_variable_id))}:i)).sort(((e,l)=>i.indexOf(e.id)-i.indexOf(l.id)));ii(a),g&&g(a)}if(e.dialog_type===b.YM.VARIABLE_LIST){const a=l.map((i=>e.id===i.id?{...i,variable_list:e.variable_list.filter((i=>i.id!==(null===ei||void 0===ei?void 0:ei.id)))}:i)).sort(((e,l)=>i.indexOf(e.id)-i.indexOf(l.id)));ii(a),g&&g(a)}}else{const a=l.map((i=>i.id===e.id?{...e,customs:e.customs.filter((i=>i.id!==ei.id))}:i)).sort(((e,l)=>i.indexOf(e.id)-i.indexOf(l.id)));ii(a),g&&g(a)}}else n.Ay.error(R("\u8bf7\u9009\u62e9"))},hi=(i,e)=>{if(!I)if(i.stopPropagation(),(i=>{W("id"),H("name"),i.dialog_type===b.YM.SIGNAL&&(W("signal_variable_id"),H("variable_name")),i.dialog_type===b.YM.RESULT&&(W("result_variable_id"),H("variable_name"))})(e),e.control_type===b.ze.NOT_CUSTOM)di(e),vi(null===e||void 0===e?void 0:e.daq_code),(i=>{let e=[];switch(i){case b.YM.SIGNAL:e=U;break;case b.YM.RESULT:e=V;break;default:e=E.filter((i=>i.type===L.Ih.GENERAL))}ri(e)})(null===e||void 0===e?void 0:e.dialog_type,null===e||void 0===e||e.daq_code),F(!0),(i=>{const{dialog_type:e,variables:l,signals:a,results:d,variable_list:n}=i;let t=[];switch(e){case b.YM.SIGNAL:t=null===a||void 0===a?void 0:a.map((i=>i.signal_variable_id));break;case b.YM.RESULT:t=null===d||void 0===d?void 0:d.map((i=>i.result_variable_id));break;case b.YM.VARIABLE_LIST:t=null===n||void 0===n?void 0:n.map((i=>i.id));break;default:t=null===l||void 0===l?void 0:l.map((i=>i.id))}ti(t)})(e);else{const i=Z.map((i=>i.id)),l=[...Z.filter((i=>i.id!==e.id)),{...e,customs:[...e.customs,{id:crypto.randomUUID(),parent_id:e.id,name:(0,A.p9)(e.customs.map((i=>i.name)),e.default_name),related_variables:e.related_variables.map((i=>({...i,parent_id:e.id,type:"related",id:crypto.randomUUID()})))}]}].sort(((e,l)=>i.indexOf(e.id)-i.indexOf(l.id)));ii(l),g&&g(l)}},_i=(0,a.useCallback)(p()((i=>g(i)),1e3),[]),mi=(i,e,l)=>{Q(i);const a=Z.map((a=>e.parent_id===a.id&&a.dialog_type===b.YM.VARIABLE_LIST?{...a,variable_list:a.variable_list.map((a=>{const d=x()(a);return d.id===e.id&&(d.default_val[l]=i),d}))}:a));ii(a),g&&_i(a),P(!1)},fi=(i,e)=>{var l;I||(i.stopPropagation(),ei?(P(!0),Q(null===ei||void 0===ei||null===(l=ei.default_val)||void 0===l?void 0:l.func)):n.Ay.error(R("\u8bf7\u9009\u62e9")))},yi=()=>{F(!1)},ji=(i,e)=>{var l;return{...i,type:"related",parent_id:e.id,id:i.id,daq_code:si,related_variables:null===ai||void 0===ai||null===(l=ai.related_variables)||void 0===l?void 0:l.map((i=>({...i,parent_id:e.id,type:"related",id:crypto.randomUUID()})))}},Ai=(i,e)=>{const l=Z.filter((e=>e.parent_id!==i.parent_id));i.related_variables&&(l.splice(e+1,0,...i.related_variables),ii(l)),li(i)},Li=(i,e)=>{Ai(i,e)},ki=(i,e,l,a)=>i&&(null===i||void 0===i?void 0:i.length)>0?null===i||void 0===i?void 0:i.map((i=>{const d=x()(i);return d[a]===ei[a]&&(d.related_variables=null===d||void 0===d?void 0:d.related_variables.map((i=>i.id===e.id?{...i,variables:e.variables.map((i=>i.id===l.id?l:i))}:i))),d})):i,Si=(i,e)=>{const l=null===i||void 0===i?void 0:i.variables.filter((i=>{var l,a;return null===(l=e.related_var_tab)||void 0===l||null===(a=l.vars)||void 0===a?void 0:a.some((e=>e.key===i.id))}));l&&(null===l||void 0===l?void 0:l.length)>0&&xi(null===Z||void 0===Z?void 0:Z.filter((i=>(null===i||void 0===i?void 0:i.type)!==b.dk.CONTROL)).flatMap((i=>null===i||void 0===i?void 0:i.variables)))},wi=i=>""+((null===ei||void 0===ei?void 0:ei.id)===i.id?"select":"info"),Ni=i=>""+((null===ei||void 0===ei?void 0:ei.result_variable_id)===i.result_variable_id?"select":"info"),Ci=i=>""+((null===ei||void 0===ei?void 0:ei.signal_variable_id)===i.signal_variable_id?"select":"info"),Ii=[{title:R("\u663e\u793a\u540d"),dataIndex:"name",key:"name"},{title:R("\u8868\u8fbe\u5f0f"),dataIndex:"func",key:"func",width:"13vw",render:(i,e)=>{var l;return(0,O.jsx)(t.A,{value:null===e||void 0===e||null===(l=e.default_val)||void 0===l?void 0:l.func,disabled:I,onChange:i=>mi(i.target.value,e,"func")})}},{title:R("\u5355\u4f4d"),dataIndex:"unit",key:"unit",render:(i,l)=>{var a,d;const n=null===(a=e.find((i=>{var e,a;return(null===i||void 0===i?void 0:i.id)===(null===l||void 0===l||null===(e=l.number_tab)||void 0===e||null===(a=e.unit)||void 0===a?void 0:a.unitType)})))||void 0===a?void 0:a.units;return(0,O.jsx)(o.A,{showSearch:!0,disabled:I,optionFilterProp:"name",fieldNames:{label:"name",value:"id"},onChange:i=>mi(i,l,"unit"),options:n,value:null===l||void 0===l||null===(d=l.default_val)||void 0===d?void 0:d.unit})}}],Oi=(i,e)=>{var d;if(i.type===b.dk.CONTROL){var n;if(i.control_type===b.ze.NOT_CUSTOM){var t,o,r;if(i.dialog_type===b.YM.VARIABLE)return(0,O.jsx)("div",{className:"row",children:(0,O.jsx)("div",{className:"content",children:null===(t=i.variables)||void 0===t?void 0:t.map((i=>(0,O.jsx)("div",{className:wi(i),onClick:()=>Li(i,e),children:i.name},null===i||void 0===i?void 0:i.id)))})});if(i.dialog_type===b.YM.SIGNAL)return(0,O.jsx)("div",{className:"row",children:(0,O.jsx)("div",{className:"content",children:null===(o=i.signals)||void 0===o?void 0:o.map((i=>(0,O.jsx)("div",{className:Ci(i),onClick:()=>Li(i,e),children:null===i||void 0===i?void 0:i.variable_name},null===i||void 0===i?void 0:i.signal_variable_id)))})});if(i.dialog_type===b.YM.RESULT)return(0,O.jsx)("div",{className:"row",children:(0,O.jsx)("div",{className:"content",children:null===(r=i.results)||void 0===r?void 0:r.map((i=>(0,O.jsx)("div",{className:Ni(i),onClick:()=>Li(i,e),children:i.variable_name},null===i||void 0===i?void 0:i.result_variable_id)))})});if(i.dialog_type===b.YM.VARIABLE_LIST)return(0,O.jsx)("div",{className:"row",children:(0,O.jsx)("div",{className:"content",children:(0,O.jsx)(j.A,{rowSelection:{type:"radio",onChange:(i,l)=>{let[a]=l;Li(a,e)}},sorter:!0,size:"small",scroll:{y:"30vh"},rowKey:"id",columns:Ii,dataSource:i.variable_list,pagination:!1})})})}return(0,O.jsx)("div",{className:"row",children:(0,O.jsx)("div",{className:"content",children:null===i||void 0===i||null===(n=i.customs)||void 0===n?void 0:n.map((i=>(0,O.jsx)("div",{className:wi(i),onClick:()=>Ai(i,e),children:null===i||void 0===i?void 0:i.name},null===i||void 0===i?void 0:i.id)))})})}return null===i||void 0===i||null===(d=i.variables)||void 0===d?void 0:d.map((e=>(0,O.jsx)(a.Fragment,{children:(0,O.jsx)(h.A,{variable:e,disabled:I,scriptData:ui,onChange:e=>((i,e)=>{let l=Z;if("related"===i.type){const a=l.map((l=>{const a=x()(l);return a.id===i.parent_id&&("signals"in a&&(a.signals=ki(null===a||void 0===a?void 0:a.signals,i,e,"signal_variable_id")),"results"in a&&(a.results=ki(null===a||void 0===a?void 0:a.results,i,e,"result_variable_id")),"customs"in a&&(a.customs=ki(null===a||void 0===a?void 0:a.customs,i,e,"id")),"variable_list"in a&&(a.variable_list=ki(null===a||void 0===a?void 0:a.variable_list,i,e,"id")),"variables"in a&&(a.variables=ki(null===a||void 0===a?void 0:a.variables,i,e,"id"))),a}));l=a}const a=l.map((l=>l.id===i.id?{...l,variables:i.variables.map((i=>i.id===e.id?e:i))}:l));ii(a),g&&g(a),Si(i,e)})(i,e),setSelectedImg:l,openMarginBottom:!0})},null===e||void 0===e?void 0:e.id)))},Ti=i=>null!==i&&void 0!==i&&i.parent_id?`${null===i||void 0===i?void 0:i.id}_${null===i||void 0===i?void 0:i.parent_id}`:null===i||void 0===i?void 0:i.id,Ri=i=>null!==i&&void 0!==i&&i.is_constant?"bianliang":"changliang",Mi=(i,e,l)=>{i.stopPropagation();const a=x()(e);a.is_constant=!a.is_constant;let d=Z.map(((i,e)=>e===l?a:i));ei&&(d=d.filter((i=>i.parent_id!==(null===ei||void 0===ei?void 0:ei.parent_id)))),ii(d),li(void 0),g&&g(d)},Ei=(i,e,l)=>{const a=x()(e);a.constant_id=i;const d=Z.map(((i,e)=>e===l?a:i));ii(d),g&&g(d)},$i=i=>null!==i&&void 0!==i&&i.code?R(`${(null===i||void 0===i?void 0:i.name)||(null===i||void 0===i?void 0:i.variable_name)}(${null===i||void 0===i?void 0:i.code})`):R((null===i||void 0===i?void 0:i.name)||(null===i||void 0===i?void 0:i.variable_name)),Di=()=>"btn-layout "+(I?"disabled":"");return(0,O.jsxs)($,{children:[N?(0,O.jsx)(K,{groups:Z,subTaskRenderKey:N,getKey:Ti,handleIcon:Ri,handelFx:Mi,handelConstantChange:Ei,handelAdd:hi,handelEdit:fi,handelSubtract:bi,inputVariables:E,panelView:Oi}):(0,O.jsx)("div",{className:"form",children:(0,O.jsx)(d.A,{activeKey:pi,bordered:!1,expandIcon:i=>{let{isActive:e}=i;return(0,O.jsx)(T,{isActive:e})},onChange:i=>{gi(i);const e=i.filter((i=>!i.includes("_"))),l=Z.filter((i=>!("parent_id"in i)||e.includes(i.parent_id)));e.includes(null===ei||void 0===ei?void 0:ei.parent_id)||li(void 0),ii(l)},expandIconPosition:"end",children:null===Z||void 0===Z?void 0:Z.map(((i,e)=>{var l;return(0,O.jsx)(q,{header:(0,O.jsx)("div",{className:"card-title",children:R(i.title)}),extra:(0,O.jsx)(O.Fragment,{children:(null===i||void 0===i?void 0:i.type)===b.dk.CONTROL&&(0,O.jsxs)(r.A,{children:[(null===i||void 0===i?void 0:i.is_fx)&&(0,O.jsx)("div",{className:Ri(i),onClick:l=>Mi(l,i,e)}),null!==i&&void 0!==i&&i.is_constant?(0,O.jsx)(o.A,{showSearch:!0,disabled:I,optionFilterProp:"name",style:{width:"10vw"},fieldNames:{label:"name",value:"id"},value:null===i||void 0===i?void 0:i.constant_id,onChange:l=>Ei(l,i,e),onClick:i=>i.stopPropagation(),options:null===E||void 0===E||null===(l=E.filter((e=>"Control"===e.variable_type&&e.type===L.Ih.GENERAL&&(e.control_tab.dialog_type===i.dialog_type||e.control_tab.type===i.control_type))))||void 0===l?void 0:l.map((i=>({...i,name:R(i.name)})))}):(0,O.jsxs)(O.Fragment,{children:[(0,O.jsx)("div",{onClick:e=>hi(e,i),className:Di(),children:(0,O.jsx)("img",{src:_.d7,alt:""})}),i.dialog_type===b.YM.VARIABLE_LIST&&(0,O.jsx)("div",{onClick:i=>fi(i),className:Di(),children:(0,O.jsx)("img",{src:_.G8,alt:""})}),(0,O.jsx)("div",{onClick:e=>bi(e,i),className:Di(),children:(0,O.jsx)("img",{src:_.pU,alt:""})})]})]})}),children:!(null!==i&&void 0!==i&&i.is_constant)&&Oi(i,e)},Ti(i))}))})}),G&&(0,O.jsx)(S.RN,{open:G,script:J,module:S.et.\u8f93\u5165\u53d8\u91cf,onOk:i=>mi(i,ei,"func"),onCancel:()=>{P(!1),Q("")}}),B&&(0,O.jsx)(m.A,{open:B,title:R("\u9009\u62e9"),onCancel:yi,footer:null,width:"50vw",children:(0,O.jsxs)(f.A,{children:[(0,O.jsx)(D,{children:(0,O.jsx)(k.A,{dataSource:oi,onChange:i=>{ti(i)},targetKeys:ni,onChangeDelWay:i=>{ti(ni.filter((e=>e!==i[z])))},isMove:!0,oneWayLabel:X,onChangeMove:i=>{ti(i)},oneWay:!0,render:i=>$i(i),wayRender:i=>$i(i),rowKey:z})}),(0,O.jsx)(Y,{children:(0,O.jsxs)(r.A,{children:[(0,O.jsx)(s.Ay,{type:"primary",onClick:()=>{const i=oi.filter((i=>ni.includes(i.id))).sort(((i,e)=>ni.indexOf(i.id)-ni.indexOf(e.id))),e=oi.filter((i=>ni.includes(i.signal_variable_id))).sort(((i,e)=>ni.indexOf(i.signal_variable_id)-ni.indexOf(e.signal_variable_id))),l=oi.filter((i=>ni.includes(i.result_variable_id))).sort(((i,e)=>ni.indexOf(i.result_variable_id)-ni.indexOf(e.result_variable_id))),a=Z.map((a=>{if(a.id===ai.id){if(a.dialog_type===b.YM.VARIABLE)return{...a,variables:i.map((i=>{var e;const l=null===a||void 0===a||null===(e=a.variables)||void 0===e?void 0:e.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.id)));return l?{...ji(i,a),related_variables:l.related_variables}:ji(i,a)}))};if(a.dialog_type===b.YM.VARIABLE_LIST)return{...a,variable_list:i.map((i=>{var e,l;const d=ji(i,a),n=null===a||void 0===a||null===(e=a.variable_list)||void 0===e||null===(l=e.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.id))))||void 0===l?void 0:l.default_val;return{...d,default_val:n||{...d.default_val,func:""}}}))};if(a.dialog_type===b.YM.SIGNAL)return{...a,daq_code:si,signals:e.map((i=>{var e;const l=null===a||void 0===a||null===(e=a.signals)||void 0===e?void 0:e.find((e=>(null===e||void 0===e?void 0:e.signal_variable_id)===(null===i||void 0===i?void 0:i.signal_variable_id)));return l?{...ji(i,a),related_variables:l.related_variables}:ji(i,a)}))};if(a.dialog_type===b.YM.RESULT)return{...a,results:null===l||void 0===l?void 0:l.map((i=>{var e;const l=null===a||void 0===a||null===(e=a.results)||void 0===e?void 0:e.find((e=>(null===e||void 0===e?void 0:e.result_variable_id)===(null===i||void 0===i?void 0:i.result_variable_id)));return l?{...ji(i,a),related_variables:l.related_variables}:ji(i,a)}))}}return a}));ii(a),g&&g(a),yi()},children:R("\u786e\u5b9a")}),(0,O.jsx)(s.Ay,{onClick:yi,children:R("\u53d6\u6d88")})]})})]})})]})}},35666:(i,e,l)=>{l.d(e,{Bx:()=>a,GL:()=>u,T$:()=>s,U9:()=>v,YM:()=>t,Yz:()=>n,dk:()=>r,vU:()=>o,ze:()=>d});const a=i=>{let{t:e}=i;return[{value:"controlLibrary",label:e("\u63a7\u5236\u5668\u4ee4\u5e93")},{value:"dataLibrary",label:e("\u6570\u636e\u91c7\u96c6\u5e93")},{value:"actionLibrary",label:e("\u8bd5\u9a8c\u52a8\u4f5c\u5e93")},{value:"userExperienceLibrary",label:e("\u7528\u6237\u4ea4\u4e92\u5e93")},{value:"deviceExperienceLibrary",label:e("\u8815\u53d8\u4e13\u7528\u5e93")},{value:"eventDetectionLibrary",label:e("\u4e8b\u4ef6\u68c0\u6d4b\u5e93")},{value:"flowLogicLibrary",label:e("\u6d41\u7a0b\u903b\u8f91\u5e93")},{value:"globalLibrary",label:e("\u5168\u5c40\u4e13\u7528\u5e93")},{value:"highFrequencyLibrary",label:e("\u9ad8\u9891\u4e13\u7528\u5e93")}]},d={NOT_CUSTOM:"not_custom",CUSTOM:"custom"},n=i=>{let{t:e}=i;return[{value:d.NOT_CUSTOM,label:e("\u975e\u81ea\u5b9a\u4e49")},{value:d.CUSTOM,label:e("\u81ea\u5b9a\u4e49")}]},t={VARIABLE:"variable",VARIABLE_LIST:"variable_list",SIGNAL:"signal",RESULT:"result",RELATED:"related"},o=[{value:t.SIGNAL,label:"\u4f20\u611f\u5668\u7a97\u53e3"},{value:t.VARIABLE,label:"\u8f93\u5165\u53d8\u91cf\u7a97\u53e3"},{value:t.RESULT,label:"\u7ed3\u679c\u53d8\u91cf\u7a97\u53e3"},{value:t.VARIABLE_LIST,label:"\u8f93\u5165\u53d8\u91cf\u5217\u8868"}],r={DIALOG:"dialog",CONTROL:"control"},s={[r.DIALOG]:"\u5bf9\u8bdd\u6846",[r.CONTROL]:"\u63a7\u4ef6"},v={STANDARD:"standard",SPECIALTY:"specialty"},u={[v.STANDARD]:"\u6807\u51c6",[v.SPECIALTY]:"\u4e13\u4e1a"}}}]);
//# sourceMappingURL=4607.fd189659.chunk.js.map