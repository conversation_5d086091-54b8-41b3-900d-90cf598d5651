"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4828],{24828:(e,n,t)=>{t.r(n),t.d(n,{default:()=>P});var o=t(65043),i=t(74117),r=t(63942),l=t(51376),u=t(80077),a=t(29977),d=t(44409),c=t(56543),s=t(67208),v=t(22),I=t(75440),f=t(61668),O=t(88359);const _="OK";var m=t(70579);const{confirm:A,info:p}=r.A,U=()=>{const{t:e}=(0,i.Bd)(),n=(0,u.wA)(),{send:t,useSubscriber:r}=(0,d.A)(),{playAudio:U,removeAudio:P}=(0,f.A)(),L=(0,a.A)(),[C,g]=(0,o.useState)(!1),[D,h]=(0,o.useState)(),[x,y]=(0,o.useState)(),[T,k]=(0,o.useState)(),[E,N]=(0,o.useState)("\u63d0\u793a"),[R,S]=(0,o.useState)(!1),[w,z]=(0,o.useState)("\u786e\u8ba4"),[b,G]=(0,o.useState)(!1),K=(0,o.useRef)(),j=(0,o.useRef)(),M=(0,o.useRef)(),V=(0,o.useRef)([]),B=(0,o.useRef)([]),J=(0,o.useRef)();(0,o.useEffect)((()=>(F(),Y(),()=>{var e,n,t,o;null===(e=K.current)||void 0===e||null===(n=e.close)||void 0===n||n.call(e),null===(t=j.current)||void 0===t||null===(o=t.close)||void 0===o||o.call(t)})),[]);const F=async()=>{K.current=await r(c.t6.UI_OPEN_MODAL);for await(const[e,n]of K.current){const e=JSON.parse(n);M.current?V.current=[...V.current,e]:(k(e),M.current=e)}},Y=async()=>{j.current=await r(c.t6.UI_TASK_PLAY_AUDIO);for await(const[e,n]of j.current){const e=JSON.parse(n);B.current=[...B.current,e]}};(0,o.useEffect)((()=>{if(T&&B.current&&B.current.length>0){const n=B.current.find((e=>e.SubTaskID===T.SubTaskID));var e;if(n)J.current=n,B.current=B.current.filter((e=>e.SubTaskID!==T.SubTaskID)),U({audio_id:null===(e=n.UIParams)||void 0===e?void 0:e.audioId,isLoopPlay:!0})}}),[T,B.current]),(0,o.useEffect)((()=>{if(!T&&V.current&&V.current.length>0){const[e]=V.current;k(e),M.current=e,V.current.shift()}if(T){if(T.UICmd===c.zx.OPEN_MODAL){const e=null===T||void 0===T?void 0:T.UIParams;y(null===e||void 0===e?void 0:e.dialogInfo),(null===e||void 0===e?void 0:e.dialogType)===c.YM.REMINDER&&(z("\u77e5\u9053\u4e86"),G(!0)),g(!0)}if(T.UICmd===c.zx.OPEN_DIALOG){const e=null===T||void 0===T?void 0:T.UIParams;N(null===e||void 0===e?void 0:e.Title),g(!0),S((null===e||void 0===e?void 0:e.ButtonType)!==_),G((null===e||void 0===e?void 0:e.ButtonType)===_)}}}),[T]),(0,o.useEffect)((()=>{if(!T&&V.current&&V.current.length>0){const[e]=V.current;k(e),M.current=e,V.current.shift()}if(T){if(T.UICmd===c.zx.INPUT_VAR_DIALOG){h(!0);const n=null===T||void 0===T?void 0:T.UIParams;if(L&&(null===L||void 0===L?void 0:L.length)>0){var e;const t=null===n||void 0===n||null===(e=n.listMap)||void 0===e?void 0:e.map((e=>{const n=L.find((n=>n.code===e.code));return{...n,default_val:{...n.default_val,value:e.value}}}));y(t)}}if(T.UICmd===c.zx.OPEN_INPUT_DIALOG){var n;const e=null===T||void 0===T?void 0:T.UIParams;if(h(!0),null!==e&&void 0!==e&&e.InputVars&&(null===e||void 0===e||null===(n=e.InputVars)||void 0===n?void 0:n.length)>0){var t;const n=null===e||void 0===e||null===(t=e.InputVars)||void 0===t?void 0:t.map((e=>L.find((n=>n.code===e))));y(n)}null!==e&&void 0!==e&&e.Title&&N(null===e||void 0===e?void 0:e.Title)}}}),[T]);const $=()=>{var e,n,t,o;(g(!1),h(!1),G(!1),S(!1),y([]),N("\u63d0\u793a"),z("\u786e\u8ba4"),k(),null!==(e=J.current)&&void 0!==e&&null!==(n=e.UIParams)&&void 0!==n&&n.audioId)&&P(null===(t=J.current)||void 0===t||null===(o=t.UIParams)||void 0===o?void 0:o.audioId);J.current=void 0,V.current&&0===V.current.length&&(M.current=void 0,B.current=[])},q=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c.Rw.CLICK_OK,n=arguments.length>1?arguments[1]:void 0;const{SubTaskID:o,ProcessID:i}=T;t(JSON.stringify({SubTaskID:o,ProcessID:i,Action:e,Target:c.pI.TO_TASK,JsonArgs:n?JSON.stringify(n):null}))},H=e=>{q(c.Rw.CLOSE_OK,e),$()},Q=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c.Rw.ModalClickOK;q(e,arguments.length>1?arguments[1]:void 0),$()},W=async()=>{try{x&&(await(0,s.tTc)({input_vars:null===x||void 0===x?void 0:x.map((e=>{let{id:n,default_val:t,is_feature:o,is_fx:i}=e;return{id:n,default_val:t,is_feature:o,is_fx:i}}))}),Q(c.Rw.CLICK_OK),n((0,O.w)()))}catch(e){console.log("\u5b50\u4efb\u52a1\u5f39\u7a97\u51fa\u9519\uff1a",e)}},X=e=>{y(null===x||void 0===x?void 0:x.map((n=>n.id===e.id?e:n)))},Z={[c.zx.OPEN_MODAL]:H,[c.zx.INPUT_VAR_DIALOG]:H,[c.zx.OPEN_DIALOG]:()=>Q(c.Rw.CLICK_OK,{ClickOk:!0}),[c.zx.OPEN_INPUT_DIALOG]:H},ee={[c.zx.OPEN_MODAL]:Q,[c.zx.INPUT_VAR_DIALOG]:W,[c.zx.OPEN_DIALOG]:()=>Q(c.Rw.CLICK_OK,{ClickOk:!0}),[c.zx.OPEN_INPUT_DIALOG]:W},ne={[c.zx.OPEN_MODAL]:H,[c.zx.INPUT_VAR_DIALOG]:H,[c.zx.OPEN_DIALOG]:()=>H({ClickOk:!1}),[c.zx.OPEN_INPUT_DIALOG]:H},te=(0,o.useCallback)((()=>{if(g(!1),b)G(!1),p({title:e(E),okText:e(w),content:x,onOk(){null!==Z&&void 0!==Z&&Z[T.UICmd]&&(null===Z||void 0===Z||Z[T.UICmd]())}});else{const n={title:e(E),icon:(0,m.jsx)(l.A,{}),content:x,okText:e(w),maskClosable:!1,onOk(){null!==ee&&void 0!==ee&&ee[T.UICmd]&&(null===ee||void 0===ee||ee[T.UICmd]())},onCancel(){null!==ne&&void 0!==ne&&ne[T.UICmd]&&(null===ne||void 0===ne||ne[T.UICmd]())}};R?A(n):p(n)}}),[b,R,T,E,w,x,C]);return(0,m.jsxs)(m.Fragment,{children:[C&&te(),D&&(0,m.jsx)(I.A,{width:"40vw",title:e(E),open:D,onOk:W,onCancel:H,children:null===x||void 0===x?void 0:x.map((e=>(0,m.jsx)(o.Fragment,{children:(0,m.jsx)(v.A,{variable:e,onChange:X,openMarginBottom:!0})},e.id)))})]})},P=(0,o.memo)(U)},61668:(e,n,t)=>{t.d(n,{A:()=>l});t(65043);var o=t(80077),i=t(63612),r=t(67208);const l=()=>{const e=(0,o.wA)();return{initAudioData:async()=>{try{const n=await(0,r.lrG)();if(n){const t=n.reverse();e({type:i.FY,param:t})}}catch(n){console.log(n)}},playAudio:async e=>{let{audio_id:n,onAudioEnd:t,isLoopPlay:o=!1}=e;try{const e=await(0,r.$i0)({audio_id:n});if(e){const i=new File([e],"test.mp3",{type:"audio/mpeg"}),r=document.createElement("audio"),l=URL.createObjectURL(i);r.src=l,r.id=n,r.load(),r.play(),document.body.appendChild(r),r.addEventListener("ended",(()=>{o?r.play():r.remove(),t&&t(!0,n),URL.revokeObjectURL(l)}),!1)}}catch(i){console.error("Error playing audio:",i)}},removeAudio:async(e,n)=>{try{const t=document.getElementById(e);t&&(t.remove(),n&&n(!0,e),URL.revokeObjectURL(t.src))}catch(t){console.error("Error removing audio:",t)}}}}}}]);
//# sourceMappingURL=4828.9b8c8e4e.chunk.js.map