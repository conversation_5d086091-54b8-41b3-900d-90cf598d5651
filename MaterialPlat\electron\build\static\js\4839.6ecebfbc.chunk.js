"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4839],{42999:(e,t,n)=>{n.d(t,{A:()=>v});var l=n(65043),i=n(37097),o=n(6051),a=n(36282),r=n(18650),d=n(81143),s=n(68374);const u=d.Ay.div`
    .color-layout {
        display: flex;
        align-items: center;
    }
 
    .background-layout {
        min-width: ${(0,s.D0)("25px")} !important;
        min-height: ${(0,s.D0)("25px")} !important;
        background-color: #000;
        border-radius: 2px;
    }
    .background-img {
        width: ${(0,s.D0)("23px")};
        height: ${(0,s.D0)("23px")};
        display: flex;
        align-items:center ;
        justify-content: center;
        cursor: pointer;
    }
    .allowed {
        cursor: not-allowed;
    }

`;var c=n(70579);const v=e=>{let{onChange:t,value:n,disabled:d=!1}=e;const[s,v]=(0,l.useState)(n);(0,l.useEffect)((()=>{v(n||"#000")}),[n]);const p=(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(i.Xq,{color:s,showMoreColor:!1,onChangeComplete:e=>{const{rgb:n}=e,l=`rgba(${n.r},${n.g},${n.b},${n.a})`;v(l),t&&t(l)}})});return(0,c.jsx)(u,{children:(0,c.jsx)("div",{className:"color-layout",children:(0,c.jsxs)(o.A,{children:[(0,c.jsx)("div",{className:"background-layout",style:{backgroundColor:s}}),!d&&(0,c.jsx)(a.A,{overlayClassName:"popover-sketch-picker",content:p,title:"",trigger:"click",placement:"bottom",destroyOnHidden:!0,arrow:!1,children:(0,c.jsx)("img",{className:"background-img "+(d?"allowed":""),src:r.Dp,alt:""})})]})})})}},54839:(e,t,n)=>{n.r(t),n.d(t,{default:()=>oe});var l=n(65043),i=n(80077),o=n(74117),a=n(21256),r=n(36950),d=n(80231),s=n(67208),u=n(63804),c=n(81929),v=n.n(c),p=n(84617);const g={"\u5e73\u94fa":"\u5e73\u94fa","\u81ea\u9002\u5e94":"\u81ea\u9002\u5e94","\u5c45\u4e2d\u663e\u793a":"\u5c45\u4e2d\u663e\u793a"},m={[g["\u5e73\u94fa"]]:{backgroundRepeat:"repeat"},[g["\u81ea\u9002\u5e94"]]:{backgroundSize:"contain",backgroundRepeat:"no-repeat",backgroundPosition:"center"},[g["\u5c45\u4e2d\u663e\u793a"]]:{backgroundPosition:"center",backgroundRepeat:"no-repeat"}},h={"\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3":"\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3","\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u4e24\u884c\u663e\u793a":"\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u4e24\u884c\u663e\u793a","\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u5355\u884c\u663e\u793a":"\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u5355\u884c\u663e\u793a","\u5c45\u4e2d\u5bf9\u9f50":"\u5c45\u4e2d\u5bf9\u9f50"},b={[h["\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3"]]:"list-item-2",[h["\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u4e24\u884c\u663e\u793a"]]:"list-item-3",[h["\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u5355\u884c\u663e\u793a"]]:"list-item-4",[h["\u5c45\u4e2d\u5bf9\u9f50"]]:"list-item-5"},x=Object.assign({},...Array.from({length:23},((e,t)=>{const n=8+t;return{[`${n}px`]:`${n}px`}}))),f={backgroundImageType:g["\u5e73\u94fa"],backgroundColor:"#ffffff",headWidth:"100",headHeight:"32",fontSize:"12",showType:h["\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3"],fontColor:"#6c74ff",headBgColor:"#bdccff",updateFreq:180},y={defName:"\u7ec4",index:1,groupName:"\u7ec4",colNumber:2,rowNumber:3,UUID:crypto.randomUUID()};var j=n(70579);const C=e=>{let{item:t,values:n,setPosition:i,...a}=e;const r=(0,l.useRef)(),{t:d}=(0,o.Bd)(),s=e=>{e.preventDefault(),e.stopPropagation();const{currentTarget:t}=e,{code:n}=t.dataset,l=window.innerWidth,o=window.innerHeight;let a=e.clientX,r=e.clientY;a+120>l&&(a=l-120),r+46>o&&(r=o-46),null===i||void 0===i||i({x:a,y:r,code:n})};return(0,l.useEffect)((()=>(r&&r.current&&r.current.addEventListener("contextmenu",s),()=>{r&&r.current&&r.current.removeEventListener("contextmenu",s)})),[]),(0,j.jsx)(j.Fragment,{children:(0,j.jsxs)("div",{...a,"data-code":null===t||void 0===t?void 0:t.code,ref:r,children:[(0,j.jsx)("div",{className:"item-div item-name",children:d(null===n||void 0===n?void 0:n.name)}),(0,j.jsx)("div",{className:"item-div item-value",children:null===n||void 0===n?void 0:n.unitStr}),(0,j.jsx)("div",{className:"item-div item-unit",children:null===n||void 0===n?void 0:n.unitIdName})]})})};var w=n(81143);const A=w.Ay.div`
    width: 100%;
    height: 100%;

    .special-head-container {
        width: 100%;
        height: 100%;
        position: relative;

        .drag-box {
            border: 1px solid #e1e1e1;
            padding: 2px;
            cursor: move;
            display: inline-block;
            position: absolute;

            .drag-box-name {
                position: absolute;
                top: 0;
                transform: translate(5px, -95%);
                font-size: 12px;
                z-index: 2;
                background: #fff;
                padding: 0 2px;
                line-height: 1;
            }

            .drag-box-item {
                position: relative;
                z-index: 1;
                display: grid;
                grid-column-gap: 2px;
                grid-row-gap: 2px;

                .list-item {
                    border: 1px solid #666666;
                    padding: 2px;
                    margin-top: 2px;
                    font-size: 12px;
                    overflow: hidden;

                    
                    .item-div {
                        display: inline-block;
                        margin-right: 8px;
                        &:last-child {
                            margin-right: 0;
                        }
                    }

                    &.list-item-1{
                    }
                    &.list-item-2{
                        display: flex;
                        .item-unit{
                            flex: 1;
                            text-align: right;
                        }
                    }
                    &.list-item-3{
                        .item-unit{
                            display: block;
                        }
                    }
                    &.list-item-4{
                    }
                    &.list-item-5{
                        display: flex;
                        align-content: center;
                        justify-content: center;
                        .item-div{
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }
                }
            }

        }
    }
    
    .right-key-box{
        position: fixed;
        top: 0;
        left: 0;
        display: inline-block;
        width: 120px;
        background: #FFF;
        border: 1px solid rgba(220 ,220,220,1);
        box-shadow: 5px 5px 3px -2px rgb(0 0 0 / 40%);
        font-size: 14px;
        .right-key-box-item{
            padding: 3px 5px !important;
            &:hover{
                background: rgba(20,115,245,0.4)
            }
        }
    }
`,I=e=>{let{id:t,config:n,dragOpen:a,updateDragPosition:d}=e;const{t:u}=(0,o.Bd)(),[c,g]=(0,l.useState)({});(0,p.A)({controlCompId:t,onMessage:e=>{let{data:t}=e;const n={};Object.keys(t).forEach((e=>{n[e]=t[e].at(-1)})),g(n)}});const x=(null===n||void 0===n?void 0:n.attributeData)||{},f=(null===n||void 0===n?void 0:n.headingData)||[],y=(null===n||void 0===n?void 0:n.dragPosition)||{},w=(0,i.d4)((e=>e.template.signalList)),I=(0,i.d4)((e=>e.global.unitList)),N=(0,r.cN)(w).map((e=>({id:null===e||void 0===e?void 0:e.signalVariableId,key:null===e||void 0===e?void 0:e.signalVariableId,label:null===e||void 0===e?void 0:e.variableName,unitId:null===e||void 0===e?void 0:e.unitId,decimal:3,isIcon:!1,img:"",value:0,dimensionId:null===e||void 0===e?void 0:e.dimensionId,code:null===e||void 0===e?void 0:e.code,signals:null===e||void 0===e?void 0:e.signals}))),k=(e,t)=>{var n,l;const i=N.find((t=>t.code===e.code))||{},{code:o,unit:a,decimal:d}=e,s=(null===w||void 0===w?void 0:w.find((e=>e.code===o)))||{},u=(null===I||void 0===I||null===(n=I.find((e=>e.id===s.dimension_id)))||void 0===n?void 0:n.units)||[],v=(null===u||void 0===u?void 0:u.find((e=>e.id===a)))||{},p=null===(l=(0,r.tJ)(c[o]||0,null===v||void 0===v?void 0:v.dimension_id,a))||void 0===l?void 0:l.toFixed(null===e||void 0===e?void 0:e.decimal),g=(0,r.Im)(a)||null===v||void 0===v?void 0:v.name;return{name:null===i||void 0===i?void 0:i.label,unitStr:p,unitIdName:g}},S=e=>{const{rowNumber:t}=e||1,{colNumber:n}=e||1;return{gridTemplate:`repeat(${t}, 1fr) / repeat(${n}, 1fr)`}},D={width:null!==x&&void 0!==x&&x.headWidth?`${null===x||void 0===x?void 0:x.headWidth}px`:"auto",height:null!==x&&void 0!==x&&x.headHeight?`${null===x||void 0===x?void 0:x.headHeight}px`:"auto",backgroundColor:(null===x||void 0===x?void 0:x.headBgColor)||"#ffffff",color:(null===x||void 0===x?void 0:x.fontColor)||"#000000",fontSize:null!==x&&void 0!==x&&x.fontSize?`${null===x||void 0===x?void 0:x.fontSize}`:"14px"},_={cursor:a?"move":"initial"},[T,E]=(0,l.useState)(null),[R,L]=(0,l.useState)(!1),[U,F]=(0,l.useState)({x:0,y:0}),P=e=>{F({x:null===e||void 0===e?void 0:e.x,y:null===e||void 0===e?void 0:e.y}),E(null===e||void 0===e?void 0:e.code),L(!0)},V=()=>{E(null),L(!1)};(0,l.useEffect)((()=>{var e;const t=e=>{if(R){const t=document.querySelector(".right-key-box");t&&!t.contains(e.target)&&V()}};return null===(e=document)||void 0===e||e.addEventListener("click",t),()=>{var e;null===(e=document)||void 0===e||e.removeEventListener("click",t)}}),[R]),(0,l.useEffect)((()=>(V(),()=>{V()})),[]);const $=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];V(),T&&(0,s.KJP)({code:T,reset:e})},z=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)+1;if(null!==y&&void 0!==y&&y[e.UUID])return null===y||void 0===y?void 0:y[e.UUID];return{x:5*(t%10===0?10:t%10)+2*(Math.ceil(t/10)-1),y:5*Math.ceil(t/10)}};return(0,j.jsxs)(A,{children:[(0,j.jsx)("div",{className:"special-head-container",style:{backgroundImage:`url(${null===x||void 0===x?void 0:x.backgroundImage})`,backgroundColor:(null===x||void 0===x?void 0:x.backgroundColor)||"#ffffff",...m[null===x||void 0===x?void 0:x.backgroundImageType]},title:x.describe||"",children:f&&(null===f||void 0===f?void 0:f.groupList)&&(null===f||void 0===f?void 0:f.groupList.length)>0&&(null===f||void 0===f?void 0:f.groupList.map(((e,t)=>{var n;return e.transfer&&e.transfer.length>0?(0,j.jsx)(v(),{bounds:"parent",disabled:!a,defaultPosition:z(e,t),onStop:(t,n)=>((e,t,n)=>{const l={...y,[e]:{x:t,y:n}};d(l)})(e.UUID,n.x,n.y),children:(0,j.jsxs)("div",{className:"drag-box drag-container-box",style:{..._,transform:"translate(100px, 200px)",marginTop:e.groupNameIsShow?"10px":0},children:[e.groupNameIsShow?(0,j.jsx)("div",{className:"drag-box-name",children:u(e.groupName||e.defName)}):null,(0,j.jsx)("div",{className:"drag-box-item",style:S(e),children:null===(n=e.transfer)||void 0===n?void 0:n.map(((t,n)=>n>=(e.rowNumber||0)*(e.colNumber||0)?null:(0,j.jsx)(C,{className:"\u9ed8\u8ba4"===(null===x||void 0===x?void 0:x.showType)?`list-item ${b[h["\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3"]]}`:`list-item ${b[null===x||void 0===x?void 0:x.showType]}`,style:D,values:k(t),item:t,setPosition:P},t.code)))})]})},e.UUID):null})))}),R?(0,j.jsxs)("div",{className:"right-key-box",style:{left:U.x,top:U.y},children:[(0,j.jsx)("div",{className:"right-key-box-item",onClick:()=>$(!0),children:u("\u6e05\u96f6")}),(0,j.jsx)("div",{className:"right-key-box-item",onClick:()=>$(!1),children:u("\u6062\u590d")})]}):null]})};var N=n(8918),k=n(6051),S=n(95206),D=n(75440),_=n(83720),T=n(25055),E=n(47419),R=n(11645),L=n(36497),U=n(42999),F=n(97588),P=n(67998),V=n(56543);const $=Object.keys(g).map((e=>({label:e,value:g[e]}))),z=Object.keys(x).map((e=>({label:e,value:x[e]}))),O=Object.keys(h).map((e=>({label:e,value:h[e]}))),{TextArea:M}=_.A,{Item:W,useForm:q}=T.A,B=(0,l.forwardRef)(((e,t)=>{const{t:n}=(0,o.Bd)(),[i]=q(),{config:a,changeConfig:r,defaultAttrData:d}=e,s=(null===a||void 0===a?void 0:a.attributeData)||{},[u,c]=(0,l.useState)(!1),v=e=>{c(!1)};return(0,l.useEffect)((()=>{null!==s&&void 0!==s&&s.backgroundImageType&&i.setFieldsValue({...s,showType:"\u9ed8\u8ba4"===(null===s||void 0===s?void 0:s.showType)?h["\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3"]:null===s||void 0===s?void 0:s.showType})}),[s]),(0,l.useImperativeHandle)(t,(()=>({getData:async()=>{const e=await i.validateFields();return e||null}}))),(0,j.jsx)("div",{className:"attribute-content",children:(0,j.jsxs)(T.A,{form:i,labelCol:{span:8},initialValues:d,wrapperCol:{span:16},onValuesChange:e=>{const t=i.getFieldsValue();r(t)},children:[(0,j.jsx)("div",{className:"attribute-title",children:n("\u63a7\u4ef6\u8bbe\u7f6e")}),(0,j.jsxs)(E.A,{children:[(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("buffer"),name:"buffer",children:(0,j.jsx)(P.A,{inputVariableType:V.ps.Buffer})})}),(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u663e\u793a\u66f4\u65b0\u9891\u7387"),name:"updateFreq",children:(0,j.jsx)(L.A,{options:[{label:"0.1s",value:90},{label:"0.2s",value:180},{label:"0.5s",value:470},{label:"1s",value:900},{label:"2s",value:1900}]})})})]}),(0,j.jsxs)(E.A,{children:[(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u80cc\u666f\u56fe\u663e\u793a"),name:"backgroundImageType",children:(0,j.jsx)(L.A,{options:null===$||void 0===$?void 0:$.map((e=>({...e,label:n(e.label)})))})})}),(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{rules:[{required:!1,message:n("\u8bf7\u9009\u62e9")}],label:n("\u80cc\u666f\u56fe"),name:"backgroundImage",children:(0,j.jsx)(F.A,{src:i.getFieldValue("backgroundImage"),btnCLick:e=>{c(!0)},btnTitle:n("\u9009\u62e9\u56fe\u7247"),open:u,onCancel:v,onChange:e=>{i.setFieldValue("icon",e),v()},modalTitle:n("\u9009\u62e9\u56fe\u7247")})})})]}),(0,j.jsxs)(E.A,{children:[(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u80cc\u666f\u8272"),name:"backgroundColor",children:(0,j.jsx)(U.A,{})})}),(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u63cf\u8ff0"),name:"describe",children:(0,j.jsx)(M,{})})})]}),(0,j.jsx)("div",{className:"attribute-title",children:n("\u8868\u5934\u8bbe\u7f6e")}),(0,j.jsxs)(E.A,{children:[(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u8868\u5934\u5bbd\u5ea6"),name:"headWidth",children:(0,j.jsx)(_.A,{suffix:"px"})})}),(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u8868\u5934\u9ad8\u5ea6"),name:"headHeight",children:(0,j.jsx)(_.A,{suffix:"px"})})})]}),(0,j.jsxs)(E.A,{children:[(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u663e\u793a\u5b57\u53f7"),name:"fontSize",children:(0,j.jsx)(L.A,{options:null===z||void 0===z?void 0:z.map((e=>({...e,label:n(e.label)})))})})}),(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u663e\u793a\u65b9\u5f0f"),name:"showType",children:(0,j.jsx)(L.A,{options:null===O||void 0===O?void 0:O.map((e=>({...e,label:n(e.label)})))})})})]}),(0,j.jsxs)(E.A,{children:[(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u6587\u5b57\u8272"),name:"fontColor",children:(0,j.jsx)(U.A,{})})}),(0,j.jsx)(R.A,{span:12,children:(0,j.jsx)(W,{label:n("\u8868\u5934\u80cc\u666f\u8272"),name:"headBgColor",children:(0,j.jsx)(U.A,{})})})]})]})})}));var H=n(32513),G=n(97914),J=n(94293),K=n(12159);const Q=e=>{let{value:t=[],onChange:n,setSelectedGroupIndex:l,setSelectedGroupIndexChange:i}=e;const{t:a}=(0,o.Bd)();return(0,j.jsxs)("div",{children:[(0,j.jsxs)("div",{className:"heading-content-group-top",children:[(0,j.jsx)("div",{className:"heading-content-group-top-name",children:a("\u5217\u8868\u7ec4")}),(0,j.jsxs)("div",{className:"heading-content-group-top-btnbox",children:[(0,j.jsx)(S.Ay,{size:"small",onClick:()=>{var e;const l=t.length,o=((null===t||void 0===t||null===(e=t.at(-1))||void 0===e?void 0:e.index)||l)+1,a={...y,groupName:y.groupName+o,defName:y.groupName+o,index:o,UUID:crypto.randomUUID()},r=[...t,a];null===n||void 0===n||n(r),0===l&&(null===i||void 0===i||i(0))},children:a("\u6dfb\u52a0")}),(0,j.jsx)(S.Ay,{size:"small",onClick:()=>{if(0===t.length)return;const e=null===t||void 0===t?void 0:t.filter(((e,t)=>t!==l));setTimeout((()=>{n(e),null===i||void 0===i||i(0)}),0)},children:a("\u5220\u9664")})]})]}),(0,j.jsx)("div",{className:"heading-content-group-list",children:t.map(((e,t)=>(0,j.jsx)("div",{className:"heading-content-group-list-item "+(t===l?"active":""),onClick:()=>{i(t)},children:a(e.groupName||e.defName)},e.UUID)))})]})};var X=n(10202);const Y=e=>{let{disabled:t=!1,value:n=[],onChange:i,dataSource:a,rowKey:r="code",transferSelectIndex:d,transferSelectIndexChange:s}=e;const{t:u}=(0,o.Bd)(),[c,v]=(0,l.useState)();return(0,j.jsx)(j.Fragment,{children:(0,j.jsx)(X.A,{disabled:t,listStyle:{width:"100%",height:"30vh"},rowKey:r,isMove:!0,oneWay:!0,dataSource:a||[],targetKeys:Array.isArray(n)&&n.length>0?null===n||void 0===n?void 0:n.map((e=>e[r])):[],onChange:e=>{const t=e.map((e=>({[r]:e})));null===i||void 0===i||i(t)},onChangeDelWay:e=>{null===i||void 0===i||i(n.filter((t=>t[r]!==e[r])))},onChangeMove:e=>{const t=e.map((e=>n.find((t=>t[r]===e))));null===i||void 0===i||i(t),s(e.findIndex((e=>e===c[r])))},onChangeWay:e=>{e?(v(e),s(n.findIndex((t=>t[r]===e[r])))):(v(null),s(null))},render:e=>null!==e&&void 0!==e&&e.code?`${u(null===e||void 0===e?void 0:e.label)}(${null===e||void 0===e?void 0:e.code})`:u(null===e||void 0===e?void 0:e.label),wayRender:e=>null!==e&&void 0!==e&&e.code?`${u(null===e||void 0===e?void 0:e.label)}(${null===e||void 0===e?void 0:e.code})`:u(null===e||void 0===e?void 0:e.label)})})},{Item:Z,useForm:ee}=T.A,te=(0,l.forwardRef)(((e,t)=>{let{config:n}=e;const{t:a}=(0,o.Bd)(),d=(0,i.d4)((e=>e.template.signalList)),s=(null===n||void 0===n?void 0:n.headingData)||[],[u]=ee(),[c,v]=(0,l.useState)([]);(0,l.useEffect)((()=>{const e=(0,r.cN)(d).map((e=>({id:null===e||void 0===e?void 0:e.signalVariableId,key:null===e||void 0===e?void 0:e.signalVariableId,label:null===e||void 0===e?void 0:e.variableName,unitId:null===e||void 0===e?void 0:e.unitId,decimal:3,isIcon:!1,img:"",value:0,dimensionId:null===e||void 0===e?void 0:e.dimensionId,code:null===e||void 0===e?void 0:e.code,signals:null===e||void 0===e?void 0:e.signals})));v(e)}),[d]);const[p,g]=(0,l.useState)(0),[m,h]=(0,l.useState)(null);(0,l.useEffect)((()=>{null!==s&&void 0!==s&&s.groupList&&(null===s||void 0===s?void 0:s.groupList.length)>0&&u.setFieldsValue({...s})}),[s]);const b=T.A.useWatch("groupList",u)||[],x=(0,l.useMemo)((()=>{if(1===(null===b||void 0===b?void 0:b.length)){const e=b[0];if((null===e||void 0===e||!e.defName)&&(null===e||void 0===e||!e.UUID))return!0}return!1}),[b]);return(0,l.useImperativeHandle)(t,(()=>({getData:async()=>{const e=await u.validateFields();return e||null}}))),(0,j.jsx)(T.A,{form:u,labelCol:{style:{width:"70px"}},children:(0,j.jsxs)("div",{className:"heading-content",children:[(0,j.jsx)("div",{className:"heading-content-group",children:(0,j.jsx)(T.A.Item,{noStyle:!0,name:"groupList",children:(0,j.jsx)(Q,{setSelectedGroupIndex:p,setSelectedGroupIndexChange:e=>{g(e),h(null)}})})}),(0,j.jsxs)("div",{className:"heading-content-set",children:[(0,j.jsx)("div",{className:"heading-content-set-title",children:a("\u7ec4\u8bbe\u7f6e")}),(0,j.jsx)("div",{children:(0,j.jsxs)(E.A,{children:[(0,j.jsx)(R.A,{span:8,children:(0,j.jsxs)("div",{className:"group-name-box",children:[(0,j.jsx)("div",{className:"group-name-box-left",children:(0,j.jsx)(Z,{labelCol:{style:{width:"auto"}},label:"",valuePropName:"checked",name:["groupList",p,"groupNameIsShow"],children:(0,j.jsxs)(H.A,{disabled:x,children:[a("\u7ec4\u540d\u79f0"),":"]})})}),(0,j.jsx)("div",{className:"group-name-box-right",children:(0,j.jsx)(Z,{label:"",name:["groupList",p,"groupName"],children:(0,j.jsx)(_.A,{disabled:x})})})]})}),(0,j.jsx)(R.A,{span:8,children:(0,j.jsx)(Z,{label:a("\u5217\u6570"),labelCol:{style:{width:"50px"}},name:["groupList",p,"colNumber"],children:(0,j.jsx)(G.A,{disabled:x,style:{width:"100%"},min:1})})}),(0,j.jsx)(R.A,{span:8,children:(0,j.jsx)(Z,{label:a("\u884c\u6570"),labelCol:{style:{width:"50px"}},name:["groupList",p,"rowNumber"],children:(0,j.jsx)(G.A,{disabled:x,style:{width:"100%"},min:1})})})]})}),(0,j.jsxs)("div",{className:"group-set-box",children:[(0,j.jsx)("div",{className:"group-set-box-transfer",children:(0,j.jsx)(Z,{style:{overflow:"hidden"},label:"",name:["groupList",p,"transfer"],children:(0,j.jsx)(Y,{disabled:x,dataSource:c,transferSelectIndex:m,transferSelectIndexChange:e=>{if(h(e),e>=0){var t;const n=(null===(t=u.getFieldValue(["groupList",p,"transfer"]).find(((t,n)=>n===e)))||void 0===t?void 0:t.code)||void 0,l=c.find((e=>e.code===n))||{};l&&l.unitId&&!u.getFieldValue(["groupList",p,"transfer",e,"unit"])&&u.setFieldValue(["groupList",p,"transfer",e,"unit"],(null===l||void 0===l?void 0:l.unitId)||void 0)}}})})}),(0,j.jsxs)("div",{className:"group-set-formitem-box",children:[(0,j.jsx)(Z,{labelCol:{span:24},wrapperCol:{span:24},label:a("\u5355\u4f4d"),name:["groupList",p,"transfer",m,"unit"],children:(0,j.jsx)(K.A,{disabled:!(0===m||m>=1),dimensionId:(()=>{const e=u.getFieldValue(["groupList",p,"transfer",m]),t=c.find((t=>t.code===(null===e||void 0===e?void 0:e.code)));return null===t||void 0===t?void 0:t.dimensionId})(),size:"small"})}),(0,j.jsx)(Z,{labelCol:{span:24},wrapperCol:{span:24},label:a("\u5c0f\u6570\u4f4d\u6570"),name:["groupList",p,"transfer",m,"decimal"],children:(0,j.jsx)(L.A,{disabled:!(0===m||m>=1),showSearch:!0,optionFilterProp:"label",size:"small",options:(0,J.hg)({t:a})})})]})]})]})]})})})),ne=w.Ay.div`
    background: #fff;
    padding: 5px 10px 10px;
    .attribute-content {
        .attribute-title{
            font-size: 14px;
            margin-bottom: 8px;
        }
    }
    .heading-content{
        display: flex;
        .heading-content-group{
            width: 140px;
            margin-right: 24px;
            .heading-content-group-top{
                display: flex;
                .heading-content-group-top-name{
                    flex: 1;
                }
                .heading-content-group-top-btnbox{
                    .ant-btn:last-child{
                        margin-left: 8px;
                    }
                }
            }
            .heading-content-group-list{
                margin-top: 12px;
                max-height: 274px;
                overflow: auto;
                .heading-content-group-list-item{
                    margin-bottom: 8px;
                    padding: 2px 4px;
                    border-radius: 4px;
                    cursor: pointer;
                    &:hover,&.active{
                        background: rgba(0,0,0,.2);
                    }
                }
            }
        }
        .heading-content-set{
            flex: 1;
            overflow: hidden;
            .heading-content-set-title{
                font-size: 14px;
                margin-bottom: 8px;
            }
            .group-name-box{
                display: flex;
                .group-name-box-left{
                    width: 70px;
                    .ant-form-item-control-input-content{
                        text-align: right;
                    }
                }
                .group-name-box-right{
                    flex: 1;
                }
            }
            .group-set-box{
                margin-top: 12px;
                flex: 1;
                display: flex;
                .group-set-box-transfer{
                    flex: 1;
                    overflow: hidden;
                    display: flex;
                    .transfer-content{
                        .ant-transfer {
                            flex: 1;
                            overflow: hidden;
                        }
                    }
                    .layout-right{
                        width: 50px;
                    }
                    .ant-transfer-list{
                        overflow: hidden;
                    }
                }
                .group-set-formitem-box{
                    margin-left: 12px;
                    overflow: hidden;
                    width: 120px;
                    
                }
            }
        }
    }
    .footer-div{
        margin-top: 20px;
        text-align: right;
    }
`,le=e=>{let{open:t,setOpen:n,config:i,updateConfig:a,defaultAttrData:r}=e;const{t:d}=(0,o.Bd)(),s=(0,l.useRef)(null),u=(0,l.useRef)(null),[c,v]=(0,l.useState)((null===i||void 0===i?void 0:i.attributeData)||{}),p=()=>{n(!1)},g=[{key:"1",label:d("\u5c5e\u6027"),children:(0,j.jsx)(B,{ref:s,config:i,defaultAttrData:r,changeConfig:e=>{v(e)}})},{key:"2",label:d("\u8868\u5934"),children:(0,j.jsx)(te,{ref:u,config:i,defaultAttrData:r,attributeFormData:c})}];return(0,j.jsx)(D.A,{open:t,title:d("\u7279\u6b8a\u8868\u5934"),maskClosable:!1,width:"800px",footer:null,onCancel:p,children:(0,j.jsxs)(ne,{children:[(0,j.jsx)(N.A,{items:g}),(0,j.jsx)("div",{className:"footer-div",children:(0,j.jsxs)(k.A,{children:[(0,j.jsx)(S.Ay,{onClick:p,children:d("\u53d6\u6d88")}),(0,j.jsx)(S.Ay,{type:"primary",onClick:async()=>{var e,t;const n=await(null===s||void 0===s||null===(e=s.current)||void 0===e?void 0:e.getData())||null,l=await(null===u||void 0===u||null===(t=u.current)||void 0===t?void 0:t.getData())||null;if(1===(null===l||void 0===l?void 0:l.groupList.length)){var o;const e=null===(o=l.groupList)||void 0===o?void 0:o[0];null!==e&&void 0!==e&&e.defName||null!==e&&void 0!==e&&e.UUID||(l.groupList=[])}const r={attributeData:n||(null!==i&&void 0!==i&&i.attributeData?null===i||void 0===i?void 0:i.attributeData:null),headingData:l||(null!==i&&void 0!==i&&i.headingData?null===i||void 0===i?void 0:i.headingData:null),dragPosition:(null===i||void 0===i?void 0:i.dragPosition)||null};a(r),p()},children:d("\u786e\u8ba4\u751f\u6210")})]})})]})})},ie=w.Ay.div`
    width: 100%;
    height: 100%;
    background: #fff;
    .attribute-content {
        .attribute-title{
            font-size: 18px;
            margin-bottom: 12px;
        }
    }
`,oe=e=>{var t,n,s;let{item:c,id:v,layoutConfig:p}=e;const{t:g}=(0,o.Bd)(),m=(0,i.d4)((e=>e.template.widgetData)),{editWidget:h}=(0,a.A)(),[b,x]=(0,l.useState)(!1),y=(0,l.useMemo)((()=>(0,r.Rm)(m,"widget_id",null===c||void 0===c?void 0:c.widget_id)),[c,m]),C=null!==(t=null===y||void 0===y?void 0:y.data_source)&&void 0!==t?t:{},w=e=>{h({...y,data_source:e})},{targetRef:A}=(0,u.A)({controlCompId:v,dataSourceType:u.d.daqbuffer,dataSourceCode:null===C||void 0===C||null===(n=C.attributeData)||void 0===n?void 0:n.buffer,dataCodes:(0,l.useMemo)((()=>{var e,t;const n=new Set;return null===C||void 0===C||null===(e=C.headingData)||void 0===e||null===(t=e.groupList)||void 0===t||t.forEach((e=>{var t;null===e||void 0===e||null===(t=e.transfer)||void 0===t||t.forEach((e=>{n.add(e.code)}))})),Array.from(n)}),[C]),timer:null!==(s=null===C||void 0===C?void 0:C.attributeData.updateFreq)&&void 0!==s?s:200,number:1,testStatus:1}),[N,k]=(0,l.useState)(!1);return(0,j.jsxs)(ie,{ref:A,children:[(0,j.jsx)(I,{id:v,config:C,dragOpen:N,updateDragPosition:e=>{const t={...C,dragPosition:e};w(t)}}),b&&(0,j.jsx)(le,{open:b,setOpen:x,config:C,updateConfig:w,defaultAttrData:f}),(0,j.jsxs)(d.A,{domId:v,layoutConfig:p,handelEditClick:!0,children:[(0,j.jsx)("div",{className:"unique-content",onClick:function(){k(!N)},children:g(N?"\u7ed3\u675f\u5e03\u5c40\u8c03\u6574":"\u5e03\u5c40\u8c03\u6574")}),(0,j.jsx)("div",{className:"unique-content",onClick:()=>x(!0),children:g("\u7f16\u8f91\u7279\u6b8a\u8868\u5934")})]})]})}},63804:(e,t,n)=>{n.d(t,{d:()=>s,A:()=>u});var l=n(65043),i=n(67208),o=n(36950),a=n(19853),r=n.n(a);function d(){let{threshold:e=0,rootMargin:t="0px",enableIntersectionObserver:n=!0,enablePageVisibility:i=!0,enableMutationObserver:o=!0,onVisibilityChange:a=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=(0,l.useRef)(null),d=(0,l.useRef)({isIntersecting:!1,isPageVisible:!0,displayStyle:"block",position:{top:0,left:0}}),s=(0,l.useRef)(!1),u=(0,l.useCallback)((()=>{const e=d.current,t=e.isIntersecting&&e.isPageVisible&&"none"!==e.displayStyle;t!==s.current&&(s.current=t,a&&a(t))}),[a]);return(0,l.useEffect)((()=>{if(!r.current)return()=>{};const l=[];if(n){const n=new IntersectionObserver((e=>{e.forEach((e=>{const t=d.current.isIntersecting,n=e.isIntersecting;t!==n&&(d.current={...d.current,isIntersecting:n},u(),n?console.log("\ud83d\udd0d \u5143\u7d20\u8fdb\u5165\u89c6\u53e3"):console.log("\ud83d\udc7b \u5143\u7d20\u79bb\u5f00\u89c6\u53e3"))}))}),{threshold:e,rootMargin:t});n.observe(r.current),l.push((()=>n.disconnect()))}if(i){const e=()=>{const e=!document.hidden;d.current={...d.current,isPageVisible:e},u(),e?console.log("\ud83d\udc41\ufe0f \u9875\u9762\u53d8\u4e3a\u53ef\u89c1"):console.log("\ud83d\ude48 \u9875\u9762\u53d8\u4e3a\u4e0d\u53ef\u89c1")};document.addEventListener("visibilitychange",e),l.push((()=>document.removeEventListener("visibilitychange",e)))}if(o){const e=new MutationObserver((e=>{e.forEach((e=>{if("attributes"===e.type&&"style"===e.attributeName){const e=window.getComputedStyle(r.current).display;d.current={...d.current,displayStyle:e},u(),"none"===e?console.log("\ud83d\udeab \u5143\u7d20\u88ab\u9690\u85cf (display: none)"):console.log("\u2705 \u5143\u7d20\u663e\u793a\u6837\u5f0f\u6062\u590d")}}))}));e.observe(r.current,{attributes:!0,attributeFilter:["style"]}),l.push((()=>e.disconnect()))}return u(),()=>{l.forEach((e=>e()))}}),[e,t,n,i,o,u]),{targetRef:r}}const s={daqbuffer:"daqbuffer","\u4e8c\u7ef4\u6570\u7ec4":"doubleArray","\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408":"doubleArraySet"},u=(e,t)=>{let{controlCompId:n,dataSourceType:a,dataSourceCode:s,dataCodes:u,timer:c=-1,number:v=-1,testStatus:p=1,daqCurveSelectedSampleCodes:g}=e;const m=(0,l.useRef)(!1),h=(0,l.useRef)(!1),b=(0,l.useRef)(null),x=(0,l.useRef)(!1),f=(0,l.useRef)(!1),y=(0,l.useRef)();(0,l.useRef)(t).current=t,(0,l.useEffect)((()=>{if(!s||!n||!a||!u||0===u.length)return;const e={templateName:(0,o.n1)(),controlCompId:n,dataSourceType:a,dataSourceCode:s,dataCodes:u,timer:c,number:v,testStatus:p,daqCurveSelectedSampleCodes:null!==g&&void 0!==g?g:[]};r()(e,y.current)||(null===t||void 0===t||t(),y.current=e,x.current?m.current?(0,i.pj8)({...y.current}):(0,i.i_N)({...y.current}).then((()=>{m.current=!0,h.current=!0})):m.current&&(f.current=!0))}),[n,a,s,u,c,v,p,g]);const{targetRef:j}=d({onVisibilityChange:(0,l.useCallback)((async e=>{var t,n,l;if(x.current=e,e&&y.current){if(!m.current)return await(0,i.i_N)({...y.current}),m.current=!0,void(h.current=!0);if(f.current)return(0,i.pj8)({...y.current}),void(f.current=!1)}(b.current&&clearTimeout(b.current),e&&!h.current&&null!==(t=y.current)&&void 0!==t&&t.controlCompId)&&(await(0,i.pkF)(null===(l=y.current)||void 0===l?void 0:l.controlCompId),h.current=!0);!e&&h.current&&null!==(n=y.current)&&void 0!==n&&n.controlCompId&&(b.current=setTimeout((async()=>{await(0,i.UVQ)(y.current.controlCompId),h.current=!1}),3e3))}),[])});return(0,l.useEffect)((()=>()=>{b.current&&clearTimeout(b.current),m.current&&(0,i.UWZ)(y.current.controlCompId)}),[]),{targetRef:j}}},84617:(e,t,n)=>{n.d(t,{A:()=>s});var l=n(65043),i=n(60383),o=n(80077),a=n(44409),r=n(36950),d=n(91465);const s=e=>{let{controlCompId:t,onMessage:n}=e;const s=(0,o.wA)(),{useSubscriber:u}=(0,a.A)(),c=(0,l.useRef)(),v=(0,l.useRef)(n),p=(0,l.useRef)();(0,l.useEffect)((()=>{v.current=n}),[n]),(0,l.useEffect)((()=>(g(),()=>{var e,t;null===(e=c.current)||void 0===e||null===(t=e.close)||void 0===t||t.call(e)})),[t]);const g=async()=>{const e=`${(0,r.n1)()}-ControlCompUIData-${t}-UIData`;c.current=await u(e);for await(const[t,o]of c.current){let e;try{e=i.D(o)}catch(n){try{e=JSON.parse(o)}catch(l){console.error("GridLayout\u6570\u636e\u89e3\u6790\u5931\u8d25",l)}}2===e.mode?p.current=s((0,d.J_)("\u5927\u6570\u636e\u91cf\u52a0\u8f7d\u4e2d...")):3===e.mode?s((0,d.ge)(p.current)):v.current(e)}}}},94293:(e,t,n)=>{n.d(t,{Ao:()=>d,Tr:()=>l,_0:()=>s,aN:()=>a,eg:()=>o,hg:()=>r,sr:()=>i});const l=[{value:8,label:"8"},{value:9,label:"9"},{value:10,label:"10"},{value:11,label:"11"},{value:12,label:"12"},{value:13,label:"13"},{value:14,label:"14"},{value:15,label:"15"},{value:16,label:"16"},{value:17,label:"17"},{value:18,label:"18"},{value:19,label:"19"},{value:20,label:"20"},{value:21,label:"21"},{value:22,label:"22"},{value:23,label:"23"}],i={BETWEEN:"between",ROWS:"rows",ROW:"row",CENTER:"center"},o=e=>{let{t:t}=e;return[{value:i.BETWEEN,label:t("\u9ed8\u8ba4")},{value:i.ROWS,label:t("\u5de6\u5bf9\u9f50,\u6570\u503c\u548c\u5355\u4f4d\u4e24\u884c\u663e\u793a")},{value:i.ROW,label:t("\u5de6\u5bf9\u9f50,\u6570\u503c\u548c\u5355\u4f4d\u5355\u884c\u663e\u793a")},{value:i.CENTER,label:t("\u5c45\u4e2d\u5bf9\u9f50")}]},a={TEST:"test",NOT_TEST:"not_test"},r=e=>{let{t:t}=e;return[{value:0,label:t("\u65e0\u5c0f\u6570")},{value:1,label:t("\u5c0f\u6570\u70b9\u540e\u4e00\u4f4d")},{value:2,label:t("\u5c0f\u6570\u70b9\u540e\u4e8c\u4f4d")},{value:3,label:t("\u5c0f\u6570\u70b9\u540e\u4e09\u4f4d")},{value:4,label:t("\u5c0f\u6570\u70b9\u540e\u56db\u4f4d")},{value:5,label:t("\u5c0f\u6570\u70b9\u540e\u4e94\u4f4d")},{value:6,label:t("\u5c0f\u6570\u70b9\u540e\u516d\u4f4d")},{value:7,label:t("\u5c0f\u6570\u70b9\u540e\u4e03\u4f4d")},{value:8,label:t("\u5c0f\u6570\u70b9\u540e\u516b\u4f4d")},{value:9,label:t("\u5c0f\u6570\u70b9\u540e\u4e5d\u4f4d")}]},d={ADD:"right",REMOVE:"left"},s={name:"xxxsds",code:"input_xxsd",variable_type:"Buffer",default_val:{value:[],isConstant:0,groups:[]},is_enable:0,is_feature:0,is_overall:1,is_fx:0,number_tab:{format:{numberRequire:"any",formatType:"auto",afterPoint:null,beforePoint:null,significantDigits:0,amendmentInterval:0,pointPosition:0,roundMode:0,threshold1:1,threshold2:1,roundType1:1,roundType2:1,roundType3:1},channel:{channelType:"\u65e0",channel:"\u65e0",isUserConversion:!1,lockChannels:[]},multipleMeasurements:{measurementCounts:1,measurementType:"min"},unit:{unitType:"\u65e0",unit:"\u65e0",isUserConversion:!1,lockChannels:[]}},reasonable_val_tab:{reasonableType:"empty",values:[0,0],defaultVal:10,minParam:10,MaxParam:10,isToResultList:!1},button_variable_tab:{isEnable:!1,content:"",position:"left",actionId:"",function:"f(x)",pic:"",buttonType:"action",script:""},button_tab:{isEnable:!1,content:"",position:"left",actionId:"",function:"f(x)",source:"action_lib",pic:"",type:"action",method:"post"},program_tab:{numericFormat:"",unit:"",isVisible:"",isDisabled:"",mode:"",isCheck:""},text_tab:{content:"",format:"single",canUseText:!1},select_tab:{selection:"list_single",group_id:"",items:[],format:"",comment:""},two_digit_array_tab:{rowCounts:1,columnCount:1,rowHeaderPlace:!0,columnHeaderPlace:!0,isRowType:!1,rowDefinition:[{title:"\u884c1",type:"text",options:[]}],columnDefinition:[{title:"\u52171",type:"text",options:[]}],columnData:[[""]]},custom_array_tab:{useType:"followComp"},control_tab:{type:"not_custom",dialog_type:"variable",control_name:"",code:"",default_name:"",related_variables:[],title:"",variables:[],signals:[],is_daq:!1},buffer_tab:{buffer_type:"ArrayQueue",size:0,size_expand:"expand",signals:[]},label_tab:{format:"",content:"",fontSize:12,fore:""},picture_tab:{src:"",showName:!1,path:"",name:""},related_var_tab:{vars:[]}}}}]);
//# sourceMappingURL=4839.6ecebfbc.chunk.js.map