"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4917],{10712:(e,l,o)=>{o.d(l,{A:()=>I});var n=o(65043),t=o(25055),a=o(83720),i=o(97914),d=o(95206),r=o(32513),s=o(19372),c=o(36497),u=o(60446),v=o.n(u),p=o(80077),h=o(38134),m=o(36950),f=o(70579);const{Item:x,useWatch:b}=t.A,g=e=>{let{text:l}=e;return(0,f.jsx)(f.Fragment,{children:(0,f.jsx)("span",{style:{width:"100%",overflow:"hidden",textOverflow:"ellipsis"},title:l,children:l})})},j=e=>{let{id:l,value:o,onChange:t,isEdit:i,disabled:d}=e;const[r,s]=(0,n.useState)();(0,n.useEffect)((()=>{s(o)}),[o]);const c=()=>{r!==o&&t(r)};return i?(0,f.jsx)(a.A,{disabled:d,value:r,onChange:e=>s(e.target.value),onBlur:c}):(0,f.jsx)(f.Fragment,{children:(0,f.jsx)(g,{text:o})})},y=e=>{let{id:l,value:o,onChange:t,isEdit:a,typeParam:{dimensionId:d,unitId:r},disabled:s}=e;const c=(0,p.d4)((e=>e.global.unitList)),[u,v]=(0,n.useState)();(0,n.useEffect)((()=>{v(d&&r&&void 0!==o&&null!==o?(0,m.tJ)(o,d,r):o)}),[o,d,r]);const h=e=>{v(e)},x=()=>{if(u!==o)try{if(d&&r){var e;const l=null===(e=c.find((e=>e.id===d)))||void 0===e?void 0:e.default_unit_id;t(l?(0,m.tJ)(u,d,l,r):u)}else t(u)}catch(l){console.log("err",l)}};return a?(0,f.jsx)(i.A,{disabled:s,style:{width:"100%"},value:u,onChange:h,onBlur:x}):(0,f.jsx)(f.Fragment,{children:(0,f.jsx)(g,{text:u})})},w=e=>{let{id:l,value:o,onChange:n,isEdit:t,disabled:a}=e;const i="object"===typeof o?null===o||void 0===o?void 0:o.value:o;return(0,f.jsx)(d.Ay,{disabled:!t||a,onClick:()=>{n({value:i})},title:i,children:i})},C=e=>{let{id:l,value:o,onChange:n,isEdit:t,disabled:a}=e;return(0,f.jsx)(r.A,{disabled:!t||a,checked:o,onChange:e=>n(e.target.checked)})},A=e=>{let{id:l,value:o,onChange:n,isEdit:t,disabled:a}=e;if(!t){const e=o?v()(o).format("YYYY-MM-DD HH:mm:ss"):o;return(0,f.jsx)(g,{text:e})}return(0,f.jsx)(s.A,{showTime:!0,disabled:a,value:o?v()(o):o,onChange:e=>{n(e?null===e||void 0===e?void 0:e.valueOf():e)}})},S=e=>{let{id:l,value:o,onChange:n,isEdit:t,typeParam:a,disabled:i}=e;if(!t){var d;const e=(null===a||void 0===a||null===(d=a.options.find((e=>e.value===o)))||void 0===d?void 0:d.label)||o;return(0,f.jsx)(g,{text:e})}return(0,f.jsx)(c.A,{style:{width:"100%"},value:o,onChange:n,options:null===a||void 0===a?void 0:a.options,disabled:i})},k=(0,n.memo)((e=>{let{isEdit:l,typeParam:o,type:t,index:a,code:i,disabled:d}=e;const r=(0,n.useMemo)((()=>{const e={isEdit:l,typeParam:o,disabled:d};switch(t){case h.J$.\u6587\u672c:return(0,f.jsx)(j,{...e});case h.J$.\u6570\u5b57:return(0,f.jsx)(y,{...e});case h.J$.\u6309\u94ae:return(0,f.jsx)(w,{...e});case h.J$.\u52fe\u9009:return(0,f.jsx)(C,{...e});case h.J$.\u65e5\u671f:return(0,f.jsx)(A,{...e});case h.J$.\u9009\u62e9:return(0,f.jsx)(S,{...e});default:return(0,f.jsx)(f.Fragment,{})}}),[l,o,t,d]);return(0,f.jsx)(x,{name:["value",a,i,"value"],noStyle:!0,children:r})})),N=e=>{let{isEdit:l,typeParam:o,type:n,index:t,code:a}=e;const i=t>=b("dataSourceLength");return(0,f.jsx)(k,{isEdit:l,typeParam:o,type:n,index:t,code:a,disabled:i})},I=e=>{let{onMouseEnter:l,onMouseLeave:o,children:n,rowHeight:t,index:a,code:i,type:d,typeParam:r,isEdit:s,...c}=e;return(0,f.jsx)("td",{...c,style:{...null===c||void 0===c?void 0:c.style,height:t,paddingTop:0,paddingBottom:0},children:(0,f.jsxs)("div",{style:{display:"flex",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[(0,f.jsx)("div",{children:"\xa0"}),(0,f.jsx)(N,{isEdit:s,typeParam:r,type:d,index:a,code:i})]})})}},11375:(e,l,o)=>{o.d(l,{A:()=>g});var n=o(65043),t=o(25055),a=o(83463),i=(o(36950),o(74837)),d=o(74117),r=o(79806),s=o(34677),c=o.n(s);const u=o(81143).Ay.div`
    width: 100%;
    height: 100%;

    .ant-table-wrapper, .ant-spin-nested-loading, .ant-spin-container, .ant-table, .ant-table-container{
        height: 100%;
    }

    .ant-table-body{
        height: ${e=>{let{scrollY:l}=e;return l}}px;
        background-color: ${e=>{let{tableBackgroundColor:l}=e;return l||"#ffffff"}};
        background: ${e=>{let{tableBackgroundColor:l}=e;return l||"#ffffff"}};
        .emptyBox{
            background-color: ${e=>{let{tableBackgroundColor:l}=e;return l||"#ffffff"}};
            height: ${e=>{let{scrollY:l}=e;return l-33}}px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .ant-table-wrapper .ant-table{
        background-color: transparent;
    }
    .ant-table-wrapper .ant-table-expanded-row-fixed{
        background-color: ${e=>{let{tableBackgroundColor:l}=e;return l||"#ffffff"}};
    }
    // 列标题单元格的有边线内的before
    .ant-table-wrapper .ant-table-thead >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
        width: 0;
    }
    // 滚动条上面的白线
    .ant-table-wrapper .ant-table-cell-scrollbar:not([rowspan]){
        box-shadow: none !important;
    }

    .ant-table-header{
        table{
            colgroup{
                col:nth-last-child(1){
                    ${e=>{let{scrollbar:l}=e;return`\n                        width: ${null===l||void 0===l?void 0:l.width}px!important; // \u6ca1\u6709\u6eda\u52a8\u6761\u7684\u65f6\u5019\u662f0 \n                    `}}
                }
            }   
        }
    }
    

`;var v=o(70579);const p=e=>{var l,o;let{scroll:t,locale:a,tableBackgroundColor:i,scrollbar:s={width:8,height:8},maxY:p,dataSource:h,...m}=e;const{t:f}=(0,d.Bd)(),x=(0,n.useRef)(),[b,g]=(0,n.useState)(0),[j,y]=(0,n.useState)(0);(0,n.useEffect)((()=>{let e;const l=new ResizeObserver((l=>{let[{contentRect:o}]=l;if(!o)return;const{height:n}=o;1!==Math.abs(e-n)&&(e=n,g(Math.floor(n)))}));return x.current&&(null===l||void 0===l||l.observe(x.current)),()=>{x.current&&(null===l||void 0===l||l.unobserve(x.current))}}),[]),(0,n.useEffect)((()=>{var e;const l=new ResizeObserver((e=>{let[{contentRect:l}]=e;if(!l)return;const{height:o}=l;y(Math.floor(o))})),o=null===(e=x.current)||void 0===e?void 0:e.querySelector(".ant-table-thead");return o&&(null===l||void 0===l||l.observe(o)),()=>{o&&(null===l||void 0===l||l.unobserve(o))}}),[]);const w=(0,n.useMemo)((()=>b-j),[b,j]);return(0,v.jsx)(u,{ref:x,scrollY:null!==(l=null===t||void 0===t?void 0:t.y)&&void 0!==l?l:w,tableBackgroundColor:i,scrollbar:s,children:(0,v.jsx)(r.A,{scroll:c()({y:w},t),rowHoverable:!1,dataSource:h,...m,locale:{emptyText:(0,v.jsx)("div",{className:"emptyBox",children:f(null!==(o=null===a||void 0===a?void 0:a.emptyText)&&void 0!==o?o:"\u6682\u65e0\u6570\u636e")})}})})};var h=o(71108),m=o(10712);const f=e=>{let{config:l,colsConfig:o,tableData:t,dataShowType:a,showRowNumber:i,isPdf:d}=e;const r=(null===l||void 0===l?void 0:l.topFixedNumber)||void 0,s=(0,n.useRef)(null),[c,u]=(0,n.useState)([]),[f,x]=(0,n.useState)();(0,n.useEffect)((()=>{o&&u([...o])}),[o]),(0,n.useEffect)((()=>{b()}),[c]);const b=()=>{const e=c.filter((e=>{let{show:l=!0}=e;return l})).map(((e,l)=>{let{code:o,colTitle:n,type:t,typeParam:a,isEdit:i}=e;return{title:(0,v.jsx)(h.A,{text:n,type:t,typeParam:a,onChangeTypeParam:e=>((e,l)=>{const o=c.map((o=>({...o,typeParam:o.code===e?l:o.typeParam})));u(o)})(o,e)}),dataIndex:o,key:o,width:100,ellipsis:!0,onCell:e=>({type:t,typeParam:a,isEdit:i,index:null===e||void 0===e?void 0:e.index,disabled:null===e||void 0===e?void 0:e.disabled,code:o})}}));x(e)};return(0,v.jsx)(p,{ref:s,bordered:!0,columns:f,dataSource:t,tableLayout:"fixed",components:{body:{cell:(0,n.useCallback)((e=>(0,v.jsx)(m.A,{rowHeight:57,...e})),[]),row:(0,n.useCallback)((e=>(0,v.jsx)("tr",{...e})),[a,r])}},scroll:{x:"max-content",...d?{y:57*i}:{}},pagination:!1})},x=(0,n.memo)(f),b=e=>{let{showData:l,total:o,upDataRange:d,config:r,colsConfig:s,onInput:c,isPdf:u,isEdit:p}=e;const[h]=t.A.useForm(),[m,f]=(0,n.useState)(1);(0,n.useEffect)((()=>{f(1)}),[r]),(0,n.useEffect)((()=>{h.setFieldValue("dataSourceLength",o),0===o&&f(1)}),[o]),(0,n.useEffect)((()=>{(null===r||void 0===r?void 0:r.dataShowType)===i.I.\u9ed8\u8ba4\u884c\u6570\u663e\u793a&&f(1)}),[null===r||void 0===r?void 0:r.dataShowType]),(0,n.useEffect)((()=>{(null===r||void 0===r?void 0:r.dataShowType)===i.I.\u9875\u7801\u663e\u793a&&null!==r&&void 0!==r&&r.isReverse&&f((e=>1===e?1:e+1))}),[Math.floor(o/(null===r||void 0===r?void 0:r.showRowNumber)),null===r||void 0===r?void 0:r.dataShowType,null===r||void 0===r?void 0:r.isReverse]),(0,n.useEffect)((()=>{p?h.setFieldsValue({value:l.slice((m-1)*(null===r||void 0===r?void 0:r.showRowNumber),m*(null===r||void 0===r?void 0:r.showRowNumber))}):h.setFieldsValue({value:l})}),[l,p,m]);const b=(0,n.useMemo)((()=>{try{const e=new Array(null===r||void 0===r?void 0:r.showRowNumber).fill(0),l=[];return e.forEach(((e,o)=>{l.push({index:o,key:undefined})})),l}catch(e){return console.log("err",e),[]}}),[null===r||void 0===r?void 0:r.showRowNumber]);return(0,v.jsxs)(t.A,{form:h,onValuesChange:(e,l)=>{try{var n;const l=e.value.length-1,t=null===(n=Object.keys(e.value[l]))||void 0===n?void 0:n[0],{value:a}=e.value[l][t],i="object"===typeof a?a.value:a,d=(m-1)*(null===r||void 0===r?void 0:r.showRowNumber)+l,s=null!==r&&void 0!==r&&r.isReverse?o-d:d;c({rowIndex:s,columnCode:t,value:i})}catch(t){console.log("err",t)}},style:{width:"100%",height:"100%",overflow:"hidden",display:"flex",flexDirection:"column"},children:[(0,v.jsx)(t.A.Item,{name:["dataSourceLength"],hidden:!0}),(0,v.jsx)("div",{style:{flex:"1",overflow:"hidden"},children:(0,v.jsx)(x,{config:r,colsConfig:s,tableData:b,dataShowType:null===r||void 0===r?void 0:r.dataShowType,showRowNumber:null===r||void 0===r?void 0:r.showRowNumber,isPdf:u})}),(null===r||void 0===r?void 0:r.dataShowType)===i.I.\u9875\u7801\u663e\u793a?(0,v.jsx)(a.A,{style:{marginTop:12},align:"end",showSizeChanger:!1,total:o,current:m,pageSize:null===r||void 0===r?void 0:r.showRowNumber,onChange:(e,l)=>{f(e),d&&(null!==r&&void 0!==r&&r.isReverse?d({startIndex:o-e*l,endIndex:o-(e-1)*l}):d({startIndex:(e-1)*l,endIndex:e*l}))}}):null]})},g=n.memo(b)},13830:(e,l,o)=>{o.d(l,{A:()=>x,p:()=>p.ps});var n=o(65043),t=o(16569),a=o(6051),i=o(95206),d=o(81143),r=o(80077),s=o(74117),c=o(88359),u=o(51554),v=o(78178),p=o(56543),h=o(754),m=o(70579);const f=d.Ay.div`
    .bind-input-variable{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;

        .label{
            margin-right: 10px;
        }
        .bind-value-span{
            word-break: break-all;
        }
        .bind-fun-div{
            white-space: nowrap;
        }
    }
`,x=e=>{let{id:l,value:o,onChange:d,inputVariableType:x,checkFn:b,isSetProgrammableParameters:g=!1}=e;const j=(0,r.wA)(),{t:y}=(0,s.Bd)(),w=(0,n.useRef)(),[C,A]=(0,n.useState)(!1),[S,k]=(0,n.useState)(),[N,I]=(0,n.useState)("add");(0,n.useEffect)((()=>{o&&E(o)}),[o]);const E=e=>{if((null===e||void 0===e?void 0:e.variable_type)!==x)return void d();(0,h.B)("inputVariable","inputVariableMap").has(e.code)||d()},R=e=>{const l=b&&b(e);if(l)return void t.Ay.error(l);const{id:o,code:n,variable_name:a,variable_type:i,name:r}=e;d({id:o,code:n,variable_name:null!==a&&void 0!==a?a:r,variable_type:i,restrict:{variableType:p.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x}})};return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(f,{children:(0,m.jsxs)("div",{className:"bind-input-variable",children:[(0,m.jsxs)("div",{className:"bind-value-span",children:[y("\u7ed1\u5b9a\u53d8\u91cf"),":",null===o||void 0===o?void 0:o.variable_name]}),(0,m.jsx)("div",{className:"bind-fun-div",children:(0,m.jsxs)(a.A,{children:[(0,m.jsx)(i.Ay,{onClick:()=>{w.current.open({variableType:p.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x})},children:"\u9009\u62e9"}),o?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(i.Ay,{onClick:()=>{k(null===o||void 0===o?void 0:o.id),I("edit"),A(!0)},children:y("\u7f16\u8f91")}),(0,m.jsx)(i.Ay,{onClick:()=>d(),children:y("\u89e3\u7ed1")})]}):(0,m.jsx)(i.Ay,{onClick:()=>{I("add"),A(!0)},children:y("\u65b0\u5efa")})]})})]})}),(0,m.jsx)(u.A,{ref:w,isSetProgrammableParameters:g,handleSelectedVariable:R}),C&&(0,m.jsx)(v.A,{isSetProgrammableParameters:g,variableType:x,modalIndex:0,editId:S,mode:N,open:C,onOk:async e=>{const l=await j((0,c.w)()),o=null===l||void 0===l?void 0:l.find((l=>l.code===e.code));o&&R(o),A(!1)},onCancel:()=>{A(!1)}})]})}},51554:(e,l,o)=>{o.d(l,{A:()=>b});var n=o(65043),t=o(80077),a=o(16569),i=o(83720),d=o(79806),r=o(74117),s=o(93950),c=o.n(s),u=o(56543),v=o(75440),p=o(29977),h=o(6051),m=o(70579);const f=e=>{let{handleSelected:l,t:o}=e;return[{title:o?o("\u540d\u79f0"):"\u540d\u79f0",dataIndex:"variable_name",key:"variable_name"},{title:o?o("\u6807\u8bc6\u7b26"):"\u6807\u8bc6\u7b26",dataIndex:"code",key:"code"},{title:o?o("\u64cd\u4f5c"):"\u64cd\u4f5c",dataIndex:"code",key:"code",render:(e,o)=>(0,m.jsx)(h.A,{size:"middle",children:(0,m.jsx)("a",{onClick:()=>l(o),children:"\u9009\u62e9"})})}]},x=(e,l)=>{let{handleSelectedVariable:o=e=>console.log(e),isSetProgrammableParameters:s=!1}=e;const h=(0,p.A)(),x=(0,t.d4)((e=>e.template.resultData)),[b,g]=(0,n.useState)(!1),[j,y]=(0,n.useState)(),[w,C]=(0,n.useState)([]),[A,S]=(0,n.useState)([]),{t:k}=(0,r.Bd)(),N=(0,n.useMemo)((()=>h.map((e=>({...e,variable_name:null===e||void 0===e?void 0:e.name})))),[h]),I=(0,n.useMemo)((()=>x.map((e=>({...e,id:e.code})))),[x]);(0,n.useEffect)((()=>{b&&E()}),[b]);const E=()=>{if(j)switch(null===j||void 0===j?void 0:j.variableType){case u.oY.\u8f93\u5165\u53d8\u91cf:{const e=[...N.filter((e=>!(null!==j&&void 0!==j&&j.inputVarType)||e.variable_type===(null===j||void 0===j?void 0:j.inputVarType)))];S(e),C(e);break}case u.oY.\u4fe1\u53f7\u53d8\u91cf:case u.oY.\u7ed3\u679c\u53d8\u91cf:S(I),C(I);break;default:console.log("\u672a\u5904\u7406\u7684\u53d8\u91cf\u7c7b\u578b",null===j||void 0===j?void 0:j.variableType)}};(0,n.useImperativeHandle)(l,(()=>({open:e=>{y(e),g(!0)}})));const R=c()((async e=>{if(e){const l=w.filter((l=>{const o=l.variable_name.toLowerCase(),n=l.code.toLowerCase(),t=e.toLowerCase();return o.includes(t)||n.includes(t)}));S(l)}else S(w)}),200);return(0,m.jsxs)(v.A,{open:b,onCancel:()=>{g(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:[(0,m.jsx)(i.A,{allowClear:!0,onChange:e=>R(e.target.value),placeholder:k("\u540d\u79f0/\u5185\u90e8\u540d"),style:{width:"300px",marginBottom:"10px"}}),(0,m.jsx)(d.A,{rowKey:"code",columns:f({handleSelected:e=>{var l;!s||"Array"===(null===e||void 0===e?void 0:e.variable_type)&&"programmableParameters"===(null===e||void 0===e||null===(l=e.custom_array_tab)||void 0===l?void 0:l.useType)?(o(e,j),g(!1)):a.Ay.error("\u8bf7\u9009\u62e9\u81ea\u5b9a\u4e49\u6570\u7ec4\u7528\u9014\u4e3a\u7a0b\u63a7\u53c2\u6570\u7684\u53d8\u91cf")}}),dataSource:A})]})},b=(0,n.forwardRef)(x)},57458:(e,l,o)=>{o.d(l,{A:()=>d});o(65043);var n=o(36497),t=o(80077),a=o(74117),i=o(70579);const d=e=>{const l=(0,t.d4)((e=>e.template.actionList)),{t:o}=(0,a.Bd)();return(0,i.jsx)(n.A,{fieldNames:{label:"action_name",value:"action_id"},options:null===l||void 0===l?void 0:l.map((e=>({...e,label:o(e.label)}))),...e})}},66103:(e,l,o)=>{o.d(l,{A:()=>J});var n=o(65043),t=o(74117),a=o(25055),i=o(47419),d=o(11645),r=o(36497),s=o(97914),c=o(12624),u=o(32513),v=o(81143),p=o(19853),h=o.n(p),m=o(75440),f=o(67998),x=o(57458),b=o(13830),g=o(16271),j=o(56543),y=o(74837);const w=v.Ay.div`
    width: 100%;
    height: 110px;
    overflow: hidden;
`;var C=o(11375),A=o(70579);const{useWatch:S}=a.A,k=()=>{const e=S("colsConfig"),l=S("dataShowType"),o=(0,n.useMemo)((()=>e?[{index:0}]:[]),[e]);return(0,A.jsx)(w,{children:(0,A.jsx)(C.A,{colsConfig:e,dataSource:o,dataShowType:l,showRowNumber:1})})};var N=o(95206),I=o(83720),E=o(46959),R=o(69312),P=o(16569);const T=v.Ay.div`
    .col-list-header{
        display: flex;
        justify-content: end;
        gap: 8px;

        &>div{
            cursor: pointer;
        }
    }

    .col-list-container{
        width: 100%;
        height: 200px;
        border: 1px solid #ccc;
        overflow-y: scroll;
        padding: 3px;

        .col-list-wrapper{

            .col-list-item{
                cursor: pointer;

                display: flex;
                align-items: center;
                gap: 4px;
            }

            .col-list-item-selected{
                background: #ccc;
            }

        }
    }
`,_=e=>{let{id:l,value:o=[],onChange:n,optIndex:a,setOptIndex:i}=e;const{t:d}=(0,t.Bd)(),r=e=>"col-list-item "+(a===e?"col-list-item-selected":"");return(0,A.jsxs)(T,{children:[(0,A.jsxs)("div",{className:"col-list-header",children:[(0,A.jsx)("div",{onClick:()=>{if(null===a)return;const e=[...o];a<=0||a>=e.length?P.Ay.error(d("\u5df2\u7ecf\u662f\u7b2c\u4e00\u4e2a")):([e[a-1],e[a]]=[e[a],e[a-1]],i(a-1),n(e))},children:(0,A.jsx)(E.A,{})}),(0,A.jsx)("div",{children:(0,A.jsx)(R.A,{onClick:()=>{if(null===a)return;const e=[...o];a<0||a>=e.length-1?P.Ay.error(d("\u5df2\u7ecf\u662f\u6700\u540e\u4e00\u4e2a")):([e[a+1],e[a]]=[e[a],e[a+1]],i(a+1),n(e))}})})]}),(0,A.jsx)("div",{className:"col-list-container",children:(0,A.jsx)("div",{className:"col-list-wrapper",children:null===o||void 0===o?void 0:o.map(((e,l)=>{let{showName:t,show:a=!0}=e;return(0,A.jsxs)("div",{className:r(l),onClick:()=>i(l),children:[(0,A.jsx)(u.A,{checked:a,onChange:e=>((e,l)=>{n(o.map(((o,n)=>({...o,show:l===n?e:o.show}))))})(e.target.checked,l)}),t]},l)}))})})]})},{Item:F,useForm:D}=a.A,B=e=>{let{open:l,setOpen:o,colsConfig:r,onOk:s}=e;const{t:c}=(0,t.Bd)(),[v]=D(),[p,h]=(0,n.useState)(null);(0,n.useEffect)((()=>{v.setFieldsValue({value:r})}),[r]);return(0,A.jsx)(m.A,{open:l,title:c("\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c"),maskClosable:!1,width:"50vw",onOk:async()=>{try{const{value:e}=await v.validateFields();s(e)}catch(e){console.log("err",e)}},onCancel:()=>{o(!1)},children:(0,A.jsx)(a.A,{form:v,labelCol:{span:8},wrapperCol:{span:16},children:(0,A.jsxs)(i.A,{children:[(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)(F,{label:"",name:"value",children:(0,A.jsx)(_,{optIndex:p,setOptIndex:h})})}),(0,A.jsxs)(d.A,{span:12,children:[(0,A.jsx)(i.A,{children:(0,A.jsx)(d.A,{span:24,children:(0,A.jsx)(F,{label:"\u663e\u793a\u540d\u79f0",name:["value",p,"showName"],children:(0,A.jsx)(I.A,{disabled:!0})})})}),(0,A.jsx)(i.A,{children:(0,A.jsx)(d.A,{span:24,children:(0,A.jsx)(F,{label:"\u5185\u90e8\u540d",name:["value",p,"code"],children:(0,A.jsx)(I.A,{disabled:!0})})})}),(0,A.jsx)(i.A,{children:(0,A.jsx)(d.A,{span:24,children:(0,A.jsx)(F,{label:"\u8868\u5934\u540d\u79f0",name:["value",p,"colTitle"],children:(0,A.jsx)(I.A,{disabled:null===p})})})}),(0,A.jsx)(i.A,{children:(0,A.jsx)(d.A,{span:20,offset:4,children:(0,A.jsx)(F,{label:"",name:["value",p,"isEdit"],valuePropName:"checked",children:(0,A.jsx)(u.A,{disabled:null===p,children:"\u53ef\u7f16\u8f91\u8be5\u5217"})})})}),(0,A.jsx)(i.A,{children:(0,A.jsx)(d.A,{span:20,offset:4,children:(0,A.jsx)(F,{label:"",name:["value",p,"isTriggerAction"],valuePropName:"checked",children:(0,A.jsx)(u.A,{disabled:null===p,children:"\u89e6\u53d1\u52a8\u4f5c"})})})})]})]})})})},M=v.Ay.div`
    display: flex;
    justify-content: end;
`,O=e=>{let{id:l,value:o=[],onChange:t}=e;const[a,i]=(0,n.useState)(!1);return(0,A.jsxs)(M,{children:[(0,A.jsx)(N.Ay,{onClick:()=>i(!0),children:"\u8bbe\u7f6e\u8868\u683c"}),(0,A.jsx)(B,{open:a,setOpen:i,colsConfig:o,onOk:e=>{t(e),i(!1)}})]})},{Item:$,useForm:V}=a.A,L=Object.keys(y.I).map((e=>({label:e,value:y.I[e]}))),Y=v.Ay.div`
    .bind-input-variable{
        display: block;
        .bind-value-span{
            margin-bottom: 4px ;
        }
    }
`,J=e=>{let{open:l,setOpen:o,config:v,updateConfig:p}=e;const{t:w}=(0,t.Bd)(),C=(0,g.A)(),S=(0,n.useRef)(null),[N]=V();(0,n.useEffect)((()=>{}),[]),(0,n.useEffect)((()=>{I()}),[v,C]);const I=(0,n.useCallback)((()=>{if(!h()(C,null===S||void 0===S?void 0:S.current)){S.current=C;let l=[];if(C&&null!==v&&void 0!==v&&v.dataSourceCode&&null!==v&&void 0!==v&&v.colsConfig){const o=null===C||void 0===C?void 0:C.find((e=>e.code===(null===v||void 0===v?void 0:v.dataSourceCode)));if(o){var e;const n=null===(e=o.double_array_tab)||void 0===e?void 0:e.columns;l=null===v||void 0===v?void 0:v.colsConfig.filter((e=>n.some((l=>l.code===e.code)))).map((e=>{const l=n.find((l=>l.code===e.code))||{};return{...e,showName:l.showName,openCorrelation:l.openCorrelation,correlationCode:l.correlationCode,type:l.type,typeParam:l.typeParam}}));n.filter((e=>!(null!==v&&void 0!==v&&v.colsConfig.some((l=>l.code===e.code))))).forEach((e=>{l.push({code:e.code,colTitle:e.showName,showName:e.showName,type:e.type,typeParam:e.typeParam,openCorrelation:e.openCorrelation,correlationCode:e.correlationCode,show:!0,isEdit:!1,isTriggerAction:!1})}))}}N.setFieldsValue({...v,colsConfig:l})}}),[C]),E=a.A.useWatch("isSaveNewData",N),R=a.A.useWatch("dataShowType",N);return(0,A.jsx)(m.A,{open:l,title:w("\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c"),maskClosable:!1,width:"50vw",onOk:async()=>{try{const e=await N.validateFields();p(e),o(!1)}catch(e){console.log("err",e)}},onCancel:()=>{o(!1)},children:(0,A.jsx)(Y,{children:(0,A.jsxs)(a.A,{form:N,labelCol:{span:8},wrapperCol:{span:16},onValuesChange:e=>{"dataSourceCode"in e&&(e=>{var l,o;const n=(null===(l=C.find((l=>l.code===e)))||void 0===l||null===(o=l.double_array_tab)||void 0===o?void 0:o.columns).map((e=>({...e,colTitle:e.showName,isEdit:!1,isTriggerAction:!1,show:!0})));N.setFieldValue("colsConfig",n)})(null===e||void 0===e?void 0:e.dataSourceCode)},children:[(0,A.jsxs)(i.A,{children:[(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u6570\u636e\u6e90",name:"dataSourceCode",children:(0,A.jsx)(f.A,{inputVariableType:j.ps.\u4e8c\u7ef4\u6570\u7ec4})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u663e\u793a\u66f4\u65b0\u9891\u7387",name:"updateFreq",initialValue:1e3,children:(0,A.jsx)(r.A,{options:[{label:"0.1s",value:90},{label:"0.2s",value:180},{label:"0.5s",value:470},{label:"1s",value:900},{label:"2s",value:1900}]})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u8868\u683c\u8f93\u5165\u52a8\u4f5c",name:"tableInputActionId",children:(0,A.jsx)(x.A,{allowClear:!0})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u663e\u793a\u884c\u6570",name:"showRowNumber",children:(0,A.jsx)(s.A,{max:50,min:1})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u6570\u636e\u663e\u793a",name:"dataShowType",initialValue:y.I.\u9ed8\u8ba4\u884c\u6570\u663e\u793a,children:(0,A.jsx)(r.A,{options:L})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u6eda\u52a8\u4f4d\u7f6e\u7ed1\u5b9a",name:"scrollToValue",children:(0,A.jsx)(b.A,{inputVariableType:j.ps.\u6570\u5b57\u578b})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u5012\u5e8f\u6392\u5217",name:"isReverse",valuePropName:"checked",children:(0,A.jsx)(c.A,{})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{style:{paddingLeft:"20px"},label:"",name:"isSaveNewData",valuePropName:"checked",children:(0,A.jsx)(u.A,{children:"\u53ea\u4fdd\u7559\u663e\u793a\u6700\u65b0\u6570\u636e"})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u663e\u793a\u6761\u6570",name:"showNumber",children:(0,A.jsx)(s.A,{disabled:!E,min:0})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u5de6\u4fa7\u56fa\u5b9a\u5217\u6570",name:"leftFixedNumber",children:(0,A.jsx)(s.A,{disabled:R!==y.I.\u6eda\u52a8\u6761\u663e\u793a,min:0,allowClear:!0})})}),(0,A.jsx)(d.A,{span:12,children:(0,A.jsx)($,{label:"\u4e0a\u65b9\u56fa\u5b9a\u884c\u6570",name:"topFixedNumber",children:(0,A.jsx)(s.A,{disabled:R!==y.I.\u6eda\u52a8\u6761\u663e\u793a,min:0,allowClear:!0})})})]}),(0,A.jsx)(i.A,{children:(0,A.jsx)(d.A,{span:24,children:(0,A.jsx)($,{label:"\u8868\u683c\u9884\u89c8",name:"colsConfig",labelCol:{span:4},wrapperCol:{span:20},children:(0,A.jsx)(O,{})})})}),(0,A.jsx)(i.A,{children:(0,A.jsx)(d.A,{span:20,offset:4,children:(0,A.jsx)(k,{})})})]})})})}},71108:(e,l,o)=>{o.d(l,{A:()=>c});var n=o(65043),t=o(74117),a=o(38134),i=o(36497),d=o(80077),r=o(70579);const s=e=>{var l,o;let{text:t,dimensionId:a,unitId:s,handleUnitChange:c}=e;if(!a)return t;const u=(0,d.d4)((e=>e.global.unitList)),[v,p]=(0,n.useState)(!1),h=(0,n.useRef)(),m=(0,n.useMemo)((()=>{var e,l;return null!==(e=null===(l=u.find((e=>e.id===a)))||void 0===l?void 0:l.units)&&void 0!==e?e:[]}),[a,u]),f=null!==s&&void 0!==s?s:null===(l=u.find((e=>e.id===a)))||void 0===l?void 0:l.default_unit_id,x=null===(o=m.find((e=>e.id===f)))||void 0===o?void 0:o.name;(0,n.useEffect)((()=>{v&&h.current.focus()}),[v]);return(0,r.jsxs)("div",{style:{display:"flex"},children:[(0,r.jsx)("span",{children:t}),v?(0,r.jsx)(i.A,{value:f,ref:h,fieldNames:{label:"name",value:"id"},options:m,onChange:e=>{c(e)},onBlur:()=>{p(!1)},style:{minWidth:"55px"}}):(0,r.jsx)("div",{onClick:()=>{p(!0)},children:`(${x})`})]})},c=e=>{let{text:l,type:o,typeParam:n,onChangeTypeParam:i}=e;const{t:d}=(0,t.Bd)();return o===a.J$.\u6570\u5b57?(0,r.jsx)(s,{text:l,dimensionId:null===n||void 0===n?void 0:n.dimensionId,unitId:null===n||void 0===n?void 0:n.unitId,handleUnitChange:e=>i({...n,unitId:e})}):(0,r.jsx)(r.Fragment,{children:d(l)})}},74837:(e,l,o)=>{o.d(l,{I:()=>n});const n={"\u9ed8\u8ba4\u884c\u6570\u663e\u793a":"RowNumber","\u6eda\u52a8\u6761\u663e\u793a":"Page","\u9875\u7801\u663e\u793a":"Scroll"}},84917:(e,l,o)=>{o.r(l),o.d(l,{default:()=>E});var n=o(65043),t=o(80077),a=o(19853),i=o.n(a),d=o(21256),r=o(36950),s=o(63804),c=o(66103),u=o(81143);const v=u.Ay.div`
    width: 100%;
    height: 100%;
    background: #fff;
    overflow: hidden;
`;var p=o(16090),h=o(67208),m=o(34458),f=o(69581),x=(o(74837),o(11375));o(93950),o(79889),o(61900),o(71424),o(71108),o(10712);u.Ay.div`
    
    .static-div{
        z-index: 3;
        .static-item{
            position: sticky;
            border-right: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
            
            &:last-child{
                border-right: none;
            }
        }
    }
    
`;var b=o(70579);var g=o(84617);const j=e=>{const l=Object.keys(e),o=Math.max(...l.map((l=>{var o;return(null===(o=e[l])||void 0===o?void 0:o.length)||0}))),n=[];for(let t=0;t<o;t+=1){const o={};l.forEach((l=>{var n;o[l]={value:null===(n=e[l])||void 0===n?void 0:n[t]}})),n.push(o)}return n},y=e=>{let{controlCompId:l,config:o,isEdit:t}=e;const[a,i]=(0,n.useState)([]),[d,c]=(0,n.useState)(0),u=(0,n.useRef)([]),v=(0,n.useRef)([]),p=(0,n.useRef)();(0,n.useEffect)((()=>{t||m()}),[null===o||void 0===o?void 0:o.showRowNumber,t]),(0,n.useEffect)((()=>{m()}),[null===o||void 0===o?void 0:o.isReverse,null===o||void 0===o?void 0:o.isSaveNewData,null===o||void 0===o?void 0:o.showNumber,null===o||void 0===o?void 0:o.dataSourceCode]);const m=()=>{var e;(c(0),u.current=[],v.current=[],i([]),null!==o&&void 0!==o&&o.isReverse)?p.current=null:x({startIndex:0,endIndex:null!==(e=null===o||void 0===o?void 0:o.showRowNumber)&&void 0!==e?e:10})},f=(0,n.useCallback)((()=>{i((e=>{const l=(e=>{let{isEdit:l,isReverse:o,isSaveNewData:n,showNumber:t,msgData:a,resfulRecords:i,oldShowData:d,range:r}=e;if(l){let e=a;return n&&(e=a.slice(-t)),o&&(e=e.reverse()),e}if(o&&!r){let e=a;return n&&(e=a.slice(-t)),e=e.reverse(),e}if(r){const{startIndex:e,endIndex:l}=r;return new Array(l-e).fill(0).map(((n,t)=>{const r=o?l-t:e+t,s=a.find((e=>{var l;return(null===e||void 0===e||null===(l=e.index)||void 0===l?void 0:l.value)===r}))||(null===i||void 0===i?void 0:i.find((e=>{var l;return(null===e||void 0===e||null===(l=e.index)||void 0===l?void 0:l.value)===r})))||d.find((e=>{var l;return(null===e||void 0===e||null===(l=e.index)||void 0===l?void 0:l.value)===r}));return{index:r,...null!==s&&void 0!==s?s:{}}}))}return console.warn("\u4e8c\u7ef4\u6570\u7ec4\u5185\u90e8\u72b6\u6001\u6709\u8bef"),[]})({oldShowData:e,isEdit:t,isReverse:null===o||void 0===o?void 0:o.isReverse,isSaveNewData:null===o||void 0===o?void 0:o.isSaveNewData,showNumber:null===o||void 0===o?void 0:o.showNumber,msgData:u.current,resfulRecords:v.current,range:p.current});return l}))}),[o]);(0,g.A)({controlCompId:l,onMessage:(0,n.useCallback)((e=>{const{mode:l,data:o,totalCount:n}=e;if(0===l&&"{}"===JSON.stringify(o))return void m();const t=j(o);switch(c(n),l){case 0:u.current=t;break;case 1:u.current=[...u.current,...t]}f()}),[o])});const x=async e=>{var l;let{startIndex:n,endIndex:a}=e;if(console.log("startIndex, endIndex",n,a,d),t)return;if(null!==o&&void 0!==o&&o.isReverse&&a>=d)return p.current=null,void f();const i=await(0,h.YIA)({templateName:(0,r.n1)(),dataSourceType:s.d.\u4e8c\u7ef4\u6570\u7ec4,dataSourceCode:null===o||void 0===o?void 0:o.dataSourceCode,dataCodes:[...null===o||void 0===o||null===(l=o.colsConfig)||void 0===l?void 0:l.map((e=>null===e||void 0===e?void 0:e.code))],startIndex:n,endIndex:a,sampleCode:"",doubleArrayIndex:0}),c=j(i.data);v.current=c.map(((e,l)=>({...e,index:{value:n+l}}))),p.current={startIndex:n,endIndex:a},f()};return{showData:a,total:d,upDataRange:x}},w=e=>{let{id:l,config:o={},isEdit:t,isPdf:a}=e;const{startAction:i}=(0,p.A)(),[d,r]=(0,n.useState)(),s=(0,f.A)(null===o||void 0===o?void 0:o.dataSourceCode),{showData:c,total:u,upDataRange:v}=y({controlCompId:l,isEdit:t,config:o});(0,n.useEffect)((()=>{if(s&&null!==o&&void 0!==o&&o.colsConfig){var e;const l=null===(e=s.double_array_tab)||void 0===e?void 0:e.columns,n=null===o||void 0===o?void 0:o.colsConfig.filter((e=>l.some((l=>l.code===e.code)))).map((e=>{const o=l.find((l=>l.code===e.code))||{};return{...e,showName:o.showName,openCorrelation:o.openCorrelation,correlationCode:o.correlationCode,type:o.type,typeParam:o.typeParam}}));l.filter((e=>!(null!==o&&void 0!==o&&o.colsConfig.some((l=>l.code===e.code))))).forEach((e=>{n.push({code:e.code,colTitle:e.showName,showName:e.showName,type:e.type,typeParam:e.typeParam,openCorrelation:e.openCorrelation,correlationCode:e.correlationCode,show:!0,isEdit:!1,isTriggerAction:!1})})),r(n)}else r([])}),[s,null===o||void 0===o?void 0:o.colsConfig]);const g=(0,n.useCallback)((async e=>{try{const{isTriggerAction:l}=null===o||void 0===o?void 0:o.colsConfig.find((l=>l.code===(null===e||void 0===e?void 0:e.columnCode)));await(0,h.tRd)({className:`project_${(0,m.HN)()}`,doubleArrayCode:null===o||void 0===o?void 0:o.dataSourceCode,doubleArrayCellParam:e}),l&&j(null===o||void 0===o?void 0:o.tableInputActionId)}catch(l){console.log("err",l)}}),[o]),j=async e=>{e&&await i({action_id:String(e)})};return(0,b.jsx)(b.Fragment,{children:d&&(0,b.jsx)(b.Fragment,{children:(0,b.jsx)(x.A,{showData:c,total:u,upDataRange:v,config:o,colsConfig:d,onInput:g,isPdf:a,isEdit:t})})})};var C=o(74117),A=o(16569),S=o(80231),k=o(754),N=o(16204);const I=e=>{let{id:l,layoutConfig:o,setOpen:a,config:i}=e;const{t:d}=(0,C.Bd)(),s=(0,t.d4)((e=>e.subTask.openExperiment)),c=(0,t.d4)((e=>e.global.systemConfig)),u=(0,t.d4)((e=>e.global.stationList)),v=(0,t.d4)((e=>e.global.cfgList)),p=(0,t.d4)((e=>e.system.projectList)),m=(0,t.d4)((e=>e.project.projectId)),[f,x]=(0,n.useState)(!1);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)(S.A,{domId:l,layoutConfig:o,children:[(0,b.jsx)("div",{className:"unique-content",onClick:()=>a(!0),children:d("\u7f16\u8f91\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c")}),!s&&(0,b.jsx)("div",{className:"unique-content",onClick:()=>{null!==i&&void 0!==i&&i.dataSourceCode?x(!0):A.Ay.error("\u672a\u9009\u62e9\u6570\u636e\u6e90")},children:d("\u5bfc\u51facsv")})]}),(0,b.jsx)(N.A,{open:f,title:"\u5bfc\u51facsv",defaultPath:null===c||void 0===c?void 0:c.project_directory,onOk:async(e,l)=>{x(!1);const{cfgId:o}=await(0,h.TEp)(),{stationId:n}=v.find((e=>e.cfgId===o)),{stationName:t}=u.find((e=>e.id===n)),{project_name:a}=p.find((e=>e.project_id===Number(m))),d=`${"\\"===e.at(-1)?e:`${e}\\`}${t}_${n}\\${a}_${m}\\`,s=k.A.getState().global.unitList,c=null===i||void 0===i?void 0:i.colsConfig.filter((e=>!1!==e.show)).map((e=>{var l,o,n,t;const a=null===s||void 0===s||null===(l=s.map((e=>e.units)))||void 0===l||null===(o=l.flat())||void 0===o?void 0:o.find((l=>{var o;return l.id===(null===e||void 0===e||null===(o=e.typeParam)||void 0===o?void 0:o.unitId)}));return{code:e.code,name:e.showName,unit:null!==(n=null===a||void 0===a?void 0:a.name)&&void 0!==n?n:"",proportion:null!==(t=null===a||void 0===a?void 0:a.proportion)&&void 0!==t?t:1}}));await(0,h.NQw)({templateName:(0,r.n1)(),type:"DoubleArray",arrayCode:null===i||void 0===i?void 0:i.dataSourceCode,codes:c,path:d,fileName:l})},onCancel:()=>{x(!1)}})]})},E=e=>{var l,o;let{item:a,id:u,layoutConfig:p,isPdf:h=!1}=e;const m=(0,t.d4)((e=>e.template.widgetData)),{editWidget:f}=(0,d.A)(),x=(0,t.d4)((e=>e.project.optSample)),[g,j]=(0,n.useState)(!1),y=(0,n.useMemo)((()=>(0,r.Rm)(m,"widget_id",null===a||void 0===a?void 0:a.widget_id)),[a,m]),C=(0,n.useRef)(),A=(0,n.useMemo)((()=>{var e;return i()(C.current,null===y||void 0===y?void 0:y.data_source)?C.current:(C.current=null===y||void 0===y?void 0:y.data_source,null!==(e=null===y||void 0===y?void 0:y.data_source)&&void 0!==e?e:{})}),[null===y||void 0===y?void 0:y.data_source]),S=(0,n.useMemo)((()=>{var e;return null===A||void 0===A||null===(e=A.colsConfig)||void 0===e?void 0:e.some((e=>e.isEdit))}),[A]),k=(0,n.useMemo)((()=>{var e;return null===A||void 0===A||null===(e=A.colsConfig)||void 0===e?void 0:e.map((e=>null===e||void 0===e?void 0:e.code))}),[null===A||void 0===A?void 0:A.colsConfig]),N=(0,n.useMemo)((()=>[x.code]),[x.code]),{targetRef:E}=(0,s.A)({controlCompId:u,dataSourceType:s.d.\u4e8c\u7ef4\u6570\u7ec4,dataSourceCode:null===A||void 0===A?void 0:A.dataSourceCode,dataCodes:k,timer:S?-1:null!==(l=null===A||void 0===A?void 0:A.updateFreq)&&void 0!==l?l:200,number:S?-1:null!==(o=null===A||void 0===A?void 0:A.showRowNumber)&&void 0!==o?o:50,testStatus:1,daqCurveSelectedSampleCodes:N});return(0,b.jsxs)(v,{ref:E,children:[(0,b.jsx)(w,{id:u,config:A,isEdit:S,isPdf:h}),g&&(0,b.jsx)(c.A,{open:g,setOpen:j,config:A,updateConfig:e=>{f({...y,data_source:e})}}),(0,b.jsx)(I,{id:u,layoutConfig:p,setOpen:j,config:A})]})}}}]);
//# sourceMappingURL=4917.c09d9704.chunk.js.map