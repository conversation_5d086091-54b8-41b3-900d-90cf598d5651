"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[53,910,9882],{3945:(e,i,n)=>{n.d(i,{Ed:()=>r,Sq:()=>d,mz:()=>t});var l=n(78117);const d=(e,i)=>({...e,order:i,key:crypto.randomUUID(),id:crypto.randomUUID()}),t=e=>e.map((e=>{const i=e;return delete i.view,i.children&&i.children.length>0&&(i.children=t(e.children)),e})),r=function(e,i){var n,d;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{key:o,display:s,layout:a,data:c={}}=e||r;delete a.view;return{formKey:o,g0_RootID:0,g1_HwType:0,g2_AxisID:(null===r||void 0===r||null===(n=r.display.find((e=>e.key===l.dS.CODE_ZHOU)))||void 0===n?void 0:n.index)||0,g3_ADID:(null===r||void 0===r||null===(d=r.display.find((e=>e.key===l.dS.CODE_AD)))||void 0===d?void 0:d.index)||0,g4_SensorID:0,eventCode:i.key,json:JSON.stringify({key:o,display:s,data:[],layout:{...a,children:t(a.children)}}),flashFormFlag:0}}},25330:(e,i,n)=>{n.d(i,{JG:()=>t,KI:()=>c,dH:()=>o,ne:()=>s,y7:()=>r,zs:()=>a});var l=n(81143),d=n(68374);const t=l.Ay.div`
    height: 100%;
    display: flex;
    justify-content: space-between;
    /* padding: ${(0,d.D0)("10px")}; */
    .left {
        width: 15vw;
        border: 1px solid #d8d8d8;
        height: 100%;
    }
    .right {
        border: 1px solid #d8d8d8;
        height: 100%;
        padding: 3px;
        width: calc(100% - 15.5vw);
    }
`,r=(l.Ay.div`
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    .layout {
        padding: 10px;
        .label {
            width: 8vw;
        }
        .table-title {
            display: flex;
            justify-content: flex-end;
            width: 100%;
        }
    }
    .select {
        background: rgba(20, 115, 245,0.2)
    }
`,l.Ay.div`
    position: fixed;
    z-index: 100000;
    user-select: none;
    display: none;
    background: #FFF;
    min-width: 120px;
    padding: 5px;
    border: 1px solid rgba(220 ,220, 220,1);
    box-shadow: 5px 5px 3px -2px rgba(0, 0, 0,0.4);
    font-size: 14px;


`),o=l.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }
    .unique-content:hover {
        background: rgba(20, 115, 245,0.4)
    }
`,s=l.Ay.div`
    display: flex;
    justify-content: flex-end;
    width: 100%;
    height: 100%;

`,a=l.Ay.div`
    width: 100%;
    .select {
        background: rgba(20, 115, 245,0.2)
    }
`,c=l.Ay.div`
    width: 100%;
    height: 100%;
    overflow: auto;
    .select {
        background: rgba(20, 115, 245,0.2)
    }
`},49882:(e,i,n)=>{n.r(i),n.d(i,{default:()=>r});var l=n(65043),d=n(72535),t=n(70579);const r=e=>{let{sizes:i,onDragEnd:n,id:r,render:o,onDrag:s,disabled:a=!1,minSize:c=25,...u}=e;const[v,h]=(0,l.useState)(i);(0,l.useEffect)((()=>{h(i)}),[i]);return(0,t.jsx)(d.A,{id:`ver-${r}`,minSize:c,snapOffset:0,render:o,onDragEnd:a?void 0:(e,i)=>{n(e,i)},onDrag:a?void 0:(e,i,n)=>{h(n),s(e,i,n)},gridTemplateRows:v,disabled:a,...u},`ver-${r}`)}},68971:(e,i,n)=>{n.d(i,{A:()=>t});n(65043),n(80077);var l=n(67208),d=n(56543);const t=()=>({getConfigList:async e=>{try{const i=await(0,l.kXj)();return i?e?e.map((e=>i.find((i=>i.config_key===e)))):i:[]}catch(i){return console.log(i),[]}},addOrUpdateConfig:async e=>{try{const{config_id:t,config_value:r,config_key:o}=e;let s=t;var i,n;if(t)s=await(0,l.mXA)({config_id:t,config_key:o,config_value:r,remark:null===(i=Object.values(d.mN).find((e=>e.key===o)))||void 0===i?void 0:i.remark}),console.log("updateDongtaiConfig res",o);else s=await(0,l.gQN)({config_key:o,config_value:r,remark:null===(n=Object.values(d.mN).find((e=>e.key===o)))||void 0===n?void 0:n.remark}),console.log("addDongtaiConfig res",o);return s}catch(t){return console.log("err",t),!1}},getConfigPidList:async()=>{try{const e=await(0,l.kXj)();return e?e.filter((e=>e.config_key===d.mN.pid\u8bbe\u7f6e.key)):[]}catch(e){return console.log(e),[]}}})},73225:(e,i,n)=>{n.d(i,{A:()=>p});var l=n(65043),d=n(74117),t=n(25055),r=n(36497),o=n(97914),s=n(6051),a=n(75440),c=n(4554),u=n(67208),v=n(70579);const h={FormKey:"",g0_RootID:0,g1_HwType:0,g2_AxisID:0,g3_ADID:0,g4_SensorID:0},{Item:g}=t.A,p=e=>{let{open:i,onCancel:n,submitCallback:p,formData:y}=e;const{t:x}=(0,d.Bd)(),[f]=t.A.useForm(),[m,b]=(0,l.useState)([]);(0,l.useEffect)((()=>{w()}),[]);const w=async()=>{const e=await(0,u.nAy)({key:"list",operation:"listInit"});e&&b(Object.keys(e).map((e=>({label:e,value:e}))))};(0,l.useEffect)((()=>{0!==m.length&&y&&m.some((e=>e.value===y.FormKey))&&f.setFieldsValue(y)}),[y,m]);return(0,v.jsx)(a.A,{open:i,onCancel:n,width:500,title:x("\u914d\u7f6epid\u8bbe\u7f6e"),footer:null,children:(0,v.jsxs)(t.A,{form:f,labelCol:{span:8},wrapperCol:{span:12},children:[(0,v.jsx)(g,{label:"FormKey",name:"FormKey",rules:[{required:!0}],children:(0,v.jsx)(r.A,{options:m,style:{width:"200px"}})}),Object.keys(h).splice(1).map((e=>(0,v.jsx)(g,{name:e,label:e,rules:[{required:!0}],children:(0,v.jsx)(o.A,{style:{width:"200px"}})},e))),(0,v.jsx)(g,{wrapperCol:{span:4,offset:10},children:(0,v.jsxs)(s.A,{children:[(0,v.jsx)(c.A,{onClick:async()=>{try{const e=await f.validateFields();e&&p(e)}catch(e){console.error(e)}},children:x("\u786e\u5b9a")}),(0,v.jsx)(c.A,{onClick:()=>{n(),y&&f.setFieldsValue(y)},children:x("\u53d6\u6d88")})]})})]})})}},78117:(e,i,n)=>{n.d(i,{Fw:()=>s,Fz:()=>r,SZ:()=>t,U8:()=>o,dS:()=>a,y1:()=>d});var l=n(8237);const d={BTN:"btn",INPUT:"input",CHECK:"check",SINGLE_SELECT:"single_select",TABLE:"2d_array",LABEL:"label"},t={"\u6570\u5b57":"number","\u6587\u672c":"text"},r=[{label:"\u6309\u94ae",value:d.BTN},{label:"\u8f93\u5165\u6846",value:d.INPUT},{label:"\u5355\u9009\u6846",value:d.CHECK},{label:"\u9009\u62e9\u6846",value:d.SINGLE_SELECT},{label:"\u4e8c\u7ef4\u6570\u636e",value:d.TABLE},{label:"LABEL",value:d.LABEL}],o={[d.BTN]:{key:"btn_code1",type:"btn",value:"\u6267\u884c",label:"\u64cd\u4f5c",visible:!0,enable:!0,order:0},[d.INPUT]:{key:"p_code2",type:"input",value:"0",label:"P:",visible:!0,enable:!0,order:1},[d.CHECK]:{key:"check_code5",type:"check",value:!1,label:"\u662f\u5426\u542f\u7528",visible:!0,enable:!0,order:0},[d.SINGLE_SELECT]:{key:"select_code6",type:"single_select",label:"\u9009\u62e9",options:["0","1","2"],value:"0",visible:!0,enable:!0,order:1,index:0},[d.TABLE]:{key:"2d_array_code7",type:"2d_array",value:[[200]],options:[{title:"\u6e29\u5ea6\u504f\u5dee",renderType:t.\u6570\u5b57}],visible:!0,enable:!0,order:2},[d.LABEL]:{key:"label",type:"label",label:"label",value:"label"},[d.INPUT_W_BTN]:{key:"btn_input_code5",type:"input_w_btn",visible:!0,enable:!0,order:4,label:"\u8bf7\u70b9\u51fb\u6309\u94ae\u4e0b\u53d1",value:{inputV:"0",inputB:{key:"btn_code6",type:"btn",value:"\u6267\u884c",visible:!0,enable:!0,order:0}}}},s=(t.\u6570\u5b57,t.\u6570\u5b57,t.\u6570\u5b57,t.\u6570\u5b57,t.\u6570\u5b57,t.\u6570\u5b57,t.\u6570\u5b57,t.\u6570\u5b57,e=>({id:e,widgetId:"0",direction:l.oM.HOR,children:[{id:`${e}-1`,widgetId:"0",direction:l.oM.VER,children:[]}]})),a={CODE_ZHOU:"code_zhou",CODE_AD:"code_ad"}},90910:(e,i,n)=>{n.r(i),n.d(i,{default:()=>s});var l=n(65043),d=n(72535),t=n(93950),r=n.n(t),o=n(70579);const s=e=>{let{sizes:i,onDragEnd:n,id:t,render:s,onDrag:a,disabled:c=!1,...u}=e;const[v,h]=(0,l.useState)(i);(0,l.useEffect)((()=>{h(i)}),[i]);(0,l.useCallback)(r()(((e,i,n)=>a(e,i,n)),500),[]);return(0,o.jsx)(d.A,{id:`hor-${t}`,minSize:25,snapOffset:0,render:s,onDragEnd:c?void 0:(e,i)=>{n(e,i)},onDrag:c?void 0:(e,i,n)=>{h(n),a(e,i,n)},gridTemplateColumns:v,disabled:c,...u},`hor-${t}`)}},95911:(e,i,n)=>{n.d(i,{A:()=>z});var l=n(65043),d=n(56434),t=n.n(d),r=n(74117),o=n(90910),s=n(49882),a=n(8237),c=n(16569),u=n(97914),v=n(83720),h=n(95206),g=n(6051),p=n(32513),y=n(36497),x=n(36950),f=n(92941),m=(n(80077),n(20790)),b=(n(92676),n(81143));n(68374);const w=b.Ay.div`
    background: #FFFFFF;
    border-radius: 6px;
    border: ${e=>null!==e&&void 0!==e&&e.isBorder?"":"1px solid #d8d8d8"} ;
    display: flex;
    align-items: center;
    justify-content:center ;
    height: 100%;
    width: 100%;
    overflow: hidden;
    .layout-content {
        display: flex;
        align-items: center;
        justify-content: center;
        .label {
            width: 2vw;
        }
        .table-title {
            display: flex;
            justify-content: flex-end;
            width: 100%;
        }
    }
`,j=b.Ay.div`
    position: fixed;
    z-index: 100000;
    user-select: none;
    display: none;
    background: #FFF;
    min-width: 120px;
    padding: 5px;
    border: 1px solid rgba(220 ,220, 220,1);
    box-shadow: 5px 5px 3px -2px rgba(0, 0, 0,0.4);
    font-size: 14px;


`,k=b.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }
    .unique-content:hover {
        background: rgba(20, 115, 245,0.4)
    }
`;var C=n(70579);const E=e=>{let{id:i,item:n,isContextMenu:d=!0,children:t,onDel:o,onVertical:s,onHorizontal:a,...c}=e;const u=(0,l.useRef)(),{t:v}=(0,r.Bd)();return(0,l.useEffect)((()=>{const e=()=>{u.current&&(u.current.style.display="none",document.onclick=null)};return window.addEventListener("click",e),()=>{var i;null===(i=document)||void 0===i||i.removeEventListener("click",e)}}),[]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w,{isBorder:!d,id:i,...d?{onContextMenu:e=>{e.preventDefault(),e.stopPropagation(),document.getElementsByName("menu").forEach((e=>{e.style.display="none"}));let{clientX:i,clientY:n}=e;u.current.style.display="block";const l=window.innerWidth,d=window.innerHeight,t=u.current.offsetWidth,r=u.current.offsetHeight;i=l-i>t?i:i-t,n=d-n>r?n:n-r,u.current.style.top=`${n}px`,u.current.style.left=`${i}px`}}:null,...c,children:t},n.id),d&&(0,C.jsx)(m.A,{children:(0,C.jsx)(j,{ref:u,name:"menu",children:(0,C.jsxs)(k,{children:[(0,C.jsx)("div",{className:"unique-content",onClick:()=>{o&&o(n)},children:v("\u5220\u9664")}),(0,C.jsx)("div",{className:"unique-content",onClick:()=>{s&&s(n)},children:v("\u7ad6\u5207")}),(0,C.jsx)("div",{className:"unique-content",onClick:()=>{a&&a(n)},children:v("\u6a2a\u5207")})]})})})]})};var A=n(78117),_=n(25330);let I="";const D=(e,i)=>{let{config:n={},isEdit:d=!1,onResize:m=()=>console.log("onResize"),onBtnChange:b,onSelectChange:w}=e;const{t:j}=(0,r.Bd)(),k=(0,l.useRef)(),D=(0,l.useRef)(),[z,S]=(0,l.useState)(),[N,F]=(0,l.useState)();(0,l.useEffect)((()=>{S(n.layout),k.current=n}),[n]);const L=e=>d?`edit-${e}`:e,T=()=>{var e,i;return null!==(e=I)&&void 0!==e&&e.startsWith("edit-")?null===(i=I)||void 0===i?void 0:i.substring(5):I},B=()=>I,H=e=>{let{id:i}=e;if(!d)return;const n=document.getElementById(I);n&&(n.style.border="1px solid #D7E2FF");const l=L(i);document.getElementById(l).style.border="2px solid #87CEEB",I=l},R=e=>{let{child:i,name:n,type:l,view:d,widgetId:t,direction:r}=e;i.children.push({id:`${i.id}-${i.children.length+1}`,name:n,type:l,sizes:"1fr 3px 1fr",direction:r,view:d,widgetId:t,children:[]})},O=(e,i,n)=>e.map((e=>e.id===i?(0===e.children.length&&R({child:e,widgetId:e.widgetId,direction:n}),R({child:e,widgetId:"0",direction:n}),e.direction=n,e.sizes="1fr 3px 1fr",e):(e.children&&e.children.length>0&&(e.children=O(e.children,i,n)),e))),M=(e,i)=>e.filter((e=>{var n;const l=e;return l.id!==i&&(null!==l&&void 0!==l&&l.children&&(null===l||void 0===l||null===(n=l.children)||void 0===n?void 0:n.length)>0&&(l.children=M(l.children,i)),!0)})),V=(e,i)=>e.map((e=>{var n,l;if(e.id===i&&1===(null===e||void 0===e||null===(n=e.children)||void 0===n?void 0:n.length)){const i=e.children[0];e.id=null===i||void 0===i?void 0:i.id,e.widgetId=null===i||void 0===i?void 0:i.widgetId,e.direction=null===i||void 0===i?void 0:i.direction,e.children=[]}return null!==e&&void 0!==e&&e.children&&(null===e||void 0===e||null===(l=e.children)||void 0===l?void 0:l.length)>0&&(e.children=V(e.children,i)),e})),$=e=>{var i,l,d,t,r;const{id:o}=e,s=null!==o&&void 0!==o&&o.startsWith("edit-")?null===o||void 0===o?void 0:o.substring(5):o;if(0===(null===z||void 0===z||null===(i=z.children[0])||void 0===i||null===(l=i.children)||void 0===l?void 0:l.length))return c.Ay.error(j("\u6700\u540e\u4e00\u7ea7\u4e0d\u80fd\u5220\u9664")),!1;const{widgetId:a}=null!==(d=(0,x.Rm)(null===z||void 0===z?void 0:z.children,"id",s))&&void 0!==d?d:{},u=null===(t=M([z],s))||void 0===t?void 0:t[0];return m({...n,display:(null===n||void 0===n||null===(r=n.display)||void 0===r?void 0:r.filter((e=>e.id!==a)))||[],layout:{...u,children:V(null===u||void 0===u?void 0:u.children,o.slice(0,-2))}}),console.log("\u5220\u9664"),I="",!0},U=e=>{const{id:i}=e,l=document.getElementById(`edit-${i}`);if((null===l||void 0===l?void 0:l.clientWidth)<=50)return c.Ay.warning(j("\u7ad6\u5207\u5bbd\u5ea6\u4e0d\u80fd\u5c0f\u4e8e50px")),!1;const d=null!==i&&void 0!==i&&i.startsWith("edit-")?null===i||void 0===i?void 0:i.substring(5):i;return m({...n,layout:{...z,children:O(null===z||void 0===z?void 0:z.children,d,a.oM.VER)}}),console.log("\u7ad6\u5207",d),I="",!0},q=e=>{const{id:i}=e,l=document.getElementById(`edit-${i}`);if((null===l||void 0===l?void 0:l.clientHeight)<=50)return c.Ay.warning(j("\u6a2a\u5207\u9ad8\u5ea6\u4e0d\u80fd\u5c0f\u4e8e50px")),!1;const d=null!==i&&void 0!==i&&i.startsWith("edit-")?null===i||void 0===i?void 0:i.substring(5):i;return m({...n,layout:{...z,children:O(null===z||void 0===z?void 0:z.children,d,a.oM.HOR)}}),console.log("\u6a2a\u5207"),I="",!0},P=(e,i,l,t)=>{if(!d){let d=e;null!=d.target&&(d="checkbox"===d.target.type?e.target.checked:null!=d.target.value?e.target.value:e);let r={...n,display:n.display.map((e=>e.id===i?{...e,value:d}:e))};l===A.y1.SINGLE_SELECT&&w&&(r={...n,display:n.display.map((e=>e.id===i?{...e,value:d,index:e.options.findIndex((e=>e===d))}:e))},w(r,t)),m(r)}},G=(e,i)=>{const{value:l,id:d}=i,r=t()(l);r.splice(e,1),m({...n,display:n.display.map((e=>e.id===d?{...e,value:r}:e))})},K=e=>{let{renderType:i,onChange:n,...l}=e;switch(i){case A.SZ.\u6570\u5b57:return(0,C.jsx)(u.A,{...l,onChange:e=>n(e)});case A.SZ.\u6587\u672c:return(0,C.jsx)(v.A,{...l,onChange:e=>n(e.target.value)});default:return(0,C.jsx)(C.Fragment,{})}},W=e=>{var i,l;const{widgetId:r}=e,{type:o,enable:s,value:a,label:u,id:w,options:k,key:I}=null!==(i=null===n||void 0===n||null===(l=n.display)||void 0===l?void 0:l.find((e=>(null===e||void 0===e?void 0:e.id)===r)))&&void 0!==i?i:{};switch(o){case A.y1.BTN:return(0,C.jsx)(E,{item:e,isContextMenu:d,id:L(e.id),onClick:()=>H(e),onDel:$,onVertical:U,onHorizontal:q,children:(0,C.jsx)(h.Ay,{onClick:()=>{return e={id:w,type:o,value:a,key:I},void(b&&b(e));var e},disabled:!s,children:j(a)})});case A.y1.INPUT:return(0,C.jsx)(E,{item:e,isContextMenu:d,id:L(e.id),onClick:()=>H(e),onDel:$,onVertical:U,onHorizontal:q,children:(0,C.jsxs)(g.A,{children:[u&&(0,C.jsx)("div",{children:j(u)}),(0,C.jsx)(v.A,{disabled:!s,value:a,onChange:e=>P(e,w)})]})});case A.y1.CHECK:return(0,C.jsx)(E,{item:e,isContextMenu:d,id:L(e.id),onClick:()=>H(e),onDel:$,onVertical:U,onHorizontal:q,children:(0,C.jsxs)(g.A,{children:[u&&(0,C.jsx)("div",{children:j(u)}),(0,C.jsx)(p.A,{disabled:!s,checked:a,onChange:e=>P(e,w)})]})});case A.y1.SINGLE_SELECT:return(0,C.jsx)(E,{item:e,isContextMenu:d,id:L(e.id),onClick:()=>H(e),onDel:$,onVertical:U,onHorizontal:q,children:(0,C.jsxs)(g.A,{children:[u&&(0,C.jsx)("div",{children:j(u)}),(0,C.jsx)(y.A,{disabled:!s,style:{minWidth:"10vw"},value:a,options:k.map((e=>({value:e,label:e}))),onChange:e=>P(e,w,A.y1.SINGLE_SELECT,{id:w,type:o,value:a,key:I})})]})});case A.y1.LABEL:return(0,C.jsx)(E,{item:e,isContextMenu:d,id:L(e.id),onClick:()=>H(e),onDel:$,onVertical:U,onHorizontal:q,children:(0,C.jsx)("div",{className:"layout-content",children:(0,C.jsx)("div",{children:j(a)})})});case A.y1.TABLE:const i=k.map(((e,i)=>{let{title:l,renderType:d}=e;return{title:l,dataIndex:"name",width:"5vw",render:(e,l,r)=>(0,C.jsx)(_.zs,{className:(null===N||void 0===N?void 0:N.recordIndex)===r?"select":"",onClick:()=>{F({recordIndex:r,id:w,value:a})},children:K({renderType:d,value:l[i],onChange:e=>((e,i,l,d)=>{const{value:r,id:o}=i,s=t()(r);s[d][l]=e,m({...n,display:n.display.map((e=>e.id===o?{...e,value:s}:e))})})(e,{value:a,id:w},i,r)})})}})),l=()=>(0,C.jsx)(_.ne,{children:(0,C.jsxs)(g.A,{children:[(0,C.jsx)(h.Ay,{onClick:e=>((e,i)=>{const{value:l,options:d,id:r}=i,o=t()(l);o.push(Array.from({length:d.length},(()=>0))),m({...n,display:n.display.map((e=>e.id===r?{...e,value:o}:e))})})(0,{id:w,options:k,value:a}),children:j("\u65b0\u589e\u4e00\u884c")}),(0,C.jsx)(h.Ay,{onClick:e=>{a.length<=1?c.Ay.error(j("\u81f3\u5c11\u4fdd\u7559\u4e00\u884c")):N?(G(null===N||void 0===N?void 0:N.recordIndex,{id:null===N||void 0===N?void 0:N.id,value:null===N||void 0===N?void 0:N.value}),F()):(G(a.length-1,{id:w,value:a}),F())},danger:!0,children:j("\u5220\u9664\u4e00\u884c")})]})});return(0,C.jsx)(E,{item:e,isContextMenu:d,id:L(e.id),onClick:()=>H(e),onDel:$,onVertical:U,onHorizontal:q,children:(0,C.jsxs)("div",{style:{width:"100%",display:"flex"},children:[(0,C.jsx)("div",{style:{marginRight:(0,x.Im)(u)?"0":"12px"},children:u&&(0,C.jsx)("div",{className:"label",children:j(u)})}),(0,C.jsx)("div",{style:{flex:"1",overflow:"hidden"},children:(0,C.jsx)(_.KI,{children:(0,C.jsx)(f.A,{columns:i,dataSource:a,title:l,scroll:{x:!0,y:"20vh"}})})})]})});default:return(0,C.jsx)(E,{item:e,isContextMenu:d,id:L(e.id),onClick:()=>H(e),onDel:$,onVertical:U,onHorizontal:q})}},Z=(e,i,n)=>e.map((e=>{const l=e;return l.id===i?(l.sizes=n,l):(l.children&&l.children.length>0&&(l.children=Z(l.children,i,n)),l)})),X=e=>{const i=e;if(null===i||void 0===i||!i.children||null===i||void 0===i||!i.children.length)return i&&(i.view=W(i)),i;const n=()=>{var e;const n={...k.current.layout,view:null,children:Z(null===(e=k.current.layout)||void 0===e?void 0:e.children,null===i||void 0===i?void 0:i.id,D.current)};k.current={...k.current,layout:n},m({...k.current,layout:n})},d=(e,n,l)=>{D.current=l,i.sizes=l},t=e=>({gridColumn:e}),r=e=>({gridRow:e});return i.direction===a.oM.VER?i.view=(0,C.jsx)(o.default,{sizes:i.sizes,id:i.id,onDragEnd:n,onDrag:d,render:e=>{let{getGridProps:n,getGutterProps:d}=e;return(0,C.jsx)("div",{className:"grid-content "+(i.children.length>1?" grid-col":""),...n(),children:i.children.map(((e,i)=>(0,C.jsxs)(l.Fragment,{children:[X(e).view,i%2===0&&(0,C.jsx)("div",{className:"gutter-col",style:t(i+2),...d("column",i+1)})]},e.id)))})}}):i.view=(0,C.jsx)(s.default,{sizes:i.sizes,id:i.id,onDragEnd:n,onDrag:d,render:e=>{let{getGridProps:n,getGutterProps:d}=e;return(0,C.jsx)("div",{className:"grid-content "+(i.children.length>1?"grid-row":""),...n(),children:i.children.map(((e,i)=>(0,C.jsxs)(l.Fragment,{children:[X(e).view,i%2===0&&(0,C.jsx)("div",{className:"gutter-row",style:r(i+2),...d("row",i+1)})]},e.id)))})}}),i};(0,l.useImperativeHandle)(i,(()=>({getSelectedId:T,getDomSelectedId:B})));const J=X(z);return null===J||void 0===J?void 0:J.view},z=(0,l.forwardRef)(D)}}]);
//# sourceMappingURL=53.23b1c05f.chunk.js.map