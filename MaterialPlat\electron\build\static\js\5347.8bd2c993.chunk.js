"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[5347],{75347:(e,n,t)=>{t.r(n),t.d(n,{default:()=>w});var o=t(65043),l=t(16569),r=t(26851),i=t(95206),d=t(89330),s=t(86178),a=t.n(s),c=t(11238),u=t(80231),p=t(67208),f=t(74117);function v(e){for(var n=new ArrayBuffer(e.length),t=new Uint8Array(n),o=0;o!==e.length;++o)t[o]=255&e.charCodeAt(o);return n}var g=t(81143);const m=g.Ay.div`
background: #FFFFFF;
    height: 100%;
    width: 100%;
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: center;
`,x=g.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }

`;var h=t(70579);const y=e=>{let{domId:n,layoutConfig:t}=e;return(0,h.jsx)(x,{children:(0,h.jsx)(u.A,{domId:n,layoutConfig:t})})},b="input_",j="result_",C="sampleParam_",w=e=>{let{id:n,layoutConfig:t}=e;const{t:s}=(0,f.Bd)(),[u,g]=(0,o.useState)(),x=e=>{const n=new FileReader;n.onload=e=>{const n=e.target.result,t=c.LF(n,{type:"binary",cellStyles:!0});g(t),console.log(t)},n.readAsBinaryString(e)};return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)(m,{children:[(0,h.jsx)(r.A,{accept:".xml,.xlsx",maxCount:1,beforeUpload:e=>(x(e),!1),showUploadList:u&&{showRemoveIcon:!1},children:(0,h.jsx)(i.Ay,{type:"primary",children:s(u?"\u66f4\u65b0\u6a21\u677f\u62a5\u8868":"\u4e0a\u4f20\u6a21\u677f\u62a5\u8868")})}),(0,h.jsx)(i.Ay,{icon:(0,h.jsx)(d.A,{}),onClick:async()=>{if(!u)return void l.Ay.error(s("\u8bf7\u5148\u4e0a\u4f20\u62a5\u8868\u6a21\u677f"));const e=await(async()=>{const{Sheets:e}=u,n=[],t={},o={};Object.entries(e).forEach((e=>{let[l,r]=e;const i=c.Wp.sheet_to_csv(r).split("\n"),d=i.findIndex((e=>-1!==e.indexOf("sampleCode")));if(o[l]=d,-1!==d){const e=i[d];t[l]=e.split(",")}Object.entries(r).forEach((e=>{let[t,o]=e;if(-1===t.indexOf("!")){const{v:e,t:t,w:l}=o;"s"===t&&0===e.indexOf(b)&&n.push(e)}}))}));const[{value:l},{value:r},{value:i}]=await Promise.allSettled([(0,p.f3x)({codes:n}),(0,p.FYB)(),(0,p.qFo)()]),d=Object.fromEntries(Object.entries(e).map((e=>{let n,[d,s]=e;if(t[d]){const e=Object.entries(r).map((e=>{let[n,o]=e;return t[d].map((e=>"sampleCode"===e?n||"":0===e.indexOf(j)?i[n]?i[n][e]:"":0===e.indexOf(C)&&o[e]?o[e].value+o[e].units:""))}));n=t[d].map(((e,n)=>{var t;const l=`${String.fromCharCode(65+n)+(o[d]+1)}`;return null===(t=s[l])||void 0===t?void 0:t.s})),c.Wp.sheet_add_aoa(s,e,{origin:o[d]||-1})}return[d,Object.fromEntries(Object.entries(s).map((e=>{let[t,r]=e,i={...r};if(-1===t.indexOf("!")){var s,a,c,u;const e=Number(t.replace(/([A-Z])/g,"")),r=t.replace(/([0-9])/g,"").charCodeAt();n&&e>=o[d]&&(i.s=n[r-65]);const{v:j,t:C,w:w,s:F}=i;var p,f,v,g,m,x,h,y;return"000000"===(null===F||void 0===F||null===(s=F.fill)||void 0===s||null===(a=s.fgColor)||void 0===a?void 0:a.rgb)&&delete F.fill.fgColor,"FFFFFF"===(null===F||void 0===F||null===(c=F.fill)||void 0===c||null===(u=c.fgColor)||void 0===u?void 0:u.rgb)&&(F.fill.fgColor.rgb="000000"),"s"===C&&-1!==j.indexOf("&#10;")&&(console.log(j),i={...i,v:(null===(p=i)||void 0===p||null===(f=p.v)||void 0===f?void 0:f.replace(/&#10;/g,"\n"))||"",r:(null===(v=i)||void 0===v||null===(g=v.r)||void 0===g?void 0:g.replace(/&#10;/g,"\n"))||"",h:(null===(m=i)||void 0===m||null===(x=m.h)||void 0===x?void 0:x.replace(/&#10;/g,"\n"))||"",w:(null===(h=i)||void 0===h||null===(y=h.w)||void 0===y?void 0:y.replace(/&#10;/g,"\n"))||""}),"s"===C&&0===j.indexOf(b)?[t,{...i,v:l[j]||"",w:l[j]||""}]:[t,i]}return[t,r]})))]})));return console.log(d),{...u,Sheets:d}})();g(null);const n=c.M9(e,{bookType:"xlsx",bookSST:!1,type:"binary"});((e,n)=>{const t=window.URL.createObjectURL(e),o=document.createElement("a");o.style.display="none",o.href=t,o.setAttribute("download",n),document.body.appendChild(o),o.click(),document.body.removeChild(o)})(new Blob([v(n)],{type:"application/octet-stream"}),`\u7ed3\u679c\u62a5\u8868${a()().format("YYYY-MM-DD HH:mm:ss")}.xlsx`)},children:s("\u4e0b\u8f7d\u62a5\u8868")})]}),(0,h.jsx)(y,{domId:n,layoutConfig:t})]})}}}]);
//# sourceMappingURL=5347.8bd2c993.chunk.js.map