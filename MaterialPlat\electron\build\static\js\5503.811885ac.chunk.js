"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[5503,5689],{45503:(e,i,n)=>{n.r(i),n.d(i,{default:()=>a});n(65043);var l=n(78070),t=n(70579);const a=e=>(0,t.jsx)(l.default,{...e,isInput:!0})},57458:(e,i,n)=>{n.d(i,{A:()=>r});n(65043);var l=n(36497),t=n(80077),a=n(74117),s=n(70579);const r=e=>{const i=(0,t.d4)((e=>e.template.actionList)),{t:n}=(0,a.Bd)();return(0,s.jsx)(l.A,{fieldNames:{label:"action_name",value:"action_id"},options:null===i||void 0===i?void 0:i.map((e=>({...e,label:n(e.label)}))),...e})}},78070:(e,i,n)=>{n.r(i),n.d(i,{default:()=>S});var l=n(65043),t=n(80231),a=n(97320),s=n(36990),r=n(72295),d=n(16090),c=n(74117),o=n(81143);const u=o.Ay.div`
    width: 100%;
    height: 100%;
    
    .container{
        width: 100%;
        height: 100%;
        background: #fff;

        display: grid;
        grid-template-rows: ${e=>{let{row:i}=e;return`repeat(${i},1fr)`}};
        grid-template-columns: ${e=>{let{column:i}=e;return`repeat(${i},1fr)`}};
        gap:8px;
    }
`,p=o.Ay.div`
    overflow: hidden;

    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;

    .control-circle{
        width: 50px;
        aspect-ratio: 1; 
        display: flex;
        align-items: center;
        justify-content: center;
        
        .outside-circle{
            cursor: pointer;
            width: 80%;
            aspect-ratio: 1; 
            border-radius: 50%;
            border:1px solid #999 ;

            display: flex;
            align-items: center;
            justify-content: center;

            .inside-circle{
                width: 80%;
                aspect-ratio: 1; 
                border-radius: 50%;
                border:1px solid #999 ;

                background-color: ${e=>{let{open:i}=e;return i?"rgb(53,213,25)":"#ccc"}};
            }
        }
    }
`;var h=n(70579);const x=e=>{let{name:i,dataSource:n,disabled:t,onAfterClick:a}=e;const{updateInputVariableValue:d}=(0,r.A)(),[o,u]=(0,l.useState)(!1),{t:x}=(0,c.Bd)();(0,s.A)({code:n,callback:e=>{u(e)}});return(0,h.jsxs)(p,{open:o,children:[(0,h.jsx)("div",{className:"control-circle",children:(0,h.jsx)("div",{className:"outside-circle",onClick:async()=>{t||(n&&await d({code:n,value:!o}),await a(!o))},children:(0,h.jsx)("div",{className:"inside-circle"})})}),(0,h.jsx)("div",{className:"control-name",children:x(i)})]})},v=e=>{let{config:i={},isInput:n}=e;const{row:t=0,column:a=0,controlList:c=[],validationVariable:o,bitNumberVariable:p,valueVariable:v,changeAction:m}=i,{updateInputVariableValue:j}=(0,r.A)(),{startAction:g}=(0,d.A)(),[b,y]=(0,l.useState)(!1);(0,s.A)({code:n?null:o,callback:e=>{y(!e)}});return(0,h.jsx)(u,{row:t,column:a,children:(0,h.jsx)("div",{className:"container",title:null===i||void 0===i?void 0:i.description,children:new Array(t*a).fill(0).map(((e,i)=>{var l,t;return null!==c&&void 0!==c&&c[i]?(0,h.jsx)(x,{disabled:n||b,name:null===c||void 0===c||null===(l=c[i])||void 0===l?void 0:l.name,dataSource:null===c||void 0===c||null===(t=c[i])||void 0===t?void 0:t.dataSource,onAfterClick:e=>(async(e,i)=>{p&&await j({code:p,value:e}),v&&await j({code:v,value:i}),m&&await g({action_id:String(m)})})(i,e)}):(0,h.jsx)(h.Fragment,{})}))})})};var m=n(25055),j=n(47419),g=n(11645),b=n(97914),y=n(83720),A=n(75440),f=n(67998),w=n(57458),I=n(56543);const k=o.Ay.div`
    .list-header{
        display: flex;
        justify-content: space-between;
        
        &>div{
            cursor: pointer;
        }
        
        .list-func{
            display: flex;
            gap: 8px;
        }
    }

    .list-container{
        width: 100%;
        height: 200px;
        border: 1px solid #ccc;
        overflow-y: scroll;
        padding: 3px;

        .list-wrapper{

            .list-item{
                height: 16px;
                line-height: 16px;
                cursor: pointer;
                margin-top: 2px;
                &:first-child{
                    margin-top: 0;
                }
            }

            .list-item-selected{
                background: #ccc;
            }

        }
    }
`,C=e=>{let{id:i,value:n=[],onChange:l,optIndex:t,setOptIndex:a}=e;const{t:s}=(0,c.Bd)(),r=e=>"list-item "+(t===e?"list-item-selected":"");return(0,h.jsxs)(k,{children:[(0,h.jsxs)("div",{className:"list-header",children:[(0,h.jsx)("div",{children:s("\u63a7\u4ef6\u5217\u8868")}),(0,h.jsxs)("div",{className:"list-func",children:[(0,h.jsxs)("div",{onClick:()=>{l([...n,{key:(new Date).getTime(),name:n.length+1,dataSource:null}])},children:["+",s("\u6dfb\u52a0")]}),(0,h.jsxs)("div",{onClick:()=>{const e=n.filter((e=>{var i;return e.key!==(null===(i=n[t])||void 0===i?void 0:i.key)}));t>=e.length&&a(null),l(e)},children:["-",s("\u5220\u9664")]})]})]}),(0,h.jsx)("div",{className:"list-container",children:(0,h.jsx)("div",{className:"list-wrapper",children:null===n||void 0===n?void 0:n.map(((e,i)=>(0,h.jsx)("div",{className:r(i),onClick:()=>a(i),children:null===e||void 0===e?void 0:e.name},i)))})})]})},{Item:N}=m.A,_=e=>{let{open:i,setOpen:n,config:t,updateConfig:a,isInput:s}=e;const{t:r}=(0,c.Bd)(),[d]=m.A.useForm(),[o,u]=(0,l.useState)(null);(0,l.useEffect)((()=>{d.setFieldsValue(t)}),[t]);return(0,h.jsx)(A.A,{open:i,title:r("\u63a7\u4ef6\u5c5e\u6027"),maskClosable:!1,width:"50vw",onOk:async()=>{try{const e=await d.validateFields();n(!1),a(e)}catch(e){console.log("err",e)}},onCancel:()=>{n(!1)},children:(0,h.jsxs)(m.A,{form:d,layout:"vertical",children:[(0,h.jsxs)(j.A,{gutter:16,children:[(0,h.jsx)(g.A,{span:12,children:(0,h.jsx)(N,{label:r("\u884c\u6570"),name:"row",children:(0,h.jsx)(b.A,{style:{width:"100%"},min:0})})}),(0,h.jsx)(g.A,{span:12,children:(0,h.jsx)(N,{label:r("\u5217\u6570"),name:"column",children:(0,h.jsx)(b.A,{style:{width:"100%"},min:0})})})]}),!s&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)(j.A,{gutter:16,children:[(0,h.jsx)(g.A,{span:12,children:(0,h.jsx)(N,{label:r("\u662f\u5426\u53ef\u7f16\u8f91"),name:"validationVariable",children:(0,h.jsx)(f.A,{inputVariableType:null===I.ps||void 0===I.ps?void 0:I.ps.\u5e03\u5c14\u578b})})}),(0,h.jsx)(g.A,{span:12,children:(0,h.jsx)(N,{label:r("\u7ed1\u5b9a\u4f4d\u6570\u53d8\u91cf"),name:"bitNumberVariable",children:(0,h.jsx)(f.A,{inputVariableType:null===I.ps||void 0===I.ps?void 0:I.ps.\u6570\u5b57\u578b})})})]}),(0,h.jsxs)(j.A,{gutter:16,children:[(0,h.jsx)(g.A,{span:12,children:(0,h.jsx)(N,{label:r("\u7ed1\u5b9a\u503c\u53d8\u91cf"),name:"valueVariable",children:(0,h.jsx)(f.A,{inputVariableType:null===I.ps||void 0===I.ps?void 0:I.ps.\u5e03\u5c14\u578b})})}),(0,h.jsx)(g.A,{span:12,children:(0,h.jsx)(N,{label:r("\u7ed1\u5b9a\u52a8\u4f5c"),name:"changeAction",children:(0,h.jsx)(w.A,{})})})]})]}),(0,h.jsx)(j.A,{gutter:16,children:(0,h.jsx)(g.A,{span:12,children:(0,h.jsx)(N,{label:r("\u63cf\u8ff0"),name:"description",children:(0,h.jsx)(y.A,{})})})}),(0,h.jsxs)(j.A,{style:{padding:"20px",border:"1px solid #ccc"},gutter:16,children:[(0,h.jsx)(g.A,{span:12,children:(0,h.jsx)(N,{name:"controlList",children:(0,h.jsx)(C,{optIndex:o,setOptIndex:u})})}),null!==o&&(0,h.jsxs)(g.A,{span:12,children:[(0,h.jsx)(N,{label:r("\u663e\u793a\u540d\u79f0"),name:["controlList",o,"name"],children:(0,h.jsx)(y.A,{})}),(0,h.jsx)(N,{label:r("\u6570\u636e\u6e90"),name:["controlList",o,"dataSource"],children:(0,h.jsx)(f.A,{inputVariableType:null===I.ps||void 0===I.ps?void 0:I.ps.\u5e03\u5c14\u578b})})]})]})]})})},V=o.Ay.div`
    width: 100%;
    height: 100%;
    background: #fff;
`,S=e=>{let{item:i,id:n,layoutConfig:s,isInput:r}=e;const{updateLayoutItem:d}=(0,a.A)(),[c,o]=(0,l.useState)(!1),[u,p]=(0,l.useState)();(0,l.useEffect)((()=>{if(null!==i&&void 0!==i&&i.data_source)try{p(JSON.parse(null===i||void 0===i?void 0:i.data_source))}catch(e){console.log("err",e)}}),[null===i||void 0===i?void 0:i.data_source]);return(0,h.jsxs)(V,{children:[(0,h.jsx)(v,{config:u,isInput:r}),c&&(0,h.jsx)(_,{isInput:r,open:c,setOpen:o,config:u,updateConfig:e=>{p(e),d({layout:s,newItem:{...i,data_source:JSON.stringify(e)}})}}),(0,h.jsx)(t.A,{domId:n,layoutConfig:s,children:(0,h.jsx)("div",{className:"unique-content",onClick:()=>o(!0),children:r?"\u7f16\u8f91\u6570\u5b57IO-input":"\u7f16\u8f91\u6570\u5b57IO-output"})})]})}},97320:(e,i,n)=>{n.d(i,{A:()=>d});n(65043);var l=n(80077),t=n(84856),a=n(67208),s=n(14463),r=n(41086);const d=()=>{const e=(0,l.wA)(),{saveLayout:i}=(0,t.A)(),n=async i=>{let{layout:n,newItem:l}=i;const t={...n,children:d(n.children,l)},[c]=await(0,a.PXE)({binder_ids:[null===n||void 0===n?void 0:n.binder_id]});await(0,a.Kv3)({binders:[{...c,layout:(0,r.gT)(t,null===n||void 0===n?void 0:n.binder_id)}]}),e({type:s.EH,param:c.binder_id})},d=(e,i)=>e.map((e=>e.id===i.id?i:e.children&&e.children.length>0?{...e,children:d(e.children,i)}:e)),c=async e=>{let{layout:n,newItem:l}=e;const t={...n,children:d(n.children,l)};await i(t)};return{updateLayoutItem:async e=>{let{layout:i,newItem:l}=e;null!==i&&void 0!==i&&i.binder_id?(console.log("\u754c\u9762\u5185\u5bb9-tab"),await n({layout:i,newItem:l})):(console.log("\u754c\u9762\u5185\u5bb9-\u4e3b\u7a97\u53e3"),await c({layout:i,newItem:l}))}}}}}]);
//# sourceMappingURL=5503.811885ac.chunk.js.map