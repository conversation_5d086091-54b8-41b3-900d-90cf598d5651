"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[5575],{33613:(e,i,d)=>{d.d(i,{P:()=>t,T:()=>n});const n={"\u6a2a\u5411":"row","\u7eb5\u5411":"column"},t={"\u5de6":"flex-start","\u4e2d":"center","\u53f3":"flex-end"}},75433:(e,i,d)=>{d.d(i,{A:()=>E});var n=d(65043),t=d(6051),l=d(74117),o=d(4554),r=d(80077),a=(d(54962),d(94376)),c=d(16569),s=d(36497),u=d(32513),v=d(14463),p=d(56434),h=d.n(p),y=d(18650),g=d(8237),f=d(84),x=d(45303),m=d(67299),_=d(81143),w=d(68374);const j=_.Ay.div`
        .head-layout {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .head-left{
                display: flex;
                flex-direction: column;

                .description {
                    display: flex;
                    align-items: center;
                    .ant-input {
                        width: ${(0,w.D0)("300px")};
                    }
                }
                
            }



            .head-right  {
                display: flex;
                height: 3vh;
                justify-content: flex-end;
                align-items: center;
                gap: 6px;
                .head-func {
                    cursor: pointer;
                    width: ${(0,w.D0)("60px")};
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    font-size: ${(0,w.D0)("15px")};
                    font-weight: 400;
                    color: rgba(0,0,0,0.45);
                    line-height: 14px;
                    >img {
                        width: ${(0,w.D0)("25px")};
                        height: ${(0,w.D0)("25px")};
                        margin-bottom: 1px;
                    }
                }
            }
        }

`;var b=d(70579);const k=e=>{let{config:i,expandedLeft:d,callBack:t}=e;const{t:o}=(0,l.Bd)(),a=(0,r.wA)(),{subContextMenuId:p,subCurrentDomId:_}=(0,m.A)(),{openDialog:w}=(0,f.A)(),{control:k,currentDomId:A,tabLayout:T,operationType:C}=(0,r.d4)((e=>e.split)),E=(0,r.d4)((e=>e.template.dialogs)),N=(0,r.d4)((e=>e.template.defaultPageId)),[I,D]=(0,n.useState)(),[O,$]=(0,n.useState)({type:g.oM.HOR,directionType:g.fJ.UNSHIFT});(0,n.useEffect)((()=>{D(h()(i))}),[i]);const R=(0,n.useRef)(),L=(0,n.useRef)({type:g.oM.HOR,directionType:g.fJ.UNSHIFT});(0,n.useEffect)((()=>{k&&(R.current=k,J(!0),a({type:v.Fk,param:null}))}),[k]),(0,n.useEffect)((()=>{C&&S()}),[C]);const S=()=>{switch(null===C||void 0===C?void 0:C.type){case g.P.CONTENT:U();break;case g.P.DLE:H();break;case g.P.VERTICAL:L.current={type:g.oM.HOR,directionType:C.directionType},$({type:g.oM.HOR,directionType:C.directionType}),V({title:"",key:"empty-3",widget_id:"3"});break;case g.P.HORIZONTAL:L.current={type:g.oM.VER,directionType:C.directionType},$({type:g.oM.VER,directionType:C.directionType}),V({title:"",key:"empty-3",widget_id:"3"})}a({type:v.ad,param:null})},M=(e,i)=>e.filter((e=>{var d;const n=e;return n.id!==i&&(null!==n&&void 0!==n&&n.children&&(null===n||void 0===n||null===(d=n.children)||void 0===d?void 0:d.length)>0&&(n.children=M(n.children,i)),!0)})),P=(e,i)=>e.map((e=>{var d,n;if(e.id===i&&1===(null===e||void 0===e||null===(d=e.children)||void 0===d?void 0:d.length)){const i=e.children[0];e.type=null===i||void 0===i?void 0:i.type,e.name=null===i||void 0===i?void 0:i.name,e.widget_data_source=null===i||void 0===i?void 0:i.widget_data_source,e.widget_id=null===i||void 0===i?void 0:i.widget_id,e.binder_id=null===i||void 0===i?void 0:i.binder_id,e.direction=null===i||void 0===i?void 0:i.direction,e.layout_id=null===i||void 0===i?void 0:i.layout_id,e.data_source=null===i||void 0===i?void 0:i.data_source,e.children=null===i||void 0===i?void 0:i.children}return null!==e&&void 0!==e&&e.children&&(null===e||void 0===e||null===(n=e.children)||void 0===n?void 0:n.length)>0&&(e.children=P(e.children,i)),e}));function B(e,i){for(let d=0;d<e.length;d+=1){if(e[d].id===i)return e[d];if(e[d].children&&e[d].children.length>0){const n=B(e[d].children,i);if(n)return n}}return null}const H=()=>{var e,i,d;const n=A,l=null!==n&&void 0!==n&&n.startsWith("edit-")?null===n||void 0===n?void 0:n.substring(5):n;if(!A)return c.Ay.error(o("\u8bf7\u9009\u62e9")),!1;if(0===(null===I||void 0===I?void 0:I.children.length)||0===(null===I||void 0===I||null===(e=I.children[0])||void 0===e||null===(i=e.children)||void 0===i?void 0:i.length))return c.Ay.error(o("\u6700\u540e\u4e00\u7ea7\u4e0d\u80fd\u5220\u9664")),!1;const r=B([I],n),a=null===(d=M([I],l))||void 0===d?void 0:d[0];return a.children=P(a.children,r.parent_id),D({...a}),t(a),!0},V=e=>{A?(R.current=e,J(!1),_()):c.Ay.error(o("\u8bf7\u9009\u62e9"))},z=()=>{const{title:e,key:i}=R.current;if(null!==i&&void 0!==i&&i.startsWith("dialog-")){var d;const e=null===i||void 0===i?void 0:i.substring(7);return null===(d=E.find((i=>i.dialog_id===e)))||void 0===d?void 0:d.dialog_name}return e},F=e=>{let{item:i,name:d,type:n,view:t,widget_id:l,data_source:o,page_id:r,children:a=[]}=e;i.direction=L.current.type;const c={id:`${i.id}-${i.children.length+1}-T${(new Date).getTime()}`,name:d,type:n,sizes:"1fr 4px 1fr",direction:L.current.type,view:t,widget_id:l,is_lock:!1,page_id:r,parent_id:i.id,children:[],data_source:o};a.forEach((e=>{F({item:c,name:e.name,type:e.type,view:e.view,page_id:N,widget_id:e.widget_id,data_source:null===e||void 0===e?void 0:e.data_source,children:e.children})})),L.current.directionType===g.fJ.UNSHIFT?i.children.unshift(c):i.children.push(c)},W=(e,i,d)=>e.map((e=>{const n=e;if(n.id===i){const{key:e,widget_id:i}=R.current,[t]=null===e||void 0===e?void 0:e.split("-");if(d)n.type=t,n.name=z(),n.widget_id=i,n.children=[],null!==e&&void 0!==e&&e.startsWith("dialog-")?n.data_source=JSON.stringify({dialog_id:null===e||void 0===e?void 0:e.substring(7)}):n.data_source=null;else{if(0===n.children.length)F({item:n,name:n.name,type:n.type,view:n.view,page_id:N,widget_id:n.widget_id,data_source:null===n||void 0===n?void 0:n.data_source});else{const e=n.children;n.children=[],F({item:n,name:n.name,type:n.type,view:n.view,page_id:N,widget_id:n.widget_id,data_source:null===n||void 0===n?void 0:n.data_source,children:e})}F({item:n,name:z(),type:t,view:null,widget_id:i,page_id:N,data_source:null===n||void 0===n?void 0:n.data_source})}return n.type=t,n.widget_id=i,n.name=z(),n.sizes="1fr 4px 1fr",t===g.rX.CONTENT_SPLIT_EMPTY&&(n.sizes="1fr 4px 1fr"),n}return n.children&&n.children.length>0&&(n.children=W(n.children,i,d)),n})),J=e=>{const i=A,d=document.getElementById(`edit-${i}`);if(L.current.type===g.oM.VER&&(null===d||void 0===d?void 0:d.clientHeight)<=50)return c.Ay.warning(o("\u6a2a\u5207\u9ad8\u5ea6\u4e0d\u80fd\u5c0f\u4e8e50px")),!1;if(L.current.type===g.oM.HOR&&(null===d||void 0===d?void 0:d.clientWidth)<=50)return c.Ay.warning(o("\u7ad6\u5207\u5bbd\u5ea6\u4e0d\u80fd\u5c0f\u4e8e50px")),!1;const n=null!==i&&void 0!==i&&i.startsWith("edit-")?null===i||void 0===i?void 0:i.substring(5):i;return null!==I&&void 0!==I&&I.children&&(I.children=W(null===I||void 0===I?void 0:I.children,n,e),D({...I}),t({...I})),!0},U=()=>A?(a({type:v.NS,param:i}),w({type:x.TS}),T||p(null),!0):(c.Ay.error(o("\u8bf7\u9009\u62e9")),!1);return(0,b.jsx)(j,{children:(0,b.jsxs)("div",{className:"head-layout",children:[d,(0,b.jsxs)("div",{className:"head-right",children:[(0,b.jsxs)("div",{className:"head-func",onClick:U,children:[(0,b.jsx)("img",{src:y.Q1,alt:""}),(0,b.jsx)("div",{children:o("\u5185\u5bb9")})]}),(0,b.jsxs)("div",{className:"head-func",onClick:H,children:[(0,b.jsx)("img",{src:y.Dk,alt:""}),(0,b.jsx)("div",{children:o("\u5220\u9664")})]}),(0,b.jsxs)("div",{className:"head-func",onClick:()=>V({title:"",key:"empty-3",widget_id:"3"}),children:[(0,b.jsx)("img",{src:y.Kp,alt:""}),(0,b.jsx)("div",{children:o("\u5206\u5272")})]}),(0,b.jsx)(s.A,{value:`${null===O||void 0===O?void 0:O.type}_${null===O||void 0===O?void 0:O.directionType}`,options:(0,g.Bd)({t:o}),onChange:(e,i)=>{L.current=i,$(i)}}),(0,b.jsx)(u.A,{checked:null===I||void 0===I?void 0:I.is_lock,onChange:e=>t({...I,is_lock:e.target.checked}),children:"\u9501\u5b9a"})]})]})})},A=_.Ay.div`
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 13px;

    .top {

    }

    .content {
        flex: 1;
    }
`,T=e=>{let{value:i,showOperation:d=!0,openContextMenu:t=!0,expandedLeft:o,onChange:c}=e;const{t:s}=(0,l.Bd)(),u=(0,r.wA)(),{viewConfigPanelLayout:p}=(0,r.d4)((e=>e.split));(0,n.useEffect)((()=>{p&&(h(p),u({type:v.Bo,param:null}))}),[p]);const h=e=>{c(e)};return(0,b.jsxs)(A,{children:[(0,b.jsx)("div",{className:"top",children:d&&(0,b.jsx)(k,{config:i,expandedLeft:o,callBack:e=>{c(e)}})}),(0,b.jsx)("div",{className:"content",children:i&&(0,b.jsx)(a.A,{config:i,isEdit:!0,onResize:h,isContextMenu:t})})]})},C=_.Ay.div`
    display: flex;
    justify-content: space-between;
    padding-left: ${(0,w.D0)("10px")};
    padding-right: ${(0,w.D0)("10px")};
    overflow-y: auto;

        .left-layout {
            padding: 8px;
            /* width: 46vw;  */
            height: 60vh;
            flex: 1;
            overflow: hidden;
        }
        .right-layout {
            display: flex;
            flex-direction: column;

            width: 9vw;
            .button{
                margin-top: ${(0,w.D0)("10px")};
            }
        }
`,E=e=>{let{value:i,expandedLeft:d,onOk:r,onCancel:a}=e;const{t:c}=(0,l.Bd)(),[s,u]=(0,n.useState)({});(0,n.useEffect)((()=>{u(i)}),[i]);return(0,b.jsxs)(C,{children:[(0,b.jsx)("div",{className:"left-layout",children:(0,b.jsx)(T,{value:s,expandedLeft:d,onChange:e=>{u(e)}})}),(0,b.jsx)("div",{className:"right-layout",children:(0,b.jsxs)(t.A,{direction:"vertical",children:[(0,b.jsx)(o.A,{block:!0,onClick:()=>{r(s)},children:c("\u786e\u5b9a")}),(0,b.jsx)(o.A,{block:!0,onClick:()=>{a()},children:c("\u53d6\u6d88")})]})})]})}},75859:(e,i,d)=>{d.d(i,{A:()=>x});var n=d(65043),t=d(6051),l=d(95206),o=d(74117),r=d(81143),a=d(80077),c=d(88359),s=d(51554),u=d(78178),v=d(56543),p=d(69581),h=d(754),y=d(70579);const g=r.Ay.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px;
    .label{
        margin-right: 10px;
    }

    .value-valve-box{
        word-break: break-all;
    }
    .button-right-box{
        white-space: nowrap;
    }
`,f=(e,i)=>{switch(i.type){case"OPEN_ADD":return{open:!0,mode:"add",editId:void 0};case"OPEN_EDIT":return{open:!0,mode:"edit",editId:i.editId};case"CLOSE":return{open:!1,mode:"add",editId:void 0};default:return e}},x=e=>{let{id:i,value:d,onChange:r,inputVariableType:x}=e;const m=(0,a.wA)(),{t:_}=(0,o.Bd)(),w=(0,n.useRef)(),[j,b]=(0,n.useReducer)(f,{open:!1,mode:"add",editId:void 0}),k=(0,p.A)(d);(0,n.useEffect)((()=>{k&&A(k)}),[k]);const A=e=>{if((null===e||void 0===e?void 0:e.variable_type)!==x)return void r();(0,h.B)("inputVariable","inputVariableMap").has(e.code)||r("")},T=e=>{const{id:i,code:d,variable_name:n,variable_type:t,name:l}=e;r(d)};return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)(g,{children:[(0,y.jsxs)("span",{className:"value-valve-box",title:`${_("\u7ed1\u5b9a\u53d8\u91cf")}:${null!==k&&void 0!==k&&k.name?null===k||void 0===k?void 0:k.name:""}`,children:[_("\u7ed1\u5b9a\u53d8\u91cf"),":",null===k||void 0===k?void 0:k.name]}),(0,y.jsx)("div",{className:"button-right-box",children:(0,y.jsxs)(t.A,{children:[(0,y.jsx)(l.Ay,{onClick:()=>{w.current.open({variableType:v.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x})},children:_("\u9009\u62e9")}),d?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(l.Ay,{onClick:()=>{b({type:"OPEN_EDIT",editId:null===k||void 0===k?void 0:k.id})},children:_("\u7f16\u8f91")}),(0,y.jsx)(l.Ay,{onClick:()=>r(""),children:_("\u89e3\u7ed1")})]}):(0,y.jsx)(l.Ay,{onClick:()=>{b({type:"OPEN_ADD"})},children:_("\u65b0\u5efa")})]})})]}),(0,y.jsx)(s.A,{ref:w,handleSelectedVariable:T}),(null===j||void 0===j?void 0:j.open)&&(0,y.jsx)(u.A,{variableType:x,modalIndex:0,open:null===j||void 0===j?void 0:j.open,mode:null===j||void 0===j?void 0:j.mode,editId:null===j||void 0===j?void 0:j.editId,onOk:async e=>{try{const i=await m((0,c.w)()),d=null===i||void 0===i?void 0:i.find((i=>i.code===e.code));d&&T(d),b({type:"CLOSE"})}catch(i){console.log("err",i)}},onCancel:()=>{b({type:"CLOSE"})}})]})}}}]);
//# sourceMappingURL=5575.f6376453.chunk.js.map