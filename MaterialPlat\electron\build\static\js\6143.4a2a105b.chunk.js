"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[6143],{56143:(i,e,l)=>{l.r(e),l.d(e,{default:()=>v});var n=l(65043),o=l(80077),r=l(19853),s=l.n(r),_=l(36950),a=l(21256),t=l(51238),d=l(72238),u=l(74448);var c=l(41234),g=l(68406),y=l(70579);const v=i=>{let{id:e,item:l,layoutConfig:r,isRightClick:v=!0}=i;const p=null!==l&&void 0!==l&&l.widget_data_source?JSON.parse(null===l||void 0===l?void 0:l.widget_data_source):null,m=(0,o.d4)((i=>{var e;return null===(e=i.staticCurve)||void 0===e?void 0:e.settingResList.find((i=>i.id===p))})),x=(0,o.d4)((i=>i.template.widgetData)),f=(0,n.useMemo)((()=>(0,_.Rm)(x,"widget_id",null===l||void 0===l?void 0:l.widget_id)),[l,x]),{editWidget:h}=(0,a.A)(),[k,b]=(0,n.useState)();(0,n.useEffect)((()=>{var i;if(null!==f&&void 0!==f&&f.data_source&&null!==f&&void 0!==f&&null!==(i=f.data_source)&&void 0!==i&&i.curveGroup)s()(k,null===f||void 0===f?void 0:f.data_source)||b(null===f||void 0===f?void 0:f.data_source);else{if(m){const i=(i=>{var e,l,n,o,r;if(!i)throw new Error("oldConfig is required");const s=i=>"boolean"===typeof i?i:1===i||"1"===i||0!==i&&"0"!==i&&Boolean(i),_=i=>({extend:"extend",not:"not",all:"all","data-range":"data-range","last-range":"last-range"}[i]||"not"),a=i=>({solid:"solid",dashed:"dashed",dotted:"dotted"}[i]||"solid"),c=(e,l,n,o)=>{const r=(0,u.C)({sourceType:o});if(null===e||void 0===e||!e[0]||null===l||void 0===l||!l[0])return r;const _=e[0],t={};return Object.keys(r).forEach((e=>{var o,d;t[e]={name:r[e].name,lines:[{isLine:s(_.line_open),lineType:a(_.line_style),lineThickness:_.line_thickness||1,isSign:s(_.sign_open),signType:_.sign_style||"o",signEach:s(_.sign_each),color:_.line_color||"#000000",code:l[0],isApply:s(i.apply_point_count),yUnit:(null===(o=i.y_units)||void 0===o?void 0:o.id)||"",id:`Y1\u8f74\u66f2\u7ebf\u7ec4-${e}-${l[0]}`,name:`Y1\u8f74\u66f2\u7ebf\u7ec4-${null===r||void 0===r||null===(d=r[e])||void 0===d?void 0:d.name}-${i.y_label||"Y\u8f74"}`,pointTags:n}]}})),t},g="\u8bd5\u6837"===i.display?d.xO.\u5355\u6570\u636e\u6e90:d.xO.\u591a\u6570\u636e\u6e90,y=(i=>{var e,l;return null!==i&&void 0!==i&&null!==(e=i.line_tags)&&void 0!==e&&null!==(l=e[0])&&void 0!==l&&l.array?i.line_tags[0].array.map((i=>({id:(0,t.A)(),color:i.color||"#000000",resultVariableId:i.id,isName:s(i.name_flag),isAll:s(i.all_flag),isChunk:s(i.chunk_flag),sampleCode:i.sample_code||"",isLine:s(i.line_flag),isSample:s(i.sample_flag),isVal:s(i.val_flag),isAbbr:s(i.abbr_flag)}))):[]})(i.line_config),v=null!==(p=i.block_config)&&void 0!==p&&p.block_tags?p.block_tags.map((i=>({id:i.block_id,color:i.color||"#000",isName:s(i.name_flag),isAll:s(i.all_flag),title:i.name||"",showTitle:!0,isChunk:s(i.chunk_flag),sampleCode:i.sample_code||"",isLine:!0,isSample:s(i.sample_flag),isVal:s(i.val_flag),isAbbr:s(i.abbr_flag),curveIndex:0,results:i.array||[]}))):[];var p;const m=c(i.curve_config,i.y_channel,y,g),x=c(i.curve_config,i.y2_channel,y,g);return{base:{isName:s(i.curve_nama_setting_enabl),name:i.curve_name||"\u66f2\u7ebf",sourceType:g,sourceInputCode:i.buffer_code||i.input_code||"",updateFreq:180,crossInputCode:""},curveGroup:{yAxis:{isEnable:!0,index:0,name:"Y1\u8f74\u66f2\u7ebf\u7ec4",xSignal:i.x_channel||"time",xUnit:(null===(e=i.x_units)||void 0===e?void 0:e.id)||"",ySignal:i.y_channel||[],curves:m},y2Axis:{isEnable:(null===(l=i.y2_channel)||void 0===l?void 0:l.length)>0,index:1,name:"Y2\u8f74\u66f2\u7ebf\u7ec4",xSignal:i.x_channel||"time",xUnit:(null===(n=i.x_units)||void 0===n?void 0:n.id)||"",ySignal:i.y2_channel||[],curves:x}},xAxis:{name:i.x_label||"X\u8f74",unit:(null===(o=i.x_units)||void 0===o?void 0:o.id)||"",proportionType:_(i.x_proportion),lowLimit:i.x_low_limit||0,upLimit:i.x_up_limit||10,lastRange:i.x_last_range||10,isLog:s(i.x_log),type:a(i.x_grid_line_style),thickness:i.x_thickness||2,color:i.x_color||"#000000",isGrid:s(i.x_grid_line),gridType:a(i.x_grid_line_style),gridThickness:i.x_grid_line_thickness||1,gridColor:i.x_grid_line_color||"#000000",isZeroLine:s(i.x_zero_line),zeroLineType:a(i.x_zero_line_style),zeroLineThickness:i.x_zero_line_thickness||1,zeroLineColor:i.x_zero_line_color||"#000000"},yAxis:{name:i.y_label||"Y\u8f74",proportionType:_(i.y_proportion),lowLimit:i.y_low_limit||0,upLimit:i.y_up_limit||10,lastRange:i.y_last_range||10,isLog:s(i.y_log),type:"solid",thickness:i.y_thickness||2,color:i.y_color||"#000000",isGrid:s(i.y_grid_line),gridType:a(i.y_grid_line_style),gridThickness:i.y_grid_line_thickness||1,gridColor:i.y_grid_line_color||"#000000",isZeroLine:s(i.y_zero_line),zeroLineType:a(i.y_zero_line_style),zeroLineThickness:i.y_zero_line_thickness||1,zeroLineColor:i.y_zero_line_color||"#000000"},y2Axis:{name:i.y2_label||"Y2\u8f74",proportionType:_(i.y2_proportion),lowLimit:i.y2_low_limit||0,upLimit:i.y2_up_limit||10,lastRange:i.y2_last_range||10,isLog:s(i.y2_log),type:"solid",thickness:i.y2_thickness||2,color:i.y2_color||"#000000",isGrid:s(i.y2_grid_line),gridType:a(i.y2_grid_line_style),gridThickness:i.y2_grid_line_thickness||1,gridColor:i.y2_grid_line_color||"#000000",isZeroLine:s(i.y2_zero_line),zeroLineType:a(i.y2_zero_line_style),zeroLineThickness:i.y2_zero_line_thickness||1,zeroLineColor:i.y2_zero_line_color||"#000000"},auxiliary:[],legend:{open:s(i.legend_flag)},pointTag:{open:s(i.line_tag_flag)},chunkTag:{open:s(i.block_tag_flag),list:v},marker:[],defineAxis:{isDefineAxis:s(i.coordinate_source_flag),inputCode:i.coordinate_source_input_code||"",source:(null===i||void 0===i||null===(r=i.coordinate_source_select)||void 0===r?void 0:r.map((i=>{var e,l,n,o,r,s,_;return{id:(0,t.A)(),label:null!==(e=null===i||void 0===i?void 0:i.label)&&void 0!==e?e:"",value:null===i||void 0===i?void 0:i.value,ySignal:null!==i&&void 0!==i&&i.y?[i.y]:[],yUnit:null===i||void 0===i||null===(l=i.y_units)||void 0===l?void 0:l.id,yName:null!==(n=null===i||void 0===i?void 0:i.y_label)&&void 0!==n?n:"",xSignal:null!==(o=null===i||void 0===i?void 0:i.x)&&void 0!==o?o:"",xUnit:null!==(r=null===i||void 0===i||null===(s=i.x_units)||void 0===s?void 0:s.id)&&void 0!==r?r:"",xName:null!==(_=null===i||void 0===i?void 0:i.x_label)&&void 0!==_?_:""}})))||[]}}})(m);console.log("\u914d\u7f6e",m,i),L(i)}b(g.c)}}),[f,m]);const L=i=>{b(i),h({...f,data_source:i})},C=(0,n.useRef)(),T=(0,n.useMemo)((()=>{if(!k)return null;const{compStatus:i,...e}=k;return s()(C.current,e)?C.current:(C.current=e,e)}),[k]),z=(0,n.useRef)(),w=(0,n.useMemo)((()=>{if(!k)return null;const{compStatus:i}=k;return s()(z.current,i)?z.current:(z.current=i,i)}),[k]);return(0,y.jsx)(c.A,{id:e,layoutConfig:r,config:T,compStatus:w,updateConfig:L,isBufferCurve:!0,isRightClick:v})}}}]);
//# sourceMappingURL=6143.4a2a105b.chunk.js.map