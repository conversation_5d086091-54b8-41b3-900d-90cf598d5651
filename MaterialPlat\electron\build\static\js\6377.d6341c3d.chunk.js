"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[6377],{55518:(e,a,i)=>{i.d(a,{A:()=>N});var l=i(65043),n=i(74117),t=i(56543),d=i(81143),o=i(68374),r=i(18650);const s=0,v="left";var b=i(70579);const c=d.Ay.div`
    width: ${(0,o.D0)("20px")};
    height: ${(0,o.D0)("20px")};
    background-size: ${(0,o.D0)("20px")} ${(0,o.D0)("20px")};
    background-image: url(${e=>{let{isConstant:a}=e;return a?r.fd:r.Mo}});
`,h=e=>{let{variable:a,onChange:i,disabled:l}=e;const{default_val:n,is_fx:t}=a;return!t||l?(0,b.jsx)(b.Fragment,{}):(0,b.jsx)(c,{isConstant:n.isConstant===s,onClick:()=>{i({...a,default_val:{...n,isConstant:0===(null===n||void 0===n?void 0:n.isConstant)?1:0}})}})};var u=i(95206),p=i(34154),x=i(67208),f=i(16090),g=i(36497),w=i(29977);const _=e=>{var a;let{disabled:i,variable:n,handleChange:t}=e;const d=(0,w.A)(),o=(0,l.useMemo)((()=>(null===d||void 0===d?void 0:d.filter((e=>e.variable_type===n.variable_type&&e.id!==n.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[d,n]);return(0,b.jsx)(g.A,{showSearch:!0,optionFilterProp:"labelName",disabled:i,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===n||void 0===n||null===(a=n.default_val)||void 0===a?void 0:a.variable_id,options:o,onChange:(e,a)=>t(a)})},m=e=>{let{disabled:a,content:i,buttonType:n,actionId:d,script:o}=e;const[r,s]=(0,l.useState)(!1),{startAction:v}=(0,f.A)(),c=()=>{n!==p.NR.\u52a8\u4f5c?n!==p.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{s(!0),await(0,x.O5k)({script:o,result_type:t.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{s(!1)}})():(async()=>{try{d&&(s(!0),await v({action_id:d}))}catch(e){console.log("err when handleSubmitAction",e)}finally{s(!1)}})()};return(0,b.jsx)(u.Ay,{loading:r,disabled:a,className:"button-width",onClick:()=>c(),children:i})},C=d.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:a}=e;return a?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,o.D0)("80px")};
        pointer-events: auto;
    }
`,j=e=>{let{disabled:a,variable:i,render:l,onChange:n,buttonShow:t}=e;const{button_variable_tab:d,default_val:o}=i;return(0,b.jsx)(C,{isLeft:(null===d||void 0===d?void 0:d.position)===v,children:1===o.isConstant?(0,b.jsx)(_,{disabled:a,variable:i,handleChange:e=>{n({...i,default_val:{...o,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,b.jsxs)(b.Fragment,{children:[t&&(null===d||void 0===d?void 0:d.isEnable)&&(0,b.jsx)(m,{...d,disabled:a}),l()]})})};var y=i(12624),S=i(32513);const k=e=>{let{variable:a,disabled:i=!1,onChange:l,usableShowType:n="checkbox"}=e;return null!==a&&void 0!==a&&a.is_enable?"switch"===n?(0,b.jsx)(y.A,{disabled:i,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{l({...a,is_feature:e})}}):(0,b.jsx)(S.A,{disabled:i,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{l({...a,is_feature:e.target.checked})}}):(0,b.jsx)(b.Fragment,{})},A=d.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,N=e=>{let{variable:a,disabled:i=!1,onChange:l,render:d,usableShow:o=!0,buttonShow:r=!0,fxShow:s=!0,nameShow:v=!0,usableShowType:c}=e;const{t:u}=(0,n.Bd)(),p=i||(a.variable_type===t.ps.\u5e03\u5c14\u578b?(null===a||void 0===a?void 0:a.is_enable)&&(null===a||void 0===a?void 0:a.is_feature):(null===a||void 0===a?void 0:a.is_enable)&&!(null!==a&&void 0!==a&&a.is_feature));return(0,b.jsxs)(A,{children:[(o||v)&&(0,b.jsx)("div",{className:"input-render-left",children:(0,b.jsxs)("div",{children:[o&&(0,b.jsx)(k,{variable:a,disabled:i,onChange:l,usableShowType:c}),v&&(0,b.jsx)("div",{className:"variable_name",children:u(a.name)})]})}),(0,b.jsxs)("div",{className:"input-render-right",children:[s&&(0,b.jsx)(h,{variable:a,onChange:l,disabled:p}),(0,b.jsx)(j,{disabled:p,variable:a,onChange:l,buttonShow:r,render:()=>d({innerDisabled:p})})]})]})}},56377:(e,a,i)=>{i.r(a),i.d(a,{default:()=>d});i(65043);var l=i(55518),n=i(70579);const t=e=>{let{variable:a}=e;const{format:i,content:l,fontSize:t,fore:d}=null===a||void 0===a?void 0:a.label_tab;return(0,n.jsx)("div",{style:{maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",alignItems:"center",fontSize:`${t}px`,color:d,wordBreak:"break-all",whiteSpace:"single"===i?"nowrap":"normal"},children:l})},d=e=>{let{variable:a,disabled:i,onChange:d}=e;return(0,n.jsx)(l.A,{variable:a,disabled:i,onChange:d,nameShow:!1,usableShow:!1,render:e=>{let{innerDisabled:i}=e;return(0,n.jsx)(t,{variable:a,disabled:i,onChange:e=>{d({...a,default_val:e})}})}})}}}]);
//# sourceMappingURL=6377.d6341c3d.chunk.js.map