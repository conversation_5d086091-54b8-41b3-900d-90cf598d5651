"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[7794],{7794:(e,n,t)=>{t.r(n),t.d(n,{default:()=>u});t(65043);var d=t(80231),i=t(81143);t(68374);const o=i.Ay.div`
    background: #FFFFFF;
    /* border-radius: 8px; */
    border: 1px solid #D7E2FF;
    display: flex;
    align-items: center;
    justify-content:center ;
    height: 100%;
    width: 100%;
    overflow: hidden;
`,r=i.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }

`;var a=t(70579);const s=e=>{let{domId:n,layoutConfig:t}=e;return(0,a.jsx)(r,{children:(0,a.jsx)(d.A,{domId:n,layoutConfig:t})})},u=e=>{let{id:n,layoutConfig:t,content:d=""}=e;return(0,a.jsxs)(o,{children:[d,(0,a.jsx)(s,{domId:n,layoutConfig:t})]})}}}]);
//# sourceMappingURL=7794.d97675a1.chunk.js.map