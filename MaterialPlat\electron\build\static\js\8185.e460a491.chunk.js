"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[8185],{51399:(t,e,i)=>{i.d(e,{A:()=>h});var s=i(58168),n=i(65043);const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M518.5 360.3a7.95 7.95 0 00-12.9 0l-178 246c-3.8 5.3 0 12.7 6.5 12.7H381c10.2 0 19.9-4.9 25.9-13.2L512 460.4l105.2 145.4c6 8.3 15.6 13.2 25.9 13.2H690c6.5 0 10.3-7.4 6.5-12.7l-178-246z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"up-circle",theme:"outlined"};var o=i(22172),a=function(t,e){return n.createElement(o.A,(0,s.A)({},t,{ref:e,icon:r}))};const h=n.forwardRef(a)},76399:(t,e,i)=>{i.d(e,{A:()=>E});var s=i(65043),n=i(98139),r=i.n(n),o=i(18574),a=i(11128),h=i(55391),l=i(12701),c=i(12366),u=i(35296),g=i(38525),d=i(61857),p=i(94414),f=i(78446),y=i(78855);const m=t=>{const{lineWidth:e,fontSizeIcon:i,calc:s}=t,n=t.fontSizeSM;return(0,f.oX)(t,{tagFontSize:n,tagLineHeight:(0,g.zA)(s(t.lineHeightSM).mul(n).equal()),tagIconSize:s(i).sub(s(e).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:t.defaultBg})},x=t=>({defaultBg:new d.Y(t.colorFillQuaternary).onBackground(t.colorBgContainer).toHexString(),defaultColor:t.colorText}),b=(0,y.OF)("Tag",(t=>(t=>{const{paddingXXS:e,lineWidth:i,tagPaddingHorizontal:s,componentCls:n,calc:r}=t,o=r(s).sub(i).equal(),a=r(e).sub(i).equal();return{[n]:Object.assign(Object.assign({},(0,p.dF)(t)),{display:"inline-block",height:"auto",marginInlineEnd:t.marginXS,paddingInline:o,fontSize:t.tagFontSize,lineHeight:t.tagLineHeight,whiteSpace:"nowrap",background:t.defaultBg,border:`${(0,g.zA)(t.lineWidth)} ${t.lineType} ${t.colorBorder}`,borderRadius:t.borderRadiusSM,opacity:1,transition:`all ${t.motionDurationMid}`,textAlign:"start",position:"relative",[`&${n}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:t.defaultColor},[`${n}-close-icon`]:{marginInlineStart:a,fontSize:t.tagIconSize,color:t.colorIcon,cursor:"pointer",transition:`all ${t.motionDurationMid}`,"&:hover":{color:t.colorTextHeading}},[`&${n}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${t.iconCls}-close, ${t.iconCls}-close:hover`]:{color:t.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${n}-checkable-checked):hover`]:{color:t.colorPrimary,backgroundColor:t.colorFillSecondary},"&:active, &-checked":{color:t.colorTextLightSolid},"&-checked":{backgroundColor:t.colorPrimary,"&:hover":{backgroundColor:t.colorPrimaryHover}},"&:active":{backgroundColor:t.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${t.iconCls} + span, > span + ${t.iconCls}`]:{marginInlineStart:o}}),[`${n}-borderless`]:{borderColor:"transparent",background:t.tagBorderlessBg}}})(m(t))),x);var S=function(t,e){var i={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(i[s]=t[s]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(t);n<s.length;n++)e.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(t,s[n])&&(i[s[n]]=t[s[n]])}return i};const w=s.forwardRef(((t,e)=>{const{prefixCls:i,style:n,className:o,checked:a,onChange:h,onClick:l}=t,c=S(t,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:g,tag:d}=s.useContext(u.QO),p=g("tag",i),[f,y,m]=b(p),x=r()(p,`${p}-checkable`,{[`${p}-checkable-checked`]:a},null===d||void 0===d?void 0:d.className,o,y,m);return f(s.createElement("span",Object.assign({},c,{ref:e,style:Object.assign(Object.assign({},n),null===d||void 0===d?void 0:d.style),className:x,onClick:t=>{null===h||void 0===h||h(!a),null===l||void 0===l||l(t)}})))})),v=w;var P=i(8835);const C=(0,y.bf)(["Tag","preset"],(t=>(t=>(0,P.A)(t,((e,i)=>{let{textColor:s,lightBorderColor:n,lightColor:r,darkColor:o}=i;return{[`${t.componentCls}${t.componentCls}-${e}`]:{color:s,background:r,borderColor:n,"&-inverse":{color:t.colorTextLightSolid,background:o,borderColor:o},[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(m(t))),x);const A=(t,e,i)=>{const s="string"!==typeof(n=i)?n:n.charAt(0).toUpperCase()+n.slice(1);var n;return{[`${t.componentCls}${t.componentCls}-${e}`]:{color:t[`color${i}`],background:t[`color${s}Bg`],borderColor:t[`color${s}Border`],[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}},T=(0,y.bf)(["Tag","status"],(t=>{const e=m(t);return[A(e,"success","Success"),A(e,"processing","Info"),A(e,"error","Error"),A(e,"warning","Warning")]}),x);var V=function(t,e){var i={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(i[s]=t[s]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(t);n<s.length;n++)e.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(t,s[n])&&(i[s[n]]=t[s[n]])}return i};const k=s.forwardRef(((t,e)=>{const{prefixCls:i,className:n,rootClassName:g,style:d,children:p,icon:f,color:y,onClose:m,bordered:x=!0,visible:S}=t,w=V(t,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:v,direction:P,tag:A}=s.useContext(u.QO),[k,M]=s.useState(!0),E=(0,o.A)(w,["closeIcon","closable"]);s.useEffect((()=>{void 0!==S&&M(S)}),[S]);const N=(0,a.nP)(y),O=(0,a.ZZ)(y),z=N||O,B=Object.assign(Object.assign({backgroundColor:y&&!z?y:void 0},null===A||void 0===A?void 0:A.style),d),I=v("tag",i),[L,R,D]=b(I),F=r()(I,null===A||void 0===A?void 0:A.className,{[`${I}-${y}`]:z,[`${I}-has-color`]:y&&!z,[`${I}-hidden`]:!k,[`${I}-rtl`]:"rtl"===P,[`${I}-borderless`]:!x},n,g,R,D),_=t=>{t.stopPropagation(),null===m||void 0===m||m(t),t.defaultPrevented||M(!1)},[,X]=(0,h.A)((0,h.d)(t),(0,h.d)(A),{closable:!1,closeIconRender:t=>{const e=s.createElement("span",{className:`${I}-close-icon`,onClick:_},t);return(0,l.fx)(t,e,(t=>({onClick:e=>{var i;null===(i=null===t||void 0===t?void 0:t.onClick)||void 0===i||i.call(t,e),_(e)},className:r()(null===t||void 0===t?void 0:t.className,`${I}-close-icon`)})))}}),$="function"===typeof w.onClick||p&&"a"===p.type,H=f||null,W=H?s.createElement(s.Fragment,null,H,p&&s.createElement("span",null,p)):p,U=s.createElement("span",Object.assign({},E,{ref:e,className:F,style:B}),W,X,N&&s.createElement(C,{key:"preset",prefixCls:I}),O&&s.createElement(T,{key:"status",prefixCls:I}));return L($?s.createElement(c.A,{component:"Tag"},U):U)})),M=k;M.CheckableTag=v;const E=M},85643:(t,e,i)=>{i.d(e,{A:()=>h});var s=i(58168),n=i(65043);const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M690 405h-46.9c-10.2 0-19.9 4.9-25.9 13.2L512 563.6 406.8 418.2c-6-8.3-15.6-13.2-25.9-13.2H334c-6.5 0-10.3 7.4-6.5 12.7l178 246c3.2 4.4 9.7 4.4 12.9 0l178-246c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"down-circle",theme:"outlined"};var o=i(22172),a=function(t,e){return n.createElement(o.A,(0,s.A)({},t,{ref:e,icon:r}))};const h=n.forwardRef(a)},97063:(t,e,i)=>{i.d(e,{Wn:()=>Tt});var s=i(8073),n=i(34566),r=i(26),o=i(64917);function a(t){return t.replace(/(?!\u3000)\s+/gm," ")}function h(t){const e=t.match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm);return e?e.map(parseFloat):[]}const l=/^[A-Z-]+$/;function c(t){const e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t);return e&&(e[2]||e[3]||e[4])||""}const u=/(\[[^\]]+\])/g,g=/(#[^\s+>~.[:]+)/g,d=/(\.[^\s+>~.[:]+)/g,p=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,f=/(:[\w-]+\([^)]*\))/gi,y=/(:[^\s+>~.[:]+)/g,m=/([^\s+>~.[:]+)/g;function x(t,e){const i=e.exec(t);return i?[t.replace(e," "),i.length]:[t,0]}const b=1e-8;function S(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function w(t,e){return(t[0]*e[0]+t[1]*e[1])/(S(t)*S(e))}function v(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(w(t,e))}function P(t){return t*t*t}function C(t){return 3*t*t*(1-t)}function A(t){return 3*t*(1-t)*(1-t)}function T(t){return(1-t)*(1-t)*(1-t)}function V(t){return t*t}function k(t){return 2*t*(1-t)}function M(t){return(1-t)*(1-t)}class E{static empty(t){return new E(t,"EMPTY","")}split(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ";const{document:e,name:i}=this;return a(this.getString()).trim().split(t).map((t=>new E(e,i,t)))}hasValue(t){const e=this.value;return null!==e&&""!==e&&(t||0!==e)&&"undefined"!==typeof e}isString(t){const{value:e}=this,i="string"===typeof e;return i&&t?t.test(e):i}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;const t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return"undefined"===typeof t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return"undefined"===typeof t?0:parseFloat(t);const{value:e}=this;let i=parseFloat(e);return this.isString(/%$/)&&(i/=100),i}getString(t){return"undefined"===typeof t||this.hasValue()?"undefined"===typeof this.value?"":String(this.value):String(t)}getColor(t){let e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=function(t){if(!t.startsWith("rgb"))return t;let e=3;return t.replace(/\d+(\.\d+)?/g,((t,i)=>e--&&i?String(Math.round(parseFloat(t))):t))}(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;const[i,s]="boolean"===typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&s:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(i);default:{const t=this.getNumber();return e&&t<1?t*n.computeSize(i):t}}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){const t=this.getString(),e=/#([^)'"]+)/.exec(t),i=(null===e||void 0===e?void 0:e[1])||t;return this.document.definitions[i]}getFillStyleDefinition(t,e){let i=this.getDefinition();if(!i)return null;if("function"===typeof i.createGradient&&"getBoundingBox"in t)return i.createGradient(this.document.ctx,t,e);if("function"===typeof i.createPattern){if(i.getHrefAttribute().hasValue()){const t=i.getAttribute("patternTransform");i=i.getHrefAttribute().getDefinition(),i&&t.hasValue()&&i.getAttribute("patternTransform",!0).setValue(t.value)}if(i)return i.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){if(!this.hasValue())return null;const t=this.getString();return E.textBaselineMapping[t]||null}addOpacity(t){let e=this.getColor();const i=e.length;let s=0;for(let n=0;n<i&&(","===e[n]&&s++,3!==s);n++);if(t.hasValue()&&this.isString()&&3!==s){const i=new n(e);i.ok&&(i.alpha=t.getNumber(),e=i.toRGBA())}return new E(this.document,this.name,e)}constructor(t,e,i){this.document=t,this.name=e,this.value=i,this.isNormalizedColor=!1}}E.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class N{clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getRoot(){const[t]=this.viewPorts;return t||O()}getCurrent(){const{viewPorts:t}=this,e=t[t.length-1];return e||O()}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"===typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}constructor(){this.viewPorts=[]}}function O(){return{width:N.DEFAULT_VIEWPORT_WIDTH,height:N.DEFAULT_VIEWPORT_HEIGHT}}N.DEFAULT_VIEWPORT_WIDTH=800,N.DEFAULT_VIEWPORT_HEIGHT=600;class z{static parse(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const[i=e,s=e]=h(t);return new z(i,s)}static parseScale(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;const[i=e,s=i]=h(t);return new z(i,s)}static parsePath(t){const e=h(t),i=e.length,s=[];for(let n=0;n<i;n+=2)s.push(new z(e[n],e[n+1]));return s}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){const{x:e,y:i}=this,s=e*t[0]+i*t[2]+t[4],n=e*t[1]+i*t[3]+t[5];this.x=s,this.y=n}constructor(t,e){this.x=t,this.y=e}}class B{isWorking(){return this.working}start(){if(this.working)return;const{screen:t,onClick:e,onMouseMove:i}=this,s=t.ctx.canvas;s.onclick=e,s.onmousemove=i,this.working=!0}stop(){if(!this.working)return;const t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(!this.working)return;const{screen:t,events:e,eventElements:i}=this,{style:s}=t.ctx.canvas;let n;s&&(s.cursor=""),e.forEach(((t,e)=>{let{run:s}=t;for(n=i[e];n;)s(n),n=n.parent})),this.events=[],this.eventElements=[]}checkPath(t,e){if(!this.working||!e)return;const{events:i,eventElements:s}=this;i.forEach(((i,n)=>{let{x:r,y:o}=i;!s[n]&&e.isPointInPath&&e.isPointInPath(r,o)&&(s[n]=t)}))}checkBoundingBox(t,e){if(!this.working||!e)return;const{events:i,eventElements:s}=this;i.forEach(((i,n)=>{let{x:r,y:o}=i;!s[n]&&e.isPointInBox(r,o)&&(s[n]=t)}))}mapXY(t,e){const{window:i,ctx:s}=this.screen,n=new z(t,e);let r=s.canvas;for(;r;)n.x-=r.offsetLeft,n.y-=r.offsetTop,r=r.offsetParent;return(null===i||void 0===i?void 0:i.scrollX)&&(n.x+=i.scrollX),(null===i||void 0===i?void 0:i.scrollY)&&(n.y+=i.scrollY),n}onClick(t){const{x:e,y:i}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:i,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){const{x:e,y:i}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:i,run(t){t.onMouseMove&&t.onMouseMove()}})}constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}}const I="undefined"!==typeof window?window:null,L="undefined"!==typeof fetch?fetch.bind(void 0):void 0;class R{wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;const t=this.waits.every((t=>t()));return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){let{document:e,ctx:i,aspectRatio:s,width:n,desiredWidth:r,height:o,desiredHeight:h,minX:l=0,minY:c=0,refX:u,refY:g,clip:d=!1,clipX:p=0,clipY:f=0}=t;const y=a(s).replace(/^defer\s/,""),[m,x]=y.split(" "),b=m||"xMidYMid",S=x||"meet",w=n/r,v=o/h,P=Math.min(w,v),C=Math.max(w,v);let A=r,T=h;"meet"===S&&(A*=P,T*=P),"slice"===S&&(A*=C,T*=C);const V=new E(e,"refX",u),k=new E(e,"refY",g),M=V.hasValue()&&k.hasValue();if(M&&i.translate(-P*V.getPixels("x"),-P*k.getPixels("y")),d){const t=P*p,e=P*f;i.beginPath(),i.moveTo(t,e),i.lineTo(n,e),i.lineTo(n,o),i.lineTo(t,o),i.closePath(),i.clip()}if(!M){const t="meet"===S&&P===v,e="slice"===S&&C===v,s="meet"===S&&P===w,r="slice"===S&&C===w;b.startsWith("xMid")&&(t||e)&&i.translate(n/2-A/2,0),b.endsWith("YMid")&&(s||r)&&i.translate(0,o/2-T/2),b.startsWith("xMax")&&(t||e)&&i.translate(n-A,0),b.endsWith("YMax")&&(s||r)&&i.translate(0,o-T)}switch(!0){case"none"===b:i.scale(w,v);break;case"meet"===S:i.scale(P,P);break;case"slice"===S:i.scale(C,C)}i.translate(-l,-c)}start(t){let{enableRedraw:e=!1,ignoreMouse:i=!1,ignoreAnimation:n=!1,ignoreDimensions:r=!1,ignoreClear:o=!1,forceRedraw:a,scaleWidth:h,scaleHeight:l,offsetX:c,offsetY:u}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{mouse:g}=this,d=1e3/R.FRAMERATE;if(this.isReadyLock=!1,this.frameDuration=d,this.readyPromise=new Promise((t=>{this.resolveReady=t})),this.isReady()&&this.render(t,r,o,h,l,c,u),!e)return;let p=Date.now(),f=p,y=0;const m=()=>{p=Date.now(),y=p-f,y>=d&&(f=p-y%d,this.shouldUpdate(n,a)&&(this.render(t,r,o,h,l,c,u),g.runEvents())),this.intervalId=s(m)};i||g.start(),this.intervalId=s(m)}stop(){this.intervalId&&(s.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){const{frameDuration:t}=this;if(this.animations.reduce(((e,i)=>i.update(t)||e),!1))return!0}return!("function"!==typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,i,s,n,r,o){const{viewPort:a,ctx:l,isFirstRender:c}=this,u=l.canvas;a.clear(),u.width&&u.height&&a.setCurrent(u.width,u.height);const g=t.getStyle("width"),d=t.getStyle("height");!e&&(c||"number"!==typeof s&&"number"!==typeof n)&&(g.hasValue()&&(u.width=g.getPixels("x"),u.style&&(u.style.width="".concat(u.width,"px"))),d.hasValue()&&(u.height=d.getPixels("y"),u.style&&(u.style.height="".concat(u.height,"px"))));let p=u.clientWidth||u.width,f=u.clientHeight||u.height;if(e&&g.hasValue()&&d.hasValue()&&(p=g.getPixels("x"),f=d.getPixels("y")),a.setCurrent(p,f),"number"===typeof r&&t.getAttribute("x",!0).setValue(r),"number"===typeof o&&t.getAttribute("y",!0).setValue(o),"number"===typeof s||"number"===typeof n){const e=h(t.getAttribute("viewBox").getString());let i=0,r=0;if("number"===typeof s){const n=t.getStyle("width");n.hasValue()?i=n.getPixels("x")/s:e[2]&&!isNaN(e[2])&&(i=e[2]/s)}if("number"===typeof n){const i=t.getStyle("height");i.hasValue()?r=i.getPixels("y")/n:e[3]&&!isNaN(e[3])&&(r=e[3]/n)}i||(i=r),r||(r=i),t.getAttribute("width",!0).setValue(s),t.getAttribute("height",!0).setValue(n);const o=t.getStyle("transform",!0,!0);o.setValue("".concat(o.getString()," scale(").concat(1/i,", ").concat(1/r,")"))}i||l.clearRect(0,0,p,f),t.render(l),c&&(this.isFirstRender=!1)}constructor(t){let{fetch:e=L,window:i=I}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.ctx=t,this.viewPort=new N,this.mouse=new B(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=i,!e)throw new Error("Can't find 'fetch' in 'globalThis', please provide it via options");this.fetch=e}}R.defaultWindow=I,R.defaultFetch=L,R.FRAMERATE=30,R.MAX_VIRTUAL_PIXELS=3e4;const{defaultFetch:D}=R,F="undefined"!==typeof DOMParser?DOMParser:void 0;class _{async parse(t){return t.startsWith("<")?this.parseFromString(t):this.load(t)}parseFromString(t){const e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(i){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){const e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent||"Unknown parse error");return t}async load(t){const e=await this.fetch(t),i=await e.text();return this.parseFromString(i)}constructor(){let{fetch:t=D,DOMParser:e=F}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!t)throw new Error("Can't find 'fetch' in 'globalThis', please provide it via options");if(!e)throw new Error("Can't find 'DOMParser' in 'globalThis', please provide it via options");this.fetch=t,this.DOMParser=e}}class X{apply(t){const{originX:e,originY:i,matrix:s}=this,n=e.getPixels("x"),r=i.getPixels("y");t.translate(n,r),t.transform(s[0],s[1],s[2],s[3],s[4],s[5]),t.translate(-n,-r)}unapply(t){const{originX:e,originY:i,matrix:s}=this,n=s[0],r=s[2],o=s[4],a=s[1],h=s[3],l=s[5],c=1/(n*(1*h-0*l)-r*(1*a-0*l)+o*(0*a-0*h)),u=e.getPixels("x"),g=i.getPixels("y");t.translate(u,g),t.transform(c*(1*h-0*l),c*(0*l-1*a),c*(0*o-1*r),c*(1*n-0*o),c*(r*l-o*h),c*(o*a-n*l)),t.translate(-u,-g)}applyToPoint(t){t.applyTransform(this.matrix)}constructor(t,e,i){this.type="matrix",this.matrix=function(t){const e=h(t);return[e[0]||0,e[1]||0,e[2]||0,e[3]||0,e[4]||0,e[5]||0]}(e),this.originX=i[0],this.originY=i[1]}}class $ extends X{constructor(t,e,i){super(t,e,i),this.type="skew",this.angle=new E(t,"angle",e)}}class H{static fromElement(t,e){const i=e.getStyle("transform",!1,!0);if(i.hasValue()){const[s,n=s]=e.getStyle("transform-origin",!1,!0).split();if(s&&n){const e=[s,n];return new H(t,i.getString(),e)}}return null}apply(t){this.transforms.forEach((e=>e.apply(t)))}unapply(t){this.transforms.forEach((e=>e.unapply(t)))}applyToPoint(t){this.transforms.forEach((e=>e.applyToPoint(t)))}constructor(t,e,i){this.document=t,this.transforms=[];a(e).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/).forEach((t=>{if("none"===t)return;const[e,s]=function(t){const[e="",i=""]=t.split("(");return[e.trim(),i.trim().replace(")","")]}(t),n=H.transformTypes[e];n&&this.transforms.push(new n(this.document,s,i))}))}}H.transformTypes={translate:class{apply(t){const{x:e,y:i}=this.point;t.translate(e||0,i||0)}unapply(t){const{x:e,y:i}=this.point;t.translate(-1*e||0,-1*i||0)}applyToPoint(t){const{x:e,y:i}=this.point;t.applyTransform([1,0,0,1,e||0,i||0])}constructor(t,e){this.type="translate",this.point=z.parse(e)}},rotate:class{apply(t){const{cx:e,cy:i,originX:s,originY:n,angle:r}=this,o=e+s.getPixels("x"),a=i+n.getPixels("y");t.translate(o,a),t.rotate(r.getRadians()),t.translate(-o,-a)}unapply(t){const{cx:e,cy:i,originX:s,originY:n,angle:r}=this,o=e+s.getPixels("x"),a=i+n.getPixels("y");t.translate(o,a),t.rotate(-1*r.getRadians()),t.translate(-o,-a)}applyToPoint(t){const{cx:e,cy:i,angle:s}=this,n=s.getRadians();t.applyTransform([1,0,0,1,e||0,i||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-i||0])}constructor(t,e,i){this.type="rotate";const s=h(e);this.angle=new E(t,"angle",s[0]),this.originX=i[0],this.originY=i[1],this.cx=s[1]||0,this.cy=s[2]||0}},scale:class{apply(t){const{scale:{x:e,y:i},originX:s,originY:n}=this,r=s.getPixels("x"),o=n.getPixels("y");t.translate(r,o),t.scale(e,i||e),t.translate(-r,-o)}unapply(t){const{scale:{x:e,y:i},originX:s,originY:n}=this,r=s.getPixels("x"),o=n.getPixels("y");t.translate(r,o),t.scale(1/e,1/i||e),t.translate(-r,-o)}applyToPoint(t){const{x:e,y:i}=this.scale;t.applyTransform([e||0,0,0,i||0,0,0])}constructor(t,e,i){this.type="scale";const s=z.parseScale(e);0!==s.x&&0!==s.y||(s.x=b,s.y=b),this.scale=s,this.originX=i[0],this.originY=i[1]}},matrix:X,skewX:class extends ${constructor(t,e,i){super(t,e,i),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}},skewY:class extends ${constructor(t,e,i){super(t,e,i),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}};class W{getAttribute(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.attributes[t];if(!i&&e){const e=new E(this.document,t,"");return this.attributes[t]=e,e}return i||E.empty(this.document)}getHrefAttribute(){let t;for(const e in this.attributes)if("href"===e||e.endsWith(":href")){t=this.attributes[e];break}return t||E.empty(this.document)}getStyle(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const s=this.styles[t];if(s)return s;const n=this.getAttribute(t);if(n.hasValue())return this.styles[t]=n,n;if(!i){const{parent:e}=this;if(e){const i=e.getStyle(t);if(i.hasValue())return i}}if(e){const e=new E(this.document,t,"");return this.styles[t]=e,e}return E.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){const e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){const e=this.getStyle("filter").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){const e=H.fromElement(this.document,this);e&&e.apply(t);const i=this.getStyle("clip-path",!1,!0);if(i.hasValue()){const e=i.getDefinition();e&&e.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach((e=>{e.render(t)}))}addChild(t){const e=t instanceof W?t:this.document.createElement(t);e.parent=this,W.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e;const{node:i}=this;if("function"===typeof i.matches)return i.matches(t);const s=null===(e=i.getAttribute)||void 0===e?void 0:e.call(i,"class");return!(!s||""===s)&&s.split(" ").some((e=>".".concat(e)===t))}addStylesFromStyleDefinition(){const{styles:t,stylesSpecificity:e}=this.document;let i;for(const s in t)if(!s.startsWith("@")&&this.matchesSelector(s)){const n=t[s],r=e[s];if(n)for(const t in n){let e=this.stylesSpecificity[t];"undefined"===typeof e&&(e="000"),r&&r>=e&&(i=n[t],i&&(this.styles[t]=i),this.stylesSpecificity[t]=r)}}}removeStyles(t,e){return e.reduce(((e,i)=>{const s=t.getStyle(i);if(!s.hasValue())return e;const n=s.getString();return s.setValue(""),[...e,[i,n]]}),[])}restoreStyles(t,e){e.forEach((e=>{let[i,s]=e;t.getStyle(i,!0).setValue(s)}))}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=i,this.type="",this.attributes={},this.styles={},this.stylesSpecificity={},this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],!e||1!==e.nodeType)return;if(Array.from(e.attributes).forEach((e=>{const i=(s=e.nodeName,l.test(s)?s.toLowerCase():s);var s;this.attributes[i]=new E(t,i,e.value)})),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){this.getAttribute("style").getString().split(";").map((t=>t.trim())).forEach((e=>{if(!e)return;const[i,s]=e.split(":").map((t=>t.trim()));i&&(this.styles[i]=new E(t,i,s))}))}const{definitions:s}=t,n=this.getAttribute("id");n.hasValue()&&(s[n.getString()]||(s[n.getString()]=this)),Array.from(e.childNodes).forEach((e=>{if(1===e.nodeType)this.addChild(e);else if(i&&(3===e.nodeType||4===e.nodeType)){const i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}}))}}W.ignoreChildTypes=["title"];class U extends W{constructor(t,e,i){super(t,e,i)}}function Y(t){const e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function q(t){if(!t)return"";const e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function j(t){if(!t)return"";const e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class Q{static parse(){let t=arguments.length>1?arguments[1]:void 0,e="",i="",s="",n="",r="";const o=a(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),h={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return o.forEach((t=>{switch(!0){case!h.fontStyle&&Q.styles.includes(t):"inherit"!==t&&(e=t),h.fontStyle=!0;break;case!h.fontVariant&&Q.variants.includes(t):"inherit"!==t&&(i=t),h.fontStyle=!0,h.fontVariant=!0;break;case!h.fontWeight&&Q.weights.includes(t):"inherit"!==t&&(s=t),h.fontStyle=!0,h.fontVariant=!0,h.fontWeight=!0;break;case!h.fontSize:"inherit"!==t&&(n=t.split("/")[0]||""),h.fontStyle=!0,h.fontVariant=!0,h.fontWeight=!0,h.fontSize=!0;break;default:"inherit"!==t&&(r+=t)}})),new Q(e,i,s,n,r,t)}toString(){return[q(this.fontStyle),this.fontVariant,j(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"===typeof process?t:t.trim().split(",").map(Y).join(","))].join(" ").trim();var t}constructor(t,e,i,s,n,r){const o=r?"string"===typeof r?Q.parse(r):r:{};this.fontFamily=n||o.fontFamily,this.fontSize=s||o.fontSize,this.fontStyle=t||o.fontStyle,this.fontWeight=i||o.fontWeight,this.fontVariant=e||o.fontVariant}}Q.styles="normal|italic|oblique|inherit",Q.variants="normal|small-caps|inherit",Q.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class G{get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){"undefined"!==typeof t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),"undefined"!==typeof e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,0)}addY(t){this.addPoint(0,t)}addBoundingBox(t){if(!t)return;const{x1:e,y1:i,x2:s,y2:n}=t;this.addPoint(e,i),this.addPoint(s,n)}sumCubic(t,e,i,s,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*i+3*(1-t)*Math.pow(t,2)*s+Math.pow(t,3)*n}bezierCurveAdd(t,e,i,s,n){const r=6*e-12*i+6*s,o=-3*e+9*i-9*s+3*n,a=3*i-3*e;if(0===o){if(0===r)return;const o=-a/r;return void(0<o&&o<1&&(t?this.addX(this.sumCubic(o,e,i,s,n)):this.addY(this.sumCubic(o,e,i,s,n))))}const h=Math.pow(r,2)-4*a*o;if(h<0)return;const l=(-r+Math.sqrt(h))/(2*o);0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,i,s,n)):this.addY(this.sumCubic(l,e,i,s,n)));const c=(-r-Math.sqrt(h))/(2*o);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,i,s,n)):this.addY(this.sumCubic(c,e,i,s,n)))}addBezierCurve(t,e,i,s,n,r,o,a){this.addPoint(t,e),this.addPoint(o,a),this.bezierCurveAdd(!0,t,i,n,o),this.bezierCurveAdd(!1,e,s,r,a)}addQuadraticCurve(t,e,i,s,n,r){const o=t+2/3*(i-t),a=e+2/3*(s-e),h=o+1/3*(n-t),l=a+1/3*(r-e);this.addBezierCurve(t,e,o,h,a,l,n,r)}isPointInBox(t,e){const{x1:i,y1:s,x2:n,y2:r}=this;return i<=t&&t<=n&&s<=e&&e<=r}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=i,this.y2=s,this.addPoint(t,e),this.addPoint(i,s)}}class Z extends W{calculateOpacity(){let t=1,e=this;for(;e;){const i=e.getStyle("opacity",!1,!0);i.hasValue(!0)&&(t*=i.getNumber()),e=e.parent}return t}setContext(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){const e=this.getStyle("fill"),i=this.getStyle("fill-opacity"),s=this.getStyle("stroke"),n=this.getStyle("stroke-opacity");if(e.isUrlDefinition()){const s=e.getFillStyleDefinition(this,i);s&&(t.fillStyle=s)}else if(e.hasValue()){"currentColor"===e.getString()&&e.setValue(this.getStyle("color").getColor());const i=e.getColor();"inherit"!==i&&(t.fillStyle="none"===i?"rgba(0,0,0,0)":i)}if(i.hasValue()){const e=new E(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=e}if(s.isUrlDefinition()){const e=s.getFillStyleDefinition(this,n);e&&(t.strokeStyle=e)}else if(s.hasValue()){"currentColor"===s.getString()&&s.setValue(this.getStyle("color").getColor());const e=s.getString();"inherit"!==e&&(t.strokeStyle="none"===e?"rgba(0,0,0,0)":e)}if(n.hasValue()){const e=new E(this.document,"stroke",t.strokeStyle).addOpacity(n).getString();t.strokeStyle=e}const r=this.getStyle("stroke-width");if(r.hasValue()){const e=r.getPixels();t.lineWidth=e||b}const o=this.getStyle("stroke-linecap"),a=this.getStyle("stroke-linejoin"),l=this.getStyle("stroke-miterlimit"),c=this.getStyle("stroke-dasharray"),u=this.getStyle("stroke-dashoffset");if(o.hasValue()&&(t.lineCap=o.getString()),a.hasValue()&&(t.lineJoin=a.getString()),l.hasValue()&&(t.miterLimit=l.getNumber()),c.hasValue()&&"none"!==c.getString()){const e=h(c.getString());"undefined"!==typeof t.setLineDash?t.setLineDash(e):"undefined"!==typeof t.webkitLineDash?t.webkitLineDash=e:"undefined"===typeof t.mozDash||1===e.length&&0===e[0]||(t.mozDash=e);const i=u.getPixels();"undefined"!==typeof t.lineDashOffset?t.lineDashOffset=i:"undefined"!==typeof t.webkitLineDashOffset?t.webkitLineDashOffset=i:"undefined"!==typeof t.mozDashOffset&&(t.mozDashOffset=i)}}if(this.modifiedEmSizeStack=!1,"undefined"!==typeof t.font){const e=this.getStyle("font"),i=this.getStyle("font-style"),s=this.getStyle("font-variant"),n=this.getStyle("font-weight"),r=this.getStyle("font-size"),o=this.getStyle("font-family"),a=new Q(i.getString(),s.getString(),n.getString(),r.hasValue()?"".concat(r.getPixels(!0),"px"):"",o.getString(),Q.parse(e.getString(),t.font));i.setValue(a.fontStyle),s.setValue(a.fontVariant),n.setValue(a.fontWeight),r.setValue(a.fontSize),o.setValue(a.fontFamily),t.font=a.toString(),r.isPixels()&&(this.document.emSize=r.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}constructor(){super(...arguments),this.modifiedEmSizeStack=!1}}class J extends Z{setContext(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);const i=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();i&&(t.textBaseline=i)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);let e=null;return this.children.forEach(((i,s)=>{const n=this.getChildBoundingBox(t,this,this,s);e?e.addBoundingBox(n):e=n})),e}getFontSize(){const{document:t,parent:e}=this,i=Q.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(i)}getTElementBoundingBox(t){const e=this.getFontSize();return new G(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,i){const s=e[i];let n;if(t.isArabic){var r;const o=e.length,a=e[i-1],h=e[i+1];let l="isolated";(0===i||" "===a)&&i<o-1&&" "!==h&&(l="terminal"),i>0&&" "!==a&&i<o-1&&" "!==h&&(l="medial"),i>0&&" "!==a&&(i===o-1||" "===h)&&(l="initial"),n=(null===(r=t.arabicGlyphs[s])||void 0===r?void 0:r[l])||t.glyphs[s]}else n=t.glyphs[s];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){const e=t||this.node,i=Array.from(e.parentNode.childNodes),s=i.indexOf(e),n=i.length-1;let r=a(e.textContent||"");return 0===s&&(r=r.replace(/^[\n \t]+/,"")),s===n&&(r=function(t){return t.replace(/[\n \t]+$/,"")}(r)),r}renderChildren(t){if("text"!==this.type)return void this.renderTElementChildren(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach(((e,i)=>{this.renderChild(t,this,this,i)}));const{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}renderTElementChildren(t){const{document:e,parent:i}=this,s=this.getText(),n=i.getStyle("font-family").getDefinition();if(n){const{unitsPerEm:r}=n.fontFace,o=Q.parse(e.ctx.font),a=i.getStyle("font-size").getNumber(o.fontSize),l=i.getStyle("font-style").getString(o.fontStyle),c=a/r,u=n.isRTL?s.split("").reverse().join(""):s,g=h(i.getAttribute("dx").getString()),d=u.length;for(let e=0;e<d;e++){const i=this.getGlyph(n,u,e);t.translate(this.x,this.y),t.scale(c,-c);const s=t.lineWidth;t.lineWidth=t.lineWidth*r/a,"italic"===l&&t.transform(1,0,.4,1,0,0),i.render(t),"italic"===l&&t.transform(1,0,-.4,1,0,0),t.lineWidth=s,t.scale(1/c,-1/c),t.translate(-this.x,-this.y),this.x+=a*(i.horizAdvX||n.horizAdvX)/r,"undefined"===typeof g[e]||isNaN(g[e])||(this.x+=g[e])}return}const{x:r,y:o}=this;t.fillStyle&&t.fillText(s,r,o),t.strokeStyle&&t.strokeText(s,r,o)}applyAnchoring(){if(this.textChunkStart>=this.leafTexts.length)return;const t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start");let i=0;i="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(let s=this.textChunkStart;s<this.leafTexts.length;s++)this.leafTexts[s].x+=i;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}adjustChildCoordinatesRecursive(t){this.children.forEach(((e,i)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,i)})),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,i,s){const n=i.children[s];n.children.length>0?n.children.forEach(((i,s)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,s)})):this.adjustChildCoordinates(t,e,i,s)}adjustChildCoordinates(t,e,i,s){const n=i.children[s];if("function"!==typeof n.measureText)return n;t.save(),n.setContext(t,!0);const r=n.getAttribute("x"),o=n.getAttribute("y"),a=n.getAttribute("dx"),h=n.getAttribute("dy"),l=n.getStyle("font-family").getDefinition(),c=Boolean(null===l||void 0===l?void 0:l.isRTL);0===s&&(r.hasValue()||r.setValue(n.getInheritedAttribute("x")),o.hasValue()||o.setValue(n.getInheritedAttribute("y")),a.hasValue()||a.setValue(n.getInheritedAttribute("dx")),h.hasValue()||h.setValue(n.getInheritedAttribute("dy")));const u=n.measureText(t);return c&&(e.x-=u),r.hasValue()?(e.applyAnchoring(),n.x=r.getPixels("x"),a.hasValue()&&(n.x+=a.getPixels("x"))):(a.hasValue()&&(e.x+=a.getPixels("x")),n.x=e.x),e.x=n.x,c||(e.x+=u),o.hasValue()?(n.y=o.getPixels("y"),h.hasValue()&&(n.y+=h.getPixels("y"))):(h.hasValue()&&(e.y+=h.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+u),e.maxX=Math.max(e.maxX,n.x,n.x+u),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,i,s){const n=i.children[s];if("function"!==typeof n.getBoundingBox)return null;const r=n.getBoundingBox(t);return r&&n.children.forEach(((i,s)=>{const o=e.getChildBoundingBox(t,e,n,s);r.addBoundingBox(o)})),r}renderChild(t,e,i,s){const n=i.children[s];n.render(t),n.children.forEach(((i,s)=>{e.renderChild(t,e,n,s)}))}measureText(t){const{measureCache:e}=this;if(~e)return e;const i=this.getText(),s=this.measureTargetText(t,i);return this.measureCache=s,s}measureTargetText(t,e){if(!e.length)return 0;const{parent:i}=this,s=i.getStyle("font-family").getDefinition();if(s){const t=this.getFontSize(),n=s.isRTL?e.split("").reverse().join(""):e,r=h(i.getAttribute("dx").getString()),o=n.length;let a=0;for(let e=0;e<o;e++){a+=(this.getGlyph(s,n,e).horizAdvX||s.horizAdvX)*t/s.fontFace.unitsPerEm,"undefined"===typeof r[e]||isNaN(r[e])||(a+=r[e])}return a}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);const{width:n}=t.measureText(e);return this.clearContext(t),t.restore(),n}getInheritedAttribute(t){let e=this;for(;e instanceof J&&e.isFirstChild()&&e.parent;){const i=e.parent.getAttribute(t);if(i.hasValue(!0))return i.getString("0");e=e.parent}return null}constructor(t,e,i){super(t,e,new.target===J||i),this.type="text",this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.measureCache=-1}}class K extends J{getText(){return this.text}constructor(t,e,i){super(t,e,new.target===K||i),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}}class tt extends K{constructor(){super(...arguments),this.type="textNode"}}class et extends r.LQ{reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new z(0,0),this.control=new z(0,0),this.current=new z(0,0),this.points=[],this.angles=[]}isEnd(){const{i:t,commands:e}=this;return t>=e.length-1}next(){const t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y";const i=new z(this.command[t],this.command[e]);return this.makeAbsolute(i)}getAsControlPoint(t,e){const i=this.getPoint(t,e);return this.control=i,i}getAsCurrentPoint(t,e){const i=this.getPoint(t,e);return this.current=i,i}getReflectedControlPoint(){const t=this.previousCommand.type;if(t!==r.LQ.CURVE_TO&&t!==r.LQ.SMOOTH_CURVE_TO&&t!==r.LQ.QUAD_TO&&t!==r.LQ.SMOOTH_QUAD_TO)return this.current;const{current:{x:e,y:i},control:{x:s,y:n}}=this;return new z(2*e-s,2*i-n)}makeAbsolute(t){if(this.command.relative){const{x:e,y:i}=this.current;t.x+=e,t.y+=i}return t}addMarker(t,e,i){const{points:s,angles:n}=this;i&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=s[s.length-1].angleTo(i)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){const{angles:t}=this,e=t.length;for(let i=0;i<e;i++)if(!t[i])for(let s=i+1;s<e;s++)if(t[s]){t[i]=t[s];break}return t}constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=new z(0,0),this.start=new z(0,0),this.current=new z(0,0),this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}}class it extends Z{path(t){const{pathParser:e}=this,i=new G;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case et.MOVE_TO:this.pathM(t,i);break;case et.LINE_TO:this.pathL(t,i);break;case et.HORIZ_LINE_TO:this.pathH(t,i);break;case et.VERT_LINE_TO:this.pathV(t,i);break;case et.CURVE_TO:this.pathC(t,i);break;case et.SMOOTH_CURVE_TO:this.pathS(t,i);break;case et.QUAD_TO:this.pathQ(t,i);break;case et.SMOOTH_QUAD_TO:this.pathT(t,i);break;case et.ARC:this.pathA(t,i);break;case et.CLOSE_PATH:this.pathZ(t,i)}return i}getBoundingBox(t){return this.path()}getMarkers(){const{pathParser:t}=this,e=t.getMarkerPoints(),i=t.getMarkerAngles();return e.map(((t,e)=>[t,i[e]]))}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);const e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());const i=this.getMarkers();if(i){const e=i.length-1,s=this.getStyle("marker-start"),n=this.getStyle("marker-mid"),r=this.getStyle("marker-end");if(s.isUrlDefinition()){const e=s.getDefinition(),[n,r]=i[0];e.render(t,n,r)}if(n.isUrlDefinition()){const s=n.getDefinition();for(let n=1;n<e;n++){const[e,r]=i[n];s.render(t,e,r)}}if(r.isUrlDefinition()){const s=r.getDefinition(),[n,o]=i[e];s.render(t,n,o)}}}static pathM(t){const e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){const{pathParser:i}=this,{point:s}=it.pathM(i),{x:n,y:r}=s;i.addMarker(s),e.addPoint(n,r),t&&t.moveTo(n,r)}static pathL(t){const{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){const{pathParser:i}=this,{current:s,point:n}=it.pathL(i),{x:r,y:o}=n;i.addMarker(n,s),e.addPoint(r,o),t&&t.lineTo(r,o)}static pathH(t){const{current:e,command:i}=t,s=new z((i.relative?e.x:0)+i.x,e.y);return t.current=s,{current:e,point:s}}pathH(t,e){const{pathParser:i}=this,{current:s,point:n}=it.pathH(i),{x:r,y:o}=n;i.addMarker(n,s),e.addPoint(r,o),t&&t.lineTo(r,o)}static pathV(t){const{current:e,command:i}=t,s=new z(e.x,(i.relative?e.y:0)+i.y);return t.current=s,{current:e,point:s}}pathV(t,e){const{pathParser:i}=this,{current:s,point:n}=it.pathV(i),{x:r,y:o}=n;i.addMarker(n,s),e.addPoint(r,o),t&&t.lineTo(r,o)}static pathC(t){const{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){const{pathParser:i}=this,{current:s,point:n,controlPoint:r,currentPoint:o}=it.pathC(i);i.addMarker(o,r,n),e.addBezierCurve(s.x,s.y,n.x,n.y,r.x,r.y,o.x,o.y),t&&t.bezierCurveTo(n.x,n.y,r.x,r.y,o.x,o.y)}static pathS(t){const{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){const{pathParser:i}=this,{current:s,point:n,controlPoint:r,currentPoint:o}=it.pathS(i);i.addMarker(o,r,n),e.addBezierCurve(s.x,s.y,n.x,n.y,r.x,r.y,o.x,o.y),t&&t.bezierCurveTo(n.x,n.y,r.x,r.y,o.x,o.y)}static pathQ(t){const{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){const{pathParser:i}=this,{current:s,controlPoint:n,currentPoint:r}=it.pathQ(i);i.addMarker(r,n,n),e.addQuadraticCurve(s.x,s.y,n.x,n.y,r.x,r.y),t&&t.quadraticCurveTo(n.x,n.y,r.x,r.y)}static pathT(t){const{current:e}=t,i=t.getReflectedControlPoint();t.control=i;return{current:e,controlPoint:i,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){const{pathParser:i}=this,{current:s,controlPoint:n,currentPoint:r}=it.pathT(i);i.addMarker(r,n,n),e.addQuadraticCurve(s.x,s.y,n.x,n.y,r.x,r.y),t&&t.quadraticCurveTo(n.x,n.y,r.x,r.y)}static pathA(t){const{current:e,command:i}=t;let{rX:s,rY:n,xRot:r,lArcFlag:o,sweepFlag:a}=i;const h=r*(Math.PI/180),l=t.getAsCurrentPoint(),c=new z(Math.cos(h)*(e.x-l.x)/2+Math.sin(h)*(e.y-l.y)/2,-Math.sin(h)*(e.x-l.x)/2+Math.cos(h)*(e.y-l.y)/2),u=Math.pow(c.x,2)/Math.pow(s,2)+Math.pow(c.y,2)/Math.pow(n,2);u>1&&(s*=Math.sqrt(u),n*=Math.sqrt(u));let g=(o===a?-1:1)*Math.sqrt((Math.pow(s,2)*Math.pow(n,2)-Math.pow(s,2)*Math.pow(c.y,2)-Math.pow(n,2)*Math.pow(c.x,2))/(Math.pow(s,2)*Math.pow(c.y,2)+Math.pow(n,2)*Math.pow(c.x,2)));isNaN(g)&&(g=0);const d=new z(g*s*c.y/n,g*-n*c.x/s),p=new z((e.x+l.x)/2+Math.cos(h)*d.x-Math.sin(h)*d.y,(e.y+l.y)/2+Math.sin(h)*d.x+Math.cos(h)*d.y),f=v([1,0],[(c.x-d.x)/s,(c.y-d.y)/n]),y=[(c.x-d.x)/s,(c.y-d.y)/n],m=[(-c.x-d.x)/s,(-c.y-d.y)/n];let x=v(y,m);return w(y,m)<=-1&&(x=Math.PI),w(y,m)>=1&&(x=0),{currentPoint:l,rX:s,rY:n,sweepFlag:a,xAxisRotation:h,centp:p,a1:f,ad:x}}pathA(t,e){const{pathParser:i}=this,{currentPoint:s,rX:n,rY:r,sweepFlag:o,xAxisRotation:a,centp:h,a1:l,ad:c}=it.pathA(i),u=1-o?1:-1,g=l+u*(c/2),d=new z(h.x+n*Math.cos(g),h.y+r*Math.sin(g));if(i.addMarkerAngle(d,g-u*Math.PI/2),i.addMarkerAngle(s,g-u*Math.PI),e.addPoint(s.x,s.y),t&&!isNaN(l)&&!isNaN(c)){const e=n>r?n:r,i=n>r?1:n/r,s=n>r?r/n:1;t.translate(h.x,h.y),t.rotate(a),t.scale(i,s),t.arc(0,0,e,l,l+c,Boolean(1-o)),t.scale(1/i,1/s),t.rotate(-a),t.translate(-h.x,-h.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){it.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}constructor(t,e,i){super(t,e,i),this.type="path",this.pathParser=new et(this.getAttribute("d").getString())}}class st extends Z{setContext(t){var e;const{document:i}=this,{screen:s,window:n}=i,r=t.canvas;if(s.setDefaults(t),"style"in r&&"undefined"!==typeof t.font&&n&&"undefined"!==typeof n.getComputedStyle){t.font=n.getComputedStyle(r).getPropertyValue("font");const e=new E(i,"fontSize",Q.parse(t.font).fontSize);e.hasValue()&&(i.rootEmSize=e.getPixels("y"),i.emSize=i.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);let{width:o,height:a}=s.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");const l=this.getAttribute("refX"),c=this.getAttribute("refY"),u=this.getAttribute("viewBox"),g=u.hasValue()?h(u.getString()):null,d=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden");let p=0,f=0,y=0,m=0;g&&(p=g[0],f=g[1]),this.root||(o=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y"),"marker"===this.type&&(y=p,m=f,p=0,f=0)),s.viewPort.setCurrent(o,a),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),g&&(o=g[2],a=g[3]),i.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s.viewPort.width,desiredWidth:o,height:s.viewPort.height,desiredHeight:a,minX:p,minY:f,refX:l.getValue(),refY:c.getValue(),clip:d,clipX:y,clipY:m}),g&&(s.viewPort.removeCurrent(),s.viewPort.setCurrent(o,a))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const s=this.getAttribute("width",!0),n=this.getAttribute("height",!0),r=this.getAttribute("viewBox"),o=this.getAttribute("style"),a=s.getNumber(0),h=n.getNumber(0);if(i)if("string"===typeof i)this.getAttribute("preserveAspectRatio",!0).setValue(i);else{const t=this.getAttribute("preserveAspectRatio");t.hasValue()&&t.setValue(t.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(s.setValue(t),n.setValue(e),r.hasValue()||r.setValue("0 0 ".concat(a||t," ").concat(h||e)),o.hasValue()){const i=this.getStyle("width"),s=this.getStyle("height");i.hasValue()&&i.setValue("".concat(t,"px")),s.hasValue()&&s.setValue("".concat(e,"px"))}}constructor(){super(...arguments),this.type="svg",this.root=!1}}class nt extends it{path(t){const e=this.getAttribute("x").getPixels("x"),i=this.getAttribute("y").getPixels("y"),s=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),r=this.getAttribute("rx"),o=this.getAttribute("ry");let a=r.getPixels("x"),h=o.getPixels("y");if(r.hasValue()&&!o.hasValue()&&(h=a),o.hasValue()&&!r.hasValue()&&(a=h),a=Math.min(a,s/2),h=Math.min(h,n/2),t){const r=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&s>0&&(t.moveTo(e+a,i),t.lineTo(e+s-a,i),t.bezierCurveTo(e+s-a+r*a,i,e+s,i+h-r*h,e+s,i+h),t.lineTo(e+s,i+n-h),t.bezierCurveTo(e+s,i+n-h+r*h,e+s-a+r*a,i+n,e+s-a,i+n),t.lineTo(e+a,i+n),t.bezierCurveTo(e+a-r*a,i+n,e,i+n-h+r*h,e,i+n-h),t.lineTo(e,i+h),t.bezierCurveTo(e,i+h-r*h,e+a-r*a,i,e+a,i),t.closePath())}return new G(e,i,e+s,i+n)}getMarkers(){return null}constructor(){super(...arguments),this.type="rect"}}class rt extends it{path(t){const{points:e}=this,[{x:i,y:s}]=e,n=new G(i,s);return t&&(t.beginPath(),t.moveTo(i,s)),e.forEach((e=>{let{x:i,y:s}=e;n.addPoint(i,s),t&&t.lineTo(i,s)})),n}getMarkers(){const{points:t}=this,e=t.length-1,i=[];return t.forEach(((s,n)=>{n!==e&&i.push([s,s.angleTo(t[n+1])])})),i.length>0&&i.push([t[t.length-1],i[i.length-1][1]]),i}constructor(t,e,i){super(t,e,i),this.type="polyline",this.points=[],this.points=z.parsePath(this.getAttribute("points").getString())}}class ot extends Z{getBoundingBox(t){const e=new G;return this.children.forEach((i=>{e.addBoundingBox(i.getBoundingBox(t))})),e}constructor(){super(...arguments),this.type="g"}}class at extends W{getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,i){let s=this;this.getHrefAttribute().hasValue()&&(s=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(s));const{stops:n}=s,r=this.getGradient(t,e);if(!r)return this.addParentOpacity(i,n[n.length-1].color);if(n.forEach((t=>{r.addColorStop(t.offset,this.addParentOpacity(i,t.color))})),this.getAttribute("gradientTransform").hasValue()){const{document:t}=this,{MAX_VIRTUAL_PIXELS:e}=R,{viewPort:i}=t.screen,s=i.getRoot(),n=new nt(t);n.attributes.x=new E(t,"x",-e/3),n.attributes.y=new E(t,"y",-e/3),n.attributes.width=new E(t,"width",e),n.attributes.height=new E(t,"height",e);const o=new ot(t);o.attributes.transform=new E(t,"transform",this.getAttribute("gradientTransform").getValue()),o.children=[n];const a=new st(t);a.attributes.x=new E(t,"x",0),a.attributes.y=new E(t,"y",0),a.attributes.width=new E(t,"width",s.width),a.attributes.height=new E(t,"height",s.height),a.children=[o];const h=t.createCanvas(s.width,s.height),l=h.getContext("2d");return l.fillStyle=r,a.render(l),l.createPattern(h,"no-repeat")}return r}inheritStopContainer(t){this.attributesToInherit.forEach((e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())}))}addParentOpacity(t,e){if(t.hasValue()){return new E(this.document,"color",e).addOpacity(t).getColor()}return e}constructor(t,e,i){super(t,e,i),this.attributesToInherit=["gradientUnits"],this.stops=[];const{stops:s,children:n}=this;n.forEach((t=>{"stop"===t.type&&s.push(t)}))}}class ht extends W{getProperty(){const t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){const{initialUnits:t}=this,{progress:e,from:i,to:s}=this.getProgress();let n=i.getNumber()+(s.getNumber()-i.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){const{parent:e}=this,i=this.getProperty();if(this.initialValue||(this.initialValue=i.getString(),this.initialUnits=i.getUnits()),this.duration>this.maxDuration){const t=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==t||this.frozen){if("remove"===t&&!this.removed)return this.removed=!0,e&&i&&i.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e&&i&&(e.animationFrozen=!0,e.animationFrozenValue=i.getString());return!1}this.duration+=t;let s=!1;if(this.begin<this.duration){let t=this.calcValue();const e=this.getAttribute("type");if(e.hasValue()){const i=e.getString();t="".concat(i,"(").concat(t,")")}i.setValue(t),s=!0}return s}getProgress(){const{document:t,values:e}=this;let i,s,n=(this.duration-this.begin)/(this.maxDuration-this.begin);if(e.hasValue()){const r=n*(e.getValue().length-1),o=Math.floor(r),a=Math.ceil(r);let h;h=e.getValue()[o],i=new E(t,"from",h?parseFloat(h):0),h=e.getValue()[a],s=new E(t,"to",h?parseFloat(h):0),n=(r-o)/(a-o)}else i=this.from,s=this.to;return{progress:n,from:i,to:s}}constructor(t,e,i){super(t,e,i),this.type="animate",this.duration=0,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new E(t,"values",null);const s=this.getAttribute("values");s.hasValue()&&this.values.setValue(s.getString().split(";"))}}class lt extends W{constructor(t,e,i){super(t,e,i),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class ct extends it{constructor(t,e,i){super(t,e,i),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class ut extends ct{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}const gt=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class dt{async load(t,e){try{const{document:i}=this,s=(await i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(s).forEach((e=>{const s=i.createElement(e);i.definitions[t]=s}))}catch(i){console.error('Error while loading font "'.concat(e,'":'),i)}this.loaded=!0}constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}}class pt extends W{constructor(t,e,i){super(t,e,i),this.type="style";a(Array.from(e.childNodes).map((t=>t.textContent)).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach((e=>{const i=e.trim();if(!i)return;const s=i.split("{"),n=s[0].split(","),r=s[1].split(";");n.forEach((e=>{const i=e.trim();if(!i)return;const s=t.styles[i]||{};if(r.forEach((e=>{const i=e.indexOf(":"),n=e.substr(0,i).trim(),r=e.substr(i+1,e.length-i).trim();n&&r&&(s[n]=new E(t,n,r))})),t.styles[i]=s,t.stylesSpecificity[i]=function(t){const e=[0,0,0];let i=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),s=0;return[i,s]=x(i,u),e[1]+=s,[i,s]=x(i,g),e[0]+=s,[i,s]=x(i,d),e[1]+=s,[i,s]=x(i,p),e[2]+=s,[i,s]=x(i,f),e[1]+=s,[i,s]=x(i,y),e[1]+=s,i=i.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[i,s]=x(i,m),e[2]+=s,e.join("")}(i),"@font-face"===i){const e=s["font-family"].getString().replace(/"|'/g,"");s.src.getString().split(",").forEach((i=>{if(i.indexOf('format("svg")')>0){const s=c(i);s&&new dt(t).load(e,s)}}))}}))}))}}pt.parseExternalUrl=c;function ft(t,e,i,s,n,r){return t[i*s*4+4*e+r]}function yt(t,e,i,s,n,r,o){t[i*s*4+4*e+r]=o}function mt(t,e,i){return t[e]*i}function xt(t,e,i,s){return e+Math.cos(t)*i+Math.sin(t)*s}class bt extends W{apply(t,e,i,s,n){const{includeOpacity:r,matrix:o}=this,a=t.getImageData(0,0,s,n);for(let h=0;h<n;h++)for(let t=0;t<s;t++){const e=ft(a.data,t,h,s,0,0),i=ft(a.data,t,h,s,0,1),n=ft(a.data,t,h,s,0,2),l=ft(a.data,t,h,s,0,3);let c=mt(o,0,e)+mt(o,1,i)+mt(o,2,n)+mt(o,3,l)+mt(o,4,1),u=mt(o,5,e)+mt(o,6,i)+mt(o,7,n)+mt(o,8,l)+mt(o,9,1),g=mt(o,10,e)+mt(o,11,i)+mt(o,12,n)+mt(o,13,l)+mt(o,14,1),d=mt(o,15,e)+mt(o,16,i)+mt(o,17,n)+mt(o,18,l)+mt(o,19,1);r&&(c=0,u=0,g=0,d*=l/255),yt(a.data,t,h,s,0,0,c),yt(a.data,t,h,s,0,1,u),yt(a.data,t,h,s,0,2,g),yt(a.data,t,h,s,0,3,d)}t.clearRect(0,0,s,n),t.putImageData(a,0,0)}constructor(t,e,i){super(t,e,i),this.type="feColorMatrix";let s=h(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":{const t=s[0];s=[.213+.787*t,.715-.715*t,.072-.072*t,0,0,.213-.213*t,.715+.285*t,.072-.072*t,0,0,.213-.213*t,.715-.715*t,.072+.928*t,0,0,0,0,0,1,0,0,0,0,0,1];break}case"hueRotate":{const t=s[0]*Math.PI/180;s=[xt(t,.213,.787,-.213),xt(t,.715,-.715,-.715),xt(t,.072,-.072,.928),0,0,xt(t,.213,-.213,.143),xt(t,.715,.285,.14),xt(t,.072,-.072,-.283),0,0,xt(t,.213,-.213,-.787),xt(t,.715,-.715,.715),xt(t,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break}case"luminanceToAlpha":s=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=s,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}}class St extends W{apply(t,e){const{document:i}=this;let s=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(!r&&!o){const e=new G;this.children.forEach((i=>{e.addBoundingBox(i.getBoundingBox(t))})),s=Math.floor(e.x1),n=Math.floor(e.y1),r=Math.floor(e.width),o=Math.floor(e.height)}const a=this.removeStyles(e,St.ignoreStyles),h=i.createCanvas(s+r,n+o),l=h.getContext("2d");i.screen.setDefaults(l),this.renderChildren(l),new bt(i,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(l,0,0,s+r,n+o);const c=i.createCanvas(s+r,n+o),u=c.getContext("2d");i.screen.setDefaults(u),e.render(u),u.globalCompositeOperation="destination-in",u.fillStyle=l.createPattern(h,"no-repeat"),u.fillRect(0,0,s+r,n+o),t.fillStyle=u.createPattern(c,"no-repeat"),t.fillRect(0,0,s+r,n+o),this.restoreStyles(e,a)}render(t){}constructor(){super(...arguments),this.type="mask"}}St.ignoreStyles=["mask","transform","clip-path"];const wt=()=>{};class vt extends W{apply(t,e){const{document:i,children:s}=this,n="getBoundingBox"in e?e.getBoundingBox(t):null;if(!n)return;let r=0,o=0;s.forEach((t=>{const e=t.extraFilterDistance||0;r=Math.max(r,e),o=Math.max(o,e)}));const a=Math.floor(n.width),h=Math.floor(n.height),l=a+2*r,c=h+2*o;if(l<1||c<1)return;const u=Math.floor(n.x),g=Math.floor(n.y),d=this.removeStyles(e,vt.ignoreStyles),p=i.createCanvas(l,c),f=p.getContext("2d");i.screen.setDefaults(f),f.translate(-u+r,-g+o),e.render(f),s.forEach((t=>{"function"===typeof t.apply&&t.apply(f,0,0,l,c)})),t.drawImage(p,0,0,l,c,u-r,g-o,l,c),this.restoreStyles(e,d)}render(t){}constructor(){super(...arguments),this.type="filter"}}vt.ignoreStyles=["filter","transform","clip-path"];const Pt={svg:st,rect:nt,circle:class extends it{path(t){const e=this.getAttribute("cx").getPixels("x"),i=this.getAttribute("cy").getPixels("y"),s=this.getAttribute("r").getPixels();return t&&s>0&&(t.beginPath(),t.arc(e,i,s,0,2*Math.PI,!1),t.closePath()),new G(e-s,i-s,e+s,i+s)}getMarkers(){return null}constructor(){super(...arguments),this.type="circle"}},ellipse:class extends it{path(t){const e=(Math.sqrt(2)-1)/3*4,i=this.getAttribute("rx").getPixels("x"),s=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y");return t&&i>0&&s>0&&(t.beginPath(),t.moveTo(n+i,r),t.bezierCurveTo(n+i,r+e*s,n+e*i,r+s,n,r+s),t.bezierCurveTo(n-e*i,r+s,n-i,r+e*s,n-i,r),t.bezierCurveTo(n-i,r-e*s,n-e*i,r-s,n,r-s),t.bezierCurveTo(n+e*i,r-s,n+i,r-e*s,n+i,r),t.closePath()),new G(n-i,r-s,n+i,r+s)}getMarkers(){return null}constructor(){super(...arguments),this.type="ellipse"}},line:class extends it{getPoints(){return[new z(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new z(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){const[{x:e,y:i},{x:s,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,i),t.lineTo(s,n)),new G(e,i,s,n)}getMarkers(){const[t,e]=this.getPoints(),i=t.angleTo(e);return[[t,i],[e,i]]}constructor(){super(...arguments),this.type="line"}},polyline:rt,polygon:class extends rt{path(t){const e=super.path(t),[{x:i,y:s}]=this.points;return t&&(t.lineTo(i,s),t.closePath()),e}constructor(){super(...arguments),this.type="polygon"}},path:it,pattern:class extends W{createPattern(t,e,i){const s=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),r=new st(this.document,null);r.attributes.viewBox=new E(this.document,"viewBox",this.getAttribute("viewBox").getValue()),r.attributes.width=new E(this.document,"width","".concat(s,"px")),r.attributes.height=new E(this.document,"height","".concat(n,"px")),r.attributes.transform=new E(this.document,"transform",this.getAttribute("patternTransform").getValue()),r.children=this.children;const o=this.document.createCanvas(s,n),a=o.getContext("2d"),h=this.getAttribute("x"),l=this.getAttribute("y");h.hasValue()&&l.hasValue()&&a.translate(h.getPixels("x",!0),l.getPixels("y",!0)),i.hasValue()?this.styles["fill-opacity"]=i:Reflect.deleteProperty(this.styles,"fill-opacity");for(let c=-1;c<=1;c++)for(let t=-1;t<=1;t++)a.save(),r.attributes.x=new E(this.document,"x",c*o.width),r.attributes.y=new E(this.document,"y",t*o.height),r.render(a),a.restore();return t.createPattern(o,"repeat")}constructor(){super(...arguments),this.type="pattern"}},marker:class extends W{render(t,e,i){if(!e)return;const{x:s,y:n}=e,r=this.getAttribute("orient").getString("auto"),o=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(s,n),"auto"===r&&t.rotate(i),"strokeWidth"===o&&t.scale(t.lineWidth,t.lineWidth),t.save();const a=new st(this.document);a.type=this.type,a.attributes.viewBox=new E(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.refX=new E(this.document,"refX",this.getAttribute("refX").getValue()),a.attributes.refY=new E(this.document,"refY",this.getAttribute("refY").getValue()),a.attributes.width=new E(this.document,"width",this.getAttribute("markerWidth").getValue()),a.attributes.height=new E(this.document,"height",this.getAttribute("markerHeight").getValue()),a.attributes.overflow=new E(this.document,"overflow",this.getAttribute("overflow").getValue()),a.attributes.fill=new E(this.document,"fill",this.getAttribute("fill").getColor("black")),a.attributes.stroke=new E(this.document,"stroke",this.getAttribute("stroke").getValue("none")),a.children=this.children,a.render(t),t.restore(),"strokeWidth"===o&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===r&&t.rotate(-i),t.translate(-s,-n)}constructor(){super(...arguments),this.type="marker"}},defs:class extends W{render(){}constructor(){super(...arguments),this.type="defs"}},linearGradient:class extends at{getGradient(t,e){const i="objectBoundingBox"===this.getGradientUnits(),s=i?e.getBoundingBox(t):null;if(i&&!s)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));const n=i?s.x+s.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),r=i?s.y+s.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),o=i?s.x+s.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),a=i?s.y+s.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===o&&r===a?null:t.createLinearGradient(n,r,o,a)}constructor(t,e,i){super(t,e,i),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}},radialGradient:class extends at{getGradient(t,e){const i="objectBoundingBox"===this.getGradientUnits(),s=e.getBoundingBox(t);if(i&&!s)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");const n=i?s.x+s.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),r=i?s.y+s.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y");let o=n,a=r;this.getAttribute("fx").hasValue()&&(o=i?s.x+s.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(a=i?s.y+s.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));const h=i?(s.width+s.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),l=this.getAttribute("fr").getPixels();return t.createRadialGradient(o,a,l,n,r,h)}constructor(t,e,i){super(t,e,i),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}},stop:class extends W{constructor(t,e,i){super(t,e,i),this.type="stop";const s=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity");let r=this.getStyle("stop-color",!0);""===r.getString()&&r.setValue("#000"),n.hasValue()&&(r=r.addOpacity(n)),this.offset=s,this.color=r.getColor()}},animate:ht,animateColor:class extends ht{calcValue(){const{progress:t,from:e,to:i}=this.getProgress(),s=new n(e.getColor()),r=new n(i.getColor());if(s.ok&&r.ok){const e=s.r+(r.r-s.r)*t,i=s.g+(r.g-s.g)*t,n=s.b+(r.b-s.b)*t;return"rgb(".concat(Math.floor(e),", ").concat(Math.floor(i),", ").concat(Math.floor(n),")")}return this.getAttribute("from").getColor()}constructor(){super(...arguments),this.type="animateColor"}},animateTransform:class extends ht{calcValue(){const{progress:t,from:e,to:i}=this.getProgress(),s=h(e.getString()),n=h(i.getString());return s.map(((e,i)=>e+(n[i]-e)*t)).join(" ")}constructor(){super(...arguments),this.type="animateTransform"}},font:class extends W{render(){}constructor(t,e,i){super(t,e,i),this.type="font",this.isArabic=!1,this.glyphs={},this.arabicGlyphs={},this.isRTL=!1,this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();const{definitions:s}=t,{children:n}=this;for(const r of n)if(r instanceof lt){this.fontFace=r;const t=r.getStyle("font-family");t.hasValue()&&(s[t.getString()]=this)}else if(r instanceof ut)this.missingGlyph=r;else if(r instanceof ct)if(r.arabicForm){this.isRTL=!0,this.isArabic=!0;const t=this.arabicGlyphs[r.unicode];"undefined"===typeof t?this.arabicGlyphs[r.unicode]={[r.arabicForm]:r}:t[r.arabicForm]=r}else this.glyphs[r.unicode]=r}},"font-face":lt,"missing-glyph":ut,glyph:ct,text:J,tspan:K,tref:class extends J{getText(){const t=this.getHrefAttribute().getDefinition();if(t){const e=t.children[0];if(e)return e.getText()}return""}constructor(){super(...arguments),this.type="tref"}},a:class extends J{getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);const{document:e,x:i,y:s}=this,{mouse:n}=e.screen,r=new E(e,"fontSize",Q.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new G(i,s-r.getPixels("y"),i+this.measureText(t),s))}else if(this.children.length>0){const e=new ot(this.document);e.children=this.children,e.parent=this,e.render(t)}}onClick(){const{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}constructor(t,e,i){super(t,e,i),this.type="a";const{childNodes:s}=e,n=s[0],r=s.length>0&&Array.from(s).every((t=>3===t.nodeType));this.hasText=r,this.text=r?this.getTextFromNode(n):""}},textPath:class extends J{getText(){return this.text}path(t){const{dataArray:e}=this;t&&t.beginPath(),e.forEach((e=>{let{type:i,points:s}=e;switch(i){case et.LINE_TO:t&&t.lineTo(s[0],s[1]);break;case et.MOVE_TO:t&&t.moveTo(s[0],s[1]);break;case et.CURVE_TO:t&&t.bezierCurveTo(s[0],s[1],s[2],s[3],s[4],s[5]);break;case et.QUAD_TO:t&&t.quadraticCurveTo(s[0],s[1],s[2],s[3]);break;case et.ARC:{const[e,i,n,r,o,a,h,l]=s,c=n>r?n:r,u=n>r?1:n/r,g=n>r?r/n:1;t&&(t.translate(e,i),t.rotate(h),t.scale(u,g),t.arc(0,0,c,o,o+a,Boolean(1-l)),t.scale(1/u,1/g),t.rotate(-h),t.translate(-e,-i));break}case et.CLOSE_PATH:t&&t.closePath()}}))}renderChildren(t){this.setTextData(t),t.save();const e=this.parent.getStyle("text-decoration").getString(),i=this.getFontSize(),{glyphInfo:s}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),s.forEach(((s,n)=>{const{p0:r,p1:o,rotation:a,text:h}=s;t.save(),t.translate(r.x,r.y),t.rotate(a),t.fillStyle&&t.fillText(h,0,0),t.strokeStyle&&t.strokeText(h,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(r.x,r.y+i/8),t.lineTo(o.x,o.y+i/5))})),"underline"===e&&(t.lineWidth=i/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,i,s,n,r,o,a,h){let l=r,c=this.measureText(t,a);" "===a&&"justify"===e&&i<s&&(c+=(s-i)/n),h>-1&&(l+=this.getLetterSpacingAt(h));const u=this.textHeight/20,g=this.getEquidistantPointOnPath(l,u,0),d=this.getEquidistantPointOnPath(l+c,u,0),p={p0:g,p1:d},f=g&&d?Math.atan2(d.y-g.y,d.x-g.x):0;if(o){const t=Math.cos(Math.PI/2+f)*o,e=Math.cos(-f)*o;p.p0={...g,x:g.x+t,y:g.y+e},p.p1={...d,x:d.x+t,y:d.y+e}}return l+=c,{offset:l,segment:p,rotation:f}}measureText(t,e){const{measuresCache:i}=this,s=e||this.getText();if(i.has(s))return i.get(s);const n=this.measureTargetText(t,s);return i.set(s,n),n}setTextData(t){if(this.glyphInfo)return;const e=this.getText(),i=e.split(""),s=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map((t=>t.getPixels("x"))),r=this.parent.getAttribute("dy").getPixels("y"),o=this.parent.getStyle("text-anchor").getString("start"),a=this.getStyle("letter-spacing"),h=this.parent.getStyle("letter-spacing");let l=0;a.hasValue()&&"inherit"!==a.getValue()?a.hasValue()&&"initial"!==a.getValue()&&"unset"!==a.getValue()&&(l=a.getPixels()):l=h.getPixels();const c=[],u=e.length;this.letterSpacingCache=c;for(let x=0;x<u;x++)c.push("undefined"!==typeof n[x]?n[x]:l);const g=c.reduce(((t,e,i)=>0===i?0:t+e||0),0),d=this.measureText(t),p=Math.max(d+g,0);this.textWidth=d,this.textHeight=this.getFontSize(),this.glyphInfo=[];const f=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*f;let m=0;"middle"!==o&&"center"!==o||(m=-p/2),"end"!==o&&"right"!==o||(m=-p),m+=y,i.forEach(((e,n)=>{const{offset:a,segment:h,rotation:l}=this.findSegmentToFitChar(t,o,p,f,s,m,r,e,n);m=a,h.p0&&h.p1&&this.glyphInfo.push({text:i[n],p0:h.p0,p1:h.p1,rotation:l})}))}parsePathData(t){if(this.pathLength=-1,!t)return[];const e=[],{pathParser:i}=t;for(i.reset();!i.isEnd();){const{current:t}=i,s=t?t.x:0,n=t?t.y:0,r=i.next();let o=r.type,a=[];switch(r.type){case et.MOVE_TO:this.pathM(i,a);break;case et.LINE_TO:o=this.pathL(i,a);break;case et.HORIZ_LINE_TO:o=this.pathH(i,a);break;case et.VERT_LINE_TO:o=this.pathV(i,a);break;case et.CURVE_TO:this.pathC(i,a);break;case et.SMOOTH_CURVE_TO:o=this.pathS(i,a);break;case et.QUAD_TO:this.pathQ(i,a);break;case et.SMOOTH_QUAD_TO:o=this.pathT(i,a);break;case et.ARC:a=this.pathA(i);break;case et.CLOSE_PATH:it.pathZ(i)}r.type!==et.CLOSE_PATH?e.push({type:o,points:a,start:{x:s,y:n},pathLength:this.calcLength(s,n,o,a)}):e.push({type:et.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){const{x:i,y:s}=it.pathM(t).point;e.push(i,s)}pathL(t,e){const{x:i,y:s}=it.pathL(t).point;return e.push(i,s),et.LINE_TO}pathH(t,e){const{x:i,y:s}=it.pathH(t).point;return e.push(i,s),et.LINE_TO}pathV(t,e){const{x:i,y:s}=it.pathV(t).point;return e.push(i,s),et.LINE_TO}pathC(t,e){const{point:i,controlPoint:s,currentPoint:n}=it.pathC(t);e.push(i.x,i.y,s.x,s.y,n.x,n.y)}pathS(t,e){const{point:i,controlPoint:s,currentPoint:n}=it.pathS(t);return e.push(i.x,i.y,s.x,s.y,n.x,n.y),et.CURVE_TO}pathQ(t,e){const{controlPoint:i,currentPoint:s}=it.pathQ(t);e.push(i.x,i.y,s.x,s.y)}pathT(t,e){const{controlPoint:i,currentPoint:s}=it.pathT(t);return e.push(i.x,i.y,s.x,s.y),et.QUAD_TO}pathA(t){let{rX:e,rY:i,sweepFlag:s,xAxisRotation:n,centp:r,a1:o,ad:a}=it.pathA(t);return 0===s&&a>0&&(a-=2*Math.PI),1===s&&a<0&&(a+=2*Math.PI),[r.x,r.y,e,i,o,a,n,s]}calcLength(t,e,i,s){let n=0,r=null,o=null,a=0;switch(i){case et.LINE_TO:return this.getLineLength(t,e,s[0],s[1]);case et.CURVE_TO:for(n=0,r=this.getPointOnCubicBezier(0,t,e,s[0],s[1],s[2],s[3],s[4],s[5]),a=.01;a<=1;a+=.01)o=this.getPointOnCubicBezier(a,t,e,s[0],s[1],s[2],s[3],s[4],s[5]),n+=this.getLineLength(r.x,r.y,o.x,o.y),r=o;return n;case et.QUAD_TO:for(n=0,r=this.getPointOnQuadraticBezier(0,t,e,s[0],s[1],s[2],s[3]),a=.01;a<=1;a+=.01)o=this.getPointOnQuadraticBezier(a,t,e,s[0],s[1],s[2],s[3]),n+=this.getLineLength(r.x,r.y,o.x,o.y),r=o;return n;case et.ARC:{n=0;const t=s[4],e=s[5],i=s[4]+e;let h=Math.PI/180;if(Math.abs(t-i)<h&&(h=Math.abs(t-i)),r=this.getPointOnEllipticalArc(s[0],s[1],s[2],s[3],t,0),e<0)for(a=t-h;a>i;a-=h)o=this.getPointOnEllipticalArc(s[0],s[1],s[2],s[3],a,0),n+=this.getLineLength(r.x,r.y,o.x,o.y),r=o;else for(a=t+h;a<i;a+=h)o=this.getPointOnEllipticalArc(s[0],s[1],s[2],s[3],a,0),n+=this.getLineLength(r.x,r.y,o.x,o.y),r=o;return o=this.getPointOnEllipticalArc(s[0],s[1],s[2],s[3],i,0),n+=this.getLineLength(r.x,r.y,o.x,o.y),n}}return 0}getPointOnLine(t,e,i,s,n){let r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:i;const a=(n-i)/(s-e+b);let h=Math.sqrt(t*t/(1+a*a));s<e&&(h*=-1);let l=a*h,c=null;if(s===e)c={x:r,y:o+l};else if((o-i)/(r-e+b)===a)c={x:r+h,y:o+l};else{let u=0,g=0;const d=this.getLineLength(e,i,s,n);if(d<b)return null;let p=(r-e)*(s-e)+(o-i)*(n-i);p/=d*d,u=e+p*(s-e),g=i+p*(n-i);const f=this.getLineLength(r,o,u,g),y=Math.sqrt(t*t-f*f);h=Math.sqrt(y*y/(1+a*a)),s<e&&(h*=-1),l=a*h,c={x:u+h,y:g+l}}return c}getPointOnPath(t){const e=this.getPathLength();let i=0,s=null;if(t<-5e-5||t-5e-5>e)return null;const{dataArray:n}=this;for(const r of n){if(r&&(r.pathLength<5e-5||i+r.pathLength+5e-5<t)){i+=r.pathLength;continue}const e=t-i;let n=0;switch(r.type){case et.LINE_TO:s=this.getPointOnLine(e,r.start.x,r.start.y,r.points[0],r.points[1],r.start.x,r.start.y);break;case et.ARC:{const t=r.points[4],i=r.points[5],o=r.points[4]+i;if(n=t+e/r.pathLength*i,i<0&&n<o||i>=0&&n>o)break;s=this.getPointOnEllipticalArc(r.points[0],r.points[1],r.points[2],r.points[3],n,r.points[6]);break}case et.CURVE_TO:n=e/r.pathLength,n>1&&(n=1),s=this.getPointOnCubicBezier(n,r.start.x,r.start.y,r.points[0],r.points[1],r.points[2],r.points[3],r.points[4],r.points[5]);break;case et.QUAD_TO:n=e/r.pathLength,n>1&&(n=1),s=this.getPointOnQuadraticBezier(n,r.start.x,r.start.y,r.points[0],r.points[1],r.points[2],r.points[3])}if(s)return s;break}return null}getLineLength(t,e,i,s){return Math.sqrt((i-t)*(i-t)+(s-e)*(s-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce(((t,e)=>e.pathLength>0?t+e.pathLength:t),0)),this.pathLength}getPointOnCubicBezier(t,e,i,s,n,r,o,a,h){return{x:a*P(t)+r*C(t)+s*A(t)+e*T(t),y:h*P(t)+o*C(t)+n*A(t)+i*T(t)}}getPointOnQuadraticBezier(t,e,i,s,n,r,o){return{x:r*V(t)+s*k(t)+e*M(t),y:o*V(t)+n*k(t)+i*M(t)}}getPointOnEllipticalArc(t,e,i,s,n,r){const o=Math.cos(r),a=Math.sin(r),h=i*Math.cos(n),l=s*Math.sin(n);return{x:t+(h*o-l*a),y:e+(h*a+l*o)}}buildEquidistantCache(t,e){const i=this.getPathLength(),s=e||.25,n=t||i/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==s){this.equidistantCache={step:n,precision:s,points:[]};let t=0;for(let e=0;e<=i;e+=s){const i=this.getPointOnPath(e),r=this.getPointOnPath(e+s);i&&r&&(t+=this.getLineLength(i.x,i.y,r.x,r.y),t>=n&&(this.equidistantCache.points.push({x:i.x,y:i.y,distance:e}),t-=n))}}}getEquidistantPointOnPath(t,e,i){if(this.buildEquidistantCache(e,i),t<0||t-this.getPathLength()>5e-5)return null;const s=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[s]||null}constructor(t,e,i){super(t,e,i),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);const s=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(s)}},image:class extends Z{async loadImage(t){try{const e=await this.document.createImage(t);this.image=e}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}this.loaded=!0}async loadSvg(t){const e=gt.exec(t);if(e){const t=e[5];t&&("base64"===e[4]?this.image=atob(t):this.image=decodeURIComponent(t))}else try{const e=await this.document.fetch(t),i=await e.text();this.image=i}catch(i){console.error('Error while loading image "'.concat(t,'":'),i)}this.loaded=!0}renderChildren(t){const{document:e,image:i,loaded:s}=this,n=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),o=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");if(s&&i&&o&&a){if(t.save(),t.translate(n,r),"string"===typeof i){const s=e.canvg.forkString(t,i,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:o,scaleHeight:a}),{documentElement:n}=s.document;n&&(n.parent=this),s.render()}else e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:o,desiredWidth:i.width,height:a,desiredHeight:i.height}),this.loaded&&("complete"in i&&!i.complete||t.drawImage(i,0,0));t.restore()}}getBoundingBox(){const t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),i=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");return new G(t,e,t+i,e+s)}constructor(t,e,i){super(t,e,i),this.type="image",this.loaded=!1;const s=this.getHrefAttribute().getString();if(!s)return;const n=s.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(s);t.images.push(this),n?this.loadSvg(s):this.loadImage(s)}},g:ot,symbol:class extends Z{render(t){}constructor(){super(...arguments),this.type="symbol"}},style:pt,use:class extends Z{setContext(t){super.setContext(t);const e=this.getAttribute("x"),i=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),i.hasValue()&&t.translate(0,i.getPixels("y"))}path(t){const{element:e}=this;e&&e.path(t)}renderChildren(t){const{document:e,element:i}=this;if(i){let s=i;if("symbol"===i.type&&(s=new st(e),s.attributes.viewBox=new E(e,"viewBox",i.getAttribute("viewBox").getString()),s.attributes.preserveAspectRatio=new E(e,"preserveAspectRatio",i.getAttribute("preserveAspectRatio").getString()),s.attributes.overflow=new E(e,"overflow",i.getAttribute("overflow").getString()),s.children=i.children,i.styles.opacity=new E(e,"opacity",this.calculateOpacity())),"svg"===s.type){const t=this.getStyle("width",!1,!0),i=this.getStyle("height",!1,!0);t.hasValue()&&(s.attributes.width=new E(e,"width",t.getString())),i.hasValue()&&(s.attributes.height=new E(e,"height",i.getString()))}const n=s.parent;s.parent=this,s.render(t),s.parent=n}}getBoundingBox(t){const{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){const{document:t,element:e}=this;return e?H.fromElement(t,e):null}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}constructor(){super(...arguments),this.type="use"}},mask:St,clipPath:class extends W{apply(t){const{document:e}=this,i=Reflect.getPrototypeOf(t),{beginPath:s,closePath:n}=t;i&&(i.beginPath=wt,i.closePath=wt),Reflect.apply(s,t,[]),this.children.forEach((s=>{if(!("path"in s))return;let r="elementTransform"in s?s.elementTransform():null;r||(r=H.fromElement(e,s)),r&&r.apply(t),s.path(t),i&&(i.closePath=n),r&&r.unapply(t)})),Reflect.apply(n,t,[]),t.clip(),i&&(i.beginPath=s,i.closePath=n)}render(t){}constructor(){super(...arguments),this.type="clipPath"}},filter:vt,feDropShadow:class extends W{apply(t,e,i,s,n){}constructor(t,e,i){super(t,e,i),this.type="feDropShadow",this.addStylesFromStyleDefinition()}},feMorphology:class extends W{apply(t,e,i,s,n){}constructor(){super(...arguments),this.type="feMorphology"}},feComposite:class extends W{apply(t,e,i,s,n){}constructor(){super(...arguments),this.type="feComposite"}},feColorMatrix:bt,feGaussianBlur:class extends W{apply(t,e,i,s,n){const{document:r,blurRadius:a}=this,h=r.window?r.window.document.body:null,l=t.canvas;l.id=r.getUniqueId(),h&&(l.style.display="none",h.appendChild(l)),(0,o.dD)(l,e,i,s,n,a),h&&h.removeChild(l)}constructor(t,e,i){super(t,e,i),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}},title:class extends W{constructor(){super(...arguments),this.type="title"}},desc:class extends W{constructor(){super(...arguments),this.type="desc"}}};const Ct=12;class At{bindCreateImage(t,e){return"boolean"===typeof e?(i,s)=>t(i,"boolean"===typeof s?s:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){const{emSizeStack:t}=this;return t[t.length-1]||Ct}set emSize(t){const{emSizeStack:e}=this;e.push(t)}popEmSize(){const{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every((t=>t.loaded))}isFontsLoaded(){return this.fonts.every((t=>t.loaded))}createDocumentElement(t){const e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){const e=t.nodeName.replace(/^[^:]+:/,""),i=At.elementTypes[e];return i?new i(this,t):new U(this,t)}createTextNode(t){return new tt(this,t)}setViewBox(t){this.screen.setViewBox({document:this,...t})}constructor(t){let{rootEmSize:e=Ct,emSize:i=Ct,createCanvas:s=At.createCanvas,createImage:n=At.createImage,anonymousCrossOrigin:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions={},this.styles={},this.stylesSpecificity={},this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=i,this.createCanvas=s,this.createImage=this.bindCreateImage(n,r),this.screen.wait((()=>this.isImagesLoaded())),this.screen.wait((()=>this.isFontsLoaded()))}}At.createCanvas=function(t,e){const i=document.createElement("canvas");return i.width=t,i.height=e,i},At.createImage=async function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=document.createElement("img");return e&&(i.crossOrigin="Anonymous"),new Promise(((e,s)=>{i.onload=()=>{e(i)},i.onerror=(t,e,i,n,r)=>{s(r)},i.src=t}))},At.elementTypes=Pt;class Tt{static async from(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=new _(i),n=await s.parse(e);return new Tt(t,n,i)}static fromString(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=new _(i).parseFromString(e);return new Tt(t,s,i)}fork(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Tt.from(t,e,{...this.options,...i})}forkString(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Tt.fromString(t,e,{...this.options,...i})}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}async render(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.start({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0,...t}),await this.ready(),this.stop()}start(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{documentElement:e,screen:i,options:s}=this;i.start(e,{enableRedraw:!0,...s,...t})}stop(){this.screen.stop()}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,i)}constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new _(i),this.screen=new R(t,i),this.options=i;const s=new At(this,i),n=s.createDocumentElement(e);this.document=s,this.documentElement=n}}}}]);
//# sourceMappingURL=8185.e460a491.chunk.js.map