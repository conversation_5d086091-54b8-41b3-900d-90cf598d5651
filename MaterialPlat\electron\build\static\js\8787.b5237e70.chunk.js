"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[8787],{22:(e,t,a)=>{a.d(t,{A:()=>w});var l=a(65043),n=a(80077),i=a(74117),o=a(56543),r=a(70579);const d=(0,l.lazy)((()=>a.e(7582).then(a.bind(a,97582)))),s=(0,l.lazy)((()=>a.e(9697).then(a.bind(a,49697)))),c=(0,l.lazy)((()=>a.e(1159).then(a.bind(a,1159)))),A=(0,l.lazy)((()=>a.e(6823).then(a.bind(a,96823)))),u=(0,l.lazy)((()=>Promise.all([a.e(9372),a.e(8643)]).then(a.bind(a,88643)))),p=(0,l.lazy)((()=>a.e(9871).then(a.bind(a,69871)))),v=(0,l.lazy)((()=>a.e(6741).then(a.bind(a,6741)))),m=(0,l.lazy)((()=>Promise.all([a.e(7326),a.e(4607),a.e(1856)]).then(a.bind(a,31856)))),h=(0,l.lazy)((()=>a.e(6377).then(a.bind(a,56377)))),g=(0,l.lazy)((()=>a.e(120).then(a.bind(a,20120)))),b={[o.ps.\u6587\u672c]:d,[o.ps.\u6570\u5b57\u578b]:s,[o.ps.\u9009\u62e9]:c,[o.ps.\u81ea\u5b9a\u4e49\u6570\u7ec4]:A,[o.ps.\u65f6\u95f4\u65e5\u671f]:u,[o.ps.\u5e03\u5c14\u578b]:p,[o.ps.\u6309\u94ae]:v,[o.ps.Control]:m,[o.ps.Label]:h,[o.ps.Picture]:g},y=e=>{let{variable:t,disabled:a,onChange:n,onError:i}=e;const o=b[null===t||void 0===t?void 0:t.variable_type];return o?(0,r.jsx)(l.Suspense,{fallback:(0,r.jsx)(r.Fragment,{}),children:(0,r.jsx)(o,{variable:t,disabled:a,onChange:n,onError:i})}):(0,r.jsx)(r.Fragment,{children:"\u5f53\u524d\u8f93\u5165\u53d8\u91cf\u7c7b\u578b\u4e0d\u652f\u6301\u6e32\u67d3"})};var x=a(81143),f=a(68374);const C=x.Ay.div`
    display: flex;
    flex-direction: column;
    ${e=>{let{openMarginBottom:t}=e;return t&&"margin-bottom: 24px;"}}

    .variable_name{
        ${e=>{let{labelItalic:t}=e;return t&&"font-style: italic;"}}
        ${e=>{let{labelBold:t}=e;return t&&"font-weight: bold;"}}
    }
    
    .input-render-right{
        ${e=>{let{contentItalic:t}=e;return t&&"font-style: italic!important;"}}
        ${e=>{let{contentBold:t}=e;return t&&"font-weight: bold!important;"}}
        input{
            ${e=>{let{contentItalic:t}=e;return t&&"font-style: italic!important;"}}
            ${e=>{let{contentBold:t}=e;return t&&"font-weight: bold!important;"}}
        }
        
        .ant-select-selection-item{
            ${e=>{let{contentBold:t}=e;return t&&"font-weight: bold!important;"}}
        }
    }

    .error-message{
        text-align: center;
        color: #ff4d4f;
        font-size: 12px;
        margin-top: 4px;
    }

    /* 加减控件用 */
    .render-params-layout {
        min-width: 10vw;
    }

    .input-width {
        width: ${(0,f.D0)("180px")};
    }

    .input-readonly{
        color: rgba(0,0,0,0.25);
        background-color: rgba(0,0,0,0.04);
        border-color: #d9d9d9;
        box-shadow: none;
        cursor: not-allowed;
        opacity: 1;
    }
    
    .checkbox-input-layout {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        .ant-checkbox-wrapper+.ant-checkbox-wrapper {
            margin-inline-start: 0;
        }
    }

    /* 时间日期输入框 */
    .date-width {
        width: ${(0,f.D0)("265px")}
    }


`,w=e=>{let{variable:t,disabled:a=!1,scriptData:o,onChange:d,openMarginBottom:s,onTriggerScript:c,labelItalic:A,labelBold:u,contentItalic:p,contentBold:v}=e;const{t:m}=(0,i.Bd)(),h=(0,n.d4)((e=>e.global.unitList)),[g,b]=(0,l.useState)(""),[x,f]=(0,l.useState)(!0),[w,E]=(0,l.useState)(!1),[S,_]=(0,l.useState)(!1),[R,j]=(0,l.useState)(t);(0,l.useEffect)((()=>{j(t)}),[t]),(0,l.useEffect)((()=>{if(o){var e,t,a,l,n,i,r,d;const m=null!==(e=null===(t=o.find((e=>e[null===R||void 0===R?void 0:R.code])))||void 0===t?void 0:t[null===R||void 0===R?void 0:R.code])&&void 0!==e?e:{};var s;if(f(null===(a=null===m||void 0===m?void 0:m.isVisible)||void 0===a||a),E(null!==(l=null===m||void 0===m?void 0:m.isDisabled)&&void 0!==l&&l),_(null!==(n=null===m||void 0===m?void 0:m.isCheckDisabled)&&void 0!==n&&n),null!==R&&void 0!==R&&R.is_enable&&"isCheck"in m)j({...R,is_feature:null!==(s=m.isCheck)&&void 0!==s&&s});if(m.unit&&null!==R&&void 0!==R&&null!==(i=R.default_val)&&void 0!==i&&i.unit&&null!==R&&void 0!==R&&null!==(r=R.default_val)&&void 0!==r&&r.unitType){var c,A,u,p,v;const e=null===h||void 0===h?void 0:h.find((e=>{var t;let{code:a}=e;return a===(null===m||void 0===m||null===(t=m.unit)||void 0===t?void 0:t[0])})),t=null===(c=e.units)||void 0===c?void 0:c.find((e=>{let{code:t}=e;return t===(null===m||void 0===m?void 0:m.unit[1])}));j({...R,default_val:{...R.default_val,unit:null!==(A=null===t||void 0===t?void 0:t.id)&&void 0!==A?A:null===R||void 0===R||null===(u=R.default_val)||void 0===u?void 0:u.unit,unitType:null!==(p=null===e||void 0===e?void 0:e.id)&&void 0!==p?p:null===R||void 0===R||null===(v=R.default_val)||void 0===v?void 0:v.unitType}})}null!==m&&void 0!==m&&m.mode&&null!==R&&void 0!==R&&null!==(d=R.default_val)&&void 0!==d&&d.type&&j({...R,default_val:{...R.default_val,type:null===m||void 0===m?void 0:m.mode}})}}),[o,R]);return x&&(0,r.jsxs)(C,{openMarginBottom:s,title:null===R||void 0===R?void 0:R.description,labelItalic:A,labelBold:u,contentItalic:p,contentBold:v,children:[(0,r.jsx)(y,{variable:R,disabled:a||w,isCheckDisabled:S,onError:e=>{var t;b(null!==(t=null===e||void 0===e?void 0:e.errorMsg)&&void 0!==t?t:""),e&&(null===c||void 0===c||c(null===e||void 0===e?void 0:e.type))},onChange:e=>{j(e),d(e)}}),g&&(0,r.jsx)("div",{className:"error-message",children:m(g)})]})}},84:(e,t,a)=>{a.d(t,{A:()=>n});a(65043);var l=a(80077);const n=()=>{const e=(0,l.wA)();return{openDialog:t=>{let{type:a,data:l}=t;e({type:a,param:!0,data:l})},closeDialog:t=>{let{type:a}=t;e({type:a,param:!1})}}}},232:(e,t,a)=>{a.d(t,{A:()=>d});var l=a(65043),n=a(80077),i=a(14387),o=a(15637),r=a(67208);const d=()=>{const e=(0,n.wA)(),t=(0,n.d4)((e=>e.template.auxiliaryLineList)),a=(0,l.useMemo)((()=>t.filter((e=>e.channel_type!==i.FR.\u4fe1\u53f7\u53d8\u91cf))),[t]),d=(0,l.useMemo)((()=>t.filter((e=>e.channel_type===i.FR.\u4fe1\u53f7\u53d8\u91cf))),[t]),s=async()=>{try{const t=await(0,r.hvW)();if(t){const a=t.sort(((e,t)=>new Date(t.created_time)-new Date(e.created_time)));e({type:o.LW,param:a})}}catch(t){console.log(t)}};return{initAuxiliaryLineData:s,saveAuxiliaryLine:async e=>{try{await("id"in e?(0,r.Yqv)(e):(0,r.A0m)(e))&&s()}catch(t){console.log(t)}},delAuxiliaryLine:async e=>{try{await(0,r.G_U)(e)&&s()}catch(t){console.log(t)}},auxiliaryLineArrayList:a,auxiliaryLineSignalList:d}}},3138:(e,t,a)=>{a.d(t,{A:()=>N});var l=a(80077),n=a(88359),i=a(72838),o=a(89800),r=a(16090),d=a(16133),s=a(42651),c=a(73154),A=a(64981),u=a(84856),p=a(21256),v=a(33154),m=a(78583),h=a(30780),g=a(41753),b=a(49952),y=a(28186),x=a(10866),f=a(67208),C=a(34458),w=a(12847),E=a(24870),S=a(44409),_=a(42866),R=a(232),j=a(17103),I=a(65694),T=a(14463),P=a(57610),k=a(50540),D=a(15637),U=a(81077);const N=()=>{const e=(0,l.wA)(),{submitSubTaskStatus:t,submitSubTaskSample:a}=(0,S.A)(),{initShortcutData:N}=(0,i.A)(),{initPageData:B,saveDefaultId:O}=(0,w.A)(),{initExportData:M}=(0,_.A)(),{initDialogData:L}=(0,s.A)(),{initHeaderData:V}=(0,A.A)(),{initGuideData:Q}=(0,c.A)(),{initSignalGroupData:F,initSignalData:J}=(0,o.A)(),{initResultData:Y,initTestResultData:K}=(0,d.A)(),{initActionData:H,runOnStartUpAction:W}=(0,r.A)(),{initTemplateLayout:G,saveLayoutData:z}=(0,u.A)(),{initProjectHistoryData:X}=(0,m.A)(),{initWidget:Z,initWidgetStatus:q}=(0,p.A)(),{initUnitsData:$,initSysUnitsData:ee}=(0,v.A)(),{initTableConfigData:te}=(0,h.A)(),{initTemplateMap:ae}=(0,E.A)(),{initModuleData:le}=(0,U.A)(),{initArrayCurveConfig:ne}=(0,y.A)(),{initDefaultSample:ie,initSampleTree:oe,updateOptSample:re,initSampleAboutList:de,initSamples:se,initSysSamples:ce}=(0,x.A)(),{initVideoData:Ae}=(0,g.A)(),{initDynamicCurveList:ue}=(0,b.A)(),{initAuxiliaryLineData:pe}=(0,R.A)(),{getStaticCurveSettingList:ve}=(0,j.A)(),me=async()=>{try{const[l,n,i]=await Promise.all([(0,f.vqI)(),(0,f.HBc)(),(0,f.BdW)()]);if(l){const{flow_chart_status:o=[],sample_code:r}=l;if(t(null!==o&&void 0!==o?o:[]),r&&n&&n.length>0){var e;const t=n.flatMap((e=>e.children)).find((e=>e.code===r));null!==(e=i.runnings)&&void 0!==e&&e.some((e=>String(e)===(0,C.HN)()))&&a(t),re(t)}}}catch(l){throw console.log(l),l}};return{initRedux:async function(){let{isNewOpen:t=!1,pageId:a="1",pageName:l}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const i=await G(a,l);return Promise.all([q(),B(),Z(),oe(),e((0,n.w)()),N(),F(),J(),H({isNewOpen:t,pageId:i}),Y(),K(),L(),Q(),V(),$(),te(),ae(),Ae(),ie(),de(),M(),ue(),pe(),ve(),ne(),...(0,C.HN)()?[me(),X()]:[],se()]).then((()=>{t&&W({pageId:i})}))},clearTemplateRedux:()=>{z()},initSystem:()=>{ee(),ce(),le()},clearRedux:()=>{e({type:I.ft}),e({type:T.sW}),e({type:P.kU}),e({type:k.E_}),e({type:D.IK})}}}},4178:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(74117),n=a(56543);const{ipcRenderer:i}="true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?window.require("electron"):{};let o=0;"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.on("subWindowClose",(()=>{o-=1}));const r=()=>{const{t:e}=(0,l.Bd)();return{updateTitle:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e(n.az);"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.send("update-title",t)},openDialog:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"open-dialog",t=arguments.length>1?arguments[1]:void 0;if("true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER){return i.sendSync(e,t)}return""},sendChildProcess:function(){arguments.length>0&&void 0!==arguments[0]||n.az;"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.send("child-process-cmd",{cmd:"remove-childs"})},saveWriteFile:e=>{"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.send("save-write-file",e)},onListenIPC:(e,t)=>{"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.on(e,t)},getReadFile:e=>{"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.send("read-file",e)},offListenIPC:(e,t)=>{"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.off(e,t)},getReadLog:async e=>"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?i.invoke("read-log",e):null,getRead:async e=>"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?i.invoke("read",e):null,saveVideoFile:e=>"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?i.invoke("save-video",e):null,readAppConfig:async function(){let{key:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?i.invoke("readAppConfig",{key:e}):null},updateWindowSize:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{width:1920,height:1080};"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.send("update-window-size",e)},windowMaximize:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER&&i.send("maximize",e)},startTaskServer:async()=>"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?i.send("startTaskServer"):null,killTaskServer:async()=>"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?i.invoke("killTaskServer"):null,openProjectSubWindow:async e=>{let{projectId:t,pageId:a,width:l,height:n}=e;const r=`/dialog/${t}/${a}`;return"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?(o+=1,i.send("openWindow",{url:r,width:l,height:n})):null},sendMsgToSubWindow:async e=>{let{subTopic:t,data:a}=e;return 0===o?null:"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?i.send("sendMsgToSubWindow",{subTopic:t,data:a}):null},showSubWindow:async()=>"true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?i.send("showSubWindow"):null}}},4554:(e,t,a)=>{a.d(t,{A:()=>c});var l=a(65043),n=a(95206),i=a(81143);a(68374);const o=i.Ay.div`
    /* 按钮基本样式 */
    .base {
        .ant-btn {
            background: rgba(232, 238, 255, 0.70);
            color: rgba(90, 120, 255, 1);
            border: 1px solid #5A78FF;
            &:hover {
                background: rgba(90, 120, 255, 0.1);
            }
            :disabled {
                border-color: #d9d9d9;
                color: rgba(0, 0, 0, 0.25);
                background-color: rgba(0, 0, 0, 0.04);
            }
        } 
    }
    /* 按钮其他样式 */
    .else {
        .ant-btn {
            background: #F2F3F9;
            color: rgba(90, 120, 255, 1);
            border: 1px solid #5A78FF;
            &:hover {
                background: rgba(90, 120, 255, 0.1);
            }
            :disabled {
                border-color: #d9d9d9;
                color: rgba(0, 0, 0, 0.25);
                background-color: rgba(0, 0, 0, 0.04);
            }
        } 
    }
    .ant-btn-default:disabled {
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);
        background-color: rgba(0, 0, 0, 0.04);
    }
`,r={base:"base",else:"else"};var d=a(70579);const s=(e,t)=>{const{children:a,type:l="base"}=e;return(0,d.jsx)(o,{children:(0,d.jsx)("div",{className:r[l],children:(0,d.jsx)(n.Ay,{...e,type:l,ref:t,children:a})})})},c=(0,l.forwardRef)(s)},8237:(e,t,a)=>{a.d(t,{Bd:()=>r,CH:()=>h,Fw:()=>c,Jx:()=>u,P:()=>m,PA:()=>n,Qu:()=>p,fJ:()=>o,h0:()=>s,oM:()=>i,pz:()=>v,qe:()=>A,rX:()=>d,y8:()=>l});const l={DEL:"del",UP:"up",DOWN:"down",CONFIRM:"confirm",CANCEL:"cancel",ADD:"add",EDIT:"edit"},n={NO_DEL:0,DEL:1},i={HOR:"hor",VER:"ver"},o={UNSHIFT:"unshift",PUSH:"push"},r=e=>{let{t:t}=e;return[{value:`${i.HOR}_${o.UNSHIFT}`,type:i.HOR,directionType:o.UNSHIFT,label:t("\u5411\u5de6\u7ad6\u5207")},{value:`${i.HOR}_${o.PUSH}`,type:i.HOR,directionType:o.PUSH,label:t("\u5411\u53f3\u7ad6\u5207")},{value:`${i.VER}_${o.UNSHIFT}`,type:i.VER,directionType:o.UNSHIFT,label:t("\u5411\u4e0a\u6a2a\u5207")},{value:`${i.VER}_${o.PUSH}`,type:i.VER,directionType:o.PUSH,label:t("\u5411\u4e0b\u6a2a\u5207")}]},d={EMPTY:"empty",CONTENT_SPLIT_EMPTY:"contentSplitEmpty",SAMPLE:"sample",SAMPLE_TABLE:"sampleTable",SAMPLE_STATISTIC_TABLE:"sampleStatisticTable",LIGHTNING_LINE_CHART:"lightningLineChart",INSTRUCTION_INPUT:"instructionInput",SHORTCUT:"shortcut",FOOTER:"footer",HEADER:"header",TAB_FIXED:"tabFixed",VIDEO:"video",PROCESS:"process",DYNAMIC_FORM:"dynamicForm",DIALOG:"dialog",LAYOUT:"layout",ELSE:"else",LOG:"log",RESTULEREPORT:"resultReport",DYNAMIC_SAMPLE:"dynamicSample",GAOZHOU_CURVE:"gaozhouCurve",GAOZHOU_TABLE:"gaozhouTable",BLOCK:"block",ATOM_INPUT:"atomInput",ATOM_INPUT_NUMBER:"atomInputNumber",ATOM_SELECT:"atomSelect",ATOM_BUTTON:"atomButton",ATOM_TABS:"atomTabs",ATOM_CHECKBOX_SINGLE:"atomCheckboxSingle",ATOM_CHECKBOX_GROUP:"atomCheckboxGroup",ATOM_TABLE_2_DATA_GATHER:"atomTable2DataGather",ATOM_RENDER_PARAMS:"atomRenderParams",ATOM_RESULT_LABEL:"atomResultLabel",ATOM_DATETIME:"atomDateTime",ATOM_LABEL:"atomLabel",RESULT_ARRAY_LABEL:"resultArrayLabel",RESULT_TABLE:"resulTable",CREEP_SAMPLE_PARAMS:"creepSampleParams",CUSTOM_WAVEFORM:"customWaveform","\u7a0b\u63a7\u53c2\u6570":"programmableParameters",CREEP_CURVE:"creepCurve",CREEP_SIGNAL_OFFSET_TABLE:"creepSignalOffsetTable",CREEP_TEST_DATA_TABLE:"creepTestDataTable",CREEP_TEMP_RANGE:"creepTempRange","\u5168\u5c40_\u5feb\u6377\u65b9\u5f0f":"globalShortcut","\u5168\u5c40_\u6570\u636e\u76d1\u63a7\u8868\u683c":"globalDataMonitorTable","\u5168\u5c40_\u65e5\u5fd7":"globalLog","\u5168\u5c40_\u5206\u5c4f\u76d1\u63a7":"globalSplitScreenMonitor","\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c":"doubleArrayTable","\u8fdb\u5ea6\u6761":"slider","\u6570\u5b57IO_input":"digitalInput","\u6570\u5b57IO_output":"digitalOutput","\u7279\u6b8a\u8868\u5934":"specialHead","\u8815\u53d8\u5206\u5c4f\u8868\u5934":"creepMonitoring","PID\u9762\u677f":"pidPanel",DYNAMIC_CURVE5:"dynamicCurve5",DYNAMIC_CURVE1:"dynamicCurve1",DYNAMIC_CURVE2:"dynamicCurve2",DYNAMIC_CURVE3:"dynamicCurve3",DYNAMIC_CURVE6:"dynamicCurve6",DYNAMIC_UP_LIMIT:"dynamicUpLimit",DYNAMIC_LOW_LIMIT:"dynamicLowLimit",DYNAMIC_FUNC_GENERATOR:"functionGenerator",DYNAMIC_CURVE_FITTING:"dynamicCurveFitting",GROUP_SAMPLE:"groupSample",SUB_TASK_PARAM:"subTaskParam",ARRAY_CURVE:"arrayCurve",PID_CONFIG:"pidConfig",TEST_REPORT:"testReport",REPORT:"report",PRINT_TIME:"printTime",PICTURE:"picture",TEXT:"text",LINE:"line",PAGE:"page",PARAM:"param",CHUNK:"chunk"},s={split:"split",pieChart:"\u997c\u56fe",SplashesChart:"\u6563\u70b9\u56fe",div:"div",empty:"",sample:"\u8bd5\u6837",sampleTable:"\u7ed3\u679c\u8868\u683c",sampleStatisticTable:"\u7ed3\u679c\u7edf\u8ba1\u8868\u683c",lightningLineChart:"\u8bd5\u9a8c\u66f2\u7ebf",instructionInput:"\u6307\u4ee4\u6027\u8f93\u5165",shortcut:"\u5feb\u6377\u65b9\u5f0f",footer:"\u5e95\u90e8",header:"\u8868\u5934",tabFixed:"\u6d3b\u9875\u5939",process:"\u6d41\u7a0b\u56fe",dynamicForm:"\u8bd5\u9a8c\u8bbe\u7f6e",dialog:"\u5bf9\u8bdd\u6846",layout:"\u5e03\u5c40",log:"\u65e5\u5fd7",resultReport:"\u7ed3\u679c\u62a5\u8868",subTaskParam:"\u5b50\u4efb\u52a1\u53c2\u6570"},c=(n.NO_DEL,n.NO_DEL,(e,t)=>({id:e,name:e,type:d.SPLIT,widget_id:"3",page_id:t,direction:i.HOR,view:null,children:[{id:`${e}-1`,name:"",widget_id:"3",page_id:t,type:d.EMPTY,direction:i.VER,view:null,children:[]}]})),A="\u672a\u9009\u62e9\u63a7\u4ef6",u={WARNING:"warning",SUCCESS:"success",ERROR:"error"},p=e=>{let{t:t}=e;return[{name:t("\u5e73\u5747\u503c"),id:"1"},{name:t("\u6700\u5c0f\u503c"),id:"2"},{name:t("\u6700\u5927\u503c"),id:"3"},{name:t("\u65b9\u5dee\u503c"),id:"4"}]},v={TABLE_CONTROL:["1","2"],TABLE_STATISTIC_CONTROL:["1","2","3"]},m={CONTENT:"content",DLE:"del",VERTICAL:"vertical",HORIZONTAL:"Horizontal"},h={"\u9ed8\u8ba4\u9875\u9762":"1","\u7ed3\u679c\u9875\u9762":"2","\u5907\u673a\u9875\u9762":"3"}},8361:(e,t,a)=>{a.d(t,{Ay:()=>y,F$:()=>h,yr:()=>m});var l=a(65043),n=a(80077),i=a(45303),o=a(45333),r=a(59551),d=a(64798),s=a(41389),c=a(16271),A=a(14255),u=a(19102),p=a(29977),v=a(56543);const m={Servo:{value:"Servo",label:"\u4f3a\u670d\u8f74"},Temp:{value:"Temp",label:"\u6e29\u5ea6\u8f74"},Creep:{value:"Creep",label:"\u8815\u53d8\u8f74"},AD:{value:"AD",label:"AD"},DA:{value:"DA",label:"DA"},Input:{value:"Input",label:"Input"},Output:{value:"Output",label:"Output"},HandBox:{value:"HandBox",label:"HandBox"}},h=(e,t)=>{try{var a,l,n,i,o,r,d,s;const c=(null===e||void 0===e||null===(a=e.mappingContext)||void 0===a?void 0:a.CreepAxisSensor)||[],A=(null===e||void 0===e||null===(l=e.mappingContext)||void 0===l?void 0:l.ServoAxisSensor)||[],u=(null===e||void 0===e||null===(n=e.mappingContext)||void 0===n?void 0:n.TempAxisSensor)||[],p=(null===e||void 0===e||null===(i=e.mappingContext)||void 0===i?void 0:i.AD)||[],h=(null===e||void 0===e||null===(o=e.mappingContext)||void 0===o?void 0:o.DA)||[],g=(null===e||void 0===e||null===(r=e.mappingContext)||void 0===r?void 0:r.Input)||[],b=(null===e||void 0===e||null===(d=e.mappingContext)||void 0===d?void 0:d.Output)||[],y=(null===e||void 0===e||null===(s=e.mappingContext)||void 0===s?void 0:s.HandBox)||[],x=e=>null!==e&&void 0!==e&&e.sensorName?e.sensorName.map(((e,t)=>({value:t,label:e}))):[],f=e=>t===v.Q1.\u8f74?[]:e.children.map(((e,a)=>{const l=t===v.Q1.\u901a\u9053?[]:x(e);return(t!==v.Q1.\u4f20\u611f\u5668||0!==l.length)&&{value:a,label:e.name,children:l}})).filter(Boolean),C=e=>e.map(((e,a)=>{var l;const n=t!==v.Q1.\u8f74?f(e):[];return(t===v.Q1.\u8f74||0!==n.length)&&{value:a,label:null!==(l=e.axisName)&&void 0!==l?l:e.name,children:n}})).filter(Boolean),w=C(A)||[],E=C(u)||[],S=C(c)||[],_=f({children:p})||[],R=f({children:h})||[],j=f({children:g})||[],I=f({children:b})||[],T=f({children:y})||[];return[0!==w.length&&{...null===m||void 0===m?void 0:m.Servo,children:w},0!==E.length&&{...null===m||void 0===m?void 0:m.Temp,children:E},0!==S.length&&{...null===m||void 0===m?void 0:m.Creep,children:S},0!==_.length&&{...null===m||void 0===m?void 0:m.AD,children:_},0!==R.length&&{...null===m||void 0===m?void 0:m.DA,children:R},0!==j.length&&{...null===m||void 0===m?void 0:m.Input,children:j},0!==I.length&&{...null===m||void 0===m?void 0:m.Output,children:I},0!==T.length&&{...null===m||void 0===m?void 0:m.HandBox,children:T}].filter(Boolean)}catch(c){return console.log("err",c),[]}},g=(e,t)=>`${e}-${t}`,b=(e,t)=>`${e}-${t}`,y=()=>{const{signalGroups:e,mappingData:t,pageData:a,resultData:m,signalList:y}=(0,n.d4)((e=>e.template)),{allActionList:x}=(0,A.A)(),f=(0,p.A)(),C=(0,c.A)(),w=(0,r.A)(),E=(0,d.A)(),S=(0,s.A)(),_=(0,n.d4)((e=>e.project.sampleData)),R=(0,n.d4)((e=>e.global.audioList)),[j,I]=(0,l.useState)([]);(0,l.useEffect)((()=>{I([...Object.entries(i.s8).map((e=>{let[t,a]=e;return{label:t,value:a}})),...a.map((e=>{let{name:t,id:a,type:l}=e;return{label:t,value:`${a}___${l}`}})),...Object.entries(o.tI).map((e=>{let[t,a]=e;return{label:t,value:a}}))])}),[a]);return{getSelectOptions:a=>{var l;const{selection:n,group_id:i,items:r,selectLayer:d,inputVariableType:s}=a;switch(n){case v.P$.\u4e8c\u7ef4\u6570\u7ec4\u5217\u6570\u636e\u6e90.value:return C.map((e=>{var t;return{label:e.name,value:e.code,children:((null===(t=e.double_array_tab)||void 0===t?void 0:t.columns)||[]).map((e=>({label:e.showName,value:e.code})))}}));case v.P$.\u6570\u636e\u6e90.value:return((null===e||void 0===e||null===(l=e.find((e=>(null===e||void 0===e?void 0:e.group_id)===i)))||void 0===l?void 0:l.variable_ids)||[]).map((e=>({...e,label:null===e||void 0===e?void 0:e.variable_name,value:e.code})));case v.P$.\u52a8\u4f5c\u6570\u636e\u6e90.value:return(x||[]).map((e=>({...e,label:e.action_name,value:e.action_id})));case v.P$.\u52a8\u4f5c\u5185\u90e8\u540d\u6570\u636e\u6e90.value:return(x||[]).filter((e=>e.action_code)).map((e=>({label:e.action_name,value:e.action_code})));case v.P$.Buffer\u6570\u636e\u6e90.value:return(w||[]).map((e=>({...e,label:e.name,value:e.code})));case v.P$.\u5e03\u5c14\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:return S.map((e=>({...e,label:e.name,value:e.code})));case v.P$.\u6570\u5b57\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:return E.map((e=>({...e,label:e.name,value:e.code})));case v.P$.\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:{let e=f;return s&&(e=e.filter((e=>e.variable_type===s))),e.map((e=>({...e,label:e.name,value:e.code})))}case v.P$.\u7a97\u4f53\u6570\u636e\u6e90.value:return j;case v.P$.\u7279\u6b8a\u52a8\u4f5c\u6570\u636e\u6e90.value:return Object.entries(o.JN).map((e=>{let[t,a]=e;return{label:t,value:a}}));case v.P$.\u6620\u50cf\u8f74\u6570\u636e\u6e90.value:return(((e,t)=>{const{usableResource:a,mappingContext:l}=e;if(l&&a){const e=Object.entries(a).map(((e,t)=>{let[a,n]=e;return null===n||void 0===n?void 0:n.map(((e,t)=>{var n;const i=null===l||void 0===l||null===(n=l[a])||void 0===n?void 0:n[t];return{...e,axisName:null===i||void 0===i?void 0:i.axisName}}))})).filter(Boolean);return(null===e||void 0===e?void 0:e.reduce(((e,t)=>Array.isArray(t)?[...e,...t]:e),[])).filter((e=>(null!==t&&void 0!==t?t:u.PL).includes(e.type)))}return[]})(t)||[]).map((e=>({...e,label:e.axisName,valueKey:`${e.hwKey}-${e.type}-${e.idx}`,value:`${e.hwKey}-${e.type}-${e.idx}`})));case v.P$.\u6620\u50cf\u6570\u636e\u6e90.value:return h(t,d);case v.P$.\u97f3\u9891\u6570\u636e\u6e90.value:return(R||[]).map((e=>({...e,label:e.audio_name,value:e.audio_id})));case v.P$.\u7ed3\u679c\u6570\u636e\u6e90.value:return(m||[]).map((e=>({...e,label:e.variable_name,value:e.code})));case v.P$["Buffer\u4fe1\u53f7\u53d8\u91cf"].value:return((e,t)=>((null===e||void 0===e?void 0:e.filter((e=>e.variable_type===v.ps.Buffer)))||[]).flatMap((e=>{var a,l;return null===e||void 0===e||null===(a=e.buffer_tab)||void 0===a||null===(l=a.signals)||void 0===l?void 0:l.map((a=>{const l=null===t||void 0===t?void 0:t.find((e=>e.code===a));return{...e,signal_code:a,buffer_name:b(e.name,null===l||void 0===l?void 0:l.variable_name),buffer_signal:g(e.code,a)}}))})))(f,y).map((e=>({...e,label:null===e||void 0===e?void 0:e.buffer_name,value:null===e||void 0===e?void 0:e.buffer_signal})));case v.P$.\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90.value:case v.P$["\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90(\u591a\u9009)"].value:return y.map((e=>({...e,label:e.variable_name,value:e.code})));case v.P$["\u53ef\u5206\u6790\u8bd5\u6837(\u591a\u9009)"].value:return(e=>e.map((e=>[...e.children])).flat())(_).map((e=>({...e,label:e.name,value:e.code})));default:return r||[]}}}}},9339:(e,t,a)=>{a.d(t,{A:()=>s});var l=a(65043),n=a(79806),i=a(81143);a(68374);const o=i.Ay.div`
    width: 100%;
    .ant-table-wrapper {
        .ant-select-dropdown .ant-select-item-option-content {
        /* overflow:hidden ; */
            white-space: nowrap;
        }
        .ant-select-dropdown .ant-select-item-option {
            word-break: break-all;
        }
        tr {
            &.ant-table-expanded-row:hover {
                >td {
                    background: rgba(215, 226, 255, 1);
                }
            }
        }
        .ant-table-expanded-row {
            &:hover {
                background: transparent;
            }
            >td {
                background: rgba(215, 226, 255, 1);
            }
        }
         .ant-table-cell-scrollbar {
            background: rgba(215, 226, 255, 1);
            box-shadow:none
         }
        .ant-table {
            &.ant-table-bordered {
                >.ant-table-container {
                    >.ant-table-content {
                        >table {
                            /* border: 1px solid #D7E2FF; */
                            >thead>tr>th {
                            /* border-inline-end: 1px solid rgba(255, 255, 255, 1) */
                            }
                        }
                    }
                }
            }
        }
        .ant-table-thead {
            .ant-table-selection-column {
                background: rgba(215, 226, 255, 1);
            }
            >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
                display: none;
            }
            >tr {
                >th {
                    background: rgba(215, 226, 255, 1);
                    >td {
                        background: rgba(215, 226, 255, 1);
                    }
                }
                >td {
                    background: rgba(215, 226, 255, 1);
                }
            }
        }
        .ant-table-tbody {
            >tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
                background: rgba(215, 226, 255, 0.2);
            }
        }
        /* .ant-table-body {
        &::-webkit-scrollbar {
            height: 5px;
            width: 5px; 
            z-index: 0;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 5px;
            -webkit-box-shadow: inset 0 0 5px rgba(215, 226, 255, 1);
            background: #DCDCDC;
            z-index: 0;
       }
        &::-webkit-scrollbar-track {
            -webkit-box-shadow: 0;
            border-radius: 0;
            background: #f0f2f5;
            z-index: 0;
        }
     } */
        
     }


`;i.Ay.div`
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
`;var r=a(70579);const d=(e,t)=>{const{children:a,title:l}=e;return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(o,{children:(0,r.jsx)(n.A,{...e,ref:t})})})},s=(0,l.memo)((0,l.forwardRef)(d))},10181:(e,t,a)=>{a.d(t,{S_:()=>o,nN:()=>i,sr:()=>n});a(65043);var l=a(70579);const n={"\u666e\u901a\u9875\u9762":"common","\u5f39\u7a97":"dialog","\u9884\u89c8\u7a97\u53e3":"subWindow"},i=e=>{let{handleGo:t,t:a}=e;return[{title:a("\u9875\u9762"),dataIndex:"name",key:"name",render:e=>a(e)},{title:a("\u64cd\u4f5c"),dataIndex:"name",key:"name",render:(e,n)=>(0,l.jsx)("a",{onClick:()=>t(n),children:a("\u8df3\u8f6c")})}]},o=e=>{let{handleDialogEdit:t,t:a}=e;return[{title:a("\u5f39\u7a97"),dataIndex:"name",key:"name",render:e=>a(e)},{title:a("\u64cd\u4f5c"),dataIndex:"name",key:"name",render:(e,n)=>(0,l.jsx)("a",{onClick:()=>t(n),children:a("\u8bbe\u7f6e\u63a7\u4ef6")})}]}},10202:(e,t,a)=>{a.d(t,{A:()=>m});var l=a(65043),n=a(16569),i=a(73737),o=a(6051),r=a(95206),d=a(74117),s=a(61966),c=a(36950),A=a(81143);a(68374);const u=A.Ay.div`
    .ant-transfer-list-body-customize-wrapper {
        overflow: auto;
    }
    .transfer-content {
        display: flex;
        width: 100%;
        height: 100%;
        .layout-right {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 20%;
            margin-left: 10px;
        }
        .ant-transfer {
            width: 100%;
        }
    }
    .way-right-layout {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        transition: all 0.3s;
        .way-title {
            display: inline-block;
            white-space: nowrap; 
            width: 100%; 
            overflow: hidden;
            text-overflow:ellipsis;
        }
        .list-layout {
            width: 100%;
            padding: 4px 8px;
            display: flex;
            justify-content: space-between;
            &:hover {
                background-color: rgba(0, 0, 0, 0.04);
                cursor: pointer;
           }
           .anticon:hover {
                background-color: rgba(0, 0, 0, 0.04);
                color: red;
                cursor: pointer;
           }
        }
        .select {
            background-color: #e6f4ff;
            &:hover {
                background-color:  #e6f4ff;
                cursor: pointer;
           }
        }
        
    }
`;var p=a(70579);const v=e=>{let{data:t,noDeleteDatas:a,selectOneWay:l,rowKey:n,onChangeOneWay:i,onChangeDelOneWay:o,oneWayLabel:r,wayRender:c=()=>""}=e;const A=e=>"list-layout "+((null===l||void 0===l?void 0:l[n])===e[n]?"select":""),{t:u}=(0,d.Bd)();return(0,p.jsx)("div",{className:"way-right-layout",children:null===t||void 0===t?void 0:t.map((e=>(0,p.jsxs)("div",{className:A(e),onClick:t=>i(t,e),children:[(0,p.jsx)("div",{className:"way-title",title:c(e)||e[r],children:c(e)||u(e[r])}),null!==a&&void 0!==a&&a.includes(e[n])?null:(0,p.jsx)(s.A,{onClick:t=>o(t,e)})]},e[n])))})},m=e=>{let{isMove:t=!1,children:a,oneWay:s,oneWayLabel:A="label",searchValueKey:m,rowKey:h="id",onChange:g,targetKeys:b,onChangeWay:y,select:x,onChangeDelWay:f,onChangeMove:C,moveChildren:w,wayRender:E,noDeleteDatas:S,..._}=e;const{t:R}=(0,d.Bd)(),[j,I]=(0,l.useState)({}),[T,P]=(0,l.useState)([]);(0,l.useEffect)((()=>{P(b)}),[b]),(0,l.useEffect)((()=>{I(x)}),[x]);return(0,p.jsx)(u,{children:(0,p.jsxs)("div",{className:"transfer-content",children:[(0,p.jsx)(i.A,{filterOption:m?(e,t)=>{var a;return(null===t||void 0===t||null===(a=t[m])||void 0===a?void 0:a.toLowerCase().indexOf(null===e||void 0===e?void 0:e.toLowerCase()))>=0}:void 0,listStyle:{width:"16vw",height:"40vh"},showSearch:!0,titles:[R("\u6e90\u6570\u636e\u5217\u8868"),R("\u76ee\u6807\u6570\u636e\u5217\u8868")],showSelectAll:!0,targetKeys:T,oneWay:s,rowKey:e=>e[h],..._,onChange:(e,t,a)=>{P(e),g(e,t,a)},children:e=>{let{direction:t,filteredItems:a}=e;if("right"===t&&s)return(0,p.jsx)(v,{noDeleteDatas:S,data:a,selectOneWay:j,rowKey:h,oneWayLabel:A,onChangeOneWay:(e,t)=>((e,t)=>{e.stopPropagation(),I(t),y&&y(t)})(e,t),onChangeDelOneWay:(e,t)=>((e,t)=>{e.stopPropagation(),P(T.filter((e=>e!==t[h]))),(null===j||void 0===j?void 0:j[h])===t[h]&&(I({}),y&&y()),f&&f(t)})(e,t),wayRender:E})}}),t&&s&&(0,p.jsx)("div",{className:"layout-right",children:(0,p.jsxs)(o.A,{direction:"vertical",children:[(0,p.jsx)(r.Ay,{block:!0,onClick:()=>{if((0,c.Im)(j))n.Ay.error(R("\u8bf7\u9009\u62e9"));else{const e=[...T],t=e.findIndex((e=>e===(null===j||void 0===j?void 0:j[h])));0!==t&&(e[t]=e.splice(t-1,1,e[t])[0]),P(e),C&&C(e)}},children:R("\u4e0a\u79fb")}),(0,p.jsx)(r.Ay,{block:!0,onClick:()=>{if((0,c.Im)(j))n.Ay.error(R("\u8bf7\u9009\u62e9"));else{const e=[...T],t=e.findIndex((e=>e===(null===j||void 0===j?void 0:j[h])));t!==e.length-1&&(e[t]=e.splice(t+1,1,e[t])[0]),P(e),C&&C(e)}},children:R("\u4e0b\u79fb")}),w]})})]})})}},10866:(e,t,a)=>{a.d(t,{A:()=>u});var l=a(65043),n=a(80077),i=a(56434),o=a.n(i),r=a(88359),d=a(67208),s=a(65694),c=a(34458),A=a(36950);const u=()=>{const e=(0,n.wA)(),{optSample:t,sampleData:a,sampleList:i,multiSample:u}=(0,n.d4)((e=>e.project)),p=(0,l.useRef)(!0);(0,l.useEffect)((()=>(p.current=!1,()=>{p.current=!0})),[]);const v=e=>e.map((e=>{const t=o()(e);return null===t||void 0===t||delete t.new,null!==t&&void 0!==t&&t.children&&(null===t||void 0===t?void 0:t.children.length)>0&&(t.children=v(null===t||void 0===t?void 0:t.children)),t})),m=async function(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];try{if((0,c.HN)()){const l=await(0,d.HBc)(),n=await(0,d.KCW)();if(l){const i=v(l);if(e({type:s.UK,param:i}),l.length>0&&t){var a;const e=null===i||void 0===i?void 0:i.flatMap((e=>e.children)).find((e=>e.code===n));await h(e||(null===(a=l[0].children)||void 0===a?void 0:a[0]))}}}}catch(l){console.log(l)}},h=async function(l){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=w().filter((e=>n.includes(e.key))).map((e=>e.code));if(l)(null===t||void 0===t?void 0:t.code)!==l.code||i?(await(0,d._fz)({sample_code:l.code,selected_insts:o}),e({type:s.vp,param:l}),await e((0,r.w)())):e({type:s.vp,param:l});else{var c,A;const t=null===a||void 0===a||null===(c=a[0])||void 0===c||null===(A=c.children)||void 0===A?void 0:A[0];e({type:s.vp,param:t})}},g=(e,t,a,l)=>e.map((e=>{var n,i,o;return{x:null===e||void 0===e||null===(n=e.find((e=>e.Code===t)))||void 0===n?void 0:n.Value,y:null===e||void 0===e||null===(i=e.find((e=>e.Code===a)))||void 0===i?void 0:i.Value,y2:null===e||void 0===e||null===(o=e.find((e=>e.Code===l)))||void 0===o?void 0:o.Value,index:null===e||void 0===e?void 0:e[0].Index}})),b=e=>{let{VarValues:t,style:a,type:l="add",id:n,x:i,y:o,y2:r,isHistory:d}=e;return{data:d?t:g(null!==t&&void 0!==t?t:[],i,o,r),id:n,auxiliaryLine:[],tags:[],style:a,style2:a,type:l}},y=e=>e?Array.isArray(e)?e:[e]:[],x=e=>{let{x:t,y2:a,y:l,VarValues:n,color:i,type:o,sampleKey:r,isHistory:d=!1}=e;const s=y(a),c=y(l),A=[],u=s?Math.max(null===c||void 0===c?void 0:c.length,null===s||void 0===s?void 0:s.length):null===c||void 0===c?void 0:c.length;if(u&&u>0)for(let m=0;m<u;m+=1){var p,v;const e=null!==(p=null===c||void 0===c?void 0:c[m])&&void 0!==p?p:"",a=null!==(v=null===s||void 0===s?void 0:s[m])&&void 0!==v?v:"";A.push(b({isHistory:d,VarValues:d?C(n,m):n,style:{color:i,thickness:1},type:o,id:0===m?r:`${r}_${m}`,x:t,y:e,y2:a}))}return A},f=(e,t)=>Array.isArray(e)?null===e||void 0===e?void 0:e[t]:e,C=(e,t)=>{const a=[];for(let l=0;l<e.length;l+=1){const n=e[l];a.push({...n,y:f(n.y,t),y2:f(n.y2,t)})}return a},w=function(){var e;var t;return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?null===a||void 0===a||null===(t=a.map((e=>e.children)))||void 0===t?void 0:t.flat().filter((e=>!e.disabled)):null===a||void 0===a||null===(e=a.map((e=>e.children)))||void 0===e?void 0:e.flat()};return{initSampleTree:m,initDefaultSample:async()=>{try{const t=await(0,d.STW)();t&&e({type:s.Ft,param:t})}catch(t){console.log(t)}},handleSampleData:e=>{let{sample:t,taskData:a,type:l="add",x:n,y:i,y2:o}=e;const{color:r,key:d}=t;return x({x:n,y2:o,y:i,VarValues:a,color:r,type:l,sampleKey:d})},getLiveSampleHisotryData:async e=>{let{sample:t,buffer_code:a,type:l="update",x:n,y:i,y2:o}=e;if((0,c.HN)()){const{color:e,code:r,key:s}=t,c=await(0,d.XJ1)({bufferCode:a,templateName:(0,A.n1)(),x:n,y:i,y2:o});if(c&&(null===c||void 0===c?void 0:c.length)>0)return x({x:n,y2:o,y:i,VarValues:c,color:e,type:l,sampleKey:s,isHistory:!0})}return null},updateOptSample:h,getSamples:w,getSample:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;if(e){const a=null===t||void 0===t?void 0:t.find((t=>{var a;return(null===t||void 0===t?void 0:t.sample_id)===(null===e||void 0===e||null===(a=e.data[0])||void 0===a?void 0:a.sample_id)}));return{...e,data:null===e||void 0===e?void 0:e.data.map((e=>{const t=null===a||void 0===a?void 0:a.parameters.find((t=>t.parameter_id===e.parameter_id));return{...e,...t,hidden_flag:t?null===t||void 0===t?void 0:t.hidden_flag:e.hidden_flag}}))}}return{}},initSampleAboutList:async()=>{try{const t=await(0,d.S9w)();t&&e({type:s.qk,param:t})}catch(t){throw console.log(t),t}},initSamples:async()=>{try{const t=await(0,d.GpS)();t&&e({type:s.OJ,param:t})}catch(t){throw console.log(t),t}},initSysSamples:async()=>{try{const t=await(0,d.qbn)();t&&e({type:s.OJ,param:t})}catch(t){throw console.log(t),t}},batchUpdateSample:async e=>{try{await(0,d.epI)({sample_insts:e}),m()}catch(t){throw console.log(t),t}}}}},12069:(e,t,a)=>{a.r(t),a.d(t,{default:()=>be});var l=a(65043),n=a(74117),i=a(80349),o=a(16569),r=a(6051),d=a(95206),s=a(63942),c=a(93950),A=a.n(c),u=a(56434),p=a.n(u),v=a(70588),m=a(80077),h=a(39470),g=a(44409),b=a(84),y=a(78583),x=a(4178),f=a(34458),C=a(36950),w=a(56543),E=a(45303),S=a(50540),_=a(37104),R=a(67208),j=a(41753),I=a(55505),T=a(16090),P=a(14255),k=a(21256),D=a(10866),U=a(70916),N=a(73206),B=a(68374),O=a(8237),M=a(33013),L=a(81143);L.Ay.div`
    .is-template {
        font-size: ${(0,B.D0)("28px")};
    }
`;const V=(0,B.D0)("530px"),Q=L.Ay.div`
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-size: 100% 100%;
    height: ${e=>e.isShowType?B.OZ.minNavbarHeight:B.OZ.navbarHeight};
    background-color: #e2f0fe;
    ${e=>e.isBgStyle?"\n                border-bottom-left-radius: 0px;\n                border-bottom-right-radius: 0px;\n            ":"border-radius: 10px;"}
   
    .fixed-left {
        width: ${V};
        display: flex;
        align-items: center;
        overflow-x: auto;
        overflow-y: hidden;
        ::-webkit-scrollbar {
                height: 3px;
            }
        ::-webkit-scrollbar-thumb {
            border-radius: 5px;
            -webkit-box-shadow: inset 0 0 5px rgba(89, 89, 89, 0.2);
            background:  rgba(20, 115, 245,0.4);
        }
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: 0;
            border-radius: 0;
            background: transparent;
        }
        .home {
            padding-left: ${(0,B.D0)("18px")};
            padding-right: ${(0,B.D0)("18px")};
            flex-shrink: 0;
            display: flex;
            color: #2D4586;
            flex-direction: column;
            align-items: center;
            font-size: ${(0,B.D0)("14px")};
            cursor: pointer;
            img {
                height: ${(0,B.D0)("36px")};
                width: ${(0,B.D0)("36px")};
                margin-bottom: 2px;
            }
            &:hover {
                background: rgba(90,120,255,0.1);
            }
         }
        .arrows {
            padding-left: 0;
            width: ${(0,B.D0)("20px")};
            &:hover {
                background: transparent;
            }
        }
    }

    // 顶栏右侧自定义shortcut 和 右侧写死[退出]等shortcut
    .content {
        /* width: calc(100% - ${V}); */
        width: 100%;
        height:  ${B.OZ.navbarHeight};
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #2D4586;
        font-size: ${(0,B.D0)("14px")};
        cursor: pointer;
        ::-webkit-scrollbar-thumb {
            border-radius: 5px;
            -webkit-box-shadow: inset 0 0 5px rgba(89, 89, 89, 0.2);
            background:  rgba(20, 115, 245,0.4);
        }
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: 0;
            border-radius: 0;
            background: transparent;
        }
        .logo-layout {
            padding: 10px;
            width: 8vw;
            height: 100%;
            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
        img {
            height: ${e=>e.isShowType?(0,B.D0)("28px"):(0,B.D0)("36px")};
            width:  ${e=>e.isShowType?(0,B.D0)("28px"):(0,B.D0)("36px")};
            object-fit: contain;
            margin-bottom: 2px;
        }
        .left {
            overflow-x: auto;
            overflow-y: hidden;
            width: calc(100% - 8vw);
            scrollbar-color: transparent transparent;
            scrollbar-track-color: transparent;
            -ms-scrollbar-track-color: transparent;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0 15px;
            ::-webkit-scrollbar {
                height: ${(0,B.D0)("6px")};
            }
            ::-webkit-scrollbar-thumb {
                border-radius: 5px;
                -webkit-box-shadow: inset 0 0 5px rgba(89, 89, 89, 0.2);
                background:  rgba(20, 115, 245,0.2);
            }
            ::-webkit-scrollbar-track {
                -webkit-box-shadow: 0;
                border-radius: 0;
                background: transparent;
            }
            .layout {
                display: flex;
                align-items: center;    
                flex-direction: column;
                ${e=>!e.isShowType&&`\n                    margin-left: ${(0,B.D0)("15px")};\n                    margin-right: ${(0,B.D0)("15px")};\n                `}
                font-size: ${(0,B.D0)("14px")};
                min-width: ${(0,B.D0)("70px")};
                &:hover {
                     background: rgba(90,120,255,0.1);
                }
            }

            
            .chunk {
                display: flex;
                align-items: center;
                white-space: nowrap;
                padding: ${(0,B.D0)("5px")} 0px;
            }
     

            .unclickable {
                cursor: not-allowed;
            }
        }

        .right {
            display: flex;
            align-items: end;
            font-size: ${(0,B.D0)("14px")};
            .home {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-left: ${(0,B.D0)("15px")};
                margin-right: ${(0,B.D0)("15px")};
            }
            /* width: 16vw; */
        }
        
    }
`,F=L.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }
    .disabled {
        cursor:no-drop ;
    }
`,J=L.Ay.div`
   background-color: #B8CBFF;
   border: 1px solid #B8CBFF;
   width: 1px;
   height: calc(${e=>e.isShowType?B.OZ.minNavbarHeight:B.OZ.navbarHeight} - ${"10px"});
   /* margin: 0 ${"10px"}; */
`,Y=L.Ay.div`
    display: flex;
    justify-content: flex-end;
    min-height: 4vh;
`;L.Ay.div`
    display: flex;
    align-items: center;    
    flex-direction: column;
    flex-shrink: 0;
    margin-left: ${(0,B.D0)("15px")};
    margin-right: ${(0,B.D0)("15px")};
    font-size: ${(0,B.D0)("14px")};
    &:hover {
        background: rgba(90,120,255,0.1);
    }
`;var K=a(83720),H=a(25055),W=a(96603),G=a(75440),z=a(40940);const X=L.Ay.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background: #FFF;
    .modal-right {
        display: flex;
        justify-content: center;
        align-items: center;
    }
`;var Z=a(45333),q=a(70579);const{TextArea:$}=K.A,ee=e=>{let{open:t,setOpen:a,handleOk:i,handleCancel:o}=e;const[s]=H.A.useForm(),{t:c}=(0,n.Bd)(),A=(0,m.d4)((e=>e.global.systemConfig)),u=H.A.useWatch("type",s),p=(0,l.useMemo)((()=>(0,f.SU)()),[]),v=(0,l.useMemo)((()=>(0,f.HN)()),[]),h=function(){return[{required:!(arguments.length>1&&void 0!==arguments[1])||arguments[1],message:c(`\u8bf7\u8f93\u5165${arguments.length>0&&void 0!==arguments[0]?arguments[0]:"\u540d\u79f0"}`)},{pattern:/^[^\\/:*?<>|]+$/,message:c("\u4e0d\u80fd\u8f93\u5165 \\ / : * ? < > | \u7b49\u7279\u6b8a\u5b57\u7b26")}]};(0,l.useEffect)((()=>{s.setFieldsValue({new_directory:null===A||void 0===A?void 0:A.project_directory,template_directory:null===A||void 0===A?void 0:A.project_directory,report_directory:null===A||void 0===A?void 0:A.project_directory,project_directory:null===A||void 0===A?void 0:A.project_directory,export_directory:null===A||void 0===A?void 0:A.project_directory})}),[u]);const g=()=>[{required:!0,message:c("\u8bf7\u9009\u62e9\u8def\u5f84")}];return(0,q.jsx)(G.A,{open:t,title:c("\u4fdd\u5b58\u63d0\u793a"),onCancel:()=>a(!1),width:"40vw",footer:null,children:(0,q.jsxs)(X,{children:[(0,q.jsxs)(H.A,{form:s,initialValues:{type:Z.ZE.SAVE_AS_TEMPLATE},labelCol:{span:6},wrapperCol:{span:16},children:[(0,q.jsx)(H.A.Item,{label:c("\u9009\u62e9"),name:"type",rules:[{required:!0,message:c("\u8bf7\u8f93\u5165\u9009\u62e9")}],children:(0,q.jsxs)(W.Ay.Group,{children:[(0,q.jsx)(W.Ay,{value:Z.ZE.SAVE_AS_TEMPLATE,children:c("\u53e6\u5b58\u4e3a\u6a21\u677f")}),(0,q.jsx)(W.Ay,{value:Z.ZE.SAVE_AS_PROJECT,children:c("\u53e6\u5b58\u4e3a\u9879\u76ee")})]})}),p&&u===Z.ZE.SAVE_AS_TEMPLATE&&(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)(H.A.Item,{label:c("\u6a21\u677f\u540d\u79f0"),name:"new_name",rules:h(),children:(0,q.jsx)(K.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{hidden:!0,label:c("\u6a21\u677f\u4f4d\u7f6e"),name:"new_directory",rules:g(),children:(0,q.jsx)(z.A,{style:{width:"15vw"}})})]}),p&&u===Z.ZE.SAVE_AS_PROJECT&&(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)(H.A.Item,{label:c("\u9879\u76ee\u540d\u79f0"),name:"project_name",rules:h(),children:(0,q.jsx)(K.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{hidden:!0,label:c("\u9879\u76ee\u4f4d\u7f6e"),name:"project_directory",rules:g(),children:(0,q.jsx)(z.A,{style:{width:"15vw"}})})]}),v&&u===Z.ZE.SAVE_AS_TEMPLATE&&(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)(H.A.Item,{label:c("\u6a21\u677f\u540d\u79f0"),name:"template_name",rules:h(),children:(0,q.jsx)(K.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{hidden:!0,label:c("\u6a21\u677f\u76ee\u5f55"),name:"template_directory",rules:g(),children:(0,q.jsx)(z.A,{style:{width:"15vw"}})})]}),v&&u===Z.ZE.SAVE_AS_PROJECT&&(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)(H.A.Item,{label:c("\u9879\u76ee\u540d\u79f0"),name:"project_name",rules:h(),children:(0,q.jsx)(K.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{label:c("\u59d4\u6258\u7f16\u53f7"),name:"entrust_number",rules:function(){return[{required:!1,message:c(`\u8bf7\u8f93\u5165${arguments.length>0&&void 0!==arguments[0]?arguments[0]:"\u540d\u79f0"}`)}]}("\u59d4\u6258\u7f16\u53f7"),children:(0,q.jsx)(K.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{label:c("\u59d4\u6258\u5355\u4f4d"),name:"entrust_unit",rules:h("\u59d4\u6258\u5355\u4f4d",!1),children:(0,q.jsx)(K.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{hidden:!0,label:c("\u9879\u76ee\u4f4d\u7f6e"),name:"project_directory",rules:g(),children:(0,q.jsx)(z.A,{style:{width:"15vw"}})})]}),(0,q.jsx)(H.A.Item,{hidden:!0,label:c("\u62a5\u544a\u76ee\u5f55"),name:"report_directory",rules:g(),children:(0,q.jsx)(z.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{hidden:!0,label:c("\u5bfc\u51fa\u76ee\u5f55"),name:"export_directory",rules:g(),children:(0,q.jsx)(z.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{label:c("\u5907\u6ce8\u63cf\u8ff0"),name:"remark",children:(0,q.jsx)($,{rows:4,placeholder:c("\u8bf7\u8f93\u5165 \u5907\u6ce8\u63cf\u8ff0")})})]}),(0,q.jsx)("div",{className:"modal-right",children:(0,q.jsxs)(r.A,{children:[(0,q.jsx)(d.Ay,{block:!0,onClick:async()=>{try{const e=await s.validateFields();i(e)}catch(e){console.error(e)}},type:"primary",children:c("\u4fdd\u5b58")}),(0,q.jsx)(d.Ay,{block:!0,onClick:o,children:c("\u53d6\u6d88")})]})})]})})};var te=a(67299),ae=a(80231);const le=e=>{var t;let{domId:a,layoutConfig:l}=e;const{openDialog:i}=(0,b.A)(),{t:o}=(0,n.Bd)(),r=(0,m.d4)((e=>e.global.roleHiddenDomClass)),{subContextMenuId:d}=(0,te.A)(),s=null===a||void 0===a||null===(t=a.split("edit-"))||void 0===t?void 0:t.at(-1);return(0,q.jsx)(F,{children:(0,q.jsx)(ae.A,{domId:a,layoutConfig:l,children:(0,q.jsx)("div",{className:`unique-content ${r}`,onClick:()=>{d(s),i({type:E.yc})},children:o("\u7f16\u8f91\u83dc\u5355")})})})};var ne=a(5520),ie=a(87011),oe=a(71424);const re=e=>{let{type:t,iconImg:a,shortCutTxt:l}=e;const{t:i}=(0,n.Bd)();return t===_.wL.ICON?(0,q.jsx)("img",{src:a,alt:"\u9876\u680f\u5feb\u6377\u65b9\u5f0ficon"}):t===_.wL.TEXT?(0,q.jsx)("span",{children:i(l)}):(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)("img",{src:a,alt:""}),(0,q.jsx)("span",{children:i(l)})]})},de=(0,l.memo)(re),se=e=>{var t;let{data:a,handleRunAction:n,items:i,isShowFeature:o,dropDownId:d,widgetId:s,...c}=e;const A=(0,l.useRef)(null),u=(0,oe.A)(null===a||void 0===a?void 0:a.input_code,!0);(0,l.useEffect)((()=>()=>{var e;null===(e=A.current)||void 0===e||e.close()}),[]);const p=()=>{u&&n(a)};return(0,q.jsxs)(r.A,{children:[a.halving_line===_.wL.FRONT&&(0,q.jsx)(J,{...c}),(null!==(t=`${null===a||void 0===a?void 0:a.shortcut_id}_${s}`)&&void 0!==t?t:"")===d?(0,q.jsx)(ne.A,{menu:{items:i},trigger:"click",placement:"bottomLeft",open:!0,disabled:!o,children:(0,q.jsx)("div",{title:null===a||void 0===a?void 0:a.tip,className:`layout ${!u&&"unclickable"}`,onClick:p,children:(0,q.jsx)(de,{type:a.show_type,iconImg:a.icon,shortCutTxt:a.shortcut_name})},a.shortcut_id)}):(0,q.jsx)("div",{title:(null===a||void 0===a?void 0:a.tip)||(null===a||void 0===a?void 0:a.shortcut_name),className:`layout ${!u&&"unclickable"}`,onClick:p,children:(0,q.jsx)(de,{type:a.show_type,iconImg:a.icon,shortCutTxt:a.shortcut_name})},a.shortcut_id),null!==a&&void 0!==a&&a.dialog_id?(0,q.jsx)(ie.A,{usable:u,data:null===a||void 0===a?void 0:a.dialog_id}):(0,q.jsx)(q.Fragment,{}),a.halving_line===_.wL.BEHIND&&(0,q.jsx)(J,{...c})]})},ce=L.Ay.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background: #FFF;
    .prompt {
        margin-bottom: 10px;
        color: red;
    }
    .modal-right {
        display: flex;
        justify-content: center;
        align-items: center;
    }
`,Ae=e=>{let{open:t,setOpen:a,handleOk:i}=e;const{t:o,i18n:s}=(0,n.Bd)(),{optSample:c}=(0,m.d4)((e=>e.project)),A=(0,m.d4)((e=>e.template.videoList)),u=(0,l.useMemo)((()=>(null===c||void 0===c?void 0:c.status)!==w.$y.READY),[null===c||void 0===c?void 0:c.status]),{initVideoData:p}=(0,j.A)();return(0,q.jsx)(G.A,{open:t,title:o("\u63d0\u793a"),onCancel:()=>a(!1),width:"30vw",footer:null,children:(0,q.jsxs)(ce,{children:[(0,q.jsx)("div",{className:"prompt",children:u?o("\u8bd5\u6837\u91cd\u590d\u505a\u5b9e\u9a8c\uff0c\u4f1a\u6e05\u7a7a\u4e4b\u524d\u5386\u53f2\u6570\u636e"):""}),(0,q.jsx)("div",{className:"modal-right",children:(0,q.jsxs)(r.A,{children:[(0,q.jsx)(d.Ay,{block:!0,onClick:async()=>{const e=A.find((e=>e.sample_code===c.code))||{};null!==e&&void 0!==e&&e.video_id&&(await(0,R.f9d)({video_id:null===e||void 0===e?void 0:e.video_id,video_file:null===e||void 0===e?void 0:e.video_file}),await p()),i({clear:!0})},type:"primary",children:o("\u5f00\u59cb\u8bd5\u9a8c")}),(0,q.jsx)(d.Ay,{block:!0,onClick:()=>{a(!1)},children:o("\u53d6\u6d88")})]})})]})})},ue=L.Ay.div`
    
`,pe=e=>{let{open:t,onCancel:a,onNext:l}=e;const[i]=H.A.useForm(),{t:r}=(0,n.Bd)(),d=(0,m.d4)((e=>e.global.loginUser));return(0,q.jsx)(G.A,{open:t,title:r("\u7528\u6237\u767b\u5f55\u6821\u9a8c"),onCancel:()=>a(),onOk:()=>(async()=>{try{const t=await i.validateFields();if(t){const{username:a,password:n}=t;if(a!==d)return void o.Ay.error(r("\u8bf7\u767b\u5f55\u5f53\u524d\u8d26\u53f7"));try{await(0,R.pHe)({account:a,password:n})&&l()}catch(e){console.error(e)}}}catch(e){console.error(e)}})(),width:"500px",children:(0,q.jsx)(ue,{children:(0,q.jsxs)(H.A,{form:i,labelAlign:"left",labelCol:{span:4},wrapperCol:{span:20},children:[(0,q.jsx)(H.A.Item,{label:r("\u7528\u6237\u540d"),name:"username",rules:[{required:!0,message:r("\u8bf7\u8f93\u5165\u7528\u6237\u540d")}],children:(0,q.jsx)(K.A,{})}),(0,q.jsx)(H.A.Item,{label:r("\u5bc6\u7801"),name:"password",rules:[{required:!0,message:r("\u8bf7\u8f93\u5165\u5bc6\u7801")}],children:(0,q.jsx)(K.A.Password,{})})]})})})};a(4554);const ve=L.Ay.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background: #FFF;
    .modal-right {
        display: flex;
        justify-content: center;
        align-items: center;
    }
`,me=e=>{let{open:t,setOpen:a,handleOk:l}=e;const[i]=H.A.useForm(),{TextArea:o}=K.A,{t:s,i18n:c}=(0,n.Bd)();return(0,q.jsx)(G.A,{open:t,title:s("\u4fdd\u5b58\u63d0\u793a"),onCancel:()=>a(!1),width:"40vw",footer:null,children:(0,q.jsxs)(ve,{children:[(0,q.jsxs)(H.A,{form:i,initialValues:{is_save_template:!1},labelCol:{span:6},wrapperCol:{span:16},children:[(0,q.jsx)(H.A.Item,{label:s("\u9879\u76ee\u540d\u79f0"),name:"project_name",placeholder:s("\u8bf7\u8f93\u5165 \u9879\u76ee\u540d\u79f0"),rules:[{required:!0,message:s("\u8bf7\u8f93\u5165\u540d\u79f0")}],children:(0,q.jsx)(K.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{label:s("\u59d4\u6258\u7f16\u53f7"),name:"entrust_number",placeholder:s("\u8bf7\u8f93\u5165 \u59d4\u6258\u7f16\u53f7"),children:(0,q.jsx)(K.A,{style:{width:"15vw"}})}),(0,q.jsx)(H.A.Item,{label:s("\u59d4\u6258\u5355\u4f4d"),name:"entrust_unit",placeholder:s("\u8bf7\u8f93\u5165 \u59d4\u6258\u5355\u4f4d"),children:(0,q.jsx)(K.A,{style:{width:"15vw"}})})]}),(0,q.jsx)("div",{className:"modal-right",children:(0,q.jsxs)(r.A,{children:[(0,q.jsx)(d.Ay,{block:!0,onClick:()=>(async()=>{try{const e=await i.validateFields();e&&(l(e),i.resetFields())}catch(e){console.error(e)}})(),type:"primary",children:s("\u4fdd\u5b58")}),(0,q.jsx)(d.Ay,{block:!0,onClick:()=>a(!1),children:s("\u53d6\u6d88")})]})})]})})};let he=Z.eq.CANCEL;const ge=e=>{var t,a,c,u,L,V,F,J,K,H,W,G,z,X,$,te,ae;let{id:ne,item:ie,isHidden:oe,layoutConfig:re}=e;const{t:de}=(0,n.Bd)(),ce=(0,m.wA)(),{subTaskNextStep:ue,subTaskShortcut:ve,subTaskSample:ge,subTaskStatus:be,videoRecording:ye,isFinishMain:xe,startValidation:fe,sampleInstStateChanged:Ce,nextStepChanged:we}=(0,m.d4)((e=>e.subTask)),Ee=(0,m.d4)((e=>e.project.optSample)),Se=(0,m.d4)((e=>e.global.imageList)),_e=(0,m.d4)((e=>e.global.isServerSuccess)),Re=(0,m.d4)((e=>e.template.shortcutList)),je=(0,m.d4)((e=>e.template.widgetData)),{sendChildProcess:Ie}=(0,x.A)(),{startCameraREC:Te,finishCameraERC:Pe,readyCameraREC:ke}=(0,j.A)(),{openDialog:De}=(0,b.A)(),{clear:Ue,submitSubTaskSample:Ne,submitIsFinishMain:Be,send:Oe,submitStartValidation:Me,submitSubTaskVideoRecording:Le,submitSampleInstStateChanged:Ve,submitNextStepChanged:Qe}=(0,g.A)(),{batchWidgetStatus:Fe}=(0,k.A)(),{initSampleTree:Je}=(0,D.A)(),{initProjectHistoryData:Ye}=(0,y.A)(),{initActionData:Ke,startAction:He}=(0,T.A)(),{actionStatus:We}=(0,P.A)(),{closeProject:Ge,quitProject:ze,temporaryProject:Xe}=(0,U.A)(),[Ze,qe]=i.Ay.useNotification(),$e=(0,l.useRef)(),[et,tt]=(0,l.useState)(!1),[at,lt]=(0,l.useState)(!1),[nt,it]=(0,l.useState)(!1),[ot,rt]=(0,l.useState)(),[dt,st]=(0,l.useState)(),[ct,At]=(0,l.useState)(),[ut,pt]=(0,l.useState)([]),vt=(0,l.useRef)(),[mt,ht]=(0,l.useState)(!1),gt=(0,l.useRef)([]),bt=(0,m.d4)((e=>e.global.stationList)),yt=(0,m.d4)((e=>e.global.optStation)),xt=(0,m.d4)((e=>e.global.globalMonitoringProjectID)),{bingStationProject:ft,bingMonitoringRelationProject:Ct}=(0,N.A)(),wt=(0,m.d4)((e=>e.template.defaultPageId)),{updateTitle:Et}=(0,x.A)(),{initProjectList:St}=(0,M.A)();(0,v.vC)(oe?[]:null===Re||void 0===Re?void 0:Re.map((e=>e.shortcut_key)),((e,t)=>{for(const n of Re)if(n.shortcut_key){var a,l;const e=null===(a=n.shortcut_key)||void 0===a?void 0:a.trim().toLowerCase(),i=null!==(l=e.split("+").filter((e=>!t.keys.includes(e))))&&void 0!==l?l:[];if([...i,...t.keys].join("+")===e&&!gt.current.some((e=>e===n.shortcut_id))){if(gt.current=[...gt.current,n.shortcut_id],i.length>0&&(0,v.vs)(i)){Ft(n);break}Ft(n);break}}}),{keydown:!0}),(0,v.vC)(oe?[]:"*",((e,t)=>{gt.current=[]}),{keyup:!0}),(0,l.useEffect)((()=>(window.addEventListener("click",Rt),()=>{window.removeEventListener("click",Rt)})),[]),(0,l.useEffect)((()=>{!ge&&oe&&xe&&_t()}),[ge,xe]);const _t=(0,l.useCallback)(A()((()=>{It()}),1e3),[]);(0,l.useEffect)((()=>{oe&&(ye&&(console.log("\u5f00\u59cb\u5f55\u5236"),Tt()),!1===ye&&(console.log("web:\u5173\u95ed\u6444\u50cf\u5934"),Pe(),Le(void 0)))}),[ye]),(0,l.useEffect)((()=>{we&&(Qt(),Qe(void 0))}),[we]),(0,l.useEffect)((()=>{console.log(fe),fe&&oe&&Lt()}),[fe]),(0,l.useEffect)((()=>{null!==Ce&&void 0!==Ce&&Ce.UIParams.TargetState&&oe&&(Je(),(null===Ce||void 0===Ce?void 0:Ce.UIParams.TargetState)===w.$y.FINISHED&&Vt("abort")),Ve(void 0)}),[Ce]),(0,l.useEffect)((()=>{var e,t;if(null!==(e=null===ve||void 0===ve||null===(t=ve.UIParams)||void 0===t?void 0:t.shortcutCode)&&void 0!==e&&e&&oe){const{shortcutCode:e}=ve.UIParams;switch(e){case Z.JN.\u5f00\u59cb:Lt();break;case Z.JN.\u6682\u505c:Vt("pause");break;case Z.JN.\u6062\u590d:Vt("resume");break;case Z.JN.\u7ec8\u6b62:Vt("abort");break;case Z.JN.\u4fdd\u5b58:Ut();break;case Z.JN.\u53e6\u5b58\u4e3a:Nt();break;case Z.JN.\u9000\u51fa:case Z.JN.\u9996\u9875:ze();break;case Z.JN.\u5173\u95ed:Bt();break;case Z.JN.\u4e0b\u4e00\u6b65:Qt();break;default:console.log("\u672a\u5b9a\u4e49\u7684\u5feb\u6377\u65b9\u5f0f",e)}ce({type:S.MY,param:null})}}),[null===ve||void 0===ve||null===(t=ve.UIParams)||void 0===t?void 0:t.shortcutCode]),(0,l.useEffect)((()=>{var e,t;if(null!==(e=null===ve||void 0===ve||null===(t=ve.UIParams)||void 0===t?void 0:t.shortcutCode)&&void 0!==e&&e){const{shortcutCode:e}=ve.UIParams;if(e===Z.JN.\u6a21\u677f\u914d\u7f6e)st(null===ot||void 0===ot?void 0:ot.dropDownId);else console.log("\u672a\u5b9a\u4e49\u7684\u5feb\u6377\u65b9\u5f0f",e);ce({type:S.MY,param:null})}}),[null===ve||void 0===ve||null===(a=ve.UIParams)||void 0===a?void 0:a.shortcutCode]),(0,l.useEffect)((()=>{ne&&Pt()}),[Se,Re,je,null===ie||void 0===ie?void 0:ie.widget_data_source]);const Rt=()=>{st("")},jt=async e=>{try{await Fe(),await(0,R.JUg)(e)}catch(t){console.error("\u4fdd\u5b58\u9519\u8bef")}},It=async()=>{Be(!1),(0,f.HN)()&&await jt(),Ze.info({message:de("\u6d41\u7a0b\u56fe\u7ed3\u675f\u6216\u7ec8\u6b62")})},Tt=async()=>{try{Te({title:`${null===Ee||void 0===Ee?void 0:Ee.name}${null===Ee||void 0===Ee?void 0:Ee.code}`,sample:Ee})}catch(e){"UserMediaStream"===e.name&&o.Ay.error(de(e))}},Pt=async()=>{try{let a=p()(Re);a=await Promise.all(a.map((async e=>{var t,a;return{...e,icon:null!==e&&void 0!==e&&null!==(t=e.icon)&&void 0!==t&&t.startsWith("data:")||null!==e&&void 0!==e&&null!==(a=e.icon)&&void 0!==a&&a.includes(w.mF)?null===e||void 0===e?void 0:e.icon:`${w.mF}${e.icon}`}})));const l=(0,C.Rm)(je,"widget_id",ie.widget_id);if(At(l),l&&null!==l&&void 0!==l&&l.data_source){var e,t;const n=null!==(e=Array.isArray(null===l||void 0===l?void 0:l.data_source)?null===l||void 0===l?void 0:l.data_source:null===l||void 0===l||null===(t=l.data_source)||void 0===t?void 0:t.shortcut_ids)&&void 0!==e?e:[];pt((e=>{const t=[];let a=[];return e.forEach((e=>{a.push(e),e.gap_flag&&(t.push(a),a=[])})),a.length>0&&t.push(a),t})(a.filter((e=>n.includes(e.shortcut_id)))))}else pt([])}catch(a){console.error("\u5feb\u6377\u65b9\u5f0f\u9519\u8bef")}},kt=[{key:"3",disabled:!1,label:(0,q.jsx)("a",{onClick:()=>De({type:E.P6}),children:de("\u8f93\u5165\u53d8\u91cf\u7ba1\u7406\u5668")})},{key:"8",disabled:!1,label:(0,q.jsx)("a",{onClick:()=>De({type:E.Um}),children:de("\u4fe1\u53f7\u53d8\u91cf\u7ba1\u7406\u5668")})},{key:"7",disabled:!1,label:(0,q.jsx)("a",{onClick:()=>De({type:E.vY}),children:de("\u7ed3\u679c\u53d8\u91cf\u7ba1\u7406\u5668")})},{key:"4",disabled:!1,label:(0,q.jsx)("a",{onClick:()=>De({type:E.IL}),children:de("\u52a8\u4f5c\u7ba1\u7406\u5668")})},{key:"6",disabled:!1,label:(0,q.jsx)("a",{onClick:()=>De({type:E.kd}),children:de("\u5355\u4f4d\u7ba1\u7406\u5668")})},{key:"9",disabled:!1,label:(0,q.jsx)("a",{onClick:()=>De({type:E.GS}),children:de("\u8bd5\u6837\u7ba1\u7406\u5668")})},{key:"15",label:(0,q.jsx)("a",{onClick:()=>De({type:E.hB}),children:de("\u8f85\u52a9\u7ebf\u7ba1\u7406\u5668")})},{key:"11",label:(0,q.jsx)("a",{onClick:()=>De({type:E.bp}),children:de("\u89c6\u9891\u7ba1\u7406\u5668")})},{key:"10",disabled:!1,label:(0,q.jsx)("a",{onClick:()=>De({type:E.i2}),children:de("\u6620\u50cf\u7ba1\u7406\u5668")})},{key:"14",label:(0,q.jsx)("a",{onClick:()=>De({type:E.RQ}),children:de("\u5bfc\u51fa\u914d\u7f6e\u7ba1\u7406\u5668")})},{key:"16",label:(0,q.jsx)("a",{onClick:()=>De({type:E._}),children:de("\u5e03\u5c40\u7ba1\u7406")})},{key:"\u5bf9\u8bdd\u6846\u7ba1\u7406",label:(0,q.jsx)("a",{onClick:()=>De({type:E.Q6}),children:de("\u5bf9\u8bdd\u6846\u7ba1\u7406")})},{key:"\u76d1\u63a7\u5173\u8054\u7ba1\u7406\u5668",label:(0,q.jsx)("a",{onClick:()=>De({type:E.YX}),children:de("\u76d1\u63a7\u5173\u8054\u7ba1\u7406\u5668")})}],Dt=e=>{let{onOk:t,okTitle:a="\u6b63\u5e38\u9000\u51fa(\u5173\u95ed\u670d\u52a1)",independentTitle:l="\u53ea\u5173\u95ed\u7a0b\u5e8f(\u4e0d\u5173\u95ed\u670d\u52a1)",cancelTitle:n="\u53d6\u6d88"}=e;return(0,q.jsx)(Y,{children:(0,q.jsxs)(r.A,{children:[(0,q.jsx)(d.Ay,{onClick:()=>{he=Z.eq.CANCEL,s.A.destroyAll()},children:de(n)}),(0,q.jsx)(d.Ay,{type:"primary",onClick:async()=>{he=Z.eq.NORMAL,t&&await t(),Ie(),Fe(),window&&window.close()},children:de(a)})]})})};window.onbeforeunload=_e?e=>{if((0,f.SU)()||(0,f.HN)())s.A.confirm({title:`${de("\u662f\u5426\u4fdd\u5b58\u5e76\u9000\u51fa\u7a0b\u5e8f")}?`,icon:(0,q.jsx)(h.A,{}),className:"close-modal-confirm",okText:de("\u786e\u8ba4"),cancelText:de("\u53d6\u6d88"),footer:(0,q.jsx)(Dt,{onOk:async()=>{(0,f.HN)()&&(await jt(),await Ge())}})});else{if([Z.eq.NORMAL,Z.eq.INDEPENDENT].includes(he))return;s.A.confirm({title:`${de("\u662f\u5426\u9000\u51fa\u7a0b\u5e8f")}?`,className:"close-modal-confirm",icon:(0,q.jsx)(h.A,{}),footer:(0,q.jsx)(Dt,{})})}return!1}:()=>{Ie()};const Ut=async()=>{(0,f.OV)()&&lt(!0),(0,f.Hq)()&&(0,f.HN)()&&(await jt(),St(),o.Ay.success(de("\u4fdd\u5b58\u6210\u529f")))},Nt=async()=>{tt(!0)},Bt=()=>{var e;if(wt===O.CH.\u7ed3\u679c\u9875\u9762)if(1===bt.length){if((null===bt||void 0===bt||null===(e=bt[0])||void 0===e?void 0:e.projectId)===Number((0,f.HN)()))return void ze()}else if(bt.length>1&&(bt.some((e=>(null===e||void 0===e?void 0:e.projectId)===Number((0,f.HN)())))||xt===Number((0,f.HN)())))return void ze();s.A.confirm({title:`${de("\u786e\u5b9a\u5173\u95ed\u5417")}?`,icon:(0,q.jsx)(h.A,{}),content:`${de("\u76f4\u63a5\u5173\u95ed\u6570\u636e\u4e0d\u505a\u4fdd\u5b58")},${de("\u4f60\u8fd8\u8981\u7ee7\u7eed\u5417")}?`,okText:de("\u786e\u8ba4"),cancelText:de("\u53d6\u6d88"),async onOk(){await Ge()}})},Ot=async function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=await(0,R._jF)({code:null===Ee||void 0===Ee?void 0:Ee.code,resave:e});return t?(Ue(),Ut(),t):t},Mt=async function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(console.log("startValidation: ",fe),console.log({Success:e,ErrorMessages:t}),!fe)return;const{SubTaskID:a,ProcessID:l}=fe;Oe(JSON.stringify({SubTaskID:a,ProcessID:l,Action:w.Rw.CLICK_OK,Target:w.pI.VERIFIED_RESULT,JsonArgs:JSON.stringify({Success:e,ErrorMessages:t})})),e&&(Ue(),Ut()),Me(void 0)},Lt=async()=>{null!==Ee&&void 0!==Ee&&Ee.code?it(!0):o.Ay.error(de("\u8bf7\u9009\u62e9\u4e00\u4e2a\u8bd5\u6837"))},Vt=async e=>{"abort"===e&&(Be(!0),Ne(null),Ye())},Qt=async()=>{try{if((0,f.HN)()){var e;const a=await(0,R.T$s)(),l=be.filter((e=>{var t;return(null===e||void 0===e||null===(t=e.UIParams)||void 0===t?void 0:t.status)===w.Y3.START_RUNNING})).map((e=>null===e||void 0===e?void 0:e.SubTaskID)),n=null===(e=(0,C.Dg)(null===a||void 0===a?void 0:a.data,"next"))||void 0===e?void 0:e.map((e=>{const t=ue.find((t=>(null===t||void 0===t?void 0:t.key)===(null===e||void 0===e?void 0:e.id)));return t?{...t,label:e.nameShow,nameShow:e.nameShow}:{key:e.id,idPrefix:e.idPrefix,label:e.nameShow,nameShow:e.nameShow,flag:0}})).filter((e=>l.includes(null===e||void 0===e?void 0:e.key)));var t;if(console.log("runStatus",l),console.log("nextTask",n),n&&n.length>0)(0,R.uwH)({"sub-task-ids":null===n||void 0===n?void 0:n.map((e=>null===e||void 0===e?void 0:e.key)),"process-id":(0,C.n1)()}),Ze.info({message:de("\u6267\u884c\u4e86\uff1a",null===n||void 0===n||null===(t=n.map((e=>null===e||void 0===e?void 0:e.nameShow)))||void 0===t?void 0:t.join(","))});else o.Ay.warning(de("\u6ca1\u6709\u53ef\u4ee5\u6267\u884c\u4e0b\u4e00\u6b65\u5b50\u4efb\u52a1"))}}catch(a){console.error(a)}},Ft=async e=>{1===(null===e||void 0===e?void 0:e.need_login_check)?(vt.current=e,ht(!0)):Jt(e)},Jt=async e=>{try{rt({...e,dropDownId:`${e.shortcut_id}_${null===ie||void 0===ie?void 0:ie.widget_id}`});const{action_id:t}=e;if(t){if((null===We||void 0===We?void 0:We[t])===w.Q6.RUNNING)return void s.A.confirm({title:`${de("\u8be5\u52a8\u4f5c\u6b63\u5728\u6267\u884c\u4e2d")}\uff1f`,icon:(0,q.jsx)(h.A,{}),okText:de("\u91cd\u65b0\u6267\u884c"),onOk:async()=>{await(0,R.fuQ)({cmd:I.__.\u505c\u6b62,action_id:t}),await He({action_id:t}),Ke()},onCancel:()=>{Ke()}});await He({action_id:String(t),code:(null===ge||void 0===ge?void 0:ge.code)||(null===Ee||void 0===Ee?void 0:Ee.code)}),Ke()}}catch(t){console.log(t)}},Yt={isShowType:(null!==(c=null===ct||void 0===ct||null===(u=ct.data_source)||void 0===u?void 0:u.bg_style)&&void 0!==c?c:_.dj.\u65b9\u89d2)===_.dj.\u5706\u89d2,isBgStyle:(null!==(L=null===ct||void 0===ct||null===(V=ct.data_source)||void 0===V?void 0:V.bg_style)&&void 0!==L?L:_.dj.\u65b9\u89d2)===_.dj.\u65b9\u89d2};return(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)("div",{style:{height:"100%",display:"flex",alignItems:"flex-end",maxHeight:null!==Yt&&void 0!==Yt&&Yt.isShowType?B.OZ.minNavbarHeight:B.OZ.navbarHeight},children:(0,q.jsxs)(Q,{...Yt,children:[qe,et&&(0,q.jsx)(ee,{open:et,setOpen:tt,handleCancel:()=>{tt(!1)},handleOk:async e=>{try{const{type:t}=e;if((0,f.HN)()&&t===Z.ZE.SAVE_AS_TEMPLATE){await(0,R.vP4)(e)&&(o.Ay.success(de("\u4fdd\u5b58\u6210\u529f")),tt(!1))}if((0,f.HN)()&&t===Z.ZE.SAVE_AS_PROJECT){await(0,R.cP1)(e)&&(o.Ay.success(de("\u4fdd\u5b58\u6210\u529f")),tt(!1))}}catch(t){console.error(t)}}}),at&&(0,q.jsx)(me,{open:at,setOpen:lt,handleOk:async e=>{await jt(e),await Xe(),lt(!1);const t=await(0,C.Fk)(de);var a;(Et(t),1===bt.length)?null!==bt&&void 0!==bt&&null!==(a=bt[0])&&void 0!==a&&a.projectId?o.Ay.error("\u5f53\u524d\u7ad9\u6b63\u5728\u8fd0\u884c\u5176\u4ed6\u9879\u76ee\uff0c\u65e0\u6cd5\u7ed1\u5b9a"):ft({optStation:null===bt||void 0===bt?void 0:bt[0],projectId:(0,f.HN)()}):bt.length>1&&(null!==yt&&void 0!==yt&&yt.id?null!==yt&&void 0!==yt&&yt.projectId?o.Ay.error("\u5f53\u524d\u7ad9\u6b63\u5728\u8fd0\u884c\u5176\u4ed6\u9879\u76ee\uff0c\u65e0\u6cd5\u7ed1\u5b9a"):ft({optStation:yt,projectId:(0,f.HN)()}):null!==yt&&void 0!==yt&&yt.id||xt?o.Ay.error("\u5f53\u524d\u7ad9\u6b63\u5728\u8fd0\u884c\u5176\u4ed6\u9879\u76ee\uff0c\u65e0\u6cd5\u7ed1\u5b9a"):Ct({optStation:yt,projectId:Number((0,f.HN)())}));St(),o.Ay.success(de("\u4fdd\u5b58\u6210\u529f"))}}),nt&&(0,q.jsx)(Ae,{open:nt,setOpen:e=>{it(e),Mt(!1)},handleOk:async e=>{let{clear:t}=e;try{if(Ee.disabled)return Mt(!1,["\u8bd5\u6837\u5904\u4e8e\u65e0\u6548\u4e2d,\u4e0d\u80fd\u505a\u5b9e\u9a8c"]),void o.Ay.error(de("\u8bd5\u6837\u5904\u4e8e\u65e0\u6548\u4e2d,\u4e0d\u80fd\u505a\u5b9e\u9a8c"));fe?(await(async()=>{const e=await(0,R.qnL)({type:"SubTaskVideoRecording"});console.log("videoRes: ",e),e&&await ke()})(),Mt()):Ot(t),it(!1)}catch(a){"UserMediaStream"===a.name&&(Mt(!1,[a.message]),o.Ay.error(de(a.message)))}}}),(0,q.jsxs)("div",{className:"content navbar-content",ref:$e,children:[(null===ct||void 0===ct||null===(F=ct.data_source)||void 0===F?void 0:F.logo)&&null!==(J=null===ct||void 0===ct||null===(K=ct.data_source)||void 0===K?void 0:K.logo_flag)&&void 0!==J&&J&&(null===ct||void 0===ct||null===(H=ct.data_source)||void 0===H?void 0:H.logo_position)===_.qo.\u5de6\u5bf9\u9f50&&(0,q.jsx)("div",{className:"logo-layout",children:(0,q.jsx)("img",{alt:"",src:null===ct||void 0===ct||null===(W=ct.data_source)||void 0===W?void 0:W.logo})}),(0,q.jsx)("div",{className:"left",style:(()=>{var e,t,a,l,n,i,o,r,d,s,c;const A={};return null!==ct&&void 0!==ct&&null!==(e=ct.data_source)&&void 0!==e&&e.logo||(A.width="100%"),null!==(t=null===ct||void 0===ct||null===(a=ct.data_source)||void 0===a?void 0:a.logo_flag)&&void 0!==t&&t||(A.width="100%"),null!==ct&&void 0!==ct&&null!==(l=ct.data_source)&&void 0!==l&&l.logo&&null!==(n=null===ct||void 0===ct||null===(i=ct.data_source)||void 0===i?void 0:i.logo_flag)&&void 0!==n&&n&&(null===ct||void 0===ct||null===(o=ct.data_source)||void 0===o?void 0:o.logo_position)===_.qo.\u5de6\u5bf9\u9f50&&(A.marginLeft=0),null!==ct&&void 0!==ct&&null!==(r=ct.data_source)&&void 0!==r&&r.logo&&null!==(d=null===ct||void 0===ct||null===(s=ct.data_source)||void 0===s?void 0:s.logo_flag)&&void 0!==d&&d&&(null===ct||void 0===ct||null===(c=ct.data_source)||void 0===c?void 0:c.logo_position)===_.qo.\u53f3\u5bf9\u9f50&&(A.marginRight=0),A})(),children:null===ut||void 0===ut?void 0:ut.map(((e,t)=>(0,q.jsx)("div",{className:"chunk",children:null===e||void 0===e?void 0:e.map((e=>(0,q.jsx)(se,{widgetId:ie.widget_id,items:kt,isShowFeature:!0,data:e,dropDownId:dt,handleRunAction:Ft,...Yt},null===e||void 0===e?void 0:e.shortcut_id)))},t)))}),(null===ct||void 0===ct||null===(G=ct.data_source)||void 0===G?void 0:G.logo)&&null!==(z=null===ct||void 0===ct||null===(X=ct.data_source)||void 0===X?void 0:X.logo_flag)&&void 0!==z&&z&&(null===ct||void 0===ct||null===($=ct.data_source)||void 0===$?void 0:$.logo_position)===_.qo.\u53f3\u5bf9\u9f50&&(0,q.jsx)("div",{className:"logo-layout",children:(null===ct||void 0===ct||null===(te=ct.data_source)||void 0===te?void 0:te.logo)&&(0,q.jsx)("img",{alt:"",src:null===ct||void 0===ct||null===(ae=ct.data_source)||void 0===ae?void 0:ae.logo})})]})]})}),!oe&&(0,q.jsx)(le,{domId:ne,layoutConfig:re}),mt?(0,q.jsx)(pe,{open:mt,onCancel:()=>{ht(!1),vt.current=null},onNext:()=>{ht(!1),Jt(vt.current)}}):null]})},be=(0,l.memo)(ge)},12159:(e,t,a)=>{a.d(t,{A:()=>d});var l=a(65043),n=a(36497),i=a(80077),o=a(74117),r=a(70579);const d=e=>{let{dimensionId:t,...a}=e;const d=(0,i.d4)((e=>e.global.unitList)),{t:s}=(0,o.Bd)(),c=(0,l.useMemo)((()=>{var e,a;return null===d||void 0===d||null===(e=d.find((e=>e.id===t)))||void 0===e||null===(a=e.units)||void 0===a?void 0:a.map((e=>({label:s(e.name),value:e.id})))}),[t,d]);return(0,r.jsx)(n.A,{options:null!==c&&void 0!==c?c:[],...a})}},12847:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(80077),n=a(41086),i=a(15637),o=a(67208);const r=()=>{const e=(0,l.wA)(),t=async t=>{try{const a=await(0,o.MXB)(t);if(a){const t=a.sort(((e,t)=>new Date(t.created_time)-new Date(e.created_time)));e({type:i.kP,param:t.map((e=>{var t;return{...e,layout:null!==(t=null===e||void 0===e?void 0:e.layout)&&void 0!==t&&t?(0,n.K6)(null===e||void 0===e?void 0:e.layout):null}}))})}return a}catch(a){console.log(a)}return null};return{initPageData:t,operatePage:async e=>{try{await(0,o.vwd)(e)&&t()}catch(a){console.log(a)}},operateDelPage:async e=>{try{await(0,o.x66)(e)&&t()}catch(a){console.log(a)}},saveDefaultId:async t=>{try{e({type:i.DB,param:t})}catch(a){console.log(a)}}}}},13313:(e,t,a)=>{a.d(t,{E:()=>l,g:()=>n});const l=[{id:"isVisible",varname:"isVisible",title:"\u53ef\u89c1\u6027"},{id:"isDisabled",varname:"isDisabled",title:"\u7981\u7528"},{id:"mode",varname:"mode",title:"\u901a\u9053\u63a7\u5236"},{id:"isCheck",varname:"isCheck",title:"\u53ef\u4f7f\u7528"},{id:"isCheckDisabled",varname:"isCheckDisabled",title:"\u7981\u7528\u53ef\u4f7f\u7528"},{id:"unit",varname:"unit",title:"\u5355\u4f4d"},{id:"onChange",varname:"onChange",title:"\u53d8\u5316\u65f6"},{id:"onExceedMax",varname:"onExceedMax",title:"\u8d85\u4e0a\u9650\u65f6"},{id:"onExceedMin",varname:"onExceedMin",title:"\u8d85\u4e0b\u9650\u65f6"}],n={mode:"mode",unit:"unit",isDisabled:"isDisabled",isCheck:"isCheck",onChange:"onChange",onExceedMax:"onExceedMax",onExceedMin:"onExceedMin"}},14255:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(65043),n=a(80077),i=a(55505),o=a(36950);const r=()=>{const e=(0,n.d4)((e=>e.template.actionList)),t=(0,n.d4)((e=>e.template.actionsStatus)),a=(0,n.d4)((e=>e.subTask.subProcessStatus)),r=(0,l.useMemo)((()=>[{action_name:"\u4e3b\u6d41\u7a0b",action_id:"0",action_category:i.K$},...e]),[e]),d=(0,l.useMemo)((()=>r.reduce(((e,l)=>{var n,i;let r=null===a||void 0===a?void 0:a.find((e=>{return t=l.action_id,`${(0,o.n1)()}-${t}`===e.ProcessID;var t}));return"0"===l.action_id&&(r=null===a||void 0===a?void 0:a.find((e=>(0,o.n1)()===e.ProcessID))),{...e,[l.action_id]:(null===(n=r)||void 0===n||null===(i=n.UIParams)||void 0===i?void 0:i.TaskStatus)||(null===t||void 0===t?void 0:t[l.action_id])}}),t)),[r,t,a]);return{allActionList:r,actionStatus:d}}},14387:(e,t,a)=>{a.d(t,{FR:()=>o,Pj:()=>i,l3:()=>d,nT:()=>l,rI:()=>n,yY:()=>r});const l={"\u76f4\u7ebf":"straight","\u7ebf\u6bb5":"segment"},n={"\u5b9e\u7ebf":"solid","\u865a\u7ebf":"dashed"},i={"\u4e24\u70b9\u914d\u7f6e":"segment","\u5782\u76f4X\u8f74\u914d\u7f6e":"xStraight","\u659c\u7387\u914d\u7f6e":"slopeStraight"},o={"\u4fe1\u53f7\u53d8\u91cf":"signal","\u4e8c\u7ef4\u6570\u7ec4":"array","\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408":"arrayList"},r={"\u540d\u79f0":{key:"name",label:"\u540d\u79f0",value:""},"\u8f85\u52a9\u7ebf\u7c7b\u578b":{key:"type",label:"\u8f85\u52a9\u7ebf\u7c7b\u578b",value:"straight",options:Object.entries(l).map((e=>{let[t,a]=e;return{label:t,value:a}}))},"\u7ebf\u6837\u5f0f":{key:"line_type",label:"\u7ebf\u6837\u5f0f",value:"solid",options:Object.entries(n).map((e=>{let[t,a]=e;return{label:t,value:a}}))},"\u989c\u8272":{key:"line_color",label:"\u989c\u8272",value:"#000000"},"\u901a\u9053\u6765\u6e90":{key:"channel_type",label:"\u901a\u9053\u6765\u6e90",value:"signal",options:Object.entries(o).map((e=>{let[t,a]=e;return{label:t,value:a}}))},"\u6570\u636e\u6e90":{key:"array_code",label:"\u6570\u636e\u6e90",value:""},"X\u8f74\u901a\u9053":{key:"x_channel",label:"X\u8f74\u901a\u9053",value:""},"Y\u8f74\u901a\u9053":{key:"y_channel",label:"Y\u8f74\u901a\u9053",value:""},"\u8f85\u52a9\u7ebf\u914d\u7f6e":{key:"config_type",label:"\u8f85\u52a9\u7ebf\u914d\u7f6e",value:"segment",options:Object.entries(i).map((e=>{let[t,a]=e;return{label:t,value:a}}))},"\u70b91":{key:"dot",label:"\u70b91",value:{is_fx:0,is_fx_x:0,x:0,is_fx_y:0,y:0,result_code:"",input_code_x:"",input_code_y:""}},"\u70b92":{key:"dot2",label:"\u70b92",value:{is_fx:0,is_fx_x:0,x:0,is_fx_y:0,y:0,result_code:"",input_code_x:"",input_code_y:""}},"a\u503c":{key:"a_value",label:"a\u503c",value:{is_fx:0,value:0,input_code:""}},"b\u503c":{key:"b_value",label:"b\u503c",value:{is_fx:0,value:0,input_code:""}},"c\u503c":{key:"c_value",label:"c\u503c",value:{is_fx:0,value:0,input_code:""}}},d={labelCol:{span:8},wrapperCol:{span:12}}},15701:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(80077),n=a(67208),i=a(70916),o=a(63612);const r=()=>{const e=(0,l.wA)(),{onOpenProject:t}=(0,i.A)(),a=(0,l.d4)((e=>e.global.globalMonitoringProjectID)),r=Array.from({length:7}).map(((e,t)=>{const a=String(2*t+8);return{key:a,label:`${a}px`}}));return{globalMonitoringProjectID:a,initGlobalProjectID:async()=>{const t=await(0,n._lq)();e({type:o.d_,param:t})},openGlobalProject:async()=>{t(a),e({type:o.h5,param:null})},fontSizeData:r,setFontSizeHandle:t=>{e({type:o.ck,param:t.key})},setGroupTypeHandle:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"tree";e({type:o.A_,param:t})},selectOptStation:async t=>{e({type:o.h5,param:t})}}}},16090:(e,t,a)=>{a.d(t,{A:()=>c});a(65043);var l=a(80077),n=a(16569),i=a(74117),o=(a(55505),a(41753)),r=(a(36950),a(754)),d=a(15637),s=a(67208);const c=()=>{const e=(0,l.wA)(),{t:t}=(0,i.Bd)(),{readyCameraREC:a}=(0,o.A)(),c=async t=>{const a=await(0,s.TvH)({action_ids:t});return e({type:d.DI,param:a}),a},A=()=>{e({type:d.Yr,param:[]})},u=async function(e){let l=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{const t=await(0,s.qnL)({...e,type:"SubTaskVideoRecording"});console.log("videoRes: ",t),t&&await a(),l&&await(0,s.CsV)(e)}catch(i){"UserMediaStream"===i.name&&n.Ay.error(t(i))}};return{initActionData:async function(){let{isNewOpen:t,pageId:a,...l}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const t=await(0,s.rFE)(l);if(t){A(),c(t.map((e=>e.action_id)));const a=t.sort(((e,t)=>new Date(t.created_time)-new Date(e.created_time)));e({type:d.Yr,param:a})}}catch(n){console.log(n)}},runOnStartUpAction:e=>{var t;let{pageId:a}=e;null===(t=r.A.getState().template.actionList)||void 0===t||t.forEach((e=>{e.run_on_startup&&e.page_id===a&&u({action_id:String(e.action_id)})}))},clearActionData:A,initActionsStatus:c,startAction:u,startActionOverall:async e=>{await(0,s.rxi)(e)}}}},16133:(e,t,a)=>{a.d(t,{A:()=>s});var l=a(80077),n=a(56543),i=a(36950),o=a(754),r=a(15637),d=a(67208);const s=()=>{const e=(0,l.wA)(),t=async()=>{try{const t=await(0,d.MC9)();t&&e({type:r.ks,param:t})}catch(t){console.log(t)}},a=()=>{e({type:r.tq,param:[]})};return{initResultData:async()=>{try{const t=await(0,d.QVv)();if(t){a();const l=t.reverse();e({type:r.tq,param:l})}}catch(t){console.log(t)}},clearResultData:a,initTestResultData:t,submitTestResult:async e=>{await(0,d.pOj)(e),t()},getCurveResults:()=>{const e=(0,o.B)("template","resultTestData").map((e=>e.id));return(0,o.B)("template","resultData").filter((t=>{var a;return([n.l4.RESULT_TABLE,n.l4.LABEL].includes(t.type)||e.includes(t.result_variable_id))&&(null===t||void 0===t||null===(a=t.display_modes)||void 0===a?void 0:a.includes(n._U.GRAPH))}))},getResultRoundingData:async()=>{const e=Object.entries((0,o.B)("project","resultHistoryData")).reduce(((e,t)=>{let[a,l]=t;const r=l.map((e=>{var t;const{format_info:a,format_type:l,dimension_id:r,unit_id:d}=(null===(t=(0,o.B)("template","resultData"))||void 0===t?void 0:t.find((t=>(null===t||void 0===t?void 0:t.code)===(null===e||void 0===e?void 0:e.code))))||{format_info:"",format_type:""};return l===n.Ho.ROUNDING&&/^\d+(\.\d+)?$/.test(e.value)?{code:e.code,round:{source_data:(0,i.tJ)(e.value,r,d)||0,type:a.roundMode||0,thres_hold1:a.threshold1||0,thres_hold2:a.threshold2||0,round_type1:a.roundType1||0,round_type2:a.roundType2||0,round_type3:a.roundType3||0}}:null})).filter(Boolean);return r&&r.length>0?[...e,{code:a,sample_rounds:r}]:e}),[]);return e&&(null===e||void 0===e?void 0:e.length)>0?(0,d.Pcy)({batch:e}):[]}}}},16271:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(65043),n=a(80077),i=a(32099);const o=()=>(0,i.Mz)([e=>e.inputVariable.inputVariableMap,e=>e.inputVariable.doubleArrayCodeList],((e,t)=>t.map((t=>e.get(t))))),r=()=>{const e=(0,l.useMemo)(o,[]);return(0,n.d4)((t=>e(t)))}},17103:(e,t,a)=>{a.d(t,{A:()=>g});var l=a(65043),n=a(80077),i=a(754),o=a(57610),r=a(30780),d=a(21256),s=a(78583),c=a(67208),A=a(70887),u=a(92676),p=a(14387),v=a(16133),m=a(56543),h=a(36950);const g=()=>{const e=(0,n.wA)(),{initTableConfigData:t}=(0,r.A)(),{initWidget:a}=(0,d.A)(),{initProjectHistoryData:g}=(0,s.A)(),{initResultData:b}=(0,v.A)(),{currentSettingId:y,widgetName:x,childrenState:f,settingResList:C}=(0,n.d4)((e=>e.staticCurve)),w=(0,l.useRef)(!0);(0,l.useEffect)((()=>(w.current=!1,()=>{w.current=!0})),[]);const E=async t=>{let{settingId:a,newOne:l,widgetName:n}=t;try{const t=(0,u.L1)(C.find((e=>e.id===a)));e({type:o.Nf,param:{settingId:a,currentStaticSetting:t,newOne:l,widgetName:n}})}catch(i){console.log(`\u521d\u59cb\u5316\u66f2\u7ebf[${a}]\u63a5\u53e3\u65f6\u51fa\u9519`,i)}},S=e=>e?Array.isArray(e)?e:[e]:[],_=async()=>{try{const t=await(0,c.OQV)();t&&e({type:o.v_,param:t.map((e=>({...e,y_channel:S(e.y_channel),y2_channel:S(e.y2_channel)})))})}catch(t){console.log("error when get \u66f2\u7ebf\u8bbe\u7f6e list",t)}},R=function(e,t,a){let l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"x";var n;if(null!==t&&void 0!==t&&t.is_fx)return{type:"index",index:null===a||void 0===a||null===(n=a.find((e=>e.resultCode===(null===t||void 0===t?void 0:t.result_code))))||void 0===n?void 0:n.index};if("segment"===e&&null!==t&&void 0!==t&&t[`is_fx_${l}`]){return i.A.getState().inputVariable.inputVariableMap.get(null===t||void 0===t?void 0:t[`input_code_${l}`]).default_val.value}return null===t||void 0===t?void 0:t[l]};return{submitStaticCurve:async t=>{let{settingId:l,params:n,current:i}=t;try{const t={...i||f[l].settingModalData,widget_name:x,...n};if(y===A.S1)await(0,c.xqS)((0,h.Ed)(t,m.bn)),a();else{const a=(0,h.Ed)({id:y,...t},m.bn);a.input_code||(a.input_code=""),await(0,c.z9p)(a),e({type:o.Xe,param:{settingId:l,newSettingParams:t}})}_()}catch(r){console.log("error when update \u66f2\u7ebf\u8bbe\u7f6e",r)}},addTags:async e=>{try{var a;const{settingId:l,sampleId:n,signalRes:i,currentPoint:o,variableName:r,variableCode:d}=e,{xAxisKey:s,yAxisKey:A,index:u,point:p,yIndex:v}=o,m=o[s],h=Array.isArray(o[A])?null===(a=o[A])||void 0===a?void 0:a[v]:o[A],y=i.find((e=>e.code===m)),x=i.find((e=>e.code===h));let f=[{variable_name:`${r}_${y.variable_name}`,signal_var_id:y.signal_variable_id,dimension_id:y.dimension_id,unit_id:y.unit_id,variable_code:`${d}_${y.variable_name}`,value:p.x,index:u,signal_key:y.code,daq_code:"testDaqCode",sample_instance_code:n}];m!==h&&(f=[...f,{variable_name:`${r}_${x.variable_name}`,signal_var_id:x.signal_variable_id,dimension_id:x.dimension_id,unit_id:x.unit_id,variable_code:`${d}_${x.variable_name}`,value:p.y,index:u,signal_key:x.code,daq_code:"testDaqCode",sample_instance_code:n}]);await(0,c.rRh)({results:f});E({settingId:l}),t(),g(),b()}catch(l){console.log("error when add \u66f2\u7ebf\u6807\u7b7e",l)}},getStaticCurveSettingList:_,initStaticCurveById:E,getAuxiliaryLines:async e=>{let{lines:t}=e;const a=i.A.getState().project.optSample,l=t.filter((e=>{var t,a;return[p.Pj.\u4e24\u70b9\u914d\u7f6e,p.Pj.\u659c\u7387\u914d\u7f6e].includes(e.config_type)&&(!(null===(t=e.dot)||void 0===t||!t.is_fx)||!(null===(a=e.dot2)||void 0===a||!a.is_fx))})).flatMap((e=>{var t,l;let n=[];return null!==(t=e.dot)&&void 0!==t&&t.is_fx&&(n=[...n,{simpleCode:a.code,resultCode:e.dot.result_code}]),null!==(l=e.dot2)&&void 0!==l&&l.is_fx&&(n=[...n,{simpleCode:a.code,resultCode:e.dot2.result_code}]),n}));let n=[];var o;0!==(null===l||void 0===l?void 0:l.length)&&(n=null!==(o=await(async e=>await(0,c.ciG)({templateName:(0,h.n1)(),results:e}))(l))&&void 0!==o?o:{data:[]});const r=i.A.getState().inputVariable.inputVariableMap,d=e=>{var t,a;switch(e.config_type){case p.Pj.\u4e24\u70b9\u914d\u7f6e:return(e=>{let{dot:t,dot2:a,resultRes:l=[],type:n}=e;return[{x:R(n,t,l,"x"),y:R(n,t,l,"y")},{x:R(n,a,l,"x"),y:R(n,a,l,"y")}]})({type:e.type,dot:e.dot,dot2:e.dot2,resultRes:null!==(t=n)&&void 0!==t?t:[]});case p.Pj.\u5782\u76f4X\u8f74\u914d\u7f6e:return(e=>{var t,a,l;let{cValue:n,inputVariableMap:i}=e;return[{x:null!==n&&void 0!==n&&n.is_fx?null!==(t=null===(a=i.get(n.input_code))||void 0===a||null===(l=a.default_val)||void 0===l?void 0:l.value)&&void 0!==t?t:0:null===n||void 0===n?void 0:n.value,y:0},{x:null!==n&&void 0!==n&&n.is_fx?0:null===n||void 0===n?void 0:n.value,y:0}]})({cValue:e.c_value,inputVariableMap:r});case p.Pj.\u659c\u7387\u914d\u7f6e:return(e=>{var t,a,l,n,i,o,r;let{dot:d,aValue:s,bValue:c,inputVariableMap:A,resultRes:u=[]}=e;const p=null!==s&&void 0!==s&&s.is_fx?null!==(t=null===(a=A.get(s.input_code))||void 0===a||null===(l=a.default_val)||void 0===l?void 0:l.value)&&void 0!==t?t:0:null===s||void 0===s?void 0:s.value;return null!==c&&void 0!==c&&c.is_fx?null===(n=A.get(c.input_code))||void 0===n||null===(i=n.default_val)||void 0===i?void 0:i.value:null===c||void 0===c||c.value,[{x:null!==d&&void 0!==d&&d.is_fx?{type:"index",index:null===u||void 0===u||null===(o=u.find((e=>e.resultCode===(null===d||void 0===d?void 0:d.result_code))))||void 0===o?void 0:o.index}:null===d||void 0===d?void 0:d.x,y:null!==d&&void 0!==d&&d.is_fx?{type:"index",index:null===u||void 0===u||null===(r=u.find((e=>e.resultCode===(null===d||void 0===d?void 0:d.result_code))))||void 0===r?void 0:r.index}:null===d||void 0===d?void 0:d.y,m:p}]})({dot:e.dot,resultRes:null!==(a=n)&&void 0!==a?a:[],aValue:e.a_value,bValue:e.b_value,inputVariableMap:r});default:return[]}};return t.map((e=>({name:e.name,type:e.type,id:e.id,configType:e.config_type,data:d(e),style:{line_color:e.line_color,line_type:e.line_type}})))}}}},17990:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(74117),n=a(81143);a(68374);const i=n.Ay.div`
  ${e=>e.isCut?"width: 100%;":"overflow: hidden;\n            text-overflow: ellipsis;\n            display: -webkit-box;\n            -webkit-line-clamp: 1;\n            -webkit-box-orient: vertical;\n            width: 100%;"}
  
`;var o=a(70579);const r=e=>{let{text:t="",isCut:a=!1,size:n=20}=e;const{t:r}=(0,l.Bd)();return(0,o.jsx)(i,{isCut:a,title:r(t),children:(()=>{let e=null!==t&&void 0!==t?t:"";return a&&(e=(null===t||void 0===t?void 0:t.length)>n?`${null===t||void 0===t?void 0:t.substring(0,n)}...`:e),r(e)})()})}},18650:(e,t,a)=>{a.d(t,{E8:()=>v,Hl:()=>m,k8:()=>M,IE:()=>K,DE:()=>z,Rx:()=>X,F2:()=>G,dd:()=>W,QS:()=>H,Dp:()=>Z,Dx:()=>Se,Np:()=>k,pl:()=>l,oO:()=>i,Vd:()=>n,dk:()=>o,Cl:()=>c,mk:()=>r,vf:()=>A,dE:()=>d,a5:()=>u,$L:()=>s,kO:()=>p,Y6:()=>oe,JY:()=>re,r7:()=>de,Mo:()=>q,fd:()=>$,yh:()=>ee,DC:()=>ce,xl:()=>se,cu:()=>te,_u:()=>ae,Wl:()=>he,uW:()=>Ae,Lt:()=>h,hc:()=>T,yF:()=>be,l0:()=>xe,CN:()=>ge,H9:()=>ye,eV:()=>ie,aY:()=>le,bn:()=>ne,d7:()=>Q,pU:()=>F,K2:()=>V,G8:()=>J,D:()=>Y,NA:()=>L,gN:()=>P,k0:()=>y,En:()=>b,dH:()=>f,Kp:()=>j,fu:()=>x,kE:()=>C,mj:()=>g,Q1:()=>I,X7:()=>R,Dk:()=>w,gW:()=>_,YG:()=>E,ne:()=>S,lV:()=>we,qT:()=>Ee,Xc:()=>fe,tq:()=>Ce,PA:()=>ue,n3:()=>pe,KZ:()=>D,c5:()=>U,pV:()=>N,Iw:()=>O,Gj:()=>B,fW:()=>me,TJ:()=>ve});const l=a.p+"static/media/background_image.3cf36e936c2d273c11ce.png",n=(a.p,a.p,a.p,a.p,"data:image/png;base64,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"),i="data:image/png;base64,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",o="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABiCAYAAACvUNYzAAAAAXNSR0IArs4c6QAABLhJREFUeF7tnU3IZmMYx39/5CsWEk1IyeeOBbKYZuGzlAWSjdDULJTkYySSJmkkH6NJLDAhG4lJinxkIQthQVnQzGyERLIgH9Nw6eI89fR6Hu+573PPXM953uvevIv3XNf/vv6/5zrn9D7XOa/IFeqAQtVTnAQQ/CFIAAkg2IFg+eyABBDsQLB8dkACCHYgWD47IAEEOxAsnx2QAIIdCJbPDkgAwQ4Ey2cHJIBgB4Llm3SAmR0PnA4cHVzPgZL/Gdgl6fuhgoMAmNkxwM3A+UM3MtL4j4AnJP1Uu/9qAGZ2GLAdOKFWfEnivgVukfRHTT1DAFwHXFsjuoQxL0l6saauIQAeB06tEV3CmD2Sbq2pawiAncAhNaJLGLNP0pU1dQ0B8HqN4LLGSLqiprYEUOPajJgE0MjI2jQJoNa5RnEJoJGRtWkSQK1zjeISQCMja9MkgFrnGsUlgEZG1qZJALXONYpLAI2MrE2TAGqdaxSXABoZWZsmAdQ61yguATQysjZNAqh1rlFcAmhkZG2asQD4Angb+AE4DbhqwCjLW8AnnWHnApdVmucjJq8Cu4HjgEuBs0pzjQHA+8Bjkv6cFGdmxwLbAB9vKVkPSfpgOsDM1gN3lSQBfJzkNkk/Tu3pYOB2YENJrkUHsBe4QdIvK4syswvdhIJiP5Z0/6zjzew+4LyCXNskvTdjT0cBzwOH9s216AC+lLR5jmk+Vfds30KBHZJ8IOA/y8z8i/GNBbk2SvLT4axcjwBn9s216AB2S5r5KTezdcDTfQsFnpP0yhzTrgZuLMi1SdJ3c3L5qdGvU73WogPY55/MWSN8ZnY5cFOvKv896DNJ984x7QHg7IJcT0p6c8YpyK9JO0rGbhYdgNfodywPSvLrwT/LzE4EvNX9nFuynpL0xnRABUgP92vSZknfTO3Jz/t3A35n1XuNAYAX8zXw7tRtqH/6fca0Zn244jb0gpokgM90OszJbejFwEmlucYCoLSu0RyfAIJRJYAEEOxAsHx2QAIIdiBYPjsgAQQ7ECyfHZAAgh0Ils8OSADBDgTLZwckgGAHguWzAxJAsAPB8tkBaxCAfzHee2wj2J/9Lb9Xkg8EFK8hT8oXjW0U72xcAXPHblYrYwiAa4DrVxNYI79/QdLLNbUOAeBvSvEuWOuvrNnTTVb46E3xqgbgSmZ2JLAJuAjW3L9DsW7C4xlJvxY73wUMAjARNbMjuk5o+dI+n8vxSeUWyyeyJ5PULfL5RLW/pOm3ocmaABi6iVnxZnZQNyBVO+8zSevzQz4Q9tf+2OfQnAsLoDvFHQ5s7V6JWVPrLuAeSb/XBB+ImIUG0EHwOc1Hu4cnSjzxqec7hrxSskSs9tiFB9BBOBl4GPCLfp/lF8U7JX3V5+DIY0YBoINwDrAF8CdY/m/5EzhbJH0aaWxf7dEA6CBc4i9JXaW47ZLe6WtA9HGjAtBB8JfF+ktjVy6/y/GnZ16LNrVEf3QAOghndBBO6f4g+DmwU5L/HNUaJYBRObzKZhNAMM0EkACCHQiWzw5IAMEOBMtnBySAYAeC5bMDEkCwA8Hy2QEJINiBYPnsgAQQ7ECwfHZAAgh2IFg+OyABBDsQLJ8dEAzgb64CjHLFZXs2AAAAAElFTkSuQmCC",r="data:image/png;base64,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",d="data:image/png;base64,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",s="data:image/png;base64,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",c="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABiCAYAAACvUNYzAAAAAXNSR0IArs4c6QAABJhJREFUeF7tnEmsZVMUhr+faEMEA10UIZEwENGXkYRJ6UI0iYE2KSlmeoIRiS7ESJsQDCSaEO1EJRhQoqpiRCIhmugGiBClCUuW2i+5ebm33tn7vLLOubX2tM5e/17/d/97z6u77hG5Qh1QqHqKkwCCXwQJIAEEOxAsnwlIAMEOBMtnAhJAsAPB8pmABBDsQLB8JiABBDsQLJ8JSADBDgTLZwISQLADwfLLkgAzOwg4Ftg7uJ//S/4HYL2kL/oK9gJgZvsAjwJn9T3ISPe/DFwh6fvW8zcDMLNdgA+Bw1rF52TfJ8BRkja19NMHwO3ArS2ic7jnDkm3tfTVB8B64JgW0Tncs0GSfwZWrz4A/gB2rFaczw1/StqppbU+AKxFcF73SGrysmmTm2hmCWDi1ZQAgqOVABJAsAPB8pmABBDsQLB8JiABBDsQLJ8JSADBDgTLZwISQLADwfKZgAQQ7ECwfCYgAQQ7ECw/lgS8BzwGfFnGWK5vHGXx7yK8zuvF99OA1dD0q08fMbnXx0yAFaXOylqeYwDwDHCRpL8XmjOz/Uvj+1U2fIGk5yb3mNn5wLOVdb71F4KkbybOtD3wNHBhTa2hA/CRjQMk/bS4KTO7GHiyotlXJZ057XozewU4o6LWJZKemnKmPYGvAR+96bSGDmCdpKmxNjOPfc2E2bWS7p8B4Brgvk6Obb5ohaSvZtTyt8sTu9YaOgAf4ztuRqOHAJ92bRS4UdI9M2rdANxdUetQSZ/NqPVB+ZzqVG7oAHyE5WBJ302J+xrgoU5dbr5oraRTZ5j2JnBKRa01kh6ZcqZ9gc+BzqMmQwfgPb4GnCfp94WGzczHGj3qe1WY5pdeKenhyT1mVgvSt/8IrJTk44X/LTPbGXgeOL3mTGMA4P18DDwB+PuuT9VdBexa0+jEtS8uug09p7HOb8CDwAbgQOAy4PDaWmMBUNvXaK5PAMGoEkACCHYgWD4TkACCHQiWzwQkgGAHguUzAQkg2IFg+UxAAgh2IFg+E5AAgh0Ils8EJIBgB4LlMwHbIAD/Jqnz2EawP1tbfpOkpm/2+vxSvmpsY2s7EFx/5tjNUufqA+Am4M6lBLaRf79Z0l0tvfYBsAOwDji6RXiO9mz0AS5Jf7X01AzAxcxsd+AB4FJgu5YDjHjPP2XC42pJv7T20QvAgqiZ7VaSsJwP7VtVJpVbe5vc55PUbyxHoVLDJ6o3Svq1b81lAdD3ENP2m5kn6gXg7J71XwLOleSv2MGtwQIob3F+a/cWMHWutIObPt95siS/ZR7kGjSAAsEfjfk+4M8mrVk+cX1Cn0dK1oi1Xjt4AAXCEcC7wB4dG/0ZOEnSRx2vD7tsFAAKBJ969g9Sv/3d0vLbwVWS1oa5WiE8GgAFgg/OPr5Ef5dL8gHgUaxRASgQbgH8obGLz+6/PbtOkv9dMpo1OgAFwvEFwpHlPwTf9p8mSXpnNM6Xg44SwNhM3tJ5E0AwzQSQAIIdCJbPBCSAYAeC5TMBCSDYgWD5TEACCHYgWD4TkACCHQiWzwQkgGAHguUzAQkg2IFg+UxAAgh2IFg+ExAM4F+en41y1PcyxQAAAABJRU5ErkJggg==",A="data:image/png;base64,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",u="data:image/png;base64,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",p="data:image/png;base64,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",v=(a.p,a.p,a.p,a.p,a.p,a.p,a.p,a.p,a.p,a.p,"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAcCAYAAAB2+A+pAAAAAXNSR0IArs4c6QAAAppJREFUSEvtl8+LTXEYxj/P8eNOkcjCyIKFsaUsrCjkL0C4VxoGxcJCFnPMwrGYe+5CSoryq6E5B2FtZYid3ZQVCguhFMrC+HEenTv3TvfiGveOsjDfxTn1fb/v93nP2/s873tEbe0a8JKvGSeA9ZhF9f2G9xOJW7MKHB+K9KnR3hu568sYx2w2Az0/+Yo3wN2ZAUeuDOplblf+yEG/ZYzaLPwFYNOW4H5PF+ujSFluiCIHT8a4Z7N2Ml/g7cwZrMrBq8Cl0KnNDokXMgMOeN14iTOkgJXOGAQKiL1prIv5mWLoPswFYEwBA84YVYAb/ZXRbTFos1QiTWKVxoH7/crQLdieVHS9VeSl0Ods9iGSNNbOGvAwpiRxPom1v6Vvv7cZrgleJxUtrgIX+12NMBDrhmM9aOW886ijLOMYMJJWtLHmewfYEAQcHy4raukbem1m7uf2tCJNA/8nqS6Ffl4t9YC+pKxLv6nqxKYocTmJ1VtjxEXDnjpNWvnWaZdTNom1bLy4Qp/BHADeIU4G+oHHrgrNSuCATUDA7rSsoZoGbLG5IZELyllgVGrmcWa6MYeBBYizaayDVeDeyPM/f+LhL+Xu508YWdHFprpy1QK/itk+qXKJx7MLrBmK9L4KnK+Dkee+HyOU2YCatdrGEs8Qt3tmcyqK9LVJ2WyVQvZJbLZZLo1L8cQybyxG5heIz0T6mO83H5g05L934N8D/0GqnwI3h8tckNTUBNxpqtsqLnEtjbWjMenF0J0VV7t0ktiaxLo5ZTq1LSBwKamob+oCMt0WW9B1ehCY8ujTwbA3weVi6M6HvXbHWwUcSso6XetMnY+3bQ304lFPgdVRpM858JQG+vyCyX5h6m1xzjyOnuvXh8bC7+QX5ju7vGU73ZLFZAAAAABJRU5ErkJggg=="),m="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAcCAYAAAB2+A+pAAAAAXNSR0IArs4c6QAAArVJREFUSEvtV79rU1EU/r73IGZwMHWw4qCDdlVwcKoQxb+gGlsr1Fjfu1cHB3FRh7aDuIgggr6b2MaArbXWuZNV6uZWcFJBHcQWhCgITYLvHbkhgST0V5KCg73De3DPOe879/B9551LVFc+n99XLBbvAkiKyJ7aft37I8mXsVhsLJ1OF+vtuVwuXi6XR0SkD8Ch5liSywBex+Px60NDQ9+snfZhQVdWVhYB7F4FsGGL5ILv+0mSkTWIiGOMeQOgdxOxP+Lx+BELXgEOgmAKwADJryRvRVG0VP8Rx3EoIodF5DaAHa7rXvI8b9z6ZLPZ4TAMHwMo2ViSi1EUSVN8t40Vkf0kp5RSgzXg7wC6AfRrrZ+vlXkQBBkAHoBJrfX5atJPAQzaHLTW/jqxZwFMA1jSWu+tAVcydF33uOd5b9cKNsaMisgIgHmt9ckq8CsAJ0iOKaVG14rNZrO9YRguWLvWmtvA/0mpjTFfLNUdxxn2fX9iHXJNisg5knml1AXrZ4wZF5GLNZmsQ66K7KxklVIHKuQyxjwUkcsACgDukWzQMcmajq2P4zhO2vf9J1Udnw7D8AWAiOQjq2MRadCxiFipXgOQsD5KqSsV4Fwut6tUKr1brd2tcoJ5pdSpWueqJv5MRPo30bk+xGKxY+l0+mcF2K6ZmZmdhULhhohYTTb0ansCkp8BzPX09NxPJpN/6kGsOZPJ2MbSJyIHbYWa7Msk5xOJxJ1UKvXb2hocNsp4K+3/HngTpf5EctbzPMvMZvK0V+pWyEVyWik1UF92Y0x75GpVTq7rnvE8b7ZjObXRQCaUUsMdN5AgCLZ/i6tKdnsQ2IrRp6Vhr17LQRB0NOy1NN4CuKq1flDVcfvjbSsDPYD3XV1dR1OpVNkCdzTQ2w9sdIWxv0URmSN5Uyn1q5727Vxh/gJZlrQ7iBbfCgAAAABJRU5ErkJggg==",h=a.p+"static/media/icon_zanwu.ead8132b0a8f6e0462b9.png",g="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAMdJREFUWEdjZBjkgHGQu49h1IGUxtBoCI6GILEhYBzawf/rE0MYSD0bH8Oqs6srPhKrF586qqRBsOM+/D/HwPBfCWIZ4z02AUYjajiSKg7Ude9IZfj3bxZKSDAxpV3eWTGb0lAcGQ4c9FEMisZBnUkoTWc0z8WjDqRlCFBqNlWKmUGfSQZ9MTNak1CakAd9FA/6TEJpDIzWJKMhSI0QGNTNrUFfzIzWJJSmwUEfxaM1CaVRTEv9VGtR08qRow6kNGRHQ5DSEAQAoBiQKfccuCUAAAAASUVORK5CYII=",b="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA29JREFUWEftl11oVEcYht9vzslmo8GGICjaFiWUQk1MNERpryTtbkw0QZB4URAsgr1TkbC6S0vECzeJaEsvChZaS3tTarGgaTZuxCooUsVmzWKwaigUpaYY0dLmb/fMWw6asobjnuwPKHLmdmbeeeb95uf7BC94kxecDx5goRF6+R1c1da9JDVhdYnIWwL4bccITlHkug848Ovp8O1CXCzIwZXBQ9Vk6hKAckAmBHjwGBCVAMsIDJeKb/PVeMeNfCHzBly37Zh/7M97P0Bzg0AGCX5kKnXXBkkrWQ7L+hJEpQAX51f4A5eO75nIBzJvwLqNPW9Y0+k+2z2DZnNiIJTIBKht6l6jtT4BYMIoNVoSvaFbRQWsbepaYWl9QCCWszBfEchqDYoSniOVzhwnoEGRNQAVKDb8v4465JtiGluG+vf+5tT/TAdrAtE+gM1Zdy0yBaAEpHIUF2UB2iLhy6YjxM9DZyKNOQGubft00fjk+BGIctxZPuFymqMsTPvn+Y/9cnL3aE6AmYP376fq7ARFhMUAy0XP9ZK0t39v3Hg0ct1+PV415tfFYjvtsObdctVzBXxyWX4SwDJNX+tgrGN4hu7t9iNl/zyc6lQl/OpaX+RmJnV9c0/VdNraXV5RGsp8YrLp5RXiJ89JDJpQPmnJBKkORg8K0CHghaH404e8Jnjwd1CWKnD7tYHItzOLZ9MrOmBNMLoD5FGInEnGw4HMBWqC0QGQ70Hkw2Q8/IUH6BRiz0HAPaPOdqg9Bz0HvWcGgHdJZv3hs78712ShUAdFsGsoHvnsuXx11cGudwT6FIDPk/HIx0/9xYHoCRE00kAoGXtOf7EN1BA8/Nrl03vuzE5m61uPzrNSY+sS/RG7sPq/FT2baWjpWTyVSg9QZKGh0JboD1/JO1u1L936aIOlcVLI+6UlZuBKX+he1nrFbTG7/n1wd7SX0O8CMlpWzhWXf4yMuc1z6q/b9EmFHp/8hmSriJytXLJ4w7mvP5gsCNCevLL5UCPSaTvpXERwWIC/mGN9IqRQsEwgy0CM0jTeT8ZC59026nqLZwRqm7q3kjpMcCmIBW7Cjv2Cv0HcNEUdHozv+24uGnMGtMXqN3a9nk5JFaGrSDjWws9aVARaoEbMEo5c7d33x1zg7DE5Ac5VtJjjPMBC3fQcLNTB/wD/kQZWQPyRuQAAAABJRU5ErkJggg==",y="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAVlJREFUWEdjZBjkgHGQu49h1IGUxtBoCA66ENR379DmYmB7fnxn0TtKHQfST9Uo1nPr1vn///dlBgbG65d3V2oNQgd2RPz//285yGEaAiosq1eH/aXUkVQOwVEHUhYhem6jITgagiSHwGguJjnI0DSMhiBNQxBUtzIw/NUh1pL/DP+sGP4z5ILUMzMyxvxjYCSyqmP8w83Iug9bAwNnFCMqfmKdR6k67A0MnA60dO8T+vLv5xEGhv+alFpNSD8jIyik/8+6tKsqC10twUwSGrqKmZAFMPnbH+9G/P3/fwmIL8PCw8HDI/GHGL3a2qH/GxoY/2FTS9CBxFgAUzNaF5MSWtjUjobgaAiSEQKjuZiMQEPRMhqCgywEO0P+//+7GlS3BllVsOGqvkhxNFWj2CywTfj7F8bDjIz/D2Cr+ElxGEwtVR1IjgMI6Rl1IKEQIiQ/GoKEQoiQPAC0E/MpzcLYaAAAAABJRU5ErkJggg==",x="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA1ZJREFUWEftmFtIFGEUx/9nvKy0BBJRkA89WA8Wu4r1UGmk0LordnkIo4fohr10g4LSWSgiwVGL6AqBgRU9CEKRELY7dLXUggJntHqpt8oXJSJJc/c7MVtC6ezOrOtuCjuv3//M+c3/fGdmzkeY5RfNcj6kAROt0Nx2sLLykuNzaOSEAO8AUT6YHbYcIQgCXdOC8kFb+hiiqA4WVzUVhMZFOzOvnE4SAvVqqrzWKrbIp5QJSBnag9qHZlpTQMO5T+GR1wYcEYXBaIVEXSDx1SqhsU4CwuFEz6u7/qFYerdXqWHBLUYOJzkW9QSODU/WmwIWepSTAnzGCCSSPH2B2sd2wOLRuDxKNYjbwJBA1K0H5RLbDro8DQMAVhDouqbK++NJbEdb6Gv0CiE6wMgmQr+TcjaYuRephtkNXRXKqNEQJEm7tUDdLTtJ7WqKKhrXCQiVGfMA+pgt5ZS+Dhz9Ei3eHNDTwBH6DGzVHvg77Ca30hX7zrpD4fGnDOSC8AVZVKrflz/GiksZYLFXWTYu+DmAxSAME7I2aMHj/VYPlRLAwk3n83hs9AUDSwF8J8JGLeh/aQUXfQ/OYIlXbT638Ofo+DOAC0A0RpJUFe2dF08Xz8geLNnSNP/bj/AjAKsjryxGdZ8q37Xj3IQmaSUu29OaM/RpsBPgMoCMB96nq/KNeOCSVuKy048zh7t77zDz5kgSCUe1gP9CvHBJAWRmcnsbb4F5ZwRIono9IJ+KBhfR+5pqCNJ7LXCiy9anzpVAk7g8ymWAD/1ORFd0VT4cy7kiT3NJGKHnAA3pqrwwqYDuioYjzLj4m41ua4G6XUSR/Rf1cvsatnAY9wyBrvqn9MSMNom7QrnKzAcI1LGgZM22J6fLQ1b7LqWARud+HRxcs3x+fld7+/awFZyxnlJAO0CTNWnA6bj2d0zawbSDZg4k85d/Shd7G3exEDeNXzE9KOfY/ZIkdWj6p0k8SguDawC81VX/lBn8v42dBmSht6mc2RigOEMCnepT5XpbDiY6uFs2Dku5ELwehL0GHBEN5GU4V3V2HhmzBWiIEj36sIT8IzDgMrOk6jf3a9+ZxcQ83Zr24ZEVHdEYmD9IoLYlmc5mM+cmbjG3j9+sjEjFetrBRF2e9Q7+ArR62TiUhMb9AAAAAElFTkSuQmCC",f="data:image/png;base64,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",C="data:image/png;base64,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",w="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABP5JREFUWEfNmG2IVGUUx3/nzr5ZGVpQKCRUuiWJpFCQmmUkGGmufTCa2V2lzAKRyo28M1pcK/eOlZb5pYxCd/dOYFaGSYWYhSlIpBFa61YEBmtForlh6+7MPfGMM9vs7NyZWUyaB+bL3HPO/d9zzv+8PMIwT9jW0WJxr/rMFhinwlhI/8zpFqVb4bhY7FafXYm4nBrOK6Rc4cgqXYDPcoXbUarK0hOSAvuw2OStlQ/K0SkJKBLV6SgvKkzLM3hUhG8VTojQbZ6pMlZgjCqTgZty5QUOIDztubK/GLBAQNu2aWjHIdYDjw8YEDoteDMU4sOtL8hPxQwvWq3Xp1LM9+ERlBtzZDc2TKVl4UJJFdIvCGixo6P6zrENZbZREuGEKk7DVN4KMhQELvNhD4vgqDImLSfsrqll4RZHTufrDQFkwPT3ckBhYkZ4Z91oIm+vlJ5yciBI5qF1OrL3FB4w7zwmvq+uY1o+qEGA0l9zmI+zngHW1dcRcxzxLwRMVtdx1OrqpRVYmfVUwxTuyfX6IEBhW1/N5owIcc+V6H8BJN9GJKquKnbm/42JuDyRlRkAZNikypfZMNXX0RDkmaaX9FL/JNOtELUFASt9I2r4erMjfxR6nvHUjoHwCTOy7PsXkK37DbVNAteO4oagnFkS06vP+hwGRgN1AR78W4RvqGGOt0bOFJIxOXXuNMdMopuS4MVleia3wBQ9TfF+RvHRRFw2B4WqMaZLfJ83gF8RPg2g7gLgcoUnE668FmQrbOtSSNtCQtxvimfaQ5GofqbKLITOhilMyqd2OKY3S4iUlUJ9WKY+j1mwRiy2F3pZVgZwQhbvGZmQktziSmeufIZER0ydEmGv58pdYnoTwu+mHVhCS4crG3KVIrY+gPC6wp/p/5XqTO/qRugPyKEhMqLUIiz3XBn0EY1RXeEr6xGSKFdJOKpNKG3GcHUV4/MrcHNU65PwLtB3IYxTuK6miplbn5ejuXZMRe9P8mOmDDRLJKptqjQBRxNxmVTspU3P6B1+kqUTalnkOJJsjGkzML6jVZ41eo0xfc5Xfki40u44WtXVyyshYXu7K18Usxu29YjpfSK0S8TWvQp3ivCO50q4hOJxYIQVYn7HWjkQttXQuqe6irtTUOUn2YdwMuHKxKZVOs/3aVOhM9EqtxWzG4lqQpUHBT43ITuGUo+wIeFKSwlAe0SYZMF97a4cbLT1kMIl1XXMMXr9fXwiPmc74jI1YustCjtNi/DiMquo3aiuR1mB0GUA9aBcJhZPea1iunvgCdt6UQBFYtqiPi8j/GVCdkZhZCUAEuipvJANM6kvTsgGJbWtWwFD35K0v1g5lKU90FayMOZmeD6gsK1dCFdWh7g1zbIUewQu91y5olyW5RZGsWgq2TqKAWqO6tyUcK3XKpvSPTGmyyzhePta2VkuoCGto5zmmgVlPARMDglzTR0qWuzKqEMFm2saUJnjRySqpqfN95WvBLqKARJhhsI4UQ56cZlZSDZw/EiDKmNAC6/UmVjsSg9mpZbF80vibxZECvWyogNaJmxljbCLV+uEpE+9qlkcgo8IqsrPnivf5UuVNcIapdwh32wcibhkB/ELmTyG6IZtjQ9sHlB4yDda+WuQ2Twm1LLqf1uDDKiKWhSzvq2oVToLqqIuG3KzsGKuY/KpUTEXVvnAMmvTXIHZKNcUutJD+EVhN8pHw73S+wckr0FdcILNowAAAABJRU5ErkJggg==",E="data:image/png;base64,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",S="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABF9JREFUWEfNmGtoW2UYgJ83aZoKRSbKwP5Qp0PwAnObDhw6Qd1k+qOdlQpJM8scCjK8IEjSeolKTRAUB0NQ56xtkx/F7vJDxFZlnbLBdBsDi3iZij8ceMHhKmvaJq98p+eUk2u/tKsukD/n+973POe9fu8n1PmLxPUSCXAfyiaFK4DLgRZXza/AaYFfEEa0wIfZtPxVzyvEdnO0R7donh0IG1AarOSEGZRDEmRXplf22cjMCxTr0dvyBV5FubVEYU6EU6oYq5i/+bWI0KLKNUC4aL9wJBjgmYFe+aIWWFWgoSENHjjB66o87lNwTmBYguy/OMTHbyZlopLyx5La/Pc092ieNoV24CLfvp1ta3i6o0PylWQrAnUlddlUjiGUja5QPiD0NTby/J4XxbOGjQfY9oK2TE3xUkHpAoJGSISRUJgH+5JyplRJGZCBmZ7ksMJ1rvBP0sCWwZflpBVBlU2Rbr1JlL2qrHD0wjehJtaXQhUBGTftP8FHc5YRxhqgvT8lfy4GxpPdmtBLZ2AY5Q7nmTDatprNfvcVAUXi+gbwhLt5rPkqNr79qEyfDxhPxyNvaWjiZ0bnoGBnNi1PeutzQE425fncc1MQbrG1TKxHbzZyA73ylQ28sVQevvTcFwxyu5d9c0CRhB52UzsfCLHWNmai3bpNC+x2PiTA9swrsscGqvM5XVWY5pgT6MKRbErWu7EFbtHbax4EhHcHU7LdRqkPxvswrQsqobsLysMOSJD7TfF0FEXi+ilwJ3CuKcxKm9SuAON9gzWUKQmTOX5w69Rn2bTcJaY3Ifxm2oHAYCYtsfms44cROKmwyk2EoyjrAGuoaFwHFDqZbTPLJdqtnVpgwHFXkAcGe2W4FlARjDACPKXKuJEJhrmyMMU7qmyyhers0fZCng/cGIxJNKH9qhir5JY1cVm1dmAESmFCYVqnJp1Of8qsh4XlGubsdI4DtlCmzZyZ5A9HXBiQSEIPujVhPJuWG6tZx7Vkv1P5Z0t/a19SJiNxvdoP9F5Kfu9KapMfKgCxwbRkqumOxPVr4AaEMQP0Lcq1wCfZtHi9q0w2mtBjqqzxw7gJUQZknvuhRDieScnaGkCjwN0I3xmgsyjNQH82LQ9VFerRDRRY1xhml7GMt6+Shbw1AzWVYwcBjmZ75VANoPeBrQgT1kA1lFW00HyZ6l+PxLUIyMplSwxU5DKroF5KoGhCx1W53gnqetK+ElStGLJxW1na11sYS1+yWKCywriQ1lESkIsK6rLWsdDmapP287msYnN1WsLszFX38cPIxp7VFfkZfvRah6nU84F4653Vjh+OlRZ4QEsmNfB9joPAP5mUbLaGqXVAc750EUdYWwhvn9UR1o2lC+eQb4D+6zHINOrW1dxbdQxyu/SSDIrmUK8z7KtrUPR16SUdpc2A2Bimw2qU9qAc9x3ntbnBcXbh/7ls8GdNresYcCaG0/7rGPcCa+V5v44pTeUL5sKqQjNd0iu9fwG2jLlVN0RqBAAAAABJRU5ErkJggg==",_="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABFZJREFUWEfNmG1oW2UUgJ+T2zYVikwcA/tDnQ5BJ8xtOnDoBHWT6Y91ViokzSxzKMhARZCm9SMqNUEQHAxBnbM2TcBit/lDZKvKPmSD6SYDq/gxFX848AOHq6xJmxx5394bkjTJbtps9EL+JO85ee75Pq9Q5xPq1SskwAMoGxSuBq4C2l01vwNnBH5DOKB5Pk4n5J96/kL8Hg7362bNsR1hHUqTLzlhGuWwOOxMDchePzIXBIr06x25PK+h3F6mMCPCaVWMVczHPO0itKtyPRAsOS8ccwI8mxyQL2qBVQUaGVFn30leB54sUnBeYFQc9l3ezP43YzJRSfkTMW37d4r7NEeHQidwWdG5HR2reKarS3KVZCsC9cR00VSGD1TZ4ArlAsJgSwsv7H5JPGv48QBbX9T2bJaX80oP4BghEQ40B3l4MCZny5XMArIwkxxVuNEV/kWa2Dz8ipzyRVDlUPfzukKn2avKUqsXvmtuZW05VAmQddPXfIKy3uoVDjVB51Bc/p4PjCe7JapXTsMoyl2u/rGOlWwsdl8JUKhX3yjEjHCo7VrWv/24TDUCxtPx2FvaPPErYwUo2JFOyFPe7wUgm005jnhucuC2Rlmm/IWMpXLwpec+x+FOL/sKQKGoHnVTOxdoZrXfmInFNPBjhoPAf6m4bPRrTRNT+SlO2EAXjqXjstaNLXCL3h7zRUB4dzgu2/wqjjynS3PT/GzOB4Ul78XlT7+y3VHdlVcetSAOD5riaS0U6tXPgLuB861BltWT2qFevQ44PRcgUxImM/zk1qnP0wm5R0xvQvjDtAOB4VRCIn7f0H2ZOQMZ+XCvJhW6mWkzSyTcp92aJ2nd5fDQ8ICMXkqg7n7tzOf40LotQETCUR1SxVgls6iVxdXaQTXI+bjM6DRt5uwkf9kQFJISiupBtyaMpxNycz3WaYTLXB3fAMtNITZA36PcAHyaTshMha7jma+FXKAx4F6EHwzQOZQ2YCidkEfqYLFHGwT0PrAFYcI3UKhf15FnTUuQnYMxmfTAawH1xLQ1m2E7AY6nB+RwjTgsAfLlsnBUT6iyyh0dNnlQ1YAMzFSGj8wII8LJVFxW1wAqcZkNahG+TcVleTUhtzwM2XFmZp6xUJWAimEADUBkOCGpqrqjOq7KTTao60n7cJ9u1Ty7iqGyk3bAL1RqDXLOs4yBkQDbUq/K7mows9K+3sJYDgU8rcq4+UMnyDX5LO+4k+YFYYzMrMI4l9ZRAgWnFFZYCwjHUdb4sYxnsVmtw03duptrMVSZO3xZxshUbK7mh7mOHxWgfMNYd1UbP6yV5jigFUGZ5lgzgIstWXNAMwfnM8JG+vVWoyM5IF/5qfS+Rlg3lhbOkG+ALvUaZArsppXcX3UNMlAXa1EM9ektouypa1H0/G+gshlGCgsjNHSVRhhrCdLla5X2oBbUZUNxptS6jgG7MZwpvo5xL7CWNfw6pjx9F8yFVTnYxb7S+x+JcrhVbGUp1gAAAABJRU5ErkJggg==",R="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABGNJREFUWEfNmF9sU3UUxz+n7f4kYsQHmCDzxUCib1uiJhsaNe7BsGxFzWLabWHwbDQaSLsKlMFsozFKeDNmg7ruYVEZiJFkURQZxhf2ojGwaKIsA+HBP+xh69p7zO+ut2m7Vm4ZrPs9/dp7zrnf+z3/fr8jVLgCIX1QPOxQizaBR1TYDGwWRYFZhFngD1EmfPV8eTwqf1fyCnErHIzoTixeU3gaxedKT0gLfKcejo0Oyik3OrcFFAxrK8q7Ci1lDKrATfNMYQNQzuYFn7AvEZMf/g9YWUBjY+odv8T7wOsFBsR++RkPjGstl7YK16NRSRuZaFR908pDkqLZAj/Qjtogc8sDH3Q0s7erSzKlgJUEtCuq61MLjKG0OUoCMyIc6GgiUc5Y8QvMR52eoleVAYUtOVvC2fse4NWPQvJPsc4yQAbM4jwXFR7LCqsIh2vqiB2PyrybOCiW2RXV+sUFwqrsz3Ppz+vW01oMqgCQ7aYpvsoxI8x5IDgSk9N3AqRYpzusHRYkUdaZZyKc7WyiPZ/xAkCBkH6YixlhzufjmcRhmbobYBwbvfu1KZ3mvAPKxNRIXN7MC42lrckmVS44bvII/rvFTEmmlHHHfT6hxcm+HEPBkE46qS3CQDImB90y092vm4zsyDtyza1OMKyHVDmQdd1kMibb7b3NTkR3aobPs3/M1NSz1W0A90W0MZXhV6Nb6+XR4UG56gaUHejzTOeyz4vfFM8lQGH9RpXnzN4j7B6JybAbo0amO6IvWxk+tXW9vDIyKJ+51g1rn6UMZVn6OhmTF8T0JoQbdjsQbvqb2OS2zqwUUDarr9nFU0ijbJRAWHtQEll3DSXjssftF64UkNEPhHUIpc9mGLolGNaEKj1Zd3VWmlkrcZn9QaY2KU7jTUgwpOcUnrUZqqMxeUhmVpOh4EHdogvYiSDwrXHZZZRtpllvq6fWaZTFoEw2LcKTy8AqT1kWe22GPbyH8OMyGeGnkSNyudSHmoZ8ZZ6UjUe4YgDdMlVT4EYyLg2llEydUYvfFWoqYS8nK6RqvDx+4ojY5aF4BUP6p8JGhDnjsn8V7q8moEBYr6M0CNxaEy6bXmBBFY/tsmoHdW9EH05nsBNpKahDegLorWLa+y3lZDauEgWFEWF4NCa7Kwncldah/MIoHnrWXuswbFSruQbCugfl42xzPZeMyfNr5vghXl5KDsrJqh3QAv06gGUf+k12XUzGpTW7Xwrh1TzCBiLaScbOLJsQEbYnYzJZAMj8qMYhHzg6Gpc3nMxe7WuQ34JPnBsHwoS/iRfLXoMMynt1UUyl6Mfi7Zyb4JeaelqKpyP3/Co9PsUuUaL5V2nDTG0dXaVGNXc0bBDlCxFOlRo2/LZIg2XxhAUdouywjxWF66i/mbcqGjbk67sdx6hgxjIb7K5dYpnURtjnZFO59nTb+ZCjuIKB1fd4OGaKnpse6RqQYyx7bWoXaENpdEZ62eezovZY76rCBMqZ0bj85QaII/MfQdKQSmyJY4cAAAAASUVORK5CYII=",j="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABBNJREFUWEfNmF1oHFUUx39nNjURDLQUhAYUtDYqFiEFfWiNXxBQrLr1IZDZJhZFfZJKI3am6cM+mM5GTLH4pKLYrDtKHmpLFJGAVWsLIihKi22wiAopCNpiBDcmO0fuuLNuNrub2TbgDCw7zJxz5n/uPR//c4UWL9vRdWLxkAb0CVyvQheEP3PNijKr8LNYTGvAh35OLrbyCYkrnBnRHQQ8q9CL0hZLT1gUOIHFq4VReT+OzoqAMq5uQ3lJYWuNwTMifKdwQYRZ806VLoENqtwO3FYtL3AK4YWCJyebAWsIaHJSU0e/ZhzYXTEgnLXgjVSKY4dflPPNDD++XzeWSjwawFMot1TJHkpvYbi/X0r19OsC2pXVtX/PM4nSZ5REuKBKNr2FNxsZagSu7NiTImRV2RDKCdNXtdP/dlYu1eotA2TALBQ5pXBrWXiqYx2Zt/bKXJwYaCTzxJh2Fi9SAB7+FxPfr+lgay2oJYBCb77ho2hlgLHuDvZlsxJcCZhIN5tVa6bIAWBvtFLpHh6sXvUlgGxHX4liRoRcwRN3NYDU2si46qnilJ8f8nPyXCRTAWSySZUvom3q7iAdZ2WM1+fn6QWKeU++jONAeaWOVrZPuCvKvv8AOXrSpLYJ4Pa13Bw3Zna62qvKMQVSwo68J5/FAWViav4S50ygm5JQyMm2cmyBKXpa4kjZ0DN+Tl6PY9TIhHUK3lPFIsWAPyqfx9W1HX0aeC0EkuIxUzzDFcq4+okq9yGcTfewudXUtl39loBOf0xujAvGyJWT6LSpUyIcL3hyv5jehPCraQeWMPyOJwdbMWpkbVe/Mv++J3e0qrvT1T2BMo6wiHKt2K4OokwYQ2vauKm2Au/ar5sWSnys8Fu9jwlhMw2ba/X9MtmA9W0WD0x4MlP9zlT0hUV+KJeBIcm4OqHKIHDGz8nmOim6XZUpEX5cBkrpotzHKnr1ngWsR7ghleKR/KhM1X7DdvS06X0i5CXj6HGFe0V4t+CJ3QhQPWPRVtXq1G5dxtXQqUaAMq76qgwIfGq27BxKN8JB35Ph/wOQ7eo4yh6EGQNoDuUasXi+cEBMd19yNfNu1VZonw5rwMsIf5ot+0OhMwmABOaSt2XJC2pHDwNDSUh7YCJRhVEsBpPXOlaluTr6kyi/F8akp5VeVre5hoCugH7YI3o3JY6I8JdlMZAflYjkrYitIf0IQV0mQRt09Z4SREPgbt+T/IpIgKYELSJal0Nhje7QiN6pAVdvbOdEXNo7U6Q5hQ15TRXJNxOHn5OIiMdxOraM7WiuMnlAfZJfxeAqY5CZPDa1MxLH6zhoWh6DjNFEDYqRl4kapSNQiTpsqI6HxBzHLCNqSTmwqkPIzdi0XaAP5bp6R3oIvyhMo3zQ6pHePw+s4E51HxMBAAAAAElFTkSuQmCC",I="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAkCAYAAADl9UilAAAAAXNSR0IArs4c6QAABTpJREFUWEfNmFtsVEUYgL9/t0s3hELrhQhEH7yARmICj6BRNCQiRapiDbttIXIxJhIMFdgtPiwBehYUAuiDFEVaukvSB4Wo0YREMAJGHoAQGgFRg2KJGi5aCb2e38zpOWVdtnspfehJNtndM//MN/PfRxjEE4pomfiYrTYzBe5TYTw4H/O0idKm8Kv4OKA2XyTjcrXQZaQQgfAafQGbZQpPoBTlJSv0CHyLj/cSG+TTvGSAvMDCUZ2OsklhWtrErSKcUrgkQpt5p8p4gXGqPAY8mjpe4CjCqoQlR3IBZgVraVH/vuNsBpb3TySc8cFOv5/9jevlp2wLLHhbH+jtZa4NS1AeThm7rWIqtZWV0juQ/IBgC2Na2tVJC8pMIyzCJVViFVP5KNuEmRZyN7hIhJgq45wxwoERxVTujsm1TDIZwQxUdwdHFR5xhT4LlhHetVrac6kg2/tXN2pJx1USwJw+Nn4IBJmWCe4WMGd3J/jSOylg48QgdbGY2LcD5cnGYuo710E9sNo7uYopzErXwi1goYhu9WxKhHjCkuhQAKXPEY6qpUrE/X9bMi5vpjnKzZ/G+1Q57KlvYpCKoTqpdDD35Pb1q1V4PNVb/3di4YgeMSHBGHpxKZNu16ZynbSxuc5rnDUOYUJJIi7TPZl+MBM8tZdP3BevJePSkGvioXgfiuhSYIfjDH5e9ILwTbCofq3KDIQzFVOYXGhIyAeyqk4XY3OxOS5feeNdZztt4pwIBxOWPO16LJjch/CnSTM+obbZki35LJTvmKU7NND+C40CzwJdfuGVPZZ848lXRXWFrWxG6EEZa3Krc2KhqFajNJnvgSIezBXR8wUy48yJ7D9JA8rLqpQgXBFhZaJednnzmAzR3cN5N3zUJC3Z44CFo9qkSjXQmozL5EIWzjZWVSVcx3ZRqhXGAJ0Ih4OlvJDuWKGInja5VYQ9CUtq+sAielDhKRH2JiwJDRVYOKKbgMUKZUA3wjGUOZnKoHBUk6rMFziUiMsMT5VnUSYibElaUpsK5qjiOAkbegLC8iZLLucDHopoTOANhTsRegWOj4DZH1vyVyb5UFQ3o6xAOJe0ZJIH1o4ySny8lagXU030P+GoztM++wsaww0Wc/+uteKUOAM98yO6UmAVcJcIJpWd8vsob9ogvw8kE67TWrV5F+HfpCUlnir/USjJBFYd1edspUFhgtk5cGKkUP5hvfwxwM5fB9ai3G3KMxFa1Ud5coNcyLYZD0ygPRGX0TlVaSarqtO4bWMCYZlCjw+O+YXn09UaqtMabN4BxjoQytkRAebsXi8/5lJ/RlXmMn7Xu7YK1KhSitAFfDdqDHMbIvK3A79GX1Kb91W5x/wWOF8UoKJxnbTmgnIjQwbjj2gjUJMtXDgR+iQfoFSijBahAzjku4N5vVd4EmVnSkPyc1GAeU3r5EQ+UGaMFy6ApmRcFhQUYGMxLTrXyW5grnEW4IY5OREmqTLBhbjgRvbv84VKDbDiozpRL819YAWkpGXbtfhym1OFzgJGitCtSsCFuOgXqlLTTT5wA6YkV8d5J/GlMR15vYsWtXnGDSNmijYfLEpN0PlAZU3iDliBZc/CmAa7b7BVxbHN6z5hSbMlpvAr6MlZ9jhwBRaKThXaSV3Az97BJP68CkVXncOztHYdob8ZMR1SMi5ew1CQinINDkU03t8pQfZmxEyW3r6ZTumhYtYMVVMy6PbNwA3LhtdTwbC8IvDghuWlSqrxDrtrqHTPGnYXd+mAbm4tF5iJcm+mq06E3xQOoHw+mKvO/wDAc9lDR9jnlgAAAABJRU5ErkJggg==";a(65043);a.p;const T="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAnlJREFUSEvFlMtrE1EUxs+508SIIlH0LyioFCdRShWk1AeSIZWKVAvtRqkbWzdCpCST1QiSmRatUMzSBwoiLRTRYM2NIgimoVrqxFURV2Jd+YSk0mTmSGoDk+k0j0JxtufOb7757vcdhA16cIO48H/B4kl1OxbgPro3DeiJ0Bf7X4qBmMoAP+pcvlOe1VR8VHnV9C2dmUKCFpcXW2YnIr+sYF9AGySiOEM6pfNoom6wL6DGgaBfcLGOuWfhd1aoXxo+RqbBAfFalstXrbOqin0B9RIR3GIMe/VkZNz6YmtwpHnJKM4AwYssl3sRkeoCi5J6Ak2aclJzKDi2LV/MZQAot9Xr6ZieCC3afXdU7O+M7TaLmAGilF2NohCbTGsJIPALm1nb3JPwglNkV4GXE7AEGQL67aRGlLQbaNKgk+drWmFNgJMavxTrN0287eR5VSv8kjpAJow6qdl/+qbXyC1+RUTNnoCaVvgk7TKYpDqBW7uu7yz8KSwAQizLZaXWKqjwuKdn3D3/89NLQmpmbk+bvWX/4kdxRNaX5ZFH1eCrLu9AcGyXYeRK+fzh8uxon316MV/ZNDVORBcYE47oyfDMWnDHuImdmghFeoNEKZ3LZ63hL18wAOzzgPvgW37lc02PbTugi8B8jIDDWS5HrbNyJAEpD+Bqz/KhXF0FKR/yBdQhIhphDM7pyeiDij2xUiIEeN19ONKtKGjWVenyIb+k3jUJ+gTA4+95JF2hfKX2yGBUT0bDDYHLSQGkPVvQs3c6Gfpuu8zlpDBBOKM/D0/WvTZLB0tJKRq5h00edt5pN5QWPRDOf0jJ9xoC1ypDQ6lYD6whj9f7gb8yoCAo2kExygAAAABJRU5ErkJggg==",P="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAnVJREFUSEu9ld1r01AYh983aYdrwM8rJ14p7MrGMS1dBUG0qZMqMtgQQfHjcjJwjLUpgrlqWoYFb+a8cejNBtMhDlcaxY8xBB1Dk8pQYX+BokUUY7ueV4pG25iuqTBze3Ie8nvP8ztBWKMH14gL/wcsSuo5AthpaHLCnkSMZrZR8fsYeeF0/r78qVHSmi8WpWSUEd5DxH5Di1+r3tzZm9pQKtASISxtCQW7nygHVlaD/zUKv6ReBqJLyPGSnos9rt7ccSS9p1xic4Awbmhyf1NgIkK/pE4CwqEW3hNYzA4vVwPESKqPMZpEhAuGJo/WgzseXldvpvVLwZwDQMHnEYLPswOfqwFWKuKwO5+THzrB61rRcSzdVv7GFgBB7wnFo4qCzAL8SYVhzkNBfTbxzg5fVbffMwUYNR7IQ9WbrVQIuJ5aIGg3paHH1kw5js7rucR4zWH+SuVkSkNwBeSXVIWIYrzQuvXV3YsFJ1OQg0E9J49Za67BQJDwrvO2Lc4MfXBUkEPZyMWvugb7pdQJIjbxszS1elXayIrmAhIut2/ccXBqqq/oCixG0gHGyk8R8Ya9EJ1Hr/tK5sd5QNjE80LgZXbgfXWSuqPYK13ZbkLxBQC8tle4opsoqbcJMQwe3Jefjedd6eaXRgSA0jwQ+pxU8ktqkoBiCNxxQ4vPuCqIohA3/Sw1TQD7neQXI8lTjMEtRBw2NHnEdaXFSDJNDAad6rpbSoXKQI84hAk9J591fQmJh9M9rFy+42RAVySz+SuZb4Dwrd2AhqPYFVbPAFJ7XkvI9pcrd8eKyW56eOGk3YCG4EZ/hWbWXTWvGaCrgvwL0NrzAzq5IyiH/Ub2AAAAAElFTkSuQmCC";a.p;const k="data:image/png;base64,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",D=(a.p,a.p,a.p,a.p,a.p,"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAzBJREFUaEPdmk1IVFEUx39n/CqrhZRBtCjChVGboqhlLYooyHEj5EcZkW3LRTiaMGA60kLbZkSWNYEbddEHtahlkdSmSEiiFhFk0aKy1HFO3NERHR3fe/pm5PZWD+59/3t+955737nnXsGH50KHrh4Z4ZDCQaAUKAGKFNYZeYGfwA9gGBgSeFpczJPOevmz3OZlqQKVDVpEgDKUMoHDqhR60RJhVOExwgBxBqLtYgA9P54B6sJa+GuMelEuJnvYc6spH5gRUuHK2gI6usIy6kXPNUBvr+b0v+KMCGFVNnlpxG1dEb6oEg7u5kZFhUy6+c4VwMkm3RyL04+yx43osusIg7kBgrdb5bOTliNATUj3xaEvU72ezkAzGgEo74nIi8UgFgWoatRqlOuqrHLqiUyUi/AX4ezdNrmTFjRdgTFe4/RkwjCvmhKgJh3EgiMw7TbPVqrnUwHNSATgwELuNA/ATNjJOC+z7fNOo2LmRE6AvakTew5AYql8zfOsrTZOVs8bCgaDu9g/e4mdA1DZoHXANa+6C9UX4V4AEpMvDtWqnPBDFzgXbZeupNYMgPnD/h5j2C/Xyctj560WeWsaOtWsOyYmeOMHgHGlNQWUJP/YMwCVIb2E0uJHI0YjX9jeHZEh814b0tJx5Z1f2gjN0YhcNnoJABOYCXzyK7bJNEAidoItJgCcAmjUWuLc9K2HMj0CxtAAp6Nt0j0FENI+lKBVAEJ/NCLlYjYjX0f45jWed4LN6Bwwvi+Mbixmg1SH9HhcGXAyyGt5pgESXiSUSVVIO1U579VAp/rZABDhqgF4qMoRJ4O8lmcJ4JEBeK+a2IT7+mQJYNgAmAm83o31JjzIzaVVYjhu97YW8CEclnGjGw5r/scxtjm1obnkxGI0uQ07RPgulSEdQ8l3EjflOcKxnog8cFN3qXVqQnp0Urnv6nth3H4A613of5jE1i+jdv/IrA8lrA/mrA+n/48Nje1bymk3sndTbwCsT6tMZyfsTWwZAOtTiwbC6uRuMga3Or2ehLD6gGM2hLVHTCnuZOchXxLC6mPWJITVB92zMwTWXjVITXNYe9ljoXzNSl63+QdQeErpRz4/uwAAAABJRU5ErkJggg=="),U="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABrFJREFUaEPdmm2MXGUVx3/n7s7O0lZ5kbW0GEhsQb9hVUITEy0fNMZEdquEhJldrbFWDYHoEnFmS+1FYGeKaYuSqFQJTdu5JITINooaNUpNTMqL+PJFaCtiBLahWE3obne3nfs3z925w93pvG6rZbyfZuZ5znme/3k/545xDp6v7tAFx47xUcH1wHuB1cDFgrc59gZvAP8CjgDPG/xmYIBf7hy1k2d7vC2WQSani/EYRAwafExiSSe8zJgW/AJjPyH7g6I5gB0/HQPY5GvJiVlGTdwRS7jjU2sInIZk3LcszY5dvk13wq9tAI8+qp6J5/i8Gb7EioaHGCcQkxivGrzq9glWIlZirEAsa0RrxqSEP/R+HrrpJiu3A6QtAJ/ZrMtPh0wgPliHadngd2bsD8VEULQXmx2cyendnjEkMSj4ENBzxn7j2V6PoT332iutQLQEMJLXdSE8Xit1gymM7UvTPLDLt9dbHVRvfZOvS6dmuRVxu2Bpco/Thgfr9xbsqWa8mwLIjmkY8QOJ/ioT47QZP0z1cddu344u5uK1NBt8XXZqjq0SGxG98boZMxhfKI3bvoZm12jBXV4hexdIBV4WDAVF+/25uHgtj0xOHzCYELxrwbkeI41A1NVAxWyeTEre4GCqn/XnSuqNBBBpY5YJieuSmvBgXT1zOgOAc9hyyDNJmzfjsUtWMPzAbTb735B8Lc9bv6P08Un2SdyYADHZ43FtrWMvABCFyj9wMBltnOQvWcm6/9Xl4wtXQBxIagLj2aE1rE2G2AUAMjltAh6sooaXU/1c247ZZMZ0vYWsxRIOX0ddBrOheDoo2q9aaTMypxmeqfGJLwZF25W44/xHl2GnZjlSNR3jNGJtOw6bHdNmhdwDOBNLN72YMYfoM+OeUsG2tALhHBuLrCKKTi68Lk2zOs7YVQ1k8roTcXcVmcf3S+P25VYHDG/RNeEpngb6KrnhVDMaiYvidS/F+/bdbX9qdUZ2TN9TyJcSoXxLUDAnMFcogivMDP6eqB6nUv2sbsd0hnO6OTS+i3g78ERvT6SJhk85ZNQVgMzH+FtK4xa0AlAxpSNxsotqJ7jSFYDzAMa0gZCHEx7/zVLBttZjPDymFTKurO4NWROK+8wQIkcPzzW7kDXZn0pxdLdvL9Wjz+Z1l8Q3qmsenwvGbfc8gLweRwxVFsvL+rmsXnkwktcnyuIJM/4dM5LwzOhz3yVkFvlBw6fZfmdeZnyyVLCf1DJwZceJGVzmn6+djImgYOvNNSOvHeP1uJ43+G2paB9pIIVvS9zWSuVns24ehdK4jdU9P6cDgg9H9zem3znApTac1w2h2J9QzWgwbjvrMfB9eYdm2AhNyunF3t7oM5G6qp+c71tY13zzGg3F9njNMwYtm9dOia8kCFa1Konjvdm87pDYBlRphjfr02GZx3qMtXHqd6VJWRzs9RjZUynMRrbqivIchxHbgqK9adtNBOBKceCvCV+93wH4mcTHK3Z1IihY1Me2eio2ecyZvivASkX7VORPOSmiFS8E28z1x2/+BgRFm/e7r8u1kBdiWE8/y/f69lqrMyO6vN6ImyIzfu4AHJaiJtw9h4OiXd0WI9cTw2SUuDx+HIzbDZXLTkHUH78QFKsAngfeA0wFRYs6smrgME4uS3NFuz1FJqdDwFUVPzjiADgHfkdFAweCgq17SwPI60lEFGTM+Kdl8pp1qb3ywyOlgmXeygCyeQUSN1cEPtf9ALrehP4fnHhRYTSazJ2PKFQnjC4qkZ0PAJ+9U6tOnY7mq/M+bNx/RinhGbfvK9iOVpGoEYBsXlOurhK88kjRoulCJqc4D5SDokWNyWLywHC9UqKTYi4JqokGXKV6IfBiULRVjiY7ph8pZL37XM3EcQXcQSLL1ivmFkhj/oYNy+l2AIx8S0vD49wyuIbtcfO96UGlpl/iawMD7IxH6p1qoGE5HQHooKGJQTTSQCvTq9J3qIHmDc0iWsoNvi6aPclRM9IGf+npYYPC+Ra1nacMv0YscePD/jSXP+Tb8UZ0LVvKihl11NT7vvoOzfA33OgcTtKiE1twOZESEfDIoa/uJ+37NtcIQMum3hEuZqxSmUj8MQIAF7Qj+UQInHbRqtfjmj3j9udGtG2PVSrhruPB1sYxLZ8RQ4LlUvsm5BlHUx4/ffhe+0cL02lvsOWYdP1o0YHo6uFurMq643XjqVSaoXaGXZ34Qu3esx6vxwy7+gVHEkTXvmKqMafufMkXg+jq16wxiK5+0Z2MFF37V4PacNe1f/aoF+PP599t/gM/Nq4HQp6KhAAAAABJRU5ErkJggg==",N="data:image/png;base64,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",B="data:image/png;base64,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",O="data:image/png;base64,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",M=a.p+"static/media/group_bac.87c89f76e171ab69244c.png",L="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAdtJREFUOE+tlLtrFFEUxn/nrkX+BdOkiZWkiAg2isEgaSysNMwsgo9UIorg7mxAiQjZiYUEg4XER7OzRCsLm0WUiDY2iohgkcYm9lZBk/vJjDvD7mZ8kltevvPjfOdllLxwVpPynAAOA8NdyTrw0hyPk3l7PhhmvR/BFe2hwjJwMAsyOlvic6qpGCMSU134awfnWrGt5fEFqNrQIQ9PJD46cT5ZsA+l2dY15o07Zux1cLzVtFepLgNVI4164w3i6foQZ1fnbLMMkv9NzGnX8Ab3MY45cSDNLAMFkVYF7ssQRwYhYUMzqSZpWmq5eCls9wYvDHw7tgnrFrZjYnzQTtjQJYlbWerG5aRpi72wsK4xGe/MMWVBpLvAaDu2yT5RCoGreN5jfMfYb3BjEBZESju4loI+mXiQLNjNHBTUdRHHNWcc9Z5TBt/MseLFM+B6u2m3c21YV03GmRT01cPMSmyPClCkh86x1Jq3t0GkxRSUxFarzmqf91xox3Y6105HOulguRTUN1s9oLJO9oK2WfsXUNhQTfpprbTYPTYLa2UZFcX+Xfu7M/ZLUF/7/zSQ05HGK2JzcMa2DeSOrkgG24mlzYuYLS/cK86I6GxZ94yIEdlfnJG+9fiPw/YDX/od/DDYeygAAAAASUVORK5CYII=",V="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAdJJREFUOE+tlL9rFFEUhb/zYpF/wTRp0kmKiGCjGAySxsJKw8wi+COViGKxOxtQIkJ2YiFBsQjR2Ows0crCZhEloo2NIiKkSGMTe6ugybsyk1kdJ0OUJa983Pu9c+6974qKE87YhHnOAieAoTxkA3gjx7NkTq/KaSpe1CIb8fAIOJYlie628TWNGRDDZkzm8HcOLrdjrffyf4NqTTvu4bkZX5xxJZnX50q1DRv14qHEIQdn2i29TeMyUKZEvMd4sTHIpdVZbVVBenfjs3ZgaJPHiNPOOJoqy0BBZKsG7tsgJ8uQqcjGBoytssIUdnCT1wLfiTWuvLBdGWNVdoLIFgQ/klj1ssqwYaMmPsoxqSCyRWCkE2uiys5eoNxN2sH1FLQmYzmZ191+QGHD6iYupqDvHqZXYj3tBzQV2TkHS5WgILInzvGgPacPRWu1GTvsPVc7sS70Hi2C1iSWk9Yfa0HDruG45cQp7zmfFluOFW+8BG53WrrfA4VNq5vtWKssdti06wY38XxC/EQcEdxJWlooliCIbKfYe7U/gxn3sskVN8qQv9r/r4EMmzadxiQtLRWV7BrIff0iGWw/Pm1P8q41YnS3la8RY9j0H2uk6L+fxfYLNW0d/JZXL3IAAAAASUVORK5CYII=",Q="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAbFJREFUOE+tlDtrFGEUhp93Nq5dLLQTNIVFfoCgzYoawU4FL2F3g52p7BScSbWN2REsrWIjkuyyqKBWCmrEWCjYayEYBUub2Ki488q3zoZhHfC20wzfN+c8c27vESVPc8EzzjgDHAB25iYfgWeGXjfVk1E3FS/mYu/pw3XBQeCDzUNFrAcbZ0xJHAV2YVYjMb+c6u3QfxM0l7iWwV3MhuFiN9WdsmjrsU8KrtpMMsHx7mU9D3YDUIgkEy9l3lTEsZttfSqDDO/OJt7eN/czM10R+0JkA1A99qpgakLsHYWcbrkabG619K0ID7Dv5hXiXaetw8oL+8hwqiydZuKlAFhpa340yjzN24YZ1S95KRSxk2p3aQcT93LQbNn3Ruz3wAM1Yr+2Wete+fWPwbH5G1AeSC2ANhBpp63F4BhqUv3KNWDbIAKzf/AWL34e+VzdyvkbLX0J50biBUw8VtB4UhtbscfRfkUc+e+BNKx3Ux36J4nUL3hHtIV7FtORCxIZ6O0vRYuYjODEcltrm6IdTuyfrhHD0wqcK10jxfEPDcj6zErUiostKCCq0FtZ1ONRufwAXWsI9f0Q+yYAAAAASUVORK5CYII=",F="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAhNJREFUOE+tlD1ok2EUhZ/zfkVqETvZQcFJ7eAgdNCpUn8Qp6JDbfNDcXEpOriYpOAkNEmXbip1UUnTVgelTopSsSAoKDgVVBAEBXWpVato8155P5OSphkc8o33O/e859w/0eRLjdoR85wCDgI7qpAPwBM5bk2N6VFjmuoD6aztqsA1wX7gDsYDL94HjDN2Io4BJw2eR3CmVNDbWv4aUTpnvR7uCh5WKpyfGdfHZmqHLtj2KGLC4KiDE6W8FgIuJgpKvHgGXC7ndbEZQWMsmbNLwIgzDgRlMVEia/NOfJ7Ka7CWkMjaYcFpQVuIGaw642qpqKc1TCpns97omi7okKqFnfOe3cHOwIhtae+go9LGNOKl4HWVaB+wJ/pDemmZ7/cmtRJsOscbOfqVyNikREe5oHRISGVswlxc1M0yfpmCGJAhE+3AT2C+nNfZEE9mrWTGipJZW8TIl4u6WWfruIO9Jt4ZdEZGVDE+ydGNeFEe03wNm8zYMCIXiJY99M8U9LjhZ48zFr1jW9x+zxcvuiVeTeV1o4Ydylqfg7mWEm2wVpX7X4rWrDUWOy7gP989Mu7j6DJj1Ylv3uhrtBaKDfzY0P5q55LmGMZzZd0gOs5hlMoFXQ/xde1vNpADGevcFFEwY2s9kYyl38bo7aK+xg/WD2RLVyQma8XS1uS35IzU1yLsn68wKNFbf9jMWHARs80O21/0NDf5kV/wFwAAAABJRU5ErkJggg==",J="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAkxJREFUOE+VlE9IVGEUxX/nvRmxjUS1E8pCymWI0MqoDIooi2xMzSKiDFrUoqhRW7go5wUR2KJFbsJ0BkujP5sCy8g2UdSmqIWgBS0KWigSar534w0zwzhNoN/ycb/fu/fccz5R5BzpsDoLaAS2AuWZku/AK4PBlKcXhdeU/6E1bpU+9Aq2Ad/MeCaHybDGAiokdgFrMUYd0dbvaTx7PwdqbbfaAB5iTBtcSHkaLtZtc9waBNfNKCPC/tRVvQ7r0qCwk0C8kfHFFfV9Cf0qBjl62dYzy09FKfWNx4FR5YotYWdpUHPcRgUVEVFTDBK7Z27Je26YOGHGpP6wN1LCzILxDjGRTGiHMsKOGBwqNk4GctPgNOECHE4KVjkL1C1E2C0YMqhT8yW7HYqY9LSucJwsBDhoMCUYl0NfENDrRth894omWuL2FXiqlrh9NmMsdU1t+aB8iO+ybwX8mPN5DmyQuDXQzTkkyzRSG4KmEV4yoe4sKA35QA9GA1A/4OltU6fVuD5PgAfz1Zy93yg/rG9ptw6M+D+gWMzcaCU9WgKkELRotONdtnJ+ltBoTUlPI//rJNt9brRCsWOdVl4S8DGAPQook7hTOE6+ljmxC9ffFLcKV3wC5s3wHRieq+ZMVpN8SMblQ3LYWdSQrRdt01yUmajP742lTHV1KSi0xrF2Wx0a0mAy5Wn7siKS0+W8rXGiPDJR5VheRNJ5W2ZoEWUOHOhPaCwX2uyflvqMGLx04VTRZyRfg3ABgc9hidr8hy1MgOMyONCt0OGLzl+zqkj1yRBtmgAAAABJRU5ErkJggg==",Y="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAh1JREFUOE+tlM9LFGEYxz/Pam0Q/gN1iCAiiAi8eAgjsfBYh1J2VguRIqhj4cxCsBC4U3QLozbs4u5YSVAQmFgZealDh4joEv045C2CwIPpzjdmdJdxHevie3p5eJ/P831+vUbKyRfUrZBe4DCwc/XJD+C1ZXhYHbEXzW6WNPS72lODuyY6MWYxphHf4zfGLkQPokvGXAucrfj2ue7fAPV76gzhMfBBrVyYuGof09Tmrmi/LTMKHMjAiUrJ5lbiAJGS0HgLPJnPcu5V0ZYje97TSYXkgO3K8KztJ6Plsi0dKap1xyJl4HhGdETKYlDO1awZNp/laB2S8zRo4g5GBbEEOGZUqyU7H/mswp5LaMK3LosLW2NaWzmYTMcZ1jvgZXDNLsfBCuq1kMqvBdqmbtpibIvS/MN7a6HHcsMqm7E78O1YsiaOqwXB6QnfHsVpuuoTjCdBkd1xNSPx1RxXnzDGgpLdSILyrq7X4NZ9374NeOqoiSmMICjZxTUBPV1CDEWg38BQ4Ntkapc87TPxBni61M6ZyV6rNSk/BYz9F5R3NSpo37uNQ8Wihc3BHFcNUGpqdYe8q/HoXvVtIE2xU09to2LXnZyCuqJ7MGKzqSBXM8CXDdvfUOTptkQ28G2wGbSm/RsNZEORpwETW6q+3UuC1g3kv1YkLZXEVK9fkRi2GUtbj7wp38iaqS6oO6zRZ0Zn8mOTmMu08CDtY/sLWegt+abUvlEAAAAASUVORK5CYII=";a.p;a.p;const K="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAh1JREFUOE+tlM9LFGEYxz/Pam0Q/gN1iCAiiAi8eAgjsfBYh1J2VguRIqhj4cxCsBC4U3QLozbs4u5YSVAQmFgZealDh4joEv045C2CwIPpzjdmdJdxHevie3p5eJ/P831+vUbKyRfUrZBe4DCwc/XJD+C1ZXhYHbEXzW6WNPS72lODuyY6MWYxphHf4zfGLkQPokvGXAucrfj2ue7fAPV76gzhMfBBrVyYuGof09Tmrmi/LTMKHMjAiUrJ5lbiAJGS0HgLPJnPcu5V0ZYje97TSYXkgO3K8KztJ6Plsi0dKap1xyJl4HhGdETKYlDO1awZNp/laB2S8zRo4g5GBbEEOGZUqyU7H/mswp5LaMK3LosLW2NaWzmYTMcZ1jvgZXDNLsfBCuq1kMqvBdqmbtpibIvS/MN7a6HHcsMqm7E78O1YsiaOqwXB6QnfHsVpuuoTjCdBkd1xNSPx1RxXnzDGgpLdSILyrq7X4NZ9374NeOqoiSmMICjZxTUBPV1CDEWg38BQ4Ntkapc87TPxBni61M6ZyV6rNSk/BYz9F5R3NSpo37uNQ8Wihc3BHFcNUGpqdYe8q/HoXvVtIE2xU09to2LXnZyCuqJ7MGKzqSBXM8CXDdvfUOTptkQ28G2wGbSm/RsNZEORpwETW6q+3UuC1g3kv1YkLZXEVK9fkRi2GUtbj7wp38iaqS6oO6zRZ0Zn8mOTmMu08CDtY/sLWegt+abUvlEAAAAASUVORK5CYII=",H="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAYAAADwMZRfAAAAAXNSR0IArs4c6QAAAgdJREFUOE+Vk09IVGEUxX/3pVJIlDFRuwyKCAnbR2YtkkKN/iwqolwU2CLMmfckNzERiM580xAuJKSgP0Q1BRa1CloIEUUQ/VlFSBbhoojIARmYmZvfezjO68GAd/Me7517vnPvOZ/wf13QZkqcR+lGmCAtcVx9AywD7lHgGqPyt7pNQhyuxoEhlBkc7lDkJln5QkLbcDiGchyYBU5i5MVC7yKJpyOUSeBwkUZSJKUYUZnQGMIY0A0cwsgziwlIPD2MksPhFCm5HWkOfVDB5TpwhDItXJHvQlLryPMZmMRITwWf0A3+e0amI6Q9upwYH4GXtkeI636Ep9SziWGZqjS4mgM6gF6M3I0QuXoGGMVhreBqCtiHkW0hoKePfYeCGqdEH1mZq2D6dB31/EDZI3hqT1lBWg7WILG/3lOgLWSvp9tp5JNVcgtYg5HOJZP06y5W8coquYxyFCOblzSOXbwwhcMOS7Jzfq5JoBUjHypEnj5E2VtjsTaYlygQsxY75HmH8o2MdFW5s5E6lGH5GnHmrDbZXQAPMNIfhG1Ad1PiuZ/WtAzVDFtSG8gz4StXWsnIr+rYn6PMVRzGKTJIVn5HyAZ0C2VuAC1+hoy8Xoz9AjqIv70bDcAT4C3CH2A9Sjv4O7JjnMCIffoVvsXBaCtRTqMcALYCq4Gf84G0p95nmkfkpFSt8h9LfL9rBLroqwAAAABJRU5ErkJggg==",W="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAYAAADwMZRfAAAAAXNSR0IArs4c6QAAAllJREFUOE+dlEtoU0EYhc8/6Utb+0piMxe7EhEfC93YutMqdVehuLBxIbVtksYHUpBCRRciFQsquDC56cKNtuJC0IJC3Vt1U6tuBB8o8SZp702KltamufNLUhO1ViTObn74vwNnzhzCipO6WVvL6bU9DG5jYDMzqgUhDsUTisWoO2g8WLlDvw6SuuZV4KsEjLFSoyVcPlmz5dPX5AdNclrtJSAIIlsswVt3IvYxv1uAzOieM4Kph1i11/cmXq9Uy9+TuqdXMZ1lRfvcQeNNdp6DWCF5gAkRZWea1h+fif8N8BOkeZn5/Py8Y2djX3SB+C4cVkq+ItBpp98Yz0Ej2jFWGECJ8rq648+TuryoFForKjJtVZ3LIqYubwvwVL0/PkSpkGePLWjI5Y/tyitZYRlhoIcEfE5fbNgMy6cAmkCq2eVPPMsJhT1bFWjMHYhtJDMsBxn8zR2IXygGsgyS7xxkt2YhtwTRw3q/MfIfkHFBfInMG9r+crJfrutNTBcLMUPysUPwYM7YaHRDTWNfNFksxNLle9uRaSVruKGZlbherLHJUMN2JnHfmTWWGZSKyHvWYuXhTafeLi4bpnWC+BzYPuIMTE9YYXmFgUNrSpd2V3aZxo/XucMQk66AcbmQWCvi2VbKIlHtN8x/hc3UPUfBNLAw79iRC1vBB136wOjPQLQ3BD5PrQZigGZ17aSC6le2aPkt9oU4D2sdyuZrBHoEYKSsTL2omqv6Mls6JxVECwsOEnNalFBHXfcqHzAPylaBSld0gelgtgoA1ABIEPETRWLU7fuzCr4Db9FPY2R3thEAAAAASUVORK5CYII=",G="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAYAAADwMZRfAAAAAXNSR0IArs4c6QAAAfdJREFUOE+tlD9oU1EUxr/v5BEQCXYXHBzErU3OQxAd/FM6qNBJJGBHra5SrQ46ilhw7iQVRAe3uDlInVrlJs84RBDBIOpSsogQm753jn2BV2pAi9S73ftxfvc7f+4lRtbExMSYiFwSkXPuPgbAAdDdO+7+NEmSxmgMtx/EcVx391sAFs3sWZIka4WuqjV3nxWRw+4+02w2PxfaFkRV5wAc7/f7Fzudzo/R24p9tVo9KiKLaZqeb7fbH/LzIURVpwDMVSqVM8vLy+mfANtA4yLysNfrHet2uz9zSElVX6dpOt1ut7/uBCj0Wq12XUQYQrjPOI5PmFm91WrNqupZkmshhDeqepJkJcuyjyIymQdHUbSUZdkUyW9RFCWDwWC12WyOU1XvklwNITRUNQB4a2Z3SD4huU5yycxmSL43s1ck65vaoSzLTovII5LXcshjkvdCCB1VXQGwbmbzInIBwHeS79xdSb4EcMDMSgDyGt4GcJlkYwvi7n0AC+5+0N2v/itkmE6WZRCRB+5eAXAFwKncCYBA8kjuxMz2k9yTOymVSvNpmi4M0ykKS/KTu38hGZN84e43N53tHQwGk+Vy+UYO2djY6ERR9NzdeySnAawMC7tDi3M9H/vtSwDYby3+X8M2vGXXY1943fUDLEAjX8G+4n397Sv4BW6zN0xbG0SdAAAAAElFTkSuQmCC",z="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAYAAADwMZRfAAAAAXNSR0IArs4c6QAAAVRJREFUOE+tlD1LA0EQhmfGRotgfoZdcjcHcmhhZSGClUVQSwlYSizsrERsrIRUVoKFlfkFV0bYveNArpAUIWAlEQJCNCw3kuCG48APuNtuZ9hnZ975QMider1eJaJDItoWkSoACACgiCQichdFUSf/BrMGz/MaInIKAO00Te+jKHq1fmZ2RaRJRCsicqC1HljfHMLMLQBYH4/H+0mSvOd/s3fHcXwiahtjduM4fp7aZxBm3gSAVqVS2QqCwPwEyIBqRHQzHA7X+v3+xxSywMyPxpidOI5f/gJYv+u6J0SESqlL9DxvI03TRhiGzUz+qyJyhohZzd601nvfQoPv+0uTyaSrta4hM58jYlcpNVedmY8A4Dof1Wg0Wuz1ep+ZaB4Q8XgKuUXEC6XUUyaS/0KuELFTGqR4OqUIW0qJy2q2WVEKt70tbeEBtKDcKli28/XbKvgCT+0ITLuAApgAAAAASUVORK5CYII=",X="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAfZJREFUOE99kzFoVEEQhuefu85OjbVNIOFC5O3soYmgQVNooYiWimATQRHEQsQmaCFiYRdBG4lgqYUWWhhREaPczi4eORHT2GrAzuqOHbmQC8/zme125t+PmZ1/QBVncnJyV71enwDQ2EivdLvdTrvd/jksx3DAe3/fzOaIaNHMvvfzzDxmZicALIYQzpff/AUQkVUAr4dFgwcichPAbAhhehDbBIjIChE1zOxUjPFpVWv9mPf+rJldV9Wx/n0dICK3iWg7Mz/OOb8xs+Mxxuf/gzjn5pl5WwjhKkRkJxGtmZmPMapzbh+AZTM7GmN8WQVpNpuNnHO/4hF47/eb2T1V3TMQF0XhmbmVc55NKS1VQUTkE4ArcM7NAZhS1XNlYVEUU8z8Ied8MKX0bhgiIgtmttoHXAYwqqoXh0VFURxg5rc55+mU0nI5LyIPzWx50MJdVd1bVWpRFIeZ+VXOuZlSCqWRfgZwYfMTmXmi1Wp1qiDOuSMAXpjZVIzxo3NOAPRhI+tj9N7fyTn/jjHe2GJ0xwA8Y+aZnPNpIvqlqtfKRvoK4FYI4dEWkJMAnhBRR1UnNo1U6us9ES2p6nwVZGNPDqnq6D9WLkEuEdEZAN/M7AcR7SCi3UQ0A+DBlstUMtIIEY3XarVxM6sxc6fX631JKa0NV/YHzD7lG92O47QAAAAASUVORK5CYII=",Z=(a.p,"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAA6VJREFUSEu9lktsVFUYx3//2ydJMRVkgy4wymMpJCa4MFoTo2hCIWgXnaGBqpD4WJj46G3RXqR0RtkYIy4UBaG3C5QIGB4GixgXYkzQnUBjdIGNmhCatCYztr2fOZO5k9vHdKaJ6V2e853vd//f6xxRxdfxhq2fmmQL8KDBncDK4rERwR/AdzW1nDy6Tz9Vcqf5DFK+PYXRZ7C2kiO3L7iG2BNm9Hk5+zmB2/fY3dEkgwYbi45uIE4hztQYvzY1MOLWx/OsnBL3YDyJ0WpwV9H+sldL+7E+/TYTPAu43beHIjhhxnJgxPPo3Xwfh9vaNDWfyuPHreb0z+yMIva6kEvc9GDbsYy+TZ6bBizAjAsGdRKnG5pJf/K6xqoJZ2zT+bYtzY8yYMZmwYQnHk1CS8BCGKf40SmTeG91Ay8HgaKFwGLbIDBvOM+7ZrxUUFrD/XF4S8BUl33vcuaUrW5gazWwdI9tQ9wc6NOlILD64RwfA62CA/c2sn84zxdFpZfDrB4o5hdcNZrxmctZ4+2sqyaMQWC1w3lcuOuBToytBq1Fp2NhVre58OZucbWY06dd9RYUprrsqit9z+O5gX4dqjaM6W4LoojeWZUoPg0z2uHW0932bBTxkWuZMKt1ck09OcEVwY3WDayqVI2x82cCW5bLM4QxavBwCSpONq2i7cPdmnBrrnpPXeF31zK1dWxQyre9ZrwpcTDM6MVq1HX4tnwKvjbjq8GsutLd9moU8Y4764nOgYwOJ/2kfHvfjBck3nLAi2a0yOOJsF/nKgELMGMIcS7MyI/tU1121mCTRCTx+EC/LpT2um2TRZyV+Ebtvl3DWFMr1h7N6Pp8wF2B3TGeZ0jiTNiv7gSsx0Ra4gQRr0nsTqrs8G3NpBXG3nUHHMNoam5k6QeBxssBy8JcSqCtvoGWI4H+3BFY45FAuaSf5wNrGs0xhhivCuhg/+S4aOLLwYx6Ssp8yxhsiWHlfjYGCsYqhnSnbyv+dTlz/eZxMHZqxnoZG+saecQpmy8V00JaqWjafTuP8dgcU/+XJR4th/r1V6VCSyWLplJbtPv2N8aKGRO/alhhsCTbYr7GT/ZXDBQsCDar8cuNtnbfXsE4kJgg44IflohUNWGMz80abUXJs4Z3yrdbZjQXDnrsC/fTK8kq5Su5X3Z4F1VOu56Gc2QNdklkw4yyCwE52+KdOPf15AwW/QIuQBfziRGHbFEfUSXoYj4TZ9xji/MQnlmR/+dT/z9Acz/6wyWNSwAAAABJRU5ErkJggg=="),q="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAXCAYAAADk3wSdAAAAAXNSR0IArs4c6QAAAehJREFUSEu1lT1oU2EUhp/3Nj+KojgoglAQHMRBXEUER/8GESLchoCCa4RCJDfgcKuQ3lREaCc3wSTEP8RBBUFbUMGh0aIgiJtDuujgFmNvjiRY0dKaa9J848d3nu/l5ZzziiEcdZjprG2xTVxBpKzNTonufZRj8N2BOjBRCfS0U6Ozvm1oNXkJfBgxLt0q6XMU2PKb7LQlvzQ4KZh2HMbLRd2Rm7esxLFqoOP/A1v51i3YQcH9RpJRuZ49NLhZC/RgEGin1vVsweCc0p7NCibKgeYGhXZYbeFHgo55tk3ikES9XNTiWp8vC+wJzeRtdElkHIfXrZD5uyV9GxiaLtj1MORqbUqNXvZEUprK29a4w3NETrDL2jwyh4Rj7KlM6tXKTyJBMznbEcZ4YXFOOz84BWxEhGHIjdWUR4J2p82zhUqgAynfNieafGoZe9fyNTJ0zLN31UD70wXbZ8YM4lp1Uo9X8zcS1Pct9rHJPA4XBYcN7qlNVYZbKel9X552+hNxu5rkKL7aHcgR32Jzvpb6VprJ2e52nPHKpC70aqdf/nen85/N73p23qBeC/R2YGiqYNsTRh6HJ5WinkUB/qXU9Wx2ZL0XylBW31CW9HrEyddFTmDM/I6TP4LvMuJMP8EneCPwl4PvJ+O4NbG2x+W+AAAAAElFTkSuQmCC",$="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAWCAYAAAAvg9c4AAAAAXNSR0IArs4c6QAAAb1JREFUSEu11TtoFFEYxfH/md0Yg2BjY6MpUgr2aieC70ZYZR+gdoKsYKGZaIqNYXXSSbQW0WQIioiVoEUshCCoSFArG1+xSRCChRozRya4EkICs4/c8nLvj++De78jgHLVm72JYUTBCVsllO5nWYZfAbwChsYjPUnv6FTNG3//5DnwPmcG747oUxascaY66u7ZGY4IRoOA82NXdU/FflclDsaRDjWDrTxbHPAuwYOZbrarGPqR4fZEpIftoOndYug3htMqh54UDI1FetYumlqJqHUcTQvMhJb7fZaAj8B3m71xpOHVump0nQkthZ4W1C12YnrjSJWW0dJl9wZmS5IwBaTQGYl3FrcWNvDhfk0/luOZKi0N+CawB7MDmDLslnmLmE9yVCbq+tw0uvTbLrnihHPAfpu5rjzb7tT1taX2T1xwXz7PS5uef8AfoEdi3vAivqYDK+FM7S9VGvpxIp4qYRE4Go9o31rvuRn0y2LCyUCUJWbHI11sCy0UnOvqY3DBXM+bYwqYjiOlE2nVlbnSZr7u+qLF0JO5Tg+UdRl96zKkOxEnc984jLnxP06WBd8VxPFWgk/wWlBrBN9fe3gisFL2A7EAAAAASUVORK5CYII=",ee="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAYAAACpF6WWAAAAAXNSR0IArs4c6QAAAjpJREFUOE+11D1oFEEUwPH/293bJHcHIZiEBGy0ULDxo5IUIhZa2AgiiuCZVDEBCURzp1ZXBe/UELBQEj+SiDYpLCzEQpEUNoI2NiJBooL5uIiH+XJvd5+ceJjs7Z14kC3fzPvNzpuZJ2zCJ5tgUjM6eF5bnAbq0ln5EvyxMvREWu22xfLFZmfRyUlxzlzUmKxSb8aZKsCOBpeOmMeH4WH5XsI3oF0D7klfzD4EK7i6pcxPRzm2bZVnnrDbh6Y/c1ZMZcly2X/3hswUYxvQREo/CxiW8rEMhSkVMo7y2hW2x5WuqMu7bxGeuEJLvcuRO9flZRiaU5h4kJH+8ANUOXuZm+rTaSnt97IsdaaY9OGgMcfWsTFZC0VFeTielb4wtOeSNq0oMwUhXqc8N/A//RQjoYoZFY6PXpXH/40WE7qTXq+PsWdNSHhg1ylPbfWnNW9cGBmRQk1oaQfdSc36wq7ID04tLOAUb0bo6SdSmqu2/bCS9A7o4YLQN5qVoxVR4NEWh1QQWF5GYjE0GF+0yQCnJzLSXBEtZlnC12Cyqagn5Y/CVdqL97IaOucKreWgOo2+HMob+sITsYPjEWVuPCNt4S8qqYOuQb+CuT7RV0xbmHeUVkPw1o8JeJbP0P2sXAlF/07W3y8tnUZyefblbV55QsRUCo1KR3OUN+l0qb5SVueqXepcv+5dtpnyIF5a0ISlmMOB20PytlLbrIr2JHXnqsG1YHKDz8CtrLyvCa2U9K94zU26Grwp6C8y0ecWp5lI0QAAAABJRU5ErkJggg==",te="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAYAAACpF6WWAAAAAXNSR0IArs4c6QAAAwRJREFUOE9jZKABYKSBmQz0MdQ9Zr/K73//DJn+MxK08B/j//+sTEzndy5xvIPsYxSNIAN5hLTOsnOJ8jEQ5Yn/DD+/vf709t014wNIBqMY6hS1N1RCznEVcQbC3Paf4cWj/WH7ljmvhomgGOoSuS9MTN5xJamR9+rh/vA9y51W4TX0y4e7O9k4hS4QMvzX93cGPALK7kQZev/S/Izj25JmEjLU0mteuqJe4gyiDf3HIryBi53F+d3bRwfZOIT/8nBzO/3+8/nTkfURW2CWqasn8QooWnlzcwif3rch8C5e74Ncyskj+UlCyWPZvUvzMjm4xD4LSZpNffPiRNyh1f6boJpZA3Of1rx7fv4KIw/35gMLHH8QNBTdpZzcbKKPH+1/pajoVfLi78fqa6vDWKIq/n/9/+/v/y8f7l5+dGer18V9RU9BBmONfZBLv/34uYWLk9P73es7u2+earkPUuwUsbNbQsG15P2ri+u/37sQ9YHhoYSYrEuRgJhB9s9vr85//P7QBuRinIYie//EtuQZKuYNfIZyoT++8P5ewS+sGfjp3a21Dz5dj7q2OvS3S8S+qaJy9hkvHu4O2b/Scx1OQ0He5+HkcHzz+t7hi/syn3olna//9vX5H85PDD2MUtJLBUR1g18/Pb5g92LrRHWTTHVjl2k3fn17V7dmknAz1hz15NaGpb9/fDgMC/h3d48s03JpLmHnFq+9drpdk1XV857Qj98zmZnY3uxYYFymYpioZeY+7+qvb2/q1kwSRTXUIWa/ijCWvH/nwpwoLl6J35KKnqvePjs5fdcSqxwGBsb/EEv/MzqF75wlLu+S/PrJocA9y5w2YpRE2Eqpf98/nv0nIPCUl03qJBevjM6bFydnP76xbsK/f1+Z5FTDCoVlbJL//Ppw8gfjD4ftk1V/EizekHOVvlOftIK6/0YuXnkjRiZmsF5Qkvrx7fnJJ/f2BJ7elvgCI0kRypYgeeO0M6xsL647c/PKGTAyMv3/9vX5OVb90P0HGhj/YE38xBhKjBoAAq+EJbFtfwgAAAAASUVORK5CYII=",ae="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAUCAYAAABiS3YzAAAAAXNSR0IArs4c6QAAAQRJREFUOE9jZKABYKSBmQyjhuINVeaoiv9/Xj0+XAhSJSZr27+sg5GFgYHhLz5dhMIUbOjLh/uLQYaIyzv2Di5Dbf2WRhzeFL2CgYEBp0ut/ZeEH90YsxJbMGD1fmDu8z3fPt57tXORdSw277vFHl3ILaAksX6ypAtBQ72SL3WysPFYsXMI67Cy8wm8eXpkhYi0TQRymL5+cmS5qIxN5O+fnz78/PH2yp9fX45tm6tXjmw4ikvRDYUZgGzom8dHlonI2kQRbSjMNpD3v36492LXYut4rN6PObqAW1BJkijvwwxFigTcEeWzJPzoFhIiCil8Bnk6RXcptbMpWcUtobw/eAwFALF5uBXBaV7/AAAAAElFTkSuQmCC";a.p;const le=a.p+"static/media/lessen.70f6b2887ed85041c9aa3d57a7f674e4.svg";const ne=a.p+"static/media/magnify.9a18b0d07cc5fa2d079d2dbeecbd4e1a.svg";const ie=a.p+"static/media/download.e8ea9b2a917df953cd43e53d5cfdfba8.svg",oe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAALCAYAAAE4goz0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADqADAAQAAAABAAAACwAAAAB1ELaKAAABfElEQVQoFZVRMUhCYRD+/sfDJ+SQm9TQEhXYUkstbbVYkUMEPQ1dJGhq9GGGEJXR4hAtTYEVtiSEm9DW0lBLSzg0tQTlYNCTfNfdk79Bp354/939d993d98D9NlwKKZ9pAsUMRIOHbVd3MHOEulU2qEJJYHtUENZmDYkMAh5fONZMBV5UCbmlVB6QIrjV0V4KhfVVXKHxr0OqmaHULssKp8rmaV1QdEP6gGFBUMn5JEZDsQiiCgsNH1fLu61yt+u+DzNg1jFThmEpVAQQ18uKjz4e3wKmeojVrBVoJBUyWHkLRGpVJ6i7L/5e3RT/bdPy6MuDlgY7qPV9f5AWcpJ/DeQTvqPORpRHk5YzkmO67x95OJQLeuapEPHHmGW158zWfUPGNhj5Vqmgfvz/W6hiMVb2QLijtvsJ0YtzDRcbHLXG5Pfw4MBnJ0WVEsz91ruWuLi2IuLOAOuWeSSKPDZbCPDrBmRpRcksXRkEx6zUGXQGk9X8/+hLrb/seMv9dKovEBQBLsAAAAASUVORK5CYII=",re="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAOCAYAAADwikbvAAAAAXNSR0IArs4c6QAAAWhJREFUOE/Fk79KA0EQxr+5JXmAvIAggn8q0UZEUBARG3sNiMR4u2dKC7FRsE+bm7uEYBEVUlooYhObYBWsRMEIvkAewLAZWU3AIpfGwi33N9/szLczVK/X051O5wLAJoA0fs4LEWW11q1SqTTveV4NwFSffQK4zmQyWWLmPQAHvV5vO5VKOQBr7S6AFWPMMjM/AGgopc4d63a7ac/zLgGUKIqiUxEZM8bk+pkRhuEKEVWNMePM/C4iuSAIGgPOzFUi+vibmJmPiGhWa701yBzH8YaIFLXWM2EYPiulDn3fvx3wKIquROSJyuXytLX2EUCTiL57BjAHoKa1PmbmEyLaB9ByQEScqYtKqQVyF5VKZcJau+YAEQmAttb6xsU6HsfxuohMigi5B5RS9/l8/m2omIhefd+/+9XGcHFS2SJSNsacjSw7yTBrbTEIgtGG/es/J43nqjFmaeR4JixG21q7UygUmqMW4wvhwiMWGZvC+wAAAABJRU5ErkJggg==",de="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAOCAYAAADwikbvAAAAAXNSR0IArs4c6QAAAVtJREFUOE/Fkz9rFFEUxX/nRQb8FIIIGivRRkLAgIjY2AXZtyFIxCat4MymiGKxmSnS2gRCitnZ3kIRm9iEVMFKFIzgx3Ax74TJv2p3LSx83eXcc9+5596rxdfOLv1mIPMYyDh930KgW/d1EAvfAWrgxhk2snn35zJdxZ6fkVgNJipj1CakI57aLDSl7sXCnyV2www7LeYRWRIN5q1i4VeYK02llbPKLBVeSGa7qXQ15v4ZxEpdavccj7m3Eb/+mZwjbjUb6pxX7uZ+hNgclLrZyf01wItBpQ8XP/c8xHzRk5eenQnsC/bQac82tyXqwYZ6ndzrEs8lDk7IJjPMHSXuqo2XCl9L5sGJ2wEHcVj3eQ9yi8c1P8RcJ9Hmj4L4VJf6MZaM+N709fFC5iTyJNk2W8NKb6bKjoXHGpZgc1j9xbD/OOfJ63m/KTU/dT3HHoY4DInlutLetMM4BuKpAPBCl7puAAAAAElFTkSuQmCC",se="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAMCAYAAABr5z2BAAAAAXNSR0IArs4c6QAAASFJREFUOE+VkS9LQ3EUhp/z28Rmswg2xbZmMRhsc2lheN1tA8OCYBB358otsplMlhsnziAizC8wP4GISW2COBDEJv67r2waRORuO/W85znnPa95FS04o43RbtWtVAxUwMgijIQSxCnHvvlVrUlEgpujhs0VqyoD+UEAIHZQtzCUu34hl4bLg127S9r6Xy/xzGFgfYBX03R3jIdOaB/DDP3WmBcob+LEHJ1W3Zb8QCuCmkh+okEci23zKsr9pHDeA6xuKkMaz9ngFNwnzW8LW5qaeOYxiux9ZAujDvzV92O8fWU55bho7th9T5Bd1/jMbDK6+4SOQ3szv6I1GRFw1WpYxq9qQ2JvmMtklMwPNC84Mzg9bFi5UNVkWiwOAjiIBZ0vqPtqTNVoWB0AAAAASUVORK5CYII=",ce="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAMCAYAAABr5z2BAAAAAXNSR0IArs4c6QAAARpJREFUOE+VkrFKxEAQhv/JEnwCuSBYCymus7EQYqdXXSFaH8huOmtttrMUFjbJCmnsRYgvcD6BiJ2lIBZCHkCXjCRYHCJJbtr5/2+Yf4astXtCiIqIKinlwjl3zMyHAAg9RUQNAEt5np8R0Q2AV6XUjnMuZeY5Mw8CmPmKtNZBFEUzInqWUr71Tf2v1ztlDKwDWGu34zj+SJLEjzGtatoM5kR0B2CplDooiuIEwOXIEC/IOTdj5grAYwvIsmwaBMHpUIgAGma+7VYoy3LLe/8ppfxee4V1DX/13Rknk8kRgKc0Td9bgTFmYwhc1zVrrb9WH+lFKTV1zp0z8/UQ4Le/IGvtrhDiAcC9Uio1xmyGYbgvhBhiNN775Q/G/W8KsFuFCQAAAABJRU5ErkJggg==",Ae="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAPCAYAAADUFP50AAAAAXNSR0IArs4c6QAAAbpJREFUOE+Nkj2LE1EUhs974wSstVoGBD8aQX+AtaWKIoIgLJudOxMRbSKLIAveYlFkMc1qIDNz183CgiCiqL/BzmYFG7UQBisbsRASc165YSOjQuKpzj3vebjnC1Kzfr9/sNFo3CB5TkSO7EmfALwcj8cb7Xb76zQdUyfP8wsAHovIZwBbxph3QVPVEySXROQQyVaWZc9DfAKWZXme5FMAq1VVrTvntF6Jc87EcbxCcg3AJWvtCwwGgwPD4fAjgAfW2rU68LdfluUqyZvNZvMovPd3SF6pquq4c+7nLNA5ty+O4/cAdlAUxVsReZWmqZsFTbWiKELeWZRl+U1Vl7Mse/Y/YJ7nF40xm+HH7yQXp9OaB+9NfzuAu8aYJ0mS3J0HBd17f1tVLwfwnoicsdaeBMBZMMnQ2q6IvA5TXVDVDwBuWWsfzlnHdZL3jTHHJgfgvV9S1VxE2mmahuv5x4qiaIlI3xiTJUmyVT+5FMAGyTcANkUklBQstLBM8pSIGACptXbwGwwZvV7vcBRFK5M9AQshRvJL2PNoNFqPoui0iDwSkdYfYL2+bre7P7w7nc6Petx7f1VVr/0CkHTAP8ZfWSYAAAAASUVORK5CYII=";const ue=a.p+"static/media/tongxin.b5e8c01c8a12452f7c4e4ed504b8cdc4.svg";const pe=a.p+"static/media/tongxinClose.246e09c862fcadcceaeaa9b8b0d334a7.svg";const ve=a.p+"static/media/open.d98daf3262134bcd963f50d5a56a0f12.svg";const me=a.p+"static/media/close.d98daf3262134bcd963f50d5a56a0f12.svg",he="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAVCAMAAAB1/u6nAAAAulBMVEUAAABddP9ggP8rOYAsOoMsPYUwQIVaeP9Ydv9beP8xQohaev9ZeP8xQ4xbd/9aeP8yRJA0RZFZeP80RZNbef80RpVaeP82SJhbeP9ad/9ZeP82SJk2SZs3SZ04TKE8UKo+Uq5aeP9beP9aeP9ad/9BV7lDWLtDWb1aeP9EWsFEW8JFXMVGXcVIYc5JYc5KYtFKY9JaeP9LZNRMZddNZ9tPaN5QauFVcfJWcvRWc/RXdPdZd/1aeP/////ZWDqVAAAAOXRSTlMACxASIy4wMzQ1PkFCVFpbZWt1dnaAgIqKi4yOl5mowMfHyMzN297g4OXl6Onw8fL09PX2+Pn6/v47K0+GAAAAAWJLR0Q90G1RWQAAAKFJREFUGNN10UcSgkAQQNFvRkEU0RHECJgjBkzc/1wuKKtgHP/yLbqruoGWHyWZIt8EcAO9QKaCHjpQXxWRKq40ui4/DToIAUDTtm3baqQsxJfX6b5jReIjUOo95wqG3UXJp53EN8/zRstHW+JXHMf35FBWDbHeE+Xs60LJ+42St+c8D8cA9KfVHOcS4t8FNdW9a+CERv47RuAAmNIvZyZ8AGJcGN9NXm2OAAAAAElFTkSuQmCC",ge=a.p+"static/media/right_bg.56f44357f73b1f6318b5.png",be=a.p+"static/media/new_logo.49e617a0414c15d74b28.png",ye="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAQCAYAAAABOs/SAAAAAXNSR0IArs4c6QAAAypJREFUSEu1VUtoXFUY/v5zZyAhBNsK7eRSKPhatqIi6EYX6cLSFBcSKtVNIXPunRTj1FV9Xm1LF6aMQbz3niuIxYV1JfYBxcdONyq+KGSlSA2ZtlKNYMOkc+/55IabMDPMGJH07M453/d/53z8D8F/WL7v32WtnQDwEMndAHaIyJacSnIJwDUR+QnAtyQvJEny80ZhZRAgCIJSs9k8SLIO4IGNAnXei8j3ItKoVCofBkGQ9uP2FfZ9/7EsyxIA9xWkDMAlAOcA/Dg8PPyrtTb/KZRSW1ZWVnZZa3eLyH6S+wCUC9684zgHoyjK3ehaXcIkxff9E9baYwBERH4hGZdKpQ/CMLy6xvQ8bzuAh4v913EcX++5O0TSJ3kvgJaIvGCMCbtcWdvkolrrMwCezc9E5N3R0dH67OzszTVMbv/i4uKrInKMZKnApSRPua77Rqet9Xp9eHl5eZZkreCfHR8ff2ZycjJ3D+s/9jzvpLX2RQBLSqnDcRx/3GuP1vo4yZcBLIjIe0VyHQawU0ROGGNe6eX4vn/AWvs+ya0i8o4x5si6sNb6EZJfAVgBsDdJki97A8zMzOxotVoLJK+OjIzsaTQaf+SY6enpO9vt9g8iUhkaGto5Nzd3rZdbq9UeTdP0CwBDSqmJOI4vrP54amrqhohsU0odjeO40S8L85dnWfaJUur1OI6DTozneYG19jUROWCMOT+A/3yWZXnsv5MkGV0VrlarNwBscxznuSiK3u5H1FpPkDy3CcJLSZJsXRXWWu8l+SmA5VKp9HgYht/cLqtF5AljzKX15NJav0VyBsDvInLIGPPZZicXgChJktUsXxcOgkA1m82PSD6Vd0IROT02NvZSEAS3NqmcLrqu++RayXU1kEL8TZJHixq9TDJ0HOdsFEV//s8GktOM67pHOut8UMscz7IsBnB38YC8zM6LyOcicoXkb+Vy+Upefu12+w6l1K40Tff0aZkLjuNMR1GUt9quNXBIVKvVsog8XQyJ+/tl+r+czSul5iqVypkgCFr9cAOFO8G1Wu2eNE33i8iDvWMRwF8k87F4GcB3juNcDMNwfqOH/gPsvcsgjXLBmwAAAABJRU5ErkJggg==",xe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAMCAYAAAB1Lg0yAAAAAXNSR0IArs4c6QAAAkJJREFUOE+tlDtoVEEYhc+5d2F9rq9CNoiolViI+Igg2mihWAQUkUREIeBcWFiC2GijFwVtFpuE7J0pFKOINj5IEWwsBFGToGApqKB4LaOiksXdPTJhI5t1gwQz5Zx//u/8M+deAkAURcOSlgPY75z76ffme8VxvCBN04cAvjrnjrIBfiGpE8CAc64431DfzxhzFcBpAC+dc9umwZsAjEtaCOC8c+7SfMKNMWcBXCFZCYKgs1wuv54CN6Y+IekagBDAAwAF59zn/zHQ19e3enJyckDSEZJ1kqeSJPEM/AE34PsA3JW0CoB/6zLJO9ba8bkYMMZsJdktqQBgMckJkj1Jkjya7jMD3ID3SLrdAvpA8p6ksSAI0jAMU0mpr6nVavkgCDrq9XqHpB0kD0la13ye5Elr7dCMvdZJjDFPAewCcMMnEMBhAGvmMjHJT5LuA1gEoJfkqLV256zgKIoOSBrxwDAM15fL5QlJjKLIJ74EYDeALwB+kcx5zdeSzEhaQfIZgDNJkjwnqWKxmKtUKu8BrCTZZa0dbnvVURQ9kbSH5AVr7cVmh8YY/w12key11l5v1qIoOi7pJoAR59zBFu2cpMvelLXW3+TUag3XLUkbs9ns3v7+/m8tDd5K2pDJZDoHBwfHmrVCobClWq2+IvnRWru2RVtSrVYfk3xnre2eNVzt3tIY49/qu9dyudzSUqn0o7mu8VfyepjNZpe1mm7X869UtyuK4zhI03SU5Btr7bFZzA2R3JzP57fHcVz9Vxh/A/Iu+g0Ag5/uAAAAAElFTkSuQmCC",fe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABSNJREFUWEfNmG2IVGUUx39n1tnZ3GITwzJQCHShsMSIIm2hN1MrSSX9MOPoGmL1wcqkmjsbeE3dGYosKcokdNW9g1ikQr61mUIp6YfECCT1Q5RomZmm1rq698Tz7MwyOzuzey8R7IWB4d7znPN/zvmfl+cRQj7xlA6RCE+oz0SBkSrcCvZnnlOinFL4WSK0qc/2XFb+DGNCggonmnQ6PgsVGlAGBVonXBP4mgjveStkS5A1/QJKODoB5U2F8cUKRWhXOAacFuW0+abCcGC4QL0qNT3k4QDCq15G9vcFrCKgzZu1aut3vA28WFAgcBnhUyJsq42ye40rf5dTnnxLa/3zPIbPUyhPK9QWya2adjeLZ82SznJrywJqdPXGjitsRploFxnXCx9Hq1na4sqvQVxfkGl09ZarHSxRZX53qIW26hizWlw5X6qrFyAD5mo7BxRut1iEExFh+sZm+SEMkFLZZFrH+MoWVUZ17ZGj0RrGl4LqAciG6TA7C54RYY8qMytlivVkO/dIhLHqcz3CSalmt7dUTpYDbzNU+ESVR/Keb5s2jinF4esBKJ7SdwucMWBGx5jsunKtgvKZwEfAkJLvKsKm2hgvrHHlbOla19VBx6+wqxsUrMpl5aUinnb9NdmkyjeFMKlybyXPxNO6CJ+V/WTLkdgQGta+JhdL5fKeOtQdPuGBQvZ1eyiR0v02tYVrVcK4SpxJLtGRfgdHVRmMcA5hsUT50m7kqg3dCoWx+Y1lvYw4ZTMxrWM6lcOG6AIHvKxMyHMLTNHTTj6zLyKs9prl+Uq7jzv6Osoy892QvTUjW4tlbcqf41tVxojwh5eRmyrpSqT1Q/V5ztqtYoYpntZDCUe/UuUhU2eiNYzqK7Xjjn6RJ/1PuazcVs7YbEfTvrLCGooxohLJbUlo54SpUyLs9TLysJh4IpyxrhPWexlp7IsbCUdPqdpqvMXLyoxysom0Llefpv4A5Z3RospcQxWUYRJ3NImyodhtfQGKp9QUszpgQy4rc0tlF63U686c4aDCnSZko2MMc13xK4atiC4IcyTh6AZVkqY31cYYWqkdFBQWyC/CEi8jbxQbWuDq4EvtbAMete+F5lxGrKcqPXnOnTW9T4SNkkjpXoUHEb7PZcRmR1/P/LTe3K7cNyrGjuIatSCrdZcvsF0Vmy0Ih+pjNLiudPSnM+7oEZS7BPaZkP2IUo+wO5eRyf0tLvfdhOm339mDcr/FIhysrWPSmpRcCKIv7ugulEkIxwygi6gt++tyGXkmiIJSmYSjq1V5tssx7KurYeoHrlwKqivu6FqUeQiXTMj+UrhBoMXLyrygSgpySVeH+Vc4rUoEOFw1lIaNr8jlMHoSKV2n0Chw8T+HLJ5SQ+C2oFlaDmiPkIUlda9wpXWK+uywgCI87jXLzjDeMbI9SZ3S9cCcoGlfamx2Sif7YEFEYEprVnaFAVSc9qa2hS6MpcZspe+arc1TH/aUUdJHk6FbR6W0N+/feVn+CeMdI5twtGfryL8M3FxLDc5r0hEdPh5QHa0i2bJcjgcFVba5WkAhxo8ypO5upAjv5zKyMCigiuOHBRVwQCs1lmzSqZ2dtn+JREh6zdIaBJAZ+isOaPmwBR5he3nJ0TtkENHWZXIkCJhAI6ytByGG/CCGy8kEHvLN4rDHoLCgQh+DjIEBdVAs7HhAHaULoAbUZUMxN4Jcx5iLqvwac3H1/1zH9ErtgXJhVbaZCk8KTEQZUe5KD+EXNTOS8nnYZvsv8B3mTpoZo5QAAAAASUVORK5CYII=",Ce="data:image/png;base64,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",we="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABGNJREFUWEfNmF9sU3UUxz+n7f4kYsQHmCDzxUCib1uiJhsaNe7BsGxFzWLabWHwbDQaSLsKlMFsozFKeDNmg7ruYVEZiJFkURQZxhf2ojGwaKIsA+HBP+xh69p7zO+ut2m7Vm4ZrPs9/dp7zrnf+z3/fr8jVLgCIX1QPOxQizaBR1TYDGwWRYFZhFngD1EmfPV8eTwqf1fyCnErHIzoTixeU3gaxedKT0gLfKcejo0Oyik3OrcFFAxrK8q7Ci1lDKrATfNMYQNQzuYFn7AvEZMf/g9YWUBjY+odv8T7wOsFBsR++RkPjGstl7YK16NRSRuZaFR908pDkqLZAj/Qjtogc8sDH3Q0s7erSzKlgJUEtCuq61MLjKG0OUoCMyIc6GgiUc5Y8QvMR52eoleVAYUtOVvC2fse4NWPQvJPsc4yQAbM4jwXFR7LCqsIh2vqiB2PyrybOCiW2RXV+sUFwqrsz3Ppz+vW01oMqgCQ7aYpvsoxI8x5IDgSk9N3AqRYpzusHRYkUdaZZyKc7WyiPZ/xAkCBkH6YixlhzufjmcRhmbobYBwbvfu1KZ3mvAPKxNRIXN7MC42lrckmVS44bvII/rvFTEmmlHHHfT6hxcm+HEPBkE46qS3CQDImB90y092vm4zsyDtyza1OMKyHVDmQdd1kMibb7b3NTkR3aobPs3/M1NSz1W0A90W0MZXhV6Nb6+XR4UG56gaUHejzTOeyz4vfFM8lQGH9RpXnzN4j7B6JybAbo0amO6IvWxk+tXW9vDIyKJ+51g1rn6UMZVn6OhmTF8T0JoQbdjsQbvqb2OS2zqwUUDarr9nFU0ijbJRAWHtQEll3DSXjssftF64UkNEPhHUIpc9mGLolGNaEKj1Zd3VWmlkrcZn9QaY2KU7jTUgwpOcUnrUZqqMxeUhmVpOh4EHdogvYiSDwrXHZZZRtpllvq6fWaZTFoEw2LcKTy8AqT1kWe22GPbyH8OMyGeGnkSNyudSHmoZ8ZZ6UjUe4YgDdMlVT4EYyLg2llEydUYvfFWoqYS8nK6RqvDx+4ojY5aF4BUP6p8JGhDnjsn8V7q8moEBYr6M0CNxaEy6bXmBBFY/tsmoHdW9EH05nsBNpKahDegLorWLa+y3lZDauEgWFEWF4NCa7Kwncldah/MIoHnrWXuswbFSruQbCugfl42xzPZeMyfNr5vghXl5KDsrJqh3QAv06gGUf+k12XUzGpTW7Xwrh1TzCBiLaScbOLJsQEbYnYzJZAMj8qMYhHzg6Gpc3nMxe7WuQ34JPnBsHwoS/iRfLXoMMynt1UUyl6Mfi7Zyb4JeaelqKpyP3/Co9PsUuUaL5V2nDTG0dXaVGNXc0bBDlCxFOlRo2/LZIg2XxhAUdouywjxWF66i/mbcqGjbk67sdx6hgxjIb7K5dYpnURtjnZFO59nTb+ZCjuIKB1fd4OGaKnpse6RqQYyx7bWoXaENpdEZ62eezovZY76rCBMqZ0bj85QaII/MfQdKQSmyJY4cAAAAASUVORK5CYII=",Ee="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAASCAYAAAC5DOVpAAAAAXNSR0IArs4c6QAAAppJREFUOE+dlE1sVGUUhp/3TsGCkYVloRC2sGKD0R2lLtjJwoXSudMSfmoMGkNCDXNnCDiQMHNjKCwIIST8lNI7U2XJxoWJEFcawLWyIgS6oRtMoLTT++qddpp2KCHw7b6/5/vOed9zxAojP+z1rGaP4DPMFqAHmML8TY6bXWZsrKapzqvqXMiXfFjmOLAGcVtw1zAl6DF8hNlheC44UY91dun9RVhfxV0bXnAds1viksQP41VNdj42UPaHNidshgQTM9sYvPGl5rJzi7Aw8jnMQeXYm1Q1vlL4S9cKZQ94jlEFnE9qOrQI64/cF8Bvgu+TWCOvA7X3C5GHDact+ho1ZSmBMPLvgveSbrZRUVooeqsCDtmsbb0o7ozXdCaMfJFZqvURPWgBKw4K09xL4WkjVq++OOqNXU0eBmIoiXUlO1OIfBkxm87xSzZ3jsl1U9x71sP+1HzNLJ+3gYWy96dzXGrOsElh0f2IRpqyceJHPW79tOTE8Gujpqutedn9mJIh0LxVrtVjfZXt9R/xhiDg0f+2ySsfeVhQrcd0g9wJGyx6VxpwLCeGminfZp5b3c3AaEXT87mzwohpQ1kLvqq9ClYo+W4A31yv6Y985Hwg/rX4pF5V5sU27LlF+bVhFiLfX9XN1vZP8iXvC8zOJFb4UpiLAuQYSqrzAuQjj2ImV4mfO23ShN2CD5JYe1titQXoYtOK1giL7kUcA95dof5eOEexfkp/ZtYIp/kLeFqPtb0Fe2vTlj3slNMpfDoR69Zbl1NY8iApVxEX6rG+W1abb1LoacpJ4ADip8fvMHirouYyWDs3y1qQua1goQWZ9w0fA71AZoWTjZrOLM3pS/2speZCc8TsEmwG1gNPDP8gbjLDWGNETzrF+Q/4JE3CXka1JAAAAABJRU5ErkJggg==",Se="data:image/png;base64,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"},19102:(e,t,a)=>{a.d(t,{CV:()=>v,D:()=>i,Di:()=>x,Fp:()=>r,Gm:()=>h,LN:()=>l,OO:()=>o,PL:()=>p,TQ:()=>b,Tj:()=>d,Yx:()=>f,Ze:()=>u,b9:()=>A,dV:()=>s,ff:()=>C,ni:()=>g,uN:()=>y,wL:()=>m,yS:()=>c,zE:()=>n});const l=()=>[{name:"\u7269\u7406\u786c\u4ef6",id:crypto.randomUUID(),deleteFlag:0,type:"HwDevice",children:[]},{name:"\u4f3a\u670d\u63a7\u5236",id:crypto.randomUUID(),deleteFlag:0,type:"ServoAxisSensor",children:[]},{name:"\u6e29\u5ea6\u63a7\u5236",type:"TempAxisSensor",id:crypto.randomUUID(),deleteFlag:0,children:[]},{name:"\u8815\u53d8\u63a7\u5236",type:"CreepAxisSensor",id:crypto.randomUUID(),deleteFlag:0,children:[]},{type:"Input",name:"IN",id:crypto.randomUUID(),deleteFlag:0,parentType:"CCSS",children:null},{type:"Output",name:"OUT",parentType:"CCSS",id:crypto.randomUUID(),deleteFlag:0,children:[]},{type:"DA",name:"DA",parentType:"CCSS",id:crypto.randomUUID(),deleteFlag:0,children:[]},{type:"AD",name:"AD",parentType:"CCSS",id:crypto.randomUUID(),deleteFlag:0,children:[]},{type:"HandBox",name:"\u624b\u63a7\u76d2",parentType:"CCSS",id:crypto.randomUUID(),deleteFlag:0,children:[]}],n=e=>{let{data:t}=e;return[`\u4f3a\u670d\u63a7\u5236\uff1a${null===t||void 0===t?void 0:t.servoAxisCount}`,`\u7269\u7406\u786c\u4ef6${null===t||void 0===t?void 0:t.hwDeviceCount}`,`Out\uff1a${null===t||void 0===t?void 0:t.outputCount}`,`\u6e29\u5ea6\u63a7\u5236\uff1a${null===t||void 0===t?void 0:t.tempAxisCount}`,`\u624b\u63a7\u76d2\uff1a${null===t||void 0===t?void 0:t.handboxCount}`,`\u8815\u53d8\u63a7\u5236\uff1a${null===t||void 0===t?void 0:t.creepAxisCount}`,`DA\uff1a${null===t||void 0===t?void 0:t.daCount}`,`IN\uff1a${null===t||void 0===t?void 0:t.inputCount}`,`AD\uff1a${null===t||void 0===t?void 0:t.adCount}`]},i={SELECT:"select",CHECKBOX:"Checkbox"},o=[{label:"0",value:0},{label:"1",value:1},{label:"2",value:2},{label:"3",value:3},{label:"4",value:4},{label:"5",value:5},{label:"6",value:6},{label:"7",value:7},{label:"8",value:8},{label:"9",value:9}],r=()=>[{label:"\u62c9\u4f38\u65b9\u5411",value:"tensileUpOrDown",type:"select"},{label:"\u538b\u7f29\u65b9\u5411",value:"pressUpOrDown",type:"select"},{label:"\u4f5c\u52a8\u5668\u5411\u4e0a\u5bf9\u5e94",value:"upDirectValue",type:"select"}],d=e=>{let{t:t}=e;return[{label:"IN",value:"Input"},{label:"OUT",value:"Output"},{label:t("\u624b\u63a7\u76d2"),value:"HandBox"},{label:"DA",value:"DA"},{label:"AD",value:"AD"}]},s=(e,t)=>{const a=Array.isArray(e)?e:[e];let l=null;for(;a.length;){const e=a.pop();if(e&&e.id===t){l=e;break}e&&e.children&&e.children.length&&a.push(...e.children)}return l},c=(e,t,a)=>{try{return e.map(((e,l)=>e.children?{...e,index:l,parentIndex:t,parentName:a,children:c(e.children,l,e.name)}:{...e,index:l,parentIndex:t}))}catch(l){return e}},A=(e,t,a)=>{if(e&&e.length){for(let l=0;l<e.length;l++){if(e[l].id===t){e[l]=a;break}A(e[l].children,t,a)}return e}},u=(e,t)=>{const a=[];let l=[],n=0;const i=(e,o)=>{for(const r of e){if(n=o,a[o]=r.id,r.id===t){l=a.slice(0,o+1);break}r.children&&(n++,i(r.children,n))}return l};return i(e,n)},p=["ServoAxisSensor","TempAxisSensor","CreepAxisSensor"],v=["AD","Input","Output","HandBox","DA","virtualChannel","HwDevice"],m={PARENT:"treeParent",CHILDREN:"treeChlidren",CONTEXT:"context"},h={REMOVE_CHILDREN:"removeChildren",ADD_HW:"addHw",REMOVE_HW:"removeHw",ADD_CHILDREN:"addChildren",COPY:"copy",AFFIX:"affix"},g={AD:"AD",INPUT:"Input",OUTPUT:"Output",HANDBOX:"HandBox",DA:"DA",AXIS:"AXIS",CCSS:"CCSS",SERVO:"ServoAxisSensor",HWDEVICE:"HwDevice",TEMP:"TempAxisSensor",CREEP:"CreepAxisSensor",SIGNAL_LIST:"virtualChannel"},b={type:g.AXIS,deleteFlag:0,daqRate:0,tensileUpOrDown:1,pressUpOrDown:1,upDirectValue:1,ctrlBlockCyclesFlag:1,ctrlCyclesFlag:1,blockLineFlag:1,cmdFrequencyFlag:1,upperLimitsFlag:1,lowerLimitsFlag:1,adSensorCount:0,sensorName:[],children:null},y={parentCcssId:null,deleteFlag:0,unitType:null,daqRate:0,unit:null,max:null,min:null,sensorName:[],initValue:null},x={deviceId:null,deleteFlag:0,hwId:null,hwKey:null,openState:0,parentId:null,pressUpOrDown:1,subId:null,tensileUpOrDown:1,upDirectValue:1,version:"1.0"},f={axis:"axis",chanle:"chanle"},C={[g.SERVO]:"\u4f3a\u670d\u8f74",[g.TEMP]:"\u6e29\u5ea6\u8f74",[g.CREEP]:"\u8815\u53d8\u8f74",[g.HWDEVICE]:"\u5b50\u786c\u4ef6",[g.INPUT]:"In",[g.OUTPUT]:"Out",[g.DA]:"DA",[g.AD]:"AD"}},20790:(e,t,a)=>{a.d(t,{A:()=>i});var l=a(65043),n=a(97950);const i=e=>{let{children:t}=e;const a=(0,l.useRef)(document.createElement("div"));return(0,l.useEffect)((()=>{const e=document.getElementById("modal-root");return e&&(null===e||void 0===e||e.appendChild(a.current)),()=>{null===e||void 0===e||e.removeChild(a.current)}}),[]),n.createPortal(t,a.current)}},21043:(e,t,a)=>{a.d(t,{B2:()=>o,Sp:()=>r,Sq:()=>s,U1:()=>d});var l=a(41445),n=a(97292),i=a(754);const o={GENERAL:"\u5e38\u89c4",EXTEND:"\u6269\u5c55"},r={samples:[],sample_type:"",num_type:"one",num:0,data:[]},d=(e,t)=>{const a=e.filter((e=>!("parent_id"in e))),n=a.filter((e=>e.type===l.dk.CONTROL)),i=a.filter((e=>e.type===l.dk.DIALOG)),o=i.filter((e=>e.code)),r=i.filter((e=>!e.code)),d=n.filter((e=>e.control_type===l.ze.CUSTOM)),s=n.filter((e=>e.control_type===l.ze.NOT_CUSTOM));return{...t,data:a,groups:[{id:crypto.randomUUID(),permission:1,title:"\u8fdb\u5ea6\u8868",type:"schedule",notCustoms:s,customs:d,dialogCodes:o,variables:r.map((e=>e.variables)).flat()}]}},s=e=>{var t,a,o,r;const d=(0,i.B)("inputVariable","inputVariableMap"),s=(0,i.B)("inputVariable","inputVariableCodeList").map((e=>d.get(e)));return{desc:(null===e||void 0===e?void 0:e.dialog_type)===l.YM.SIGNAL&&e.is_daq?"input":"daq",groups:[{id:crypto.randomUUID(),title:(null===e||void 0===e?void 0:e.type)===l.ze.CUSTOM?null===e||void 0===e?void 0:e.control_name:null===(t=l.vU.find((t=>t.value===e.dialog_type)))||void 0===t?void 0:t.label,control_type:null===e||void 0===e?void 0:e.type,type:"control",dialog_type:null===e||void 0===e?void 0:e.dialog_type,default_name:null===e||void 0===e?void 0:e.default_name,code:null===e||void 0===e?void 0:e.code,signals:[],results:[],variables:[],customs:[],related_variables:(null===e||void 0===e||null===(a=e.related_variables)||void 0===a?void 0:a.length)>0?[{id:crypto.randomUUID(),title:"\u5173\u8054\u53d8\u91cf",control_type:null===e||void 0===e?void 0:e.type,type:"control",variables:null===s||void 0===s||null===(o=s.filter((t=>{var a;return null===e||void 0===e||null===(a=e.related_variables)||void 0===a?void 0:a.includes(t.id)})))||void 0===o||null===(r=o.sort(((t,a)=>{var l,n;return(null===e||void 0===e||null===(l=e.related_variables)||void 0===l?void 0:l.indexOf(t.id))-(null===e||void 0===e||null===(n=e.related_variables)||void 0===n?void 0:n.indexOf(a.id))})))||void 0===r?void 0:r.map((e=>{var t;return{...e,code:null===e||void 0===e||null===(t=e.code)||void 0===t?void 0:t.replace(n.O.INPUT,n.O.CONTROL_INPUT)}}))}]:[]}]}}},21256:(e,t,a)=>{a.d(t,{A:()=>A,b:()=>c});var l=a(80077),n=a(34458),i=a(8237),o=a(15637),r=a(67208);const d=[{title:"\u5176\u4ed6",key:"\u5176\u4ed6",widget_id:"\u5176\u4ed6",widget_type:"\u5176\u4ed6",children:[{title:"\u7a7a",widget_type:i.rX.EMPTY},{title:"\u533a\u5757",widget_type:i.rX.BLOCK},{title:"\u6307\u4ee4\u6027\u8f93\u5165",widget_type:i.rX.INSTRUCTION_INPUT},{title:"\u72b6\u6001\u680f",widget_type:i.rX.FOOTER},{title:"\u6d3b\u9875\u5939",widget_type:i.rX.TAB_FIXED},{title:"\u6d41\u7a0b\u56fe",widget_type:i.rX.PROCESS},{title:"\u5411\u5bfc",widget_type:i.rX.DYNAMIC_FORM},{title:"\u65e5\u5fd7\u63a7\u4ef6",widget_type:i.rX.LOG},{title:"\u89c6\u9891\u63a7\u4ef6",widget_type:i.rX.VIDEO},{title:"\u6d4b\u8bd5\u62a5\u544a",widget_type:i.rX.TEST_REPORT},{title:"pid\u9762\u677f",widget_type:i.rX["PID\u9762\u677f"]},{title:"\u5b50\u4efb\u52a1\u53c2\u6570",widget_type:i.rX.SUB_TASK_PARAM},{title:"\u6570\u5b57IO-input",widget_type:i.rX["\u6570\u5b57IO_input"]},{title:"\u6570\u5b57IO-output",widget_type:i.rX["\u6570\u5b57IO_output"]},{title:"\u8fdb\u5ea6\u6761\u63a7\u4ef6",widget_type:i.rX["\u8fdb\u5ea6\u6761"]},{title:"\u81ea\u5b9a\u4e49\u6ce2\u5f62",widget_type:i.rX.CUSTOM_WAVEFORM},{title:"\u7a0b\u63a7\u53c2\u6570",widget_type:i.rX["\u7a0b\u63a7\u53c2\u6570"]},{title:"\u5206\u5c4f\u76d1\u63a7",widget_type:i.rX["\u8815\u53d8\u5206\u5c4f\u8868\u5934"]},{title:"\u8868\u5934",widget_type:i.rX.HEADER},{title:"\u7279\u6b8a\u8868\u5934",widget_type:i.rX["\u7279\u6b8a\u8868\u5934"]}]},{title:"\u539f\u5b50\u63a7\u4ef6",key:"\u539f\u5b50\u63a7\u4ef6",widget_id:"\u539f\u5b50\u63a7\u4ef6",widget_type:"\u539f\u5b50\u63a7\u4ef6",children:[{title:"\u6587\u672c\u8f93\u5165\u6846",widget_type:i.rX.ATOM_INPUT},{title:"\u9009\u62e9\u5668",widget_type:i.rX.ATOM_SELECT},{title:"\u6570\u5b57\u8f93\u5165\u6846",widget_type:i.rX.ATOM_INPUT_NUMBER},{title:"\u52a0\u51cf\u63a7\u4ef6",widget_type:i.rX.ATOM_RENDER_PARAMS},{title:"\u65e5\u671f\u65f6\u95f4",widget_type:i.rX.ATOM_DATETIME},{title:"\u52fe\u9009\u6846",widget_type:i.rX.ATOM_CHECKBOX_SINGLE},{title:"\u6309\u94ae",widget_type:i.rX.ATOM_BUTTON},{title:"Label",widget_type:i.rX.ATOM_LABEL}]},{title:"\u8bd5\u6837",key:"\u8bd5\u6837",widget_id:"\u8bd5\u6837",widget_type:"\u8bd5\u6837",children:[{title:"\u8bd5\u6837",widget_type:i.rX.SAMPLE},{title:"\u8815\u53d8\u8bd5\u6837\u53c2\u6570",widget_type:i.rX.CREEP_SAMPLE_PARAMS}]},{title:"\u8868\u683c\u63a7\u4ef6",key:"\u8868\u683c\u63a7\u4ef6",widget_id:"\u8868\u683c\u63a7\u4ef6",widget_type:"\u8868\u683c\u63a7\u4ef6",children:[{title:"\u7ed3\u679c\u8868\u683c",widget_type:i.rX.SAMPLE_TABLE},{title:"\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c",widget_type:i.rX["\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c"]},{title:"\u7ed3\u679c\u53d8\u91cf\u5c55\u793a",widget_type:i.rX.ATOM_RESULT_LABEL},{title:"\u7ed3\u679c\u53d8\u91cf\u8868\u683c",widget_type:i.rX.RESULT_TABLE},{title:"\u7ed3\u679c\u7edf\u8ba1\u8868\u683c",widget_type:i.rX.SAMPLE_STATISTIC_TABLE},{title:"\u6570\u7ec4\u7ed3\u679c\u53d8\u91cf\u5c55\u793a",widget_type:i.rX.RESULT_ARRAY_LABEL},{title:"\u6570\u636e\u91c7\u96c6\u8868\u683c",widget_type:i.rX.ATOM_TABLE_2_DATA_GATHER},{title:"\u8815\u53d8\u6e29\u5ea6\u8303\u56f4\u9009\u62e9",widget_type:i.rX.CREEP_TEMP_RANGE}]},{title:"\u66f2\u7ebf",key:"\u66f2\u7ebf",widget_id:"\u66f2\u7ebf",widget_type:"\u66f2\u7ebf",children:[{title:"daqbuffer\u66f2\u7ebf",widget_type:i.rX.LIGHTNING_LINE_CHART},{title:"\u4e8c\u7ef4\u6570\u7ec4\u66f2\u7ebf",widget_type:i.rX.ARRAY_CURVE}]},{title:"\u5df2\u5f03\u7528",key:"\u5df2\u5f03\u7528",widget_id:"\u5df2\u5f03\u7528",widget_type:"\u5df2\u5f03\u7528",children:[{title:"\u5185\u5bb9\u5206\u5272",widget_type:i.rX.CONTENT_SPLIT_EMPTY},{title:"\u6807\u7b7e\u9875",widget_type:i.rX.ATOM_TABS},{title:"\u591a\u9009\u6846",widget_type:i.rX.ATOM_CHECKBOX_GROUP},{title:"\u8815\u53d8\u66f2\u7ebf",widget_type:i.rX.CREEP_CURVE},{title:"\u4fe1\u53f7\u504f\u79fb\u91cf\u8868\u683c",widget_type:i.rX.CREEP_SIGNAL_OFFSET_TABLE},{title:"\u8bd5\u9a8c\u6570\u636e\u8868\u683c",widget_type:i.rX.CREEP_TEST_DATA_TABLE},{title:"\u52a8\u6001\u4e0a\u4f4d\u673a\u9650\u4f4d\u68c0\u6d4b",widget_type:i.rX.DYNAMIC_UP_LIMIT},{title:"\u52a8\u6001\u4e0b\u4f4d\u673a\u9650\u4f4d\u68c0\u6d4b",widget_type:i.rX.DYNAMIC_LOW_LIMIT},{title:"\u52a8\u6001\u51fd\u6570\u53d1\u751f\u5668",widget_type:i.rX.DYNAMIC_FUNC_GENERATOR},{title:"\u5feb\u6377\u65b9\u5f0f",widget_type:i.rX["\u5168\u5c40_\u5feb\u6377\u65b9\u5f0f"]},{title:"\u6570\u636e\u76d1\u63a7\u8868\u683c",widget_type:i.rX["\u5168\u5c40_\u6570\u636e\u76d1\u63a7\u8868\u683c"]},{title:"\u5168\u5c40\u65e5\u5fd7",widget_type:i.rX["\u5168\u5c40_\u65e5\u5fd7"]},{title:"\u5206\u5c4f\u76d1\u63a7",widget_type:i.rX["\u5168\u5c40_\u5206\u5c4f\u76d1\u63a7"]},{title:"\u52a8\u6001\u8bd5\u6837\u53c2\u6570\u914d\u7f6e",widget_type:i.rX.DYNAMIC_SAMPLE},{title:"\u8bd5\u6837\u9884\u8bbe\u5206\u7ec4",widget_type:i.rX.GROUP_SAMPLE},{title:"\u52a8\u6001\u9ad8\u5468\u8868\u683c",widget_type:i.rX.GAOZHOU_TABLE},{title:"\u52a8\u6001\u66f2\u7ebf\uff08\u7f13\u5b58N\u6761\uff09",widget_type:i.rX.DYNAMIC_CURVE5},{title:"\u52a8\u6001\u66f2\u7ebf\uff08\u65ad\u88c21\uff09",widget_type:i.rX.DYNAMIC_CURVE1},{title:"\u52a8\u6001\u66f2\u7ebf\uff08\u65ad\u88c22\uff09",widget_type:i.rX.DYNAMIC_CURVE2},{title:"\u52a8\u6001\u66f2\u7ebf\uff08\u65ad\u88c23\uff09",widget_type:i.rX.DYNAMIC_CURVE3},{title:"\u52a8\u6001\u66f2\u7ebf\uff08\u6700\u5927/\u5c0f\uff09",widget_type:i.rX.DYNAMIC_CURVE6},{title:"\u52a8\u6001\u62df\u5408\u66f2\u7ebf",widget_type:i.rX.DYNAMIC_CURVE_FITTING}]}],s={[i.rX.EMPTY]:!0,[i.rX.FOOTER]:!0,[i.rX.PROCESS]:!0,[i.rX.LOG]:!0,[i.rX.VIDEO]:!0,[i.rX.CONTENT_SPLIT_EMPTY]:!0,[i.rX.TEST_REPORT]:!0,[i.rX.SUB_TASK_PARAM]:!0,[i.rX["\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c"]]:!0,[i.rX.GAOZHOU_CURVE]:!0,[i.rX.DYNAMIC_CURVE5]:!0,[i.rX.DYNAMIC_CURVE1]:!0,[i.rX.DYNAMIC_CURVE2]:!0,[i.rX.DYNAMIC_CURVE3]:!0,[i.rX.DYNAMIC_CURVE6]:!0,[i.rX.DYNAMIC_CURVE_FITTING]:!0,[i.rX.DYNAMIC_SAMPLE]:!0,[i.rX.GROUP_SAMPLE]:!0,[i.rX.BLOCK]:!0,[i.rX.CREEP_SAMPLE_PARAMS]:!0,[i.rX.CUSTOM_WAVEFORM]:!0,[i.rX.CREEP_CURVE]:!0,[i.rX.CREEP_TEMP_RANGE]:!0,[i.rX.CREEP_SIGNAL_OFFSET_TABLE]:!0,[i.rX.CREEP_TEST_DATA_TABLE]:!0,[i.rX["\u8815\u53d8\u5206\u5c4f\u8868\u5934"]]:!0,[i.rX["\u7a0b\u63a7\u53c2\u6570"]]:!0,[i.rX.ATOM_INPUT]:!0,[i.rX.ATOM_SELECT]:!0,[i.rX.ATOM_BUTTON]:!0,[i.rX.ATOM_TABS]:!0,[i.rX.ATOM_CHECKBOX_SINGLE]:!0,[i.rX.ATOM_TABLE_2_DATA_GATHER]:!0,[i.rX.ATOM_INPUT_NUMBER]:!0,[i.rX.ATOM_RENDER_PARAMS]:!0,[i.rX.ATOM_CHECKBOX_GROUP]:!0,[i.rX.ATOM_RESULT_LABEL]:!0,[i.rX.RESULT_ARRAY_LABEL]:!0,[i.rX.RESULT_TABLE]:!0,[i.rX.ATOM_DATETIME]:!0,[i.rX.DYNAMIC_UP_LIMIT]:!0,[i.rX.DYNAMIC_LOW_LIMIT]:!0,[i.rX.DYNAMIC_FUNC_GENERATOR]:!0,[i.rX["\u5168\u5c40_\u5feb\u6377\u65b9\u5f0f"]]:!0,[i.rX["\u5168\u5c40_\u6570\u636e\u76d1\u63a7\u8868\u683c"]]:!0,[i.rX["\u5168\u5c40_\u65e5\u5fd7"]]:!0,[i.rX["\u5168\u5c40_\u5206\u5c4f\u76d1\u63a7"]]:!0},c=function(){var e,t,a,l;let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!n||null===(e=n[0])||void 0===e||!e.children)return[];const r=[],c=function(){const e={};return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach((t=>{var a;t.widget_type===i.rX.SHORTCUT?e[t.widget_type]=t:null===t||void 0===t||null===(a=t.children)||void 0===a||a.forEach((t=>{e[t.widget_type]||(e[t.widget_type]=t)}))})),e}((null===n||void 0===n||null===(t=n[0])||void 0===t?void 0:t.children)||[]);d.forEach((e=>{var t;const a={...e,children:null===e||void 0===e?void 0:e.children.map((e=>({...c[e.widget_type],...e})))};o||(a.children=a.children.filter((e=>!s[e.widget_type]))),(null===a||void 0===a||null===(t=a.children)||void 0===t?void 0:t.length)>0&&r.push(a)})),r.splice(1,0,null===(a=c[i.rX.SHORTCUT])||void 0===a||null===(l=a.children)||void 0===l?void 0:l[0]);return[{...null===n||void 0===n?void 0:n[0],children:r},...n.slice(1)]},A=()=>{const e=(0,l.wA)(),t=async()=>{try{const t=await(0,r.PG0)();t&&e({type:o.Yn,param:t})}catch(t){console.log(t)}};return{initWidget:t,editWidget:async e=>{try{const a=await(0,r.c3_)({widget_id:e.widget_id,parent_id:null===e||void 0===e?void 0:e.parent_id,widget_type:null===e||void 0===e?void 0:e.widget_type,widget_name:e.widget_name||e.title,data_source:e.data_source});return t(),a}catch(a){console.error("\u7f16\u8f91\u63a7\u4ef6\u5931\u8d25:",a)}return null},addWidget:async e=>{try{await(0,r.c3_)({widget_id:(null===e||void 0===e?void 0:e.current_id)||crypto.randomUUID(),parent_id:null===e||void 0===e?void 0:e.parent_id,widget_type:null===e||void 0===e?void 0:e.widget_type,widget_name:e.widget_name,data_source:e.data_source}),t()}catch(a){console.error("\u65b0\u589e\u63a7\u4ef6\u5931\u8d25:",a)}},delWidget:()=>{},initWidgetStatus:async()=>{try{if(!(0,n.HN)())throw new Error("\u9879\u76eeID\u4e0d\u5b58\u5728");{const e=await(0,r.ct8)();(0,n.cV)(e)}}catch(e){console.error(e)}},batchWidgetStatus:async()=>{try{(0,n.oJ)()&&(0,n.HN)()&&await(0,r.aC7)({data_sources:(0,n.oJ)()})}catch(e){console.error(e)}}}}},24870:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=a(15637),i=a(67208);const o=()=>{const e=(0,l.wA)();return{initTemplateMap:async()=>{try{const t=await(0,i.TEp)();t&&e({type:n.Jl,param:t})}catch(t){console.log(t)}}}}},28186:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=a(15637),i=a(67208);const o=()=>{const e=(0,l.wA)(),t=async()=>{try{const t=await(0,i.Gfq)();t&&e({type:n.WW,param:t})}catch(t){console.log(t)}};return{initArrayCurveConfig:t,updateArrayCurveConfig:async e=>{try{await(0,i.PO3)(e)&&t()}catch(a){console.log(a)}},delArrayCurveConfig:async e=>{try{await(0,i.dxS)(e)&&t()}catch(a){console.log(a)}},createArrayCurveConfig:async e=>{try{const a=await(0,i.RaR)(e);return a?(t(),a):a}catch(a){console.log(a)}return null}}}},29977:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(65043),n=a(80077),i=a(32099);const o=()=>(0,i.Mz)([e=>e.inputVariable.inputVariableMap,e=>e.inputVariable.inputVariableCodeList],((e,t)=>t.map((t=>e.get(t))))),r=()=>{const e=(0,l.useMemo)(o,[]);return(0,n.d4)((t=>e(t)))}},30092:(e,t,a)=>{a.r(t),a.d(t,{default:()=>Ue});var l=a(65043),n=a(80077),i=a(19853),o=a.n(i),r=a(93950),d=a.n(r),s=a(29977),c=a(88359),A=a(72295),u=a(67208),p=a(56543),v=a(94817),m=a(84665),h=a(33981),g=a(25055),b=a(36497),y=a(6051),x=a(95206),f=a(56434),C=a.n(f),w=a(74117),E=a(44409),S=a(34458),_=a(36950),R=a(30780),j=a(97292),I=a(10866),T=a(21043),P=a(63612),k=a(81143),D=a(68374);const U=k.Ay.div`
    display: flex;
    justify-content: space-between;
    .sample-left {
        width: 100%;
        .ant-form-item {
            .ant-form-item-control-input-content {
                display: flex;
                justify-content: flex-end;
            }
            .ant-form-item-label {
                >label::after {
                    content: '';    
                }
            }
        }
    }
    .advanced {
        display: flex;
        justify-content: flex-end;
    }
    .table-layout { 
        width: 50vw;
    }

`;var N=a(8354),B=a(83720),O=a(4554),M=a(75337);const L=k.Ay.div`
    width: 100%;
    .line-layout {
        display: flex;
        .divider-layout {
            width: 90%
        }
        .button-layout {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
        }
    }
    

`;var V=a(32513),Q=a(75440);const F=k.Ay.div`
    display: flex;
    justify-content: space-between;
    .select-layout {
        width: 100%;
        display: flex;
        width: 60vw;
        .checkbox-layout {
            width: 70%;
            margin-left: 50px;
            overflow: auto;
            height: ${(0,D.D0)("400px")};
            .ant-checkbox-group {
                flex-direction: column;
            }
            .ant-checkbox-wrapper {
                margin-inline-start: 0;
                margin-bottom: 10px;
            }
        }
    }
    .button-layout {
        width: 10vw;
        display: flex;
        flex-direction: column; 
        .button {
            margin-top: 10px;
        }
    }
`;var J=a(70579);const Y=e=>{let{open:t,setOpen:a}=e;const{t:i}=(0,w.Bd)(),o=(0,n.d4)((e=>e.subTask.openExperiment)),r=(0,n.d4)((e=>e.project.sampleAboutList)),d=(0,n.d4)((e=>e.project.sampleData)),{initSampleAboutList:s,batchUpdateSample:c,getSamples:A}=(0,I.A)(),[p,v]=(0,l.useState)([]),[m,h]=(0,l.useState)([]);(0,l.useEffect)((()=>{var e,t;v(null!==r&&void 0!==r?r:[]),h(null===d||void 0===d||null===(e=d[0])||void 0===e||null===(t=e.children)||void 0===t?void 0:t[0].samples.map((e=>e.id)))}),[r,d]);const g=async e=>{try{const t=await(0,u.WKV)({instance_about_List:e});(t||null===t)&&s()}catch(t){throw console.log(t),t}};return(0,J.jsx)(y.A,{children:(0,J.jsx)(Q.A,{open:t,width:"50vw",footer:null,onCancel:()=>a(!1),title:i("\u8f93\u5165\u4e0e\u8bd5\u6837\u76f8\u5173\u7684\u6570\u636e"),children:(0,J.jsxs)(F,{children:[(0,J.jsxs)("div",{className:"select-layout",children:[(0,J.jsx)("div",{children:i("\u9009\u62e9")}),(0,J.jsx)("div",{className:"checkbox-layout",children:(0,J.jsx)(V.A.Group,{onChange:e=>{h(e)},value:m,children:null===p||void 0===p?void 0:p.map((e=>e.isEdit?(0,J.jsx)(V.A,{value:e.id,disabled:o,children:(0,J.jsx)(y.A,{children:(0,J.jsx)(B.A,{disabled:o,defaultValue:e.name,onChange:t=>((e,t)=>{e.preventDefault(),v(p.map((a=>a.id===t.id?{...a,name:e.target.value}:a)))})(t,e)})})},e.id):(0,J.jsx)(V.A,{value:e.id,disabled:o,children:e.name},e.id)))})})]}),(0,J.jsxs)("div",{className:"button-layout",children:[(0,J.jsx)(x.Ay,{className:"button",onClick:async()=>{const e=p.filter((e=>m.includes(e.id)));await g(p),await c(A().map((t=>({...t,samples:e.map((e=>{var a;const l=null===(a=t.samples.find((t=>t.id===e.id)))||void 0===a?void 0:a.value;return{...e,value:l}}))})))),a(!1)},children:i("\u786e\u5b9a")}),(0,J.jsx)(x.Ay,{className:"button",onClick:()=>{a(!1)},children:i("\u53d6\u6d88")}),(0,J.jsx)(x.Ay,{className:"button",onClick:(e,t)=>{v([...p,{id:crypto.randomUUID(),name:"",func:"",value:"",code:`${j.O.SAMPLE_ELSE}${(0,_.vx)()}`,isEdit:!0}])},children:i("\u6dfb\u52a0")}),(0,J.jsx)(x.Ay,{className:"button",onClick:()=>{v(p.map((e=>m.includes(e.id)?{...e,isEdit:!0}:e)))},children:i("\u7f16\u8f91")}),(0,J.jsx)(x.Ay,{className:"button",onClick:()=>{v(p.filter((e=>!m.includes(e.id))))},children:i("\u5220\u9664")})]})]})})})},K=e=>{let{relevantData:t,relevantForm:a}=e;const{t:i}=(0,w.Bd)(),[o,r]=(0,l.useState)(!1),{getSamples:d}=(0,I.A)(),{sampleData:s,sampleAboutList:c}=(0,n.d4)((e=>e.project)),{openExperiment:A}=(0,n.d4)((e=>e.subTask)),u=(0,l.useMemo)((()=>{var e;return 1!==(null===(e=d())||void 0===e?void 0:e.length)}),[s]);return(0,J.jsxs)(L,{children:[(0,J.jsxs)("div",{className:"line-layout",children:[(0,J.jsx)("div",{className:"divider-layout",children:(0,J.jsx)(N.A,{orientation:"left",orientationMargin:"0",style:{borderColor:"rgba(20, 115, 245,0.4)"},children:i("\u8f93\u5165\u4e0e\u8bd5\u6837\u76f8\u5173\u7684\u6570\u636e")})}),(0,J.jsx)("div",{className:"button-layout",children:(0,J.jsx)(O.A,{icon:(0,J.jsx)(M.A,{}),disabled:A||u,onClick:()=>{r(!0)}})})]}),(0,J.jsx)(g.A,{labelAlign:"left",form:a,...p.B_,children:null===t||void 0===t?void 0:t.map((e=>(0,J.jsx)(g.A.Item,{label:e.name,name:e.id,children:(0,J.jsx)(B.A,{style:{width:"20vw"}})},e.id)))}),o&&(0,J.jsx)(Y,{open:o,setOpen:r})]})},H=e=>{let{value:t={units_id:"",value:"",name:""},onChange:a,...l}=e;return(0,J.jsx)(y.A,{children:(0,J.jsx)(b.A,{...l,style:{width:"13vw"},value:t.value?String(t.value):void 0,onChange:e=>{null===a||void 0===a||a({value:e})}})})};var W=a(97914),G=a(91688);const z=e=>{let{item:t={},value:a={units_id:"",value:"",name:""},onChange:i,unitData:o={},disabled:r}=e;(0,G.zy)();const{openExperiment:d}=(0,n.d4)((e=>e.subTask)),[s,c]=(0,l.useState)(a.value),[A,u]=(0,l.useState)(a.units_id);(0,l.useEffect)((()=>{c(a.value),u(a.units_id)}),[a]);const p=e=>{null===i||void 0===i||i({value:s,units_id:A,...e})};return(0,J.jsxs)(y.A,{children:[(0,J.jsx)(W.A,{value:a.val||s,style:{width:"7vw"},disabled:r||d,onChange:e=>{c(e),p({value:e})},max:99999999.99999999,min:-99999999.99999999,formatter:e=>{if(""===e||null===e||void 0===e)return"";const t=e.toString().split(".");return t[0]&&t[0].length>8&&(t[0]=t[0].slice(0,8)),t[1]&&t[1].length>8&&(t[1]=t[1].slice(0,8)),t.join(".")},parser:e=>{if(""===e||null===e||void 0===e)return"";const t=e.toString().split(".");return t[0]&&t[0].length>8&&(t[0]=t[0].slice(0,8)),t[1]&&t[1].length>8&&(t[1]=t[1].slice(0,8)),t.join(".")}}),null!==t&&void 0!==t&&t.units_id?(0,J.jsx)(b.A,{value:a.unit||A,showSearch:!0,optionFilterProp:"name",style:{width:"5vw"},fieldNames:{label:"name",value:"id"},onChange:e=>{const t=(0,_.tJ)(s,null===o||void 0===o?void 0:o.id,e,A);u(e),c(t),p({units_id:e,value:t})},options:o.units,disabled:r||d}):null]})};var X=a(54962),Z=a(9339);const q=k.Ay.div`
    padding: 10px;
    display: flex;
    width: 100%;
    justify-content: space-between;
    .left-layout {
        width: 45vw;
    }

`,$=k.Ay.div`
    display: flex;
    justify-content: space-between;
    .right-layout {
        display: flex;
        width: 9vw;
        flex-direction: column;
        justify-content: space-between;
    }
`,ee={labelCol:{flex:"300px"},wrapperCol:{flex:"1"}},te=e=>{let{advancedOpen:t,onAdvancedCancel:a}=e;(0,G.zy)();const{t:i}=(0,w.Bd)(),{getSamples:o,batchUpdateSample:r}=(0,I.A)(),{openExperiment:s}=(0,n.d4)((e=>e.subTask)),c=(0,n.d4)((e=>e.global.unitList)),{sampleData:A,defaultSample:u,optSample:v}=(0,n.d4)((e=>e.project)),[m,h]=(0,l.useState)([]),[b,x]=(0,l.useState)([]),[f,C]=(0,l.useState)(!1),E=(0,l.useMemo)((()=>o()),[A]);(0,l.useEffect)((()=>{S()}),[E]);const S=()=>{x(E.length),h(E)},R=(0,l.useCallback)(d()(((e,t)=>((e,t)=>{const a=null===A||void 0===A?void 0:A[0].count,l=e-t.length;l>0&&h((e=>[...e,...Array.from({length:l},((t,n)=>{var i,o,r;return{key:crypto.randomUUID(),name:`\u8bd5\u6837${a+n+1+(null!==(i=null===(o=e.filter((e=>e.new)))||void 0===o?void 0:o.length)&&void 0!==i?i:0)}`,code:`sample_${l+1}${(0,_.vx)()}`,data:(null===u||void 0===u?void 0:u.data)||[],samples:(null===E||void 0===E||null===(r=E[0])||void 0===r?void 0:r.samples.map((e=>({...e,value:""}))))||[],color:"#FFFFFF",sample_type:(null===u||void 0===u?void 0:u.sample_type)||"",new:!0,status:p.$y.READY,disabled:!1,parent_group:v.parent_group}}))])),l<0&&h((e=>e.filter(((t,a)=>a<=e.length+l-1))))})(e,t)),800),[]),j=(0,l.useCallback)(((e,t)=>{var a;const{value:l,units_id:n,data_type:i}=null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.find((e=>e.parameter_id===t.parameter_id));return(0,J.jsx)(J.Fragment,{children:"select"===i?(0,J.jsx)(H,{disabled:!!t.func||!!t.is_disabled_func||!!t.is_visible_func||s,value:{value:l},options:(null===t||void 0===t?void 0:t.select_options)||[],onChange:a=>{h((l=>l.map((l=>l.key===e.key?{...l,data:l.data.map((e=>e.parameter_id===t.parameter_id?{...e,...a}:e))}:l))))}}):(0,J.jsx)(z,{disabled:!!t.func||!!t.is_disabled_func||!!t.is_visible_func||s,value:{units_id:n,value:l},unitData:null===c||void 0===c?void 0:c.find((e=>e.id===t.dimension_id)),onChange:a=>{h((l=>l.map((l=>l.key===e.key?{...l,data:l.data.map((e=>e.parameter_id===t.parameter_id?{...e,...a}:e))}:l))))}})})}),[c]),T=(0,l.useCallback)(((e,t)=>{var a,l;const{value:n}=null!==(a=null===e||void 0===e||null===(l=e.samples)||void 0===l?void 0:l.find((e=>e.id===t.id)))&&void 0!==a?a:{};return(0,J.jsx)(B.A,{value:n,disabled:e.status!==p.$y.READY,onChange:a=>{h((l=>l.map((l=>l.key===e.key?{...l,samples:l.samples.map((e=>e.id===t.id?{...e,value:a.target.value}:e))}:l))))}})}),[]),P=()=>{a&&a()};return(0,J.jsx)(Q.A,{open:t,title:i("\u9ad8\u7ea7\u8bbe\u7f6e"),width:"60vw",onCancel:a,footer:null,children:(0,J.jsxs)($,{children:[(0,J.jsx)(X.A,{children:(0,J.jsx)(q,{children:(0,J.jsx)("div",{className:"left-layout",children:(0,J.jsx)(g.A,{...ee,labelAlign:"left",children:(0,J.jsxs)(J.Fragment,{children:[(0,J.jsx)(g.A.Item,{label:i("\u8bd5\u6837\u6570\u91cf"),extra:i("\u6700\u591a\u53ea\u80fd\u7f16\u8f91100\u4e2a\u8bd5\u6837"),children:(0,J.jsx)(W.A,{min:null===E||void 0===E?void 0:E.length,max:100,value:b,disabled:s,onChange:e=>{e&&(x(e),R(e,m))}})}),(0,J.jsx)("div",{className:"table-layout",children:(0,J.jsx)(Z.A,{rowKey:"key",bordered:!0,dataSource:m,scroll:{x:"25vw",y:"33vh"},columns:(e=>{var t;return[{title:i("\u8bd5\u6837\u540d\u79f0"),dataIndex:"value",width:100,ellipsis:!0,align:"center",render:(e,t)=>null===t||void 0===t?void 0:t.name},...null===(t=e.data)||void 0===t?void 0:t.map((e=>({title:i(e.parameter_name),dataIndex:"value",key:e.parameter_id,width:"18vw",ellipsis:!0,render:(t,a)=>j(a,e)}))),...e.samples.map((e=>({title:i(e.name),dataIndex:"value",key:e.id,width:"18vw",ellipsis:!0,render:(t,a)=>T(a,e)})))]})(null===E||void 0===E?void 0:E[0]),pagination:!1})})]})})})})}),(0,J.jsx)("div",{className:"right-layout",children:(0,J.jsxs)(y.A,{direction:"vertical",children:[(0,J.jsx)(O.A,{loading:f,block:!0,onClick:async()=>{try{C(!0),setTimeout((async()=>{await r(m),P(),C(!1)}),1e3)}catch(e){C(!1)}},children:i("\u786e\u8ba4")}),(0,J.jsx)(O.A,{loading:f,block:!0,onClick:P,children:i("\u53d6\u6d88")})]})})]})})},ae=e=>{var t,a,i;let{isBase:o=!1,isSelected:r,onCallBackImg:d}=e;const s=(0,n.wA)(),{useSubscriber:c}=(0,E.A)(),{initTableConfigData:A}=(0,R.A)(),[v]=g.A.useForm(),[m]=g.A.useForm(),{sampleList:h,optSample:f,sampleData:k,defaultSample:D}=(0,n.d4)((e=>e.project)),N={...T.Sp,sample_type:k?null===h||void 0===h||null===(t=h.find((e=>{var t,a,l;return(null===e||void 0===e?void 0:e.sample_id)===(null===(t=k[0])||void 0===t||null===(a=t.children[0])||void 0===a||null===(l=a.data[0])||void 0===l?void 0:l.sample_id)})))||void 0===t?void 0:t.code:"",surveying:k?null===(a=k[0])||void 0===a||null===(i=a.children[0])||void 0===i?void 0:i.data:[]},{openExperiment:B}=(0,n.d4)((e=>e.subTask)),O=(0,n.d4)((e=>e.global.unitList)),{initDefaultSample:M,initSampleTree:L,updateOptSample:V,getSample:Q,getSamples:F}=(0,I.A)(),{t:Y}=(0,w.Bd)(),[W,G]=(0,l.useState)([]),[X,Z]=(0,l.useState)([]),[q,$]=(0,l.useState)(!1),[ee,ae]=(0,l.useState)(),le=(0,l.useRef)({}),ne=(0,l.useMemo)((()=>{var e;return 1!==(null===(e=F())||void 0===e?void 0:e.length)}),[k]);(0,l.useEffect)((()=>(ie(),()=>{re()})),[W]),(0,l.useEffect)((()=>()=>{re()}),[]);const ie=()=>{let e=null;f?e=f.code:D&&(e=D.code),e&&W.forEach((t=>{let{code:a,parameter_id:l}=t;oe(e,a,l)}))},oe=async(e,t,a)=>{const l=await c(`${e}-${t}`);le.current={...le.current,[`${e}-${t}`]:l};for await(const[n,i]of l){const l=JSON.parse(i);if(console.log(`${e}-${t}\u6536\u5230zmq\u6d88\u606f,\u65b0\u503c:`,l),l){const e={...v.getFieldValue(a),value:l.Value};v.setFieldValue(a,e),L()}}},re=()=>{Object.values(le.current).forEach((e=>null===e||void 0===e?void 0:e.close()))};(0,l.useEffect)((()=>{de()}),[f,h]);const de=async()=>{var e,t;let a="",l=[],n=[];if(f){const e=Q(f);a=e.sample_type,l=se(null===e||void 0===e?void 0:e.data),n=null===e||void 0===e?void 0:e.samples}else{const e=Q(D);a=e.sample_type,l=se(null===e||void 0===e?void 0:e.data),n=null===e||void 0===e?void 0:e.samples}const i=null===(e=l)||void 0===e?void 0:e.reduce(((e,t)=>({...e,[t.parameter_id]:t})),{});v.setFieldsValue({...N,sample_type:a}),i&&v.setFieldsValue({...N,...i,sample_type:a}),m.setFieldsValue(null===(t=n)||void 0===t?void 0:t.reduce(((e,t)=>({...e,[t.id]:t.value})),{})),Z(n),G(l||[])},se=e=>{if((null===e||void 0===e?void 0:e.length)>0){return e.map((e=>"select"===(null===e||void 0===e?void 0:e.data_type)?e:{...e,value:(0,_.tJ)(e.value,e.dimension_id,e.units_id)}))}return[]},ce=async()=>{const e=await v.validateFields(),t=await m.validateFields(),a=X.map((e=>({...e,value:t[e.id]}))),{sample_type:l}=e,n=W.map((e=>{if("select"===e.data_type)return{...e,value:e.value};const t=O.find((t=>t.id===(null===e||void 0===e?void 0:e.dimension_id)));return t?{...e,value:(0,_.tJ)(e.value,e.dimension_id,t.default_unit_id,e.units_id)}:{...e,value:e.value}}));if(s({type:P.lx,param:!0}),f){var i;const e=null===(i=k.find((e=>e.children.some((e=>e.key===f.key)))))||void 0===i?void 0:i.key,t={...f,samples:a||[],checked:!!f.checked,sample_type:l,data:n,parent_group:e,id:f.key};await(0,u.jSz)({sample_instance:t})&&V(t)}else if(null!==D&&void 0!==D&&D.id){const e={...D,samples:a||[],checked:!!D.checked,sample_type:l,data:n,id:D.id};await(0,u.jSz)({sample_instance:e})}else{var o;const e=(0,_.qD)({id:"2",key:"2",name:"\u9ed8\u8ba4\u8bd5\u6837",data:W.map((e=>{const t=O.find((t=>t.id===(null===e||void 0===e?void 0:e.dimension_id)));return{...e,value:(0,_.tJ)(e.value,null===e||void 0===e?void 0:e.dimension_id,null===t||void 0===t?void 0:t.default_unit_id,e.units_id)}})),samples:a||[],code:"2",checked:!1,color:"",sample_type:l,disabled:!1,new:!0,create_user_id:null===(o=(0,S.ug)())||void 0===o?void 0:o.id},j.O.SAMPLE);await(0,u.jSz)({sample_instance:{...e,parent_group:""}})}await M(),await(0,u.olN)({sample_param:C()(W)}),await A(),await L(),s({type:P.lx,param:!1})},Ae=e=>{var t;"type"===e.parameter_id?d(null===h||void 0===h||null===(t=h.find((e=>(null===e||void 0===e?void 0:e.code)===v.getFieldValue("sample_type"))))||void 0===t?void 0:t.img):d(null===e||void 0===e?void 0:e.parameter_img);ae(e)};return(0,J.jsxs)(U,{children:[(0,J.jsxs)("div",{className:"sample-left",children:[(0,J.jsxs)(g.A,{form:v,labelAlign:"left",...p.B_,children:[(0,J.jsx)(g.A.Item,{label:Y("\u8bd5\u6837\u5f62\u72b6"),name:"sample_type",rules:[{required:!0}],onClick:()=>Ae({parameter_id:"type"}),style:{background:r&&"type"===(null===ee||void 0===ee?void 0:ee.parameter_id)&&!o?"rgba(66,111,255,0.1)":""},children:(0,J.jsx)(b.A,{style:{width:"20vw"},showSearch:!0,optionFilterProp:"sample_name",disabled:B||ne||f.status!==p.$y.READY,fieldNames:{label:"sample_name",value:"code"},options:h.map((e=>({...e,sample_name:Y(e.sample_name)}))),onChange:(e,t)=>{const a=C()(null===t||void 0===t?void 0:t.parameters),l=null===a||void 0===a?void 0:a.map((e=>{var t;return{...e,name:null===O||void 0===O||null===(t=O.find((t=>t.id===e.dimension_id)))||void 0===t?void 0:t.code,value:(null===e||void 0===e?void 0:e.value)||e.default_val}})),n=null===l||void 0===l?void 0:l.reduce(((e,t)=>({...e,[t.parameter_id]:t})),{});v.setFieldsValue(n),G([...l]),o?setTimeout((()=>{ce()}),500):f.sample_type===t.code&&de()}})}),!o&&(null===W||void 0===W?void 0:W.map((e=>(0,J.jsx)(J.Fragment,{children:"select"===e.data_type?(0,J.jsx)(g.A.Item,{label:Y(e.parameter_name),name:e.parameter_id,hidden:!(null===e||void 0===e||!e.hidden_flag),onClick:()=>Ae(e),style:{background:r&&(null===ee||void 0===ee?void 0:ee.parameter_id)===(null===e||void 0===e?void 0:e.parameter_id)?"rgba(66,111,255,0.1)":""},rules:[{required:!e.func}],children:(0,J.jsx)(H,{disabled:!!e.func||!!e.is_disabled_func||!!e.is_visible_func||B,options:(null===e||void 0===e?void 0:e.select_options)||[],onChange:t=>{G(W.map((a=>a.parameter_id===e.parameter_id?{...a,...t}:a)))}})},e.parameter_id):(0,J.jsx)(g.A.Item,{label:Y(e.parameter_name),name:e.parameter_id,hidden:!(null===e||void 0===e||!e.hidden_flag),onClick:()=>Ae(e),style:{background:r&&(null===ee||void 0===ee?void 0:ee.parameter_id)===(null===e||void 0===e?void 0:e.parameter_id)?"rgba(66,111,255,0.1)":""},rules:[{required:!e.func}],children:(0,J.jsx)(z,{item:e,type:e.type,unitData:null===O||void 0===O?void 0:O.find((t=>t.id===e.dimension_id)),disabled:!!e.func||!!e.is_disabled_func||!!e.is_visible_func||B,onChange:t=>{G(W.map((a=>a.parameter_id===e.parameter_id?{...a,...t}:a)))}})},e.parameter_id)})))),!o&&(0,J.jsx)(K,{relevantForm:m,relevantData:X})]}),(0,J.jsxs)(y.A,{children:[!o&&(0,J.jsx)(x.Ay,{type:"primary",disabled:B,onClick:()=>ce(),children:Y("\u786e\u5b9a")}),!o&&(0,J.jsx)(x.Ay,{onClick:()=>{$(!0)},children:Y("\u6279\u91cf\u8bbe\u7f6e")})]})]}),q&&(0,J.jsx)(te,{advancedOpen:q,onAdvancedCancel:()=>{$(!1),G(W)}})]})};var le=a(16569),ne=a(96651),ie=a(16133),oe=a(68945),re=a(65175),de=a(18650);const se=e=>{let{t:t}=e;return[{id:3,disabled:!0,element:(0,J.jsx)("img",{src:de.d7,alt:""}),name:t("\u65b0\u5efa"),isHiddenNoAdmin:!0},{id:4,disabled:!0,element:(0,J.jsx)("img",{src:de.pU,alt:""}),name:t("\u5220\u9664"),isHiddenNoAdmin:!0},{id:5,disabled:!0,element:(0,J.jsx)("img",{src:de.G8,alt:""}),name:t("\u7f16\u8f91")},{id:7,disabled:!0,element:(0,J.jsx)("img",{src:de.D,alt:""}),name:t("\u5e2e\u52a9")}].filter(Boolean)},ce=k.Ay.div`
  .icons-style {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .ant-select{
      width: 200px;
    }
  }
  .icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4px;
    cursor: pointer;
    .title {
      color: rgba(0,0,0,0.45);
    }
    

  }
  .disabled {
    cursor: no-drop;
  }
`,Ae=e=>[{value:0,label:e("\u5168\u90e8")},{value:1,label:e("\u6d4b\u8bd5\u7ed3\u679c\u548c\u6240\u6709\u8bd5\u6837\u7279\u5f81\u53c2\u6570")},{value:2,label:e("\u6d4b\u8bd5\u7ed3\u679c")}],ue=e=>{let{click:t}=e;const a=(0,n.d4)((e=>e.global.userIsAdmin)),{t:l}=(0,w.Bd)(),i=(0,n.d4)((e=>e.global.roleHiddenDomClass));return(0,J.jsx)(ce,{children:(0,J.jsxs)("div",{className:"icons-style",children:[(0,J.jsxs)(y.A,{children:[(0,J.jsx)(b.A,{defaultValue:1,options:Ae(l),onChange:e=>t({id:"filterType",value:e})}),(0,J.jsx)(V.A,{onChange:e=>t({id:"filterChecked",value:e.target.checked}),children:l("\u53ea\u663e\u793a\u88ab\u6fc0\u6d3b\u7684")})]}),(0,J.jsx)(y.A,{children:se({t:l}).map((e=>3!==e.id&&4!==e.id&&5!==e.id||a?(0,J.jsxs)("div",{className:`icon ${e.isHiddenNoAdmin?i:""} ${e.disabled?"":"disabled"}`,children:[(0,J.jsx)("div",{onClick:()=>(e=>{e.disabled&&t(e)})(e),children:e.element}),(0,J.jsx)("div",{className:"title",children:e.name})]},e.id):null))})]})})},pe=3,ve=4,me=5,he="RESULT_VAR",ge="INPUT_VAR",be="SAMPLE_PARAM",ye=(k.Ay.div`
  display: flex;
  .min-layout {
    font-size: ${(0,D.D0)("15px")};
    display: flex;
    justify-content: end;
    flex-direction: column;
  }
`,k.Ay.div`
table:hover,
tr:hover,
thead:hover {
}
.ant-table-tbody > tr.ant-table-row:hover > td {
    background: none !important;
}
.table_bac{
       background-color: #c8dcfc;
    }

     min-height: ${e=>e.isMin?"50vh":"0"};
     display: flex;
     flex-direction: column;
     justify-content: space-between;

     .edit_box{
      margin-top:  ${e=>e.isMin?"0":(0,D.D0)("50px")};
      .info{
        overflow-x: hidden;
        max-height: 30vh;
        overflow-y: auto;
        padding: ${(0,D.D0)("10px")};
     }
     
     }
`),xe=()=>{const{t:e}=(0,w.Bd)(),{initResultData:t,submitTestResult:a}=(0,ie.A)(),i=(0,n.d4)((e=>e.global.unitList)),o=(0,n.d4)((e=>e.template.resultData)),r=(0,n.d4)((e=>e.template.dialogs)),s=(0,n.d4)((e=>e.template.resultTestData)),c=(0,n.d4)((e=>e.project.optSample)),A=(0,n.d4)((e=>e.project.sampleList)),{getSample:p}=(0,I.A)(),[v,m]=le.Ay.useMessage(),[h,g]=(0,l.useState)(!1),[b,y]=(0,l.useState)([]),[x,f]=(0,l.useState)(),[C,E]=(0,l.useState)(1),[S,_]=(0,l.useState)(!1),[R,j]=(0,l.useState)([]);(0,l.useEffect)((()=>{h||t()}),[h]),(0,l.useEffect)((()=>{T()}),[r]),(0,l.useEffect)((()=>{y(s.map((e=>e.id)))}),[s]);const T=async()=>{const e=null===r||void 0===r?void 0:r.flatMap((e=>e.variable_ids)),t=(await(0,u.L8k)({ids:Array.from(new Set(e))})).map((e=>{let{id:t,name:a,default_val:l}=e;return{id:t,abbreviation:a,type:ge,unit_name:null!==l&&void 0!==l&&l.unit?i.flatMap((e=>e.units)).find((e=>e.id===(null===l||void 0===l?void 0:l.unit))):"-",variable_name:a}}));j(t)},P=(0,l.useMemo)((()=>{var e,t,a;return null!==(e=null===(t=p(c,A))||void 0===t||null===(a=t.data)||void 0===a?void 0:a.filter((e=>!e.hidden_flag)).map((e=>{var t,a,l;let{parameter_id:n,parameter_name:o,dimension_id:r,units_id:d}=e;return{id:n,type:be,abbreviation:o,unit_name:null===i||void 0===i||null===(t=i.find((e=>e.id===r)))||void 0===t||null===(a=t.units)||void 0===a||null===(l=a.find((e=>e.id===d)))||void 0===l?void 0:l.name,variable_name:o}})))&&void 0!==e?e:[]}),[c,A]),k=(0,l.useMemo)((()=>{var e;return null!==(e=null===o||void 0===o?void 0:o.map((e=>{let{abbreviation:t,unit_name:a,variable_name:l,result_variable_id:n,variables:i,description:o}=e;return{type:he,id:n,abbreviation:t,unit_name:a,variable_name:l,variables:i,description:o}})))&&void 0!==e?e:[]}),[o]),D=(0,l.useMemo)((()=>{let e=[];switch(C){case 0:e=[...k,...P,...R];break;case 1:e=[...k,...P];break;case 2:e=[...k];break;default:e=[]}return S?null===b||void 0===b?void 0:b.map((t=>e.find((e=>e.id===t)))).filter(Boolean):e}),[o,c,C,S]),U=(0,l.useCallback)(d()((e=>(async e=>{try{var t,l,n,i;const o=null===(t=e.result_variable_array)||void 0===t?void 0:t.map((e=>e.id)),r=null===(l=e.testData)||void 0===l?void 0:l.map((e=>e.id));a({data:null===(n=e.result_variable_array)||void 0===n?void 0:n.filter((e=>!r.includes(e.id))),del_data:null===(i=e.testData)||void 0===i?void 0:i.filter((e=>!o.includes(e.id))).map((e=>e.id))})}catch(o){console.error(o)}})(e)),100),[]),N={defaultSelectedRowKeys:s.map((e=>e.id)),selectedRowKeys:b,onChange:e=>{y(e),U({result_variable_array:D.filter((t=>e.includes(t.id))),testData:s})}},B=[{title:e("\u7f29\u5199"),dataIndex:"abbreviation",key:"abbreviation",render:(e,t)=>(0,J.jsx)(oe.A,{text:e,variables:null===t||void 0===t?void 0:t.variables})},{title:e("\u5355\u4f4d"),dataIndex:"unit_name",key:"unit_name"},{title:e("\u540d\u79f0"),dataIndex:"variable_name",key:"variable_name",render:(t,a)=>(0,J.jsx)(ne.A,{title:`${e(null===a||void 0===a?void 0:a.description)||""}`,children:e(t)})}];return(0,J.jsxs)(J.Fragment,{children:[m,(0,J.jsx)(ye,{isMin:!1,children:(0,J.jsxs)("div",{className:"table-box",children:[(0,J.jsx)(ue,{click:async a=>{if(a||0===a){if("filterType"===a.id)return E(a.value),a;if("filterChecked"===a.id)return _(a.value),a;if(a.id===pe)f(!1),g(!0);else if(a.id===ve){var l;if(1!==(null===b||void 0===b?void 0:b.length))return v.open({type:"warning",content:e("\u8bf7\u9009\u62e9\u4e00\u6761\u6570\u636e")});if((null===(l=D.find((e=>e.id===b[0])))||void 0===l?void 0:l.type)!==he)return v.open({type:"warning",content:e("\u8bf7\u9009\u62e9\u4e00\u6761\u6d4b\u8bd5\u7ed3\u679c\u6570\u636e")});await(0,u.EXm)({result_variable_id:b[0]})&&(v.open({type:"success",content:e("\u5220\u9664\u6210\u529f")}),y([]),t())}else if(a.id===me){var n;if(1!==(null===b||void 0===b?void 0:b.length))return v.open({type:"warning",content:e("\u8bf7\u9009\u62e9\u4e00\u6761\u6570\u636e")});if((null===(n=D.find((e=>e.id===b[0])))||void 0===n?void 0:n.type)!==he)return v.open({type:"warning",content:e("\u8bf7\u9009\u62e9\u4e00\u6761\u6d4b\u8bd5\u7ed3\u679c\u6570\u636e")});g(!0),f(!0)}}return a}}),(0,J.jsx)(Z.A,{rowSelection:N,bordered:!0,scroll:{y:"40vh"},rowKey:e=>e.id,pagination:!1,dataSource:D,columns:B,onRow:e=>({onClick:()=>{let t=[];t=b.includes(null===e||void 0===e?void 0:e.id)?b.filter((t=>t!==(null===e||void 0===e?void 0:e.id))):[...b,null===e||void 0===e?void 0:e.id],y(t),U({result_variable_array:D.filter((e=>t.includes(e.id))),testData:s})}})})]})}),(0,J.jsx)(re.A,{open:h,setOpen:g,editId:1===b.length&&b[0]&&x?b[0]:null})]})};var fe=a(78178),Ce=a(36990),we=a(22),Ee=a(13313),Se=a(80231);const _e=k.Ay.div`
    display: flex;
    width: 100%;
    height: 100%;
    border-bottom: 1px solid #C0CFFF;
    position: relative;

    .block_mask{
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1000;
    }

    .dynamic-param {
        flex: 1;
        padding: ${(0,D.D0)("24px")} ${(0,D.D0)("36px")} 0;
        overflow-y: auto;
        background: ${D.o$.splitBack};

        display: flex;
        flex-direction: column;

    }

    .dynamic-image {
        background: ${D.o$.splitBack};
        width: 40%;
        padding: ${(0,D.D0)("10px")};
        display: flex;
        align-items: center;
        justify-content: center;
        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    .selected{
        background: rgba(215, 226, 255, 0.4);
    }
`,Re=k.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }
    .disabled {
        cursor: no-drop;
    }

`,je=k.Ay.div`
    min-height: 3vh;
    ${e=>`\n        border-top: 1px solid rgba(20, 115, 245,0.4);\n        width:  ${e.isShort?"70%":"100%"};\n      `}

`,Ie=e=>{let{domId:t,layoutConfig:a,showMenu:l,handleClick:i}=e;const{t:o}=(0,w.Bd)(),r=(0,n.d4)((e=>e.global.roleHiddenDomClass));return(0,J.jsx)(Re,{children:(0,J.jsx)(Se.A,{domId:t,layoutConfig:a,children:l&&(0,J.jsx)("div",{className:`unique-content line ${r}`,onClick:()=>{i()},children:o("\u53c2\u6570\u7f16\u8f91")})})})},Te=e=>Object.values(p.Tj).includes(e)||e.startsWith(p.Tj.\u7ebf)||e.startsWith(p.Tj.\u77ed\u7ebf),Pe=e=>{let{renderParam:t}=e;return(0,J.jsx)(v.A,{sizes:[100],children:(0,J.jsx)("div",{className:"dynamic-param",children:t()})})},ke=e=>{let{selectedImg:t,renderParam:a,dialogId:i}=e;const o=(0,n.d4)((e=>e.template.resultTestData)),r=(0,n.d4)((e=>e.project.optSample)),s=(0,n.d4)((e=>e.project.sampleList)),[c,A]=(0,l.useState)(""),h=d()((async()=>{var e,t;if(o&&1===o.length&&"SAMPLE_PARAM"===(null===o||void 0===o||null===(e=o[0])||void 0===e?void 0:e.type)){var a;const e=null===s||void 0===s?void 0:s.find((e=>(null===e||void 0===e?void 0:e.code)===(null===r||void 0===r?void 0:r.sample_type))),t=null===e||void 0===e||null===(a=e.parameters)||void 0===a?void 0:a.find((e=>{var t;return e.parameter_id===(null===o||void 0===o||null===(t=o[0])||void 0===t?void 0:t.id)}));A(null===t||void 0===t?void 0:t.parameter_img)}else if(o&&1===o.length&&"RESULT_VAR"===(null===o||void 0===o||null===(t=o[0])||void 0===t?void 0:t.type)){var l;const e=await(0,u.cKU)({result_variable_id:null===o||void 0===o||null===(l=o[0])||void 0===l?void 0:l.id});A(null===e||void 0===e?void 0:e.function_img)}else A("")}),300);return(0,l.useEffect)((()=>{h()}),[o]),(0,J.jsxs)(v.A,{sizes:[72,28],minSize:window.innerWidth/10,children:[(0,J.jsx)("div",{className:"dynamic-param",children:a()}),p.Tj.\u7ed3\u679c\u53d8\u91cf===i?(0,J.jsx)("div",{className:"dynamic-image",children:c?(0,J.jsx)("img",{src:c,alt:"#"}):(0,J.jsx)(m.A,{})}):(0,J.jsx)("div",{className:"dynamic-image",children:t?(0,J.jsx)("img",{src:t,alt:"#"}):(0,J.jsx)(m.A,{})})]})},De=e=>{var t;let{id:a,layoutConfig:i,dialogId:r,showImg:d=!0}=e;const v=(0,n.wA)(),{copy:m}=(0,h.A)(),g=(0,s.A)(),{subCorrelationVariables:b,updateInputVariable:y}=(0,A.A)(),x=(0,n.d4)((e=>e.project.sampleData)),f=(0,n.d4)((e=>e.project.sampleList)),C=(0,n.d4)((e=>e.template.dialogs)),[w,E]=(0,l.useState)(),[S,_]=(0,l.useState)([]),[R,j]=(0,l.useState)(),[I,T]=(0,l.useState)(null),[P,k]=(0,l.useState)([]),[D,U]=(0,l.useState)(),[N,B]=(0,l.useState)(!1),[O,M]=(0,l.useState)(!0);(0,Ce.A)({code:null===w||void 0===w?void 0:w.disabled_bind_code,callback:e=>{B(!e)}}),(0,Ce.A)({code:null===w||void 0===w?void 0:w.visible_bind_code,callback:e=>{M(e)}}),(0,l.useEffect)((()=>{B(!1),M(!0),j(),T()}),[r]),(0,l.useEffect)((()=>{L()}),[r,C,g]),(0,l.useEffect)((()=>{V()}),[r,C]);const L=()=>{var e;const t=C.find((e=>e.dialog_id===r));E(t);const a=null===t||void 0===t||null===(e=t.variable_ids)||void 0===e?void 0:e.map((e=>Te(e)?{id:e}:g.find((t=>t.id===e)))).filter(Boolean);_(a),S&&S.length>0&&!o()(null===S||void 0===S?void 0:S.flatMap((e=>{var t;return Object.values(null!==(t=null===e||void 0===e?void 0:e.program_tab)&&void 0!==t?t:{})})),null===a||void 0===a?void 0:a.flatMap((e=>{var t;return Object.values(null!==(t=e.program_tab)&&void 0!==t?t:{})})))&&V();const l=null===a||void 0===a?void 0:a.find((e=>e.id===R));(null===a||void 0===a?void 0:a.length)>0&&l&&Q(l)},V=async()=>{const e=C.find((e=>e.dialog_id===r));if(null!==e&&void 0!==e&&e.variable_ids){const a=await(0,u.L8k)({ids:null===e||void 0===e?void 0:e.variable_ids});var t;if(F(a),(null===a||void 0===a?void 0:a.length)>0)j(null===a||void 0===a||null===(t=a[0])||void 0===t?void 0:t.id),Q(null===a||void 0===a?void 0:a[0])}},Q=e=>{var t;[p.Tj.\u8bd5\u6837\u53c2\u6570,p.Tj.\u8bd5\u6837\u4e0d\u5e26\u53c2\u6570].includes(e.id)&&T(null===f||void 0===f||null===(t=f.find((e=>{var t,a,l;return(null===e||void 0===e?void 0:e.sample_id)===(null===(t=x[0])||void 0===t||null===(a=t.children[0])||void 0===a||null===(l=a.data[0])||void 0===l?void 0:l.sample_id)})))||void 0===t?void 0:t.img);[p.Tj.\u7ed3\u679c\u53d8\u91cf].includes(e.id)&&T(),"pic"in e&&T(null===e||void 0===e?void 0:e.pic)},F=async e=>{const t=await b(e);k(t)},Y=async e=>{try{y({code:e.code},e);await(0,u.Tnc)(e)&&(F(S),H(e,Ee.g.onChange))}catch(t){console.log(t)}},K=(e,t)=>{if(e.startsWith(p.Tj.\u7ebf)||e.startsWith(p.Tj.\u77ed\u7ebf))return(0,J.jsx)(je,{isShort:e.startsWith(p.Tj.\u77ed\u7ebf)});switch(e){case p.Tj.\u8bd5\u6837\u53c2\u6570:case p.Tj.\u8bd5\u6837\u4e0d\u5e26\u53c2\u6570:return(0,J.jsx)(ae,{isBase:e===p.Tj.\u8bd5\u6837\u4e0d\u5e26\u53c2\u6570,isSelected:R===e,onCallBackImg:T});case p.Tj.\u7ed3\u679c\u53d8\u91cf:return(0,J.jsx)(xe,{});default:return(0,J.jsx)(J.Fragment,{})}},H=(e,t)=>{const a=e.program_tab[t];a&&(0,u.O5k)({script:a,result_type:"BOOL"})},W=()=>(0,J.jsx)(J.Fragment,{children:null===S||void 0===S?void 0:S.map(((e,t)=>(0,J.jsx)("div",{onMouseUp:t=>{j(e.id)},onDoubleClick:()=>(null===e||void 0===e?void 0:e.code)&&m(null===e||void 0===e?void 0:e.code),className:R===e.id?"selected":"",onClick:()=>{j(e.id),Te(e.id)||T(e.pic)},children:Te(e.id)?K(e.id):(0,J.jsx)(we.A,{variable:e,scriptData:P,onChange:Y,onTriggerScript:t=>H(e,t),openMarginBottom:!0})},e.id)))});return O?(0,J.jsxs)(_e,{children:[N&&(0,J.jsx)("div",{className:"block_mask",style:{background:`rgba(0,0,0,${null!==(t=null===w||void 0===w?void 0:w.mask_opacity)&&void 0!==t?t:.3})`}}),d&&null!==w&&void 0!==w&&w.is_show_img?(0,J.jsx)(ke,{dialogId:r,selectedImg:I,renderParam:W}):(0,J.jsx)(Pe,{renderParam:W}),D&&(0,J.jsx)(fe.A,{editId:R,mode:"edit",open:D,onOk:()=>{v((0,c.w)()),U(!1)},onCancel:()=>{U(!1)}}),(0,J.jsx)(Ie,{domId:a,showMenu:R&&!Te(R),layoutConfig:i,handleClick:()=>{U(!0)}})]}):(0,J.jsx)(J.Fragment,{})},Ue=(0,l.memo)(De)},30212:(e,t,a)=>{a.d(t,{A:()=>W});var l=a(65043),n=a(80077),i=a(83720),o=a(25055),r=a(16569),d=a(32513),s=a(47419),c=a(11645),A=a(54522),u=a(36497),p=a(55008),v=a(97914),m=a(74117),h=a(94733),g=a(67208),b=a(68374),y=a(75440),x=a(4554),f=a(18650),C=a(36069),w=a(46085),E=a(36950),S=a(97292),_=a(33981),R=a(54962),j=a(10202);const I={labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left"},T=e=>{let{t:t}=e;return[{value:"GRAPH",label:t("\u5728\u66f2\u7ebf\u56fe\u4e2d\u663e\u793a")},{value:"RESULT_TABLE",label:t("\u5728\u7ed3\u679c\u8868\u4e2d\u663e\u793a")},{value:"STATISTICAL_TABLE",label:t("\u5728\u7edf\u8ba1\u8868\u4e2d\u663e\u793a")}]},P=e=>{let{t:t}=e;return[{value:"auto",label:t("\u81ea\u52a8")},{value:"power",label:t("\u5e42\u6570\u578b")},{value:"custom",label:t("\u81ea\u5b9a\u4e49")},{value:"valid",label:t("\u6709\u6548\u6570\u5b57")},{value:"rounding",label:t("\u4fee\u7ea6\u533a\u95f4")}]},k="auto",D="power",U="custom",N="valid",B="rounding",O="NO_SET";var M=a(81143);const L=M.Ay.div`
    height: 60vh;
    width: 100%;
    display: flex;
    /* justify-content: space-between; */

    .modal_left{
        height: 49.5%;
        border: 1px solid ${b.o$.borderGray};
        padding: ${(0,b.D0)("16px")} ${(0,b.D0)("10px")};
        border-radius: ${(0,b.D0)("5px")};
        display: flex;
        overflow-y: auto;
        flex-direction: column;
        background: rgba(255,255,255,0.8);
        box-shadow: 3px 0px 9px 0px rgba(3,36,71,0.08);
        .top-layout {
            display: flex;
            justify-content: space-between;
        }
        .context-layout {
            margin-top: 1vw;
            overflow-y: auto;
        }
        
        >.left_checkbox{
            height: ${(0,b.D0)("160px")};
            /* overflow-y: scroll; */
            border-bottom: 1px solid ${b.o$.borderGray};
        }
    }


    // 中间的部分
    .modal_right {
        margin-left: ${(0,b.D0)("10px")};
        height: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .right_top {
            background: rgba(255,255,255,0.8);
            box-shadow: 3px 0px 9px 0px rgba(3,36,71,0.08);
            border-radius: 5px;
            display: flex;
            height: calc(100% - 33% - 30% - ${(0,b.D0)("32px")});
            box-sizing: border-box;
            overflow-y: auto;
            flex-direction: column;

            .title{
                width: 100%;
                min-height: ${(0,b.D0)("48px")};
                background: #FFFFFF;
                box-shadow: 0px 3px 5px 0px rgba(7,26,112,0.1);
                color: #0838B9;
                display: flex;
                align-items: center;
                font-size: ${(0,b.D0)("16px")};
                padding-left: ${(0,b.D0)("16px")};
                font-weight: bold;
            }

            .context{
                padding: ${(0,b.D0)("12px")};
            }
        }

        .right_middle {
            padding: ${(0,b.D0)("16px")};
            border-radius: ${(0,b.D0)("5px")};
            display: flex;
            flex-direction: column;
            height: 33%;
            box-sizing: border-box;
            overflow-y: auto;
            background: rgba(255,255,255,0.8);
            box-shadow: 3px 0px 9px 0px rgba(3,36,71,0.08);
        }

        .right_bottom {
            height: 32.5%;
            width: 100%;
            box-sizing: border-box;
            overflow: auto;
            border: 1px solid ${b.o$.borderGray};
            border-radius: ${(0,b.D0)("5px")};
            background: rgba(255,255,255,0.8);
            box-shadow: 3px 0px 9px 0px rgba(3,36,71,0.08);
            display: flex;
            /* align-items: center; */
            /* justify-content: space-between; */
            padding: ${(0,b.D0)("20px")} ${(0,b.D0)("39px")} ;
        }
    }

    // 右边操作栏
    .right-container {
        margin-left: ${(0,b.D0)("17px")};
        width: ${(0,b.D0)("149px")};
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .operate-btn{
            margin-bottom: ${(0,b.D0)("17px")};
        }
    }

    .right_mar{
        margin-bottom: 1rem;
    }

    .title_box{
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .center_box{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .left_form_box{
        padding: ${(0,b.D0)("20px")} 0;
        min-height: 10%;
        padding-top: 1rem;
        border-bottom: 1px solid ${b.o$.borderGray};
        max-height: 33%;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .left_bottom_box{
       flex: 1;
       padding: 1rem 0;
       display: flex;
       flex-direction: column;
        overflow-y: auto;
        overflow-x: hidden;
       /* justify-content: space-between; */
    }

    .bottom_mar{
        margin-bottom: 1rem;
    }
`,V=(M.Ay.div`
   display: flex;
   justify-content: space-between;
   width: 100%;
   height: 40vh;

   .left_box{
    padding: ${(0,b.D0)("6px")};
    width: 69%;
    height: 100%;
    overflow-y: auto;
    box-shadow: 2px 0px 7px 0px rgba(3,36,71,0.08);
    border-radius: 4px;

    .item{
       padding: ${(0,b.D0)("6px")};
       cursor: pointer;

       &:hover {
            transition: all 0.3s;
            background-color: dodgerblue;
            color: #fff;
            border-radius: ${(0,b.D0)("8px")};
        }
    }

    .item_select{
        border-radius: ${(0,b.D0)("8px")};
        cursor: pointer;
        padding: ${(0,b.D0)("6px")};
        background-color: dodgerblue;
        color: #fff;
    }
   }

   .right_box{
    width: 29%;
    height: 100%;
    box-shadow: 2px 0px 7px 0px rgba(3,36,71,0.08);
    border-radius: 4px;

    .btn{
        width: 90%;
        margin: 0 auot;

        .btn_box{
             margin-top: ${(0,b.D0)("12px")};
         }
    }
   }
   
`,M.Ay.div`
    display: flex;
   justify-content: space-between;
   width: 100%;
   height: 48vh;
   .left_box{
    padding: ${(0,b.D0)("6px")};
    width: 69%;
    height: 100%;

    .item{
       padding: ${(0,b.D0)("6px")};
       cursor: pointer;

       &:hover {
            transition: all 0.3s;
            background-color: dodgerblue;
            color: #fff;
            border-radius: ${(0,b.D0)("8px")};
        }
    }

    .item_select{
        border-radius: ${(0,b.D0)("8px")};
        cursor: pointer;
        padding: ${(0,b.D0)("6px")};
        background-color: dodgerblue;
        color: #fff;
    }
   }

   .right_box{
    width: 20%;
    height: 100%;
    box-shadow: 2px 0px 7px 0px rgba(3,36,71,0.08);
    border-radius: 4px;

    .btn{
        width: 90%;
        margin: 0 auot;

        .btn_box{
             margin-top: ${(0,b.D0)("12px")};
         }
    }
   }
`),Q=(M.Ay.div`
    position: fixed;
    z-index: 50;
    user-select: none;
    display: none;

    .list {
        border: ${(0,b.D0)("1px")} solid #555;
        border-radius: ${(0,b.D0)("4px")};
        min-width: ${(0,b.D0)("100px")};
        overflow: hidden; /* 处理圆角 */

        .item {
            font-size: ${(0,b.D0)("18px")};
            box-sizing: border-box;
            padding: 0 ${(0,b.D0)("5px")};
            height: ${(0,b.D0)("30px")};
            line-height: ${(0,b.D0)("30px")};
            word-break: keep-all; /* 很重要，否则会换行 */
            background-color: #fff;
            cursor: pointer;

            &:hover {
                background-color: dodgerblue;
                color: #fff;
            }
        }
    }
`,M.Ay.div`
   height: 40vh;
   padding: 1rem;
   display: flex;

   .left{
       width: 50%;
       display: flex;
       flex-direction: column;

    .title{
        height: ${(0,b.D0)("50px")};
    }

    .list{
        padding: 0.5rem;
        overflow-y: auto;
        flex: 1;
        border-radius: 3px;
        border: 1px solid ${b.o$.borderGray};

        >div{
            margin-bottom: ${(0,b.D0)("20px")};
            &:hover {
                cursor: pointer;
                /* border: 1px solid ${b.o$.borderGray}; */
            }
        }

        .select_box{
            background-color: ${b.o$.borderGray};
        }
    }
   }

   .right{
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: ${(0,b.D0)("50px")};
   }
`,M.Ay.div`
        width: 100%;
        padding: ${(0,b.D0)("20px")} 0;
        /* border-bottom: 1px solid ${b.o$.borderGray}; */
        max-height: 33%;
        /* overflow-y: auto;
        overflow-x: hidden; */
`);M.Ay.div`
    .search {
       position: relative;
       border-radius: ${(0,b.D0)("6px")};
       padding: ${(0,b.D0)("8px")};
       height: ${(0,b.D0)("112px")};

  .hide-input {
    width:calc(100% - ${(0,b.D0)("22px")});
    padding: ${(0,b.D0)("6px")};
    height: ${(0,b.D0)("100px")};
    position: absolute;
    overflow: auto;
    overflow-x: hidden;
    caret-color: black;
    border: 1px solid ${b.o$.borderGray};
  }

  .show-input {
      width:calc(100% - ${(0,b.D0)("22px")});
      padding: ${(0,b.D0)("6px")};
      height: ${(0,b.D0)("100px")};
      position: absolute;
      overflow: auto;
      overflow-x: hidden;
      background-color:white;
      border: 1px solid ${b.o$.borderGray};
    }

  .color-blue {
    color: #1677ff;
  }

  .color-orange {
    color: orange;
  }
}
`,M.Ay.div`
    display: flex;
    align-items: center;

    .left{
        width: 32%;
    }
`;var F=a(70579);const J=e=>{let{open:t,setOpen:a,data:i,onOk:o}=e;const{t:r}=(0,m.Bd)(),d=(0,n.d4)((e=>e.template.auxiliaryLineList)),[s,c]=(0,l.useState)([]);(0,l.useEffect)((()=>{i&&c(i)}),[i]);const A=()=>{a(!1)};return(0,F.jsx)(y.A,{footer:null,width:"60vw",open:t,title:r("\u9009\u62e9\u8f85\u52a9\u7ebf"),onCancel:A,children:(0,F.jsx)(R.A,{children:(0,F.jsxs)(V,{children:[(0,F.jsx)("div",{className:"left_box",children:(0,F.jsx)(j.A,{dataSource:d,targetKeys:s,onChange:c,onChangeDelWay:e=>{c(s.filter((t=>t!==e.id)))},render:e=>e.name,oneWayLabel:"name",oneWay:!0})}),(0,F.jsx)("div",{className:"right_box",children:(0,F.jsxs)("div",{className:"btn",children:[(0,F.jsx)(x.A,{block:!0,className:"btn_box",onClick:()=>{o(s)},children:r("\u786e\u5b9a")}),(0,F.jsx)(x.A,{block:!0,className:"btn_box",onClick:A,children:r("\u53d6\u6d88")})]})})]})})})};var Y=a(41445);const K=e=>{let{form:t}=e;const{t:a}=(0,m.Bd)(),n=o.A.useWatch("format_type",t)||0;(0,l.useEffect)((()=>{i()}),[n]);const i=async()=>{const e=await t.getFieldValue("format_info");switch(n){case B:t.setFieldsValue({format_info:{roundMode:0,threshold1:0,threshold2:0,roundType1:1,roundType2:1,roundType3:1,...e}});break;case k:t.setFieldsValue({format_info:{}});break;case D:t.setFieldsValue({format_info:{exponential_decimal_digits:1,...e}});break;case U:t.setFieldsValue({format_info:{decimal_digits:1,...e}});break;case N:t.setFieldsValue({format_info:{significant_digits:1,...e}})}},r={width:(0,b.D0)("201px")};return(0,F.jsx)(Q,{children:(0,F.jsxs)(o.A,{labelAlign:"left",form:t,labelCol:{span:8},wrapperCol:{span:12},children:[(0,F.jsx)(o.A.Item,{name:"format_type",label:`${a("\u683c\u5f0f")}  `,children:(0,F.jsx)(u.A,{showSearch:!0,optionFilterProp:"label",style:r,options:P({t:a})})}),n===D&&(0,F.jsx)(F.Fragment,{children:(0,F.jsx)(o.A.Item,{label:`${a("\u5c0f\u6570\u4f4d\u6570")}(D)`,name:["format_info","exponential_decimal_digits"],children:(0,F.jsx)(v.A,{min:0,placeholder:a("\u8bf7\u8f93\u5165"),style:r})})}),n===U&&(0,F.jsx)(F.Fragment,{children:(0,F.jsx)(o.A.Item,{label:`${a("\u5c0f\u6570\u4f4d\u6570")}(D)`,name:["format_info","decimal_digits"],children:(0,F.jsx)(v.A,{min:0,placeholder:a("\u8bf7\u8f93\u5165"),style:r})})}),n===N&&(0,F.jsx)(F.Fragment,{children:(0,F.jsx)(o.A.Item,{label:`${a("\u6709\u6548\u6570\u5b57")}(G)`,name:["format_info","significant_digits"],children:(0,F.jsx)(v.A,{min:1,style:r,placeholder:a("\u8bf7\u8f93\u5165")})})}),n===B&&(0,F.jsxs)(F.Fragment,{children:[(0,F.jsx)(o.A.Item,{label:a("\u4fee\u7ea6\u65b9\u5f0f"),name:["format_info","roundMode"],hidden:!0,tooltip:(0,F.jsx)(Y.qv,{t:a}),children:(0,F.jsx)(u.A,{style:r,options:Y.$e})}),(0,F.jsx)(o.A.Item,{label:`${a("\u9608\u503c")}(1)`,name:["format_info","threshold1"],hidden:!0,children:(0,F.jsx)(v.A,{style:r})}),(0,F.jsx)(o.A.Item,{label:`${a("\u9608\u503c")}(2)`,hidden:!0,name:["format_info","threshold2"],children:(0,F.jsx)(v.A,{style:r})}),(0,F.jsx)(o.A.Item,{label:a("\u4fee\u7ea6\u4f4d\u6570"),name:["format_info","roundType1"],children:(0,F.jsx)(u.A,{style:r,options:Y.cx})}),(0,F.jsx)(o.A.Item,{label:`${a("\u4fee\u7ea6\u4f4d\u6570")}(2)`,hidden:!0,name:["format_info","roundType2"],children:(0,F.jsx)(u.A,{style:r,options:Y.cx})}),(0,F.jsx)(o.A.Item,{label:`${a("\u4fee\u7ea6\u4f4d\u6570")}(3)`,hidden:!0,name:["format_info","roundType3"],children:(0,F.jsx)(u.A,{style:r,options:Y.cx})})]})]})})},{TextArea:H}=i.A,W=e=>{var t;let{resultIsModal:a,result_variable_id:R,isEdit:j,setLoading:P=()=>{},setLoadingText:k=()=>{},handleCancel:D,handleOk:U=()=>{}}=e;const{t:N}=(0,m.Bd)(),B=(0,n.d4)((e=>e.global.unitList)),M=(0,n.d4)((e=>e.template.actionList)),V=(0,n.d4)((e=>e.template.auxiliaryLineList)),{copy:Q}=(0,_.A)(),[Y]=o.A.useForm(),[W]=o.A.useForm(),[G]=o.A.useForm(),[z]=o.A.useForm(),[X,Z]=(0,l.useState)(""),[q,$]=(0,l.useState)(),[ee,te]=(0,l.useState)(),[ae,le]=(0,l.useState)(null),[ne,ie]=r.Ay.useMessage(),[oe,re]=(0,l.useState)(!1),[de,se]=(0,l.useState)(),[ce,Ae]=(0,l.useState)(!1),[ue,pe]=(0,l.useState)(!1),[ve,me]=(0,l.useState)(!1),[he,ge]=(0,l.useState)([]),[be,ye]=(0,l.useState)([]),[xe,fe]=(0,l.useState)(),Ce=(0,l.useRef)(),we=o.A.useWatch("code",Y);(0,l.useEffect)((()=>{a?R&&j?(P(!0),Ee()):(fe(crypto.randomUUID()),G.setFieldsValue({marking_flag:!1,marking_count:1,marking_action:""})):Se()}),[a]);const Ee=async()=>{try{const e=(0,E.qD)(await(0,g.cKU)({result_variable_id:R}),S.O.RESULT,!0),t=B.find((t=>t.id===e.dimension_id));t&&(le(t),$(t.id),te(t.default_unit_id)),te(e.unit_id),se(e.function_img),ye(e.locked_unit_ids),Z(e.result_function),me(!(null===e||void 0===e||!e.auxiliary_line_flag)),ge((null===e||void 0===e?void 0:e.auxiliary_line_arr)||[]),z.setFieldsValue({format_type:e.format_type,format_info:e.format_info}),Y.setFieldsValue({variable_name:e.variable_name,abbreviation:e.abbreviation,description:e.description,code:e.code,dimension_id:e.dimension_id,unit_id:e.unit_id,locked_unit_ids:e.locked_unit_ids}),W.setFieldsValue({display_modes:(null===e||void 0===e?void 0:e.display_modes)||[]}),G.setFieldsValue({marking_flag:!(null===e||void 0===e||!e.marking_flag)||!1,marking_count:(null===e||void 0===e?void 0:e.marking_count)||1,marking_action:(null===e||void 0===e?void 0:e.marking_action)||""}),P(!1)}catch(e){P(!1),ne.open({type:"error",content:N(`\u9519\u8bef\u4e86${e}`)})}},Se=()=>{Z(""),te(""),$(""),se(""),le([]),ye([]),me(!1),ge([]),z.setFieldsValue({format_type:""}),Y.setFieldsValue({variable_name:"",abbreviation:"",description:"",code:"",dimension_id:"",unit_id:"",locked_unit_ids:""}),W.setFieldsValue({display_modes:[]}),G.setFieldsValue({marking_flag:!1,marking_count:1,marking_action:""})},_e=()=>(0,F.jsxs)("div",{children:[(0,F.jsx)("p",{children:N("1. \u683c\u5f0f\uff1a{lo m}")}),(0,F.jsx)("p",{children:N("1. \u683c\u5f0f\uff1a{hi m}")})]});return(0,F.jsx)(F.Fragment,{children:a&&(0,F.jsxs)(F.Fragment,{children:[ie,(0,F.jsxs)(L,{ref:Ce,children:[(0,F.jsxs)("div",{className:"modal_right",children:[(0,F.jsxs)("div",{className:"modal_left",children:[(0,F.jsx)("div",{className:"left_checkbox",children:(0,F.jsx)(o.A,{form:W,...I,children:(0,F.jsx)(o.A.Item,{rules:[{required:!1,message:N("\u8bf7\u81f3\u5c11\u9009\u62e9\u4e00\u4e2a\u663e\u793a\u4f4d\u7f6e")}],name:"display_modes",labelCol:{span:4},wrapperCol:{span:20},children:(0,F.jsx)(d.A.Group,{children:(0,F.jsx)(s.A,{children:T({t:N}).map(((e,t)=>(0,F.jsx)(c.A,{span:"24",children:(0,F.jsx)(d.A,{value:e.value,children:N(e.label)})},t)))})})})})}),(0,F.jsx)(K,{form:z})]}),(0,F.jsxs)("div",{className:"modal_left",children:[(0,F.jsxs)("div",{className:"top-layout",children:[(0,F.jsx)(d.A,{checked:ve,onChange:e=>me(e.target.checked),children:N("\u8f85\u52a9\u7ebf\u6dfb\u52a0")}),(0,F.jsx)(x.A,{onClick:()=>pe(!0),children:N("\u9009\u62e9\u8f85\u52a9\u7ebf")})]}),(0,F.jsx)("div",{className:"context-layout",children:(0,F.jsx)(A.A,{size:"small",bordered:!0,dataSource:V.filter((e=>null===he||void 0===he?void 0:he.includes(e.id))),renderItem:e=>(0,F.jsx)(A.A.Item,{children:N(null===e||void 0===e?void 0:e.name)})})})]})]}),(0,F.jsxs)("div",{className:"modal_right",children:[(0,F.jsx)("div",{className:"right_bottom",children:(0,F.jsx)(o.A,{form:Y,labelAlign:"left",...I,children:(0,F.jsxs)(s.A,{children:[(0,F.jsx)(c.A,{span:12,children:(0,F.jsx)(o.A.Item,{name:"variable_name",rules:[{required:!0,message:N("\u8bf7\u8f93\u5165\u540d\u79f0")}],label:N("\u540d\u79f0"),children:(0,F.jsx)(i.A,{})})}),(0,F.jsx)(c.A,{span:12,children:(0,F.jsx)(o.A.Item,{name:"abbreviation",tooltip:(0,F.jsx)(_e,{}),rules:[{required:!0,validator:async(e,t)=>t?function(e){const t=[];for(const a of e)if("{".includes(a))t.push(a);else if("}".includes(a)){const e=t.pop();if(!e||"{".indexOf(e)!=="}".indexOf(a))return!1}return 0===t.length}(t)?Promise.resolve():Promise.reject(new Error("\u62ec\u53f7\u4e0d\u5339\u914d")):Promise.reject(new Error("\u8bf7\u8f93\u5165\u7f29\u5199"))}],label:`${N("\u7f29\u5199")}\uff1a`,children:(0,F.jsx)(i.A,{})})}),(0,F.jsx)(c.A,{span:12,children:(0,F.jsx)(o.A.Item,{name:"code",rules:[{required:!0,message:N("\u8bf7\u8f93\u5165\u5185\u90e8\u540d\u79f0")}],label:N("\u5185\u90e8\u540d"),onClick:e=>j&&Q(`${S.O.RESULT+we}`),children:(0,F.jsx)(i.A,{disabled:j,title:`t(${S.O.RESULT})${we}`,prefix:S.O.RESULT})})}),(0,F.jsx)(c.A,{span:12,children:(0,F.jsx)(o.A.Item,{label:N("\u91cf\u7eb2"),rules:[{required:!1,message:N("\u8bf7\u9009\u62e9\u91cf\u7eb2")}],name:"dimension_id",children:(0,F.jsx)(u.A,{showSearch:!0,allowClear:!0,optionFilterProp:"label",value:q,onChange:e=>{if(e){const t=B.find((t=>t.id===e));t&&(le(t),$(t.id),Y.setFieldValue("unit_id",t.default_unit_id),te(t.default_unit_id))}else le(""),$(""),Y.setFieldValue("unit_id",""),te("")},options:null===B||void 0===B?void 0:B.map((e=>({label:N(e.name),value:e.id})))})})}),(0,F.jsx)(c.A,{span:12,children:(0,F.jsx)(o.A.Item,{label:N("\u5355\u4f4d\uff1a"),rules:[{required:!1,message:N("\u8bf7\u9009\u62e9\u5355\u4f4d")}],name:"unit_id",children:(0,F.jsx)(u.A,{showSearch:!0,allowClear:!0,optionFilterProp:"label",value:ee,onChange:te,options:null===ae||void 0===ae||null===(t=ae.units)||void 0===t?void 0:t.map((e=>({label:N(e.name),value:e.id})))})})}),(0,F.jsx)(c.A,{span:12,children:(0,F.jsx)(o.A.Item,{name:"description",rules:[{required:!1,message:N("\u8bf7\u8f93\u5165\u63cf\u8ff0")}],label:N("\u63cf\u8ff0"),children:(0,F.jsx)(H,{rows:3,maxLength:30})})})]})})}),(0,F.jsx)("div",{className:"right_bottom",children:(0,F.jsxs)(s.A,{gutter:[10,16],align:"middle",children:[(0,F.jsx)(c.A,{span:"5",align:"end",children:N("\u7ed3\u679c\u51fd\u6570\uff1a")}),(0,F.jsx)(c.A,{span:"6",children:(0,F.jsx)(i.A,{disabled:!0,value:X,placeholder:N("\u8bf7\u9009\u62e9")})}),(0,F.jsx)(c.A,{span:"13",children:(0,F.jsx)(x.A,{size:"small",onClick:()=>{re(!0)},children:N("\u9009\u62e9\u51fd\u6570")})}),(0,F.jsxs)(c.A,{span:"5",align:"end",children:[N("\u793a\u610f\u56fe"),"\uff1a"]}),(0,F.jsx)(c.A,{span:"6",children:(0,F.jsx)(p.A,{src:de||f.Np,width:60,height:60,preview:!1})}),(0,F.jsx)(c.A,{span:"13",children:(0,F.jsx)(x.A,{size:"small",icon:(0,F.jsx)(h.A,{}),onClick:()=>Ae(!0),children:N("\u9009\u62e9\u56fe\u7247")})})]})}),(0,F.jsx)("div",{className:"right_bottom",children:(0,F.jsx)(o.A,{form:G,labelAlign:"left",children:(0,F.jsxs)(s.A,{children:[(0,F.jsx)(c.A,{span:24,children:(0,F.jsx)(o.A.Item,{valuePropName:"checked",name:"marking_flag",children:(0,F.jsx)(d.A,{children:N("\u624b\u5de5\u6807\u8bb0")})})}),(0,F.jsx)(c.A,{span:24,children:(0,F.jsx)(o.A.Item,{label:N("\u6807\u8bb0\u70b9\u6570"),name:"marking_count",children:(0,F.jsx)(v.A,{style:{width:(0,b.D0)("160px")},min:1})})}),(0,F.jsx)(c.A,{span:24,children:(0,F.jsx)(o.A.Item,{label:N("\u89e6\u53d1\u52a8\u4f5c"),name:"marking_action",children:(0,F.jsx)(u.A,{allowClear:!0,style:{width:(0,b.D0)("160px")},fieldNames:{label:"action_name",value:"action_id"},options:null===M||void 0===M?void 0:M.map((e=>({...e,label:N(e.label)})))})})})]})})})]}),(0,F.jsxs)("div",{className:"right-container",children:[(0,F.jsxs)("div",{children:[(0,F.jsx)(x.A,{className:"operate-btn",block:!0,onClick:async()=>{try{var e;const t=await z.validateFields(),a=await W.validateFields(),l=await Y.validateFields(),n=await G.validateFields();k(N("\u4e0a\u4f20\u4e2d\uff0c\u8bf7\u7b49\u5f85...")),P(!0);const i=(0,E.qD)({...l,...a,...n,...t,unit_id:null!==ee&&void 0!==ee?ee:"",dimension_id:null!==q&&void 0!==q?q:"",function_img:de,reasonable_val:O,reasonable_info:{},locked_unit_ids:be,result_function:X,reasonable_val_type:"",marking_action:null!==(e=n.marking_action)&&void 0!==e?e:"",marking_flag:n.marking_flag?1:0,auxiliary_line_flag:ve?1:0,auxiliary_line_arr:he},S.O.RESULT);R&&j?await(0,g.hG1)({...i,result_variable_id:R}):await(0,g.NWS)({...i,result_variable_id:xe}),ne.open({type:"warning",content:N("\u64cd\u4f5c\u6210\u529f")}),U({...i,result_variable_id:j?R:xe}),P(!1),k(""),D()}catch(a){var t;P(!1),k("");const e=null===a||void 0===a||null===(t=a.errorFields[0])||void 0===t?void 0:t.errors[0];ne.open({type:"error",content:N(e||"\u8bf7\u586b\u5199\u5fc5\u586b\u9879\u540e\u518d\u8bd5")})}},children:N("\u4fdd\u5b58")}),(0,F.jsx)(x.A,{className:"operate-btn",block:!0,onClick:D,children:N("\u53d6\u6d88")})]}),(0,F.jsx)("div",{className:"bottom-btn",children:(0,F.jsx)(x.A,{className:"help-btn",block:!0,children:N("\u5e2e\u52a9")})})]})]}),ue&&(0,F.jsx)(J,{open:ue,data:he,onOk:e=>{ge(e),pe(!1)},setOpen:pe}),(0,F.jsx)(w.RN,{open:oe,module:w.et.\u7ed3\u679c\u53d8\u91cf,script:X,onOk:e=>{Z(e),re(!1)},onCancel:()=>{re(!1)}}),(0,F.jsx)(y.A,{title:N("\u9009\u62e9\u56fe\u7247"),width:1e3,height:500,open:ce,onCancel:()=>Ae(!1),footer:null,children:(0,F.jsx)(C.A,{updateImage:e=>{se(e),Ae(!1)},t:N})})]})})}},30780:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=a(15637),i=a(67208);const o=()=>{const e=(0,l.wA)(),t=async t=>{try{const a=await(0,i.AD8)(t);a&&e({type:n.pn,param:a})}catch(a){console.log(a)}};return{initTableConfigData:t,saveTableConfigData:async e=>{const a=await(0,i.M0J)(e);return a&&t(),a}}}},33013:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=a(67208),i=a(59066);const o=()=>{const e=(0,l.wA)();return{initProjectList:async t=>{const a=await(0,n.wD6)(t);a&&e({type:i.v,param:{projectList:null===a||void 0===a?void 0:a.filter((e=>!e.is_extrapolation_project)),extrapolationProjectList:null===a||void 0===a?void 0:a.filter((e=>e.is_extrapolation_project))}})}}}},33154:(e,t,a)=>{a.d(t,{A:()=>d});a(65043);var l=a(80077),n=a(754),i=a(63612),o=a(67208);const r=e=>e.map((e=>({...e,units:e.units.filter((e=>e.visible))}))),d=()=>{const e=(0,l.wA)(),t=()=>{e({type:i.eX,param:[]})};return{initUnitsData:async()=>{try{const a=await(0,o._jD)();a&&(t(),e({type:i.eX,param:r(a)}))}catch(a){console.log(a)}},clearUnitsData:t,getSignalChannelUnit:e=>{if(e){var t;const a=null!==(t=(0,n.B)("template","signalList").find((t=>t.code===e)))&&void 0!==t?t:{dimension_id:void 0},{dimension_id:l}=a;return(0,n.B)("global","unitList").find((e=>e.id===l))}return{}},initSysUnitsData:async()=>{try{const a=await(0,o.BL1)();a&&(t(),e({type:i.eX,param:r(a)}))}catch(a){console.log(a)}},findUnit:(e,t)=>{if(e){var a,l;const i=(null===(a=(0,n.B)("global","unitList"))||void 0===a?void 0:a.find((t=>t.id===e)))||{};return null===i||void 0===i||null===(l=i.units)||void 0===l?void 0:l.find((e=>{var a;return null!==(a=e.id===t)&&void 0!==a?a:null===i||void 0===i?void 0:i.default_unit_id}))}return{}}}}},33981:(e,t,a)=>{a.d(t,{A:()=>i});a(65043);var l=a(56543),n=a(16569);const i=()=>({copy:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l.Je.\u6587\u672c;try{switch(t){case l.Je.\u56fe\u7247:await navigator.clipboard.write([new ClipboardItem({"image/png":e})]);break;case l.Je.\u6587\u672c:default:await navigator.clipboard.writeText(e)}n.Ay.success("\u590d\u5236\u6210\u529f\uff01")}catch(a){n.Ay.error("\u590d\u5236\u5931\u8d25\uff01"),console.log(a)}}})},34154:(e,t,a)=>{a.d(t,{Gt:()=>l,NR:()=>i,qw:()=>n});const l={labelCol:{span:9},wrapperCol:{span:15}},n={width:"11vw"},i={"\u52a8\u4f5c":"action","\u811a\u672c":"script"}},36069:(e,t,a)=>{a.d(t,{A:()=>c});var l=a(65043),n=(a(80077),a(67208)),i=a(36950),o=a(75122),r=a(81143);a(68374);const d=r.Ay.div`
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    justify-content: start;
    overflow-y: scroll;
    overflow-x: hidden;
    height: 50vh;
    gap: 16px;
    padding: 16px;
`;var s=a(70579);const c=e=>{var t;const a=(0,l.useRef)(),[r,c]=(0,l.useState)([]),A=(0,l.useRef)({page:0,size:20,name:""}),u=(0,l.useRef)({});(0,l.useEffect)((()=>{p()}),[]);const p=async e=>{const t={...A.current,...e};A.current=t;const a=await(0,n.M5Z)(t);a&&(c(a),u.current=a)};a.current=(0,l.useMemo)((()=>new IntersectionObserver((e=>{e.forEach((e=>{if(e.intersectionRatio>0){const t=e.target.children[0];t.src&&t.src===t.dataset.src||(t.src=t.dataset.src);const a=5;u.current.total>u.current.data.length&&e.target.dataset.index===String(u.current.data.length-a)&&(async e=>{const t={...A.current,...e};A.current=t;const a=await(0,n.M5Z)(t);a&&(u.current={...a,data:[...new Map([...u.current.data,...a.data].map((e=>[e.id,e]))).values()]},c({...a,data:u.current.data}))})({page:A.current.page+1})}}))}))),[]),(0,l.useEffect)((()=>()=>{a.current.disconnect()}),[]);return(0,s.jsx)(d,{children:null===r||void 0===r||null===(t=r.data)||void 0===t?void 0:t.map(((t,l)=>(0,s.jsx)(o.A,{imgObj:t,index:l,onClick:()=>(async t=>{const a=await(0,n.QpR)(t.id),l=await(0,i.ZJ)(new File([a],"test.png"));null!==e&&void 0!==e&&e.updateImage&&(null===e||void 0===e||e.updateImage(l,null===t||void 0===t?void 0:t.id)),null!==e&&void 0!==e&&e.handleClick&&(null===e||void 0===e||e.handleClick(null===t||void 0===t?void 0:t.id))})(t),observer:null===a||void 0===a?void 0:a.current},l)))})}},36581:(e,t,a)=>{a.d(t,{A:()=>v});var l=a(80077),n=a(16569),i=a(74117),o=a(67208),r=(a(45303),a(70916)),d=a(34458),s=a(68130),c=a(15701),A=a(8237),u=a(36950),p=a(73206);const v=()=>{(0,l.wA)();const{onOpenProject:e}=(0,r.A)(),t=(0,l.d4)((e=>e.global.stationList)),a=((0,l.d4)((e=>e.global.cfgList)),(0,l.d4)((e=>e.global.optStation))),v=(0,l.d4)((e=>e.global.globalMonitoringProjectID)),{initStationInfo:m}=(0,s.A)(),{initGlobalProjectID:h}=(0,c.A)(),{bingStationProject:g,bingMonitoringRelationProject:b}=(0,p.A)(),y=(0,l.d4)((e=>e.system.projectList)),{t:x}=(0,i.Bd)(),f=async(l,i)=>{const{pageId:o,optStation:r=a}=i||{};if(o===A.CH["\u9ed8\u8ba4\u9875\u9762"]){if(0===t.length)throw n.Ay.error(x("\u8bf7\u5148\u914d\u7f6e\u7ad9")),Error("");if(1===t.length)try{var d,s,c,u,p;if(null!==(d=t[0])&&void 0!==d&&d.projectId&&(null===(s=t[0])||void 0===s?void 0:s.projectId)!==l){var m;const e=C(null===(m=t[0])||void 0===m?void 0:m.projectId);throw n.Ay.error(`${x("\u5f53\u524d\u7ad9\u5df2\u7ecf\u6253\u5f00\u4e86\u9879\u76ee")}[${null===e||void 0===e?void 0:e.project_name}]\uff0c${x("\u8bf7\u5148\u5173\u95ed\u5df2\u7ecf\u6253\u5f00\u7684\u9879\u76ee")}`),Error("")}if(null===(c=t[0])||void 0===c||!c.projectId){try{await g({optStation:t[0],projectId:l})}catch(h){throw Error("")}return void await e(l,{...i,optStation:t[0]})}if(null!==(u=t[0])&&void 0!==u&&u.projectId&&(null===(p=t[0])||void 0===p?void 0:p.projectId)===l)return void await e(l,{...i,optStation:t[0]})}catch(h){throw Error("")}if(t.length>1)try{if(r&&r.id){if(null===r||void 0===r||!r.projectId){try{await g({optStation:r,projectId:l})}catch(h){throw Error("")}return void await e(l,i)}if(null!==r&&void 0!==r&&r.projectId&&(null===r||void 0===r?void 0:r.projectId)!==l){const e=C(null===r||void 0===r?void 0:r.projectId);throw n.Ay.error(`${x("\u5f53\u524d\u7ad9\u5df2\u7ecf\u6253\u5f00\u4e86\u9879\u76ee")}[${null===e||void 0===e?void 0:e.project_name}]\uff0c${x("\u8bf7\u5148\u5173\u95ed\u5df2\u7ecf\u6253\u5f00\u7684\u9879\u76ee")}`),Error("")}if(null!==r&&void 0!==r&&r.projectId&&(null===r||void 0===r?void 0:r.projectId)===l)return void await e(l,i)}if(v&&v===Number(l))return void await e(l,i);if(v&&v!==Number(l))throw n.Ay.error(x("\u5f53\u524d\u7ad9\u6b63\u5728\u8fd0\u884c\u5176\u4ed6\u9879\u76ee\uff0c\u65e0\u6cd5\u6253\u5f00")),Error("");if(!v)return b({optStation:r,projectId:l}),void await e(l,i);throw Error("")}catch(h){throw Error(h)}}else await e(l,i)},C=e=>y.find((t=>t.project_id===e));return{openProject:f,openTemplate:async(e,t)=>{const a=Number(e),l=await(0,o.$Fq)({template_id:a});if(l){const e=(0,d.ug)(),n=await(0,o.Cqh)({template_id:a});(0,d.W5)([{...n,recently_time:(0,u.Ln)(),use_name:e.name,use_id:e.id},...(0,d.IW)().filter((e=>e.template_id!==n.template_id))]),await f(null===l||void 0===l?void 0:l.project_id,t)}}}}},36990:(e,t,a)=>{a.d(t,{A:()=>d});var l=a(67208),n=a(65043),i=a(44409),o=a(60383);const r=e=>{let{subTopic:t,callback:a=()=>{}}=e;const{useSubscriber:l}=(0,i.A)(),r=(0,n.useRef)();(0,n.useEffect)((()=>(t&&d(),()=>{var e;null===(e=r.current)||void 0===e||e.close()})),[t,a]);const d=async()=>{r.current=await l(t);for await(const[l,i]of r.current){if(decodeURIComponent(l)===t){let t;try{t=o.D(i)}catch(e){try{t=JSON.parse(i)}catch(n){console.error("\u6570\u636e\u89e3\u6790\u5931\u8d25",n)}}a(t)}}}},d=e=>{let{code:t,callback:a=()=>{}}=e;r({subTopic:t,callback:(0,n.useCallback)((e=>a(null===e||void 0===e?void 0:e.Value)),[a])}),(0,n.useEffect)((()=>{t&&i()}),[t]);const i=async()=>{try{const i=await(0,l.Hgb)({codes:[t]});var e,n;if(i)a(null===i||void 0===i||null===(e=i[0])||void 0===e||null===(n=e.default_val)||void 0===n?void 0:n.value)}catch(i){console.log(i)}}}},37104:(e,t,a)=>{a.d(t,{A_:()=>l,Ud:()=>c,Zp:()=>A,dj:()=>i,jG:()=>u,oE:()=>n,qo:()=>o,sK:()=>s,wL:()=>r,xj:()=>d});const l=e=>{let{t:t}=e;return[{value:"NONE",label:t("\u4e0d\u8bbe\u7f6e")},{value:"FRONT",label:t("\u524d")},{value:"BEHIND",label:t("\u540e")}]},n=e=>{let{t:t}=e;return[{value:"TEXT&ICON",label:t("\u663e\u793a\u56fe\u6807\u4e0e\u6587\u672c")},{value:"ICON",label:t("\u4ec5\u663e\u793a\u56fe\u6807")},{value:"TEXT",label:t("\u4ec5\u663e\u793a\u6587\u5b57")}]},i={"\u65b9\u89d2":"corner","\u5706\u89d2":"circle"},o={"\u5de6\u5bf9\u9f50":"left","\u53f3\u5bf9\u9f50":"right"},r={FRONT:"FRONT",TEXT_ICON:"TEXT&ICON",ICON:"ICON",TEXT:"TEXT",BEHIND:"BEHIND",NONE:"NONE"},d={"\u540d\u79f0":{label:"\u540d\u79f0",name:"shortcut_name",initVal:null},"\u5feb\u6377\u952e":{label:"\u5feb\u6377\u952e",name:"shortcut_key",initVal:null},"\u52a8\u4f5c":{label:"\u52a8\u4f5c",name:"action_id",initVal:null},"\u6743\u9650":{label:"\u6743\u9650",name:"power",initVal:0},"\u5206\u9694\u7ebf":{label:"\u5206\u9694\u7ebf",name:"halving_line",initVal:r.NONE},"\u53ef\u7f16\u8f91":{label:"\u53ef\u7f16\u8f91",name:"input_code"},"\u53c2\u6570\u754c\u9762":{label:"\u53c2\u6570\u754c\u9762",name:"dialog_id",initVal:""},"\u56fe\u6807":{label:"\u56fe\u6807",name:"icon",initVal:""},"\u63cf\u8ff0":{label:"\u63cf\u8ff0",name:"tip",initVal:""},"\u811a\u672c":{label:"\u811a\u672c",name:"program_tab",initVal:""},"\u5143\u7d20\u53f3\u4fa7\u7559\u6709\u95f4\u9699":{label:"\u53f3\u4fa7\u7559\u6709\u95f4\u9699",name:"gap_flag",initVal:0},"\u9700\u8981\u767b\u5f55\u6821\u9a8c":{label:"\u9700\u8981\u767b\u5f55\u6821\u9a8c",name:"need_login_check",initVal:0}},s={"\u80cc\u666f\u6837\u5f0f":{label:"\u80cc\u666f\u6837\u5f0f",name:"bg_style",initVal:i.\u65b9\u89d2},"logo\u663e\u793a":{label:"logo\u663e\u793a",name:"logo_flag",initVal:!0},"logo\u663e\u793a\u4f4d\u7f6e":{label:"logo\u663e\u793a\u4f4d\u7f6e",name:"logo_position",initVal:o.\u53f3\u5bf9\u9f50},"logo\u4e0a\u4f20":{label:"logo\u4e0a\u4f20",name:"logo",initVal:""}},c={labelCol:{span:8},wrapperCol:{span:16}},A={labelCol:{span:8},wrapperCol:{span:16}},u="85b303d7-5008-4bec-9429-f75a971bcca3"},38134:(e,t,a)=>{a.d(t,{G1:()=>n,J$:()=>l});const l={"\u6570\u5b57":"Number","\u6587\u672c":"Text","\u65e5\u671f":"DateTime","\u9009\u62e9":"Select","\u52fe\u9009":"Checkbox","\u6309\u94ae":"Button"},n={showName:"",code:"",type:l.\u6587\u672c,typeParam:{dimensionId:"",unitId:"",options:[]},openCorrelation:!1,correlationCode:""}},40940:(e,t,a)=>{a.d(t,{A:()=>A});a(65043);var l=a(6051),n=a(83720),i=a(95206),o=a(46989),r=a(4178),d=a(81143);a(68374);const s=d.Ay.div`
    
`;var c=a(70579);const A=e=>{let{value:t="",onChange:a,style:d,placeholder:A,disabled:u,type:p,param:v}=e;const{openDialog:m}=(0,r.A)(),h=e=>{a(e)};return(0,c.jsx)(s,{children:(0,c.jsxs)(l.A,{children:[(0,c.jsx)(n.A,{disabled:!0,value:(null===t||void 0===t?void 0:t.replace(/\//g,"\\"))||"",style:d,placeholder:A,title:(null===t||void 0===t?void 0:t.replace(/\//g,"\\"))||""}),!u&&(0,c.jsx)(i.Ay,{onClick:()=>(()=>{const e=m(p,v);if(e)if(null!==e&&void 0!==e&&e.canceled)h(t);else{var a;let t=null!==(a=e.filePath)&&void 0!==a?a:e.filePaths[0];t=t.replace(/\//g,"\\"),h(t)}})(),icon:(0,c.jsx)(o.A,{})})]})})}},41086:(e,t,a)=>{a.d(t,{CL:()=>n,K6:()=>i,gT:()=>o,wS:()=>l});const l=(e,t)=>{const{id:a,delete_flag:l,order_num:n,label:i,layout:r,page_id:d,parent_id:s,visible_bind_code:c,disabled_bind_code:A,mask_opacity:u}=e,p={binder_name:i,binder_id:a,parent_id:s,order_num:n,delete_flag:l,created_user_id:null===e||void 0===e?void 0:e.user_id,page_id:d,...Object.fromEntries(Object.entries({visible_bind_code:c,disabled_bind_code:A,mask_opacity:u}).filter((e=>{let[t,a]=e;return null!==a})))};return r&&(p.layout=o(r,t)),p},n=e=>{const{binder_id:t,binder_name:a,order_num:l,delete_flag:n,layout:i,page_id:r,widget_data_source:d,parent_id:s,visible_bind_code:c,disabled_bind_code:A,mask_opacity:u,is_lock:p}=e,v={key:t,label:a,type:"tab",id:t,parent_id:s,children:"",order_num:l,delete_flag:n,user_id:null===e||void 0===e?void 0:e.created_user_id,page_id:r,widget_data_source:d,visible_bind_code:c,is_lock:p,disabled_bind_code:A,mask_opacity:u};return i&&(v.layout=o(i,t)),v},i=e=>{const{binder_id:t,type:a,direction:l,sizes:n,name:o,layout_id:r,children:d,id:s,data_source:c,widget_id:A,page_id:u,widget_data_source:p,parent_id:v,is_lock:m}=e;return{id:s,parent_id:v,binder_id:t,data_source:c,widget_data_source:p,name:o,type:a,direction:l,sizes:n,view:null,layout_id:r,widget_id:A,page_id:u,is_lock:!!m,children:d?d.map((e=>i(e))):[]}},o=(e,t)=>{const{id:a,name:l,type:n,direction:i,sizes:r,layout_id:d,children:s,data_source:c,widget_id:A,page_id:u,widget_data_source:p,parent_id:v,is_lock:m}=e;return{binder_id:t,id:a,parent_id:v,widget_id:A,type:n,direction:i,sizes:r,is_lock:!!m,name:l,data_source:c,layout_id:d,view:null,page_id:u,widget_data_source:p,children:s&&0!==(null===s||void 0===s?void 0:s.length)?s.map((e=>o(e,t))):[]}}},41389:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(65043),n=a(80077),i=a(32099);const o=()=>(0,i.Mz)([e=>e.inputVariable.inputVariableMap,e=>e.inputVariable.booleanCodeList],((e,t)=>t.map((t=>e.get(t))))),r=()=>{const e=(0,l.useMemo)(o,[]);return(0,n.d4)((t=>e(t)))}},41445:(e,t,a)=>{a.d(t,{$e:()=>m,YM:()=>o,Yz:()=>i,cx:()=>v,dk:()=>d,hX:()=>p,qv:()=>h,r1:()=>c,vU:()=>r,yw:()=>s,ze:()=>n});a(65043);var l=a(70579);const n={NOT_CUSTOM:"not_custom",CUSTOM:"custom"},i=e=>{let{t:t}=e;return[{value:n.NOT_CUSTOM,label:t("\u975e\u81ea\u5b9a\u4e49")},{value:n.CUSTOM,label:t("\u81ea\u5b9a\u4e49")}]},o={VARIABLE:"variable",VARIABLE_LIST:"variable_list",SIGNAL:"signal",RESULT:"result",RELATED:"related"},r=[{value:o.SIGNAL,label:"\u4f20\u611f\u5668\u7a97\u53e3"},{value:o.VARIABLE,label:"\u8f93\u5165\u53d8\u91cf\u7a97\u53e3"},{value:o.RESULT,label:"\u7ed3\u679c\u53d8\u91cf\u7a97\u53e3"},{value:o.VARIABLE_LIST,label:"\u8f93\u5165\u53d8\u91cf\u5217\u8868"}],d={DIALOG:"dialog",CONTROL:"control"},s=(d.DIALOG,d.CONTROL,{ARRAY_QUEUE:"ArrayQueue",CIRCLE_ARRAY_QUEUE:"CircleArrayQueue",NOT:"\u65e0"}),c=e=>{let{t:t}=e;return[{value:s.NOT,label:t("\u65e0")},{value:s.ARRAY_QUEUE,label:t("\u7ebf\u6027")},{value:s.CIRCLE_ARRAY_QUEUE,label:t("\u73af\u5f62")}]},A="expand",u="clear",p=e=>{let{t:t}=e;return[{value:A,label:t("\u6269\u5bb9")},{value:u,label:t("\u6e05\u9664")}]},v=[{value:.1,label:"0.1"},{value:1,label:"1"},{value:10,label:"10"},{value:100,label:"100"},{value:1e3,label:"1000"},{value:.05,label:"0.05"},{value:.5,label:"0.5"},{value:5,label:"5"},{value:50,label:"50"},{value:500,label:"500"}],m=[{value:0,label:"0"},{value:1,label:"1"}],h=e=>{let{t:t}=e;return(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{children:t("1. \u7c7b\u578b\u4e3a0,\u6309\u4fee\u7ea6\u4f4d\u65701\u3002")}),(0,l.jsx)("p",{children:t("2. \u7c7b\u578b\u4e3a1,\u4e14\u3010\u503c\u3011\u5c0f\u4e8e\u9608\u503c1,\u6309\u4fee\u7ea6\u4f4d\u65701\u3002")}),(0,l.jsx)("p",{children:t("3. \u7c7b\u578b\u4e3a1,\u4e14\u3010\u503c\u3011\u5927\u4e8e\u7b49\u4e8e\u9608\u503c2,\u6309\u4fee\u7ea6\u4f4d\u65703\u3002")})]})}},41753:(e,t,a)=>{a.d(t,{A:()=>f});a(65043);var l=a(80077),n=a(16569),i=a(34458),o=a(36950),r=a(74117),d=a(63612),s=a(15637),c=a(67208),A=a(4178);const u="webm",p="mp4";let v,m,h,g,b,y=[];function x(e,t){this.name=e,this.message=t}const f=()=>{const e=(0,l.wA)(),{saveVideoFile:t,getReadFile:a,onListenIPC:f,offListenIPC:C}=(0,A.A)(),{t:w}=(0,r.Bd)(),E=async()=>{try{const t=await(0,c.oEV)();t&&e({type:s.o7,param:t})}catch(t){console.log(t)}},S=async()=>{try{return b||(b=await navigator.mediaDevices.getUserMedia({video:!0})),b}catch(e){if("NotAllowedError"===e.name)throw new x("UserMediaStream","\u7528\u6237\u7981\u6b62\u8bbf\u95ee\u6444\u50cf\u5934");if("NotReadableError"===e.name)throw new x("UserMediaStream","\u6444\u50cf\u5934\u88ab\u5360\u7528\u6216\u65e0\u6cd5\u8bbf\u95ee");throw new x("UserMediaStream",`\u65e0\u6cd5\u8bbf\u95ee\u6444\u50cf\u5934, ${e.message}`)}};return{initVideoData:E,getVideoUrl:async e=>{try{if(e){console.log(e);const t=await(e=>{let t,l,n;return new Promise(((i,o)=>{let r=new Uint8Array;a({path:e}),t=(e,t)=>{const a=null===t||void 0===t?void 0:t.data,l=r.length+a.length,n=new Uint8Array(l);n.set(r,0),n.set(a,r.length),r=n},l=(e,a)=>{C("video-data",t),C("video-data-end",l),C("video-error",n),i(r)},n=(e,a)=>{C("video-data",t),C("video-data-end",l),C("video-error",n),o(a.error)},f("video-data",t),f("video-data-end",l),f("video-error",n)}))})(`${(0,i.Ti)()}/${e.video_file}`);if(t){const e=new Blob([t],{type:`video/${p}`});return URL.createObjectURL(e)}}return null}catch(t){return null}},readyCameraREC:async a=>{v=void 0,v=new MediaRecorder(a||await S(),{mimeType:`video/${u}`}),v.ondataavailable=e=>{y.push(e.data)},v.onstop=()=>{(async()=>{try{if(e({type:d.J6,param:!0}),e({type:d.LR,param:"\u6b63\u5728\u751f\u6210\u89c6\u9891\uff0c\u8bf7\u8010\u5fc3\u7b49\u5f85"}),y&&y.length>0){const e=new Blob(y,{type:`video/${u}`}),a=await e.arrayBuffer(),l=Buffer.from(a);await t({buffer:l,path:`${(0,i.Ti)()}/video/${(0,o.n1)()}/video/${m}.${p}`,folderPath:`${(0,i.Ti)()}/video/${(0,o.n1)()}/video`}),(0,c.VJ8)({video_file:`/video/${m}.${p}`,video_type:"",name_file:`${m}.${p}`,video_name:`${m}.${p}`,sample_code:h.code,sample_id:h.key,current_create_time:g}),y=[],v=void 0,h=void 0,setTimeout((()=>{E(),n.Ay.info("\u89c6\u9891\u5df2\u4fdd\u5b58")}),1e3)}e({type:d.J6,param:!1})}catch(a){e({type:d.J6,param:!1}),n.Ay.error("\u4fdd\u5b58\u89c6\u9891\u5931\u8d25"),console.log(a)}})()},v.onerror=e=>{console.error(e)}},startCameraREC:async e=>{try{console.log("startCameraREC"),h=e.sample,y=[],m=e.title,g=(0,o.Ln)(o.NL),v&&v.start()}catch(t){console.error("Error accessing camera:",t)}},finishCameraERC:()=>{v&&("recording"===v.state&&v.stop(),console.log("finishCameraERC"))},getUserMediaStream:S,stopCamera:()=>{if(b){console.log("\u6b63\u5728\u505c\u6b62\u6444\u50cf\u5934...");try{b.getTracks().forEach((e=>{try{e.stop(),console.log(`\u8f68\u9053\u5df2\u505c\u6b62: ${e.kind}`)}catch(t){console.warn(`\u505c\u6b62\u8f68\u9053\u65f6\u51fa\u9519: ${e.kind}, \u9519\u8bef: ${t.message}`)}})),b=null,v=null,console.log("\u6444\u50cf\u5934\u5df2\u6210\u529f\u505c\u6b62")}catch(e){console.error("\u505c\u6b62\u6444\u50cf\u5934\u65f6\u51fa\u9519: ",e)}return!0}return console.warn("\u6ca1\u6709\u53ef\u505c\u6b62\u7684\u6444\u50cf\u5934\u6d41"),!1},cameraStream:b}}},42225:(e,t,a)=>{a.d(t,{A:()=>c});var l=a(65043),n=a(93950),i=a.n(n),o=a(16569),r=a(91688),d=a(34458),s=a(51907);const c=()=>{const e=(0,r.W6)(),t=(0,l.useCallback)(i()((e=>{n(e)}),3e3),[]),a=async e=>{try{return await(0,s.x)({url:"dongle_login",method:"post",data:{userName:e.name,roleName:e.role_name}})}catch(t){throw console.log(t),t}},n=async t=>{try{const l=(0,d.ug)()||t;if(l){const t=await a(l),n=JSON.parse(null===t||void 0===t?void 0:t.data),{code:i,data:r}=n;return 0!==i?(o.Ay.error(r||"\u9a8c\u8bc1\u4e0d\u901a\u8fc7"),e.push("/login"),n):n}return{code:0}}catch(l){throw console.log(l),l}};return{verifyDog:t,getHardWareAxios:a}}},42651:(e,t,a)=>{a.d(t,{A:()=>o});var l=a(80077),n=a(15637),i=a(67208);const o=()=>{const e=(0,l.wA)();return{initDialogData:async()=>{try{const t=await(0,i.yOU)();if(t){const a=t.sort(((e,t)=>new Date(t.created_time)-new Date(e.created_time)));e({type:n.bb,param:a})}}catch(t){console.log(t)}}}}},42866:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=a(15637),i=a(67208);const o=()=>{const e=(0,l.wA)();return{initExportData:async t=>{try{const a=await(0,i.bcx)(t);if(a){const t=a.sort(((e,t)=>new Date(t.created_time)-new Date(e.created_time)));e({type:n.HM,param:t})}}catch(a){console.log(a)}}}}},44409:(e,t,a)=>{a.d(t,{A:()=>T});var l=a(65043),n=a(80077),i=a(80349),o=a(56543),r=a(84),d=a(67208),s=a(88359),c=a(72295),A=a(45303),u=a(84856),p=a(78583),v=a(45333),m=a(34458),h=a(10181),g=a(754),b=a(36950),y=a(4178),x=a(60383),f=a(10866),C=a(59066),w=a(50540);const E={[o.zx.SAVE]:v.JN.\u4fdd\u5b58,[o.zx.SAVE_AS]:v.JN.\u53e6\u5b58\u4e3a,[o.zx.OPEN_SETTING]:v.JN.\u6a21\u677f\u914d\u7f6e,[o.zx.QUIT]:v.JN.\u9000\u51fa,[o.zx.CLOSE]:v.JN.\u5173\u95ed},S="true"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3000/api/",REACT_APP_HARDWARE_API_URL:"http://localhost:5000/api/",REACT_APP_IS_DEV:"false",REACT_APP_MQ_PUB:"tcp://*:10247",REACT_APP_MQ_SUB:"tcp://localhost:10247",REACT_APP_MQ_UI_URL:"tcp://localhost:5560",REACT_APP_MQ_URL:"tcp://localhost:5561",REACT_APP_TASK_SERVER_API2_URL:"http://localhost:5002/",REACT_APP_TASK_SERVER_API_URL:"http://localhost:5002/api/",REACT_APP_WS_URL:"ws://localhost:9876"}.REACT_APP_IS_BROWSER?window.require("zeromq"):{};let _,R,j;window.isDebug=!1;class I{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;this.queue=[],this.sending=!1,this.socket=e,this.max=t}send(e){try{this.queue.length>this.max&&console.log("publisher \u6d88\u606f\u961f\u5217\u5df2\u6ee1"),this.queue.push(e),this.trySend()}catch(t){console.log("publisher send error",t)}}async trySend(){try{if(this.sending)return;for(this.sending=!0;this.queue.length>0;){const e=this.queue.shift();await this.socket.send(e)}this.sending=!1}catch(e){console.log("publisher trySend error",e)}}}const T=()=>{const e=(0,n.wA)(),{updateInputVariable:t}=(0,c.A)(),{syncResult:a}=(0,p.A)(),{subCurrentPageId:T}=(0,u.A)(),{openDialog:P}=(0,r.A)(),{openProjectSubWindow:k,sendMsgToSubWindow:D}=(0,y.A)(),{initSampleTree:U}=(0,f.A)(),N=(0,l.useRef)(!1),B=async()=>{for(;;){const a=await _.receive(),l=a[0],n=a[1];if(!n)return;let i;try{i=x.D(n)}catch(e){try{i=JSON.parse(n)}catch(t){console.error("JSON.parse\u89e3\u6790\u5931\u8d25",t,n);continue}}const o=decodeURIComponent(l);await O(o,i,n)}},O=async(e,t,a)=>{if(window.isDebug&&console.log(`subTopic: ${e}, UICmd: ${null===t||void 0===t?void 0:t.UICmd}`,t),e.includes("ControlCompUIData"))return L({subTopic:e,data:t}),void(N.current||j.send([e,a]));switch(e){case o.t6.UI_CONNECT:L({subTopic:e,data:t}),(0,m.V9)(!0);break;case o.t6.UI_ERROR:L({subTopic:e,data:t}),q(t);break;case o.t6.PROJECT_STATE_NEWVAR:L({subTopic:e,data:t}),Q(t);break;case o.t6.SAMPLE_PARAMS:L({subTopic:e,data:t}),await G(t,a);break;case o.t6.INPUT_VAR:L({subTopic:e,data:t}),await H(t,a);break;case o.t6.RESULT_VAR:L({subTopic:e,data:t}),await K(t,a);break;case o.t6.HOST_STATE_VAR:L({subTopic:e,data:t}),await Y(t,a);break;case o.zx.LOG_CONTROL_DATA:L({subTopic:e,data:t}),Z(t,a);break;default:await M(e,t,a)}},M=async(e,t,a)=>{switch(null===t||void 0===t?void 0:t.UICmd){case o.zx.OPEN_MODAL:case o.zx.INPUT_VAR_DIALOG:case o.zx.OPEN_DIALOG:case o.zx.OPEN_INPUT_DIALOG:j.send([o.t6.UI_OPEN_MODAL,a]);break;case o.zx.OPEN_HOST_DIALOG:g.A.dispatch({type:A.tE,param:t});break;case o.zx.TASK_STATUS:L({subTopic:e,data:t}),ie(t);break;case o.zx.PROCESS_STATUS:L({subTopic:e,data:t}),oe(t);break;case o.zx.ACTION_DIALOG:F(t,a);break;case o.zx.TASK_PLAY_AUDIO:j.send([o.t6.UI_TASK_PLAY_AUDIO,a]);break;case o.zx.INPUT_VAR:L({subTopic:e,data:t}),V(null===t||void 0===t?void 0:t.UIParams);break;case o.zx.REPLACE_DATA:L({subTopic:e,data:t}),z(t);break;case o.zx.LOG_DATA:L({subTopic:e,data:t}),X(t);break;case o.zx.NEXT_STATUS:se(t);break;case o.zx.SHORTCUT_DATA:case o.zx.SAVE:case o.zx.SAVE_AS:case o.zx.OPEN_SETTING:case o.zx.QUIT:case o.zx.CLOSE:$(t);break;case o.zx.VIDEO_RECORDING:te(!0);break;case o.zx.END_OF_VIDEO_RECORDING:te(!1);break;case o.zx.START_VALIDATION:ae(t);break;case o.zx.SAMPLE_INST_STATE_CHANGED:le(t);break;case o.zx.NEXT_STEP:ne(t);break;default:window.isDebug&&console.log("\u672a\u76d1\u542c\u5230\u7684\u6d88\u606f",e,t)}},L=e=>{let{subTopic:t,data:a}=e;_&&D({subTopic:t,data:a})},V=async t=>{const a=await(0,d.Eqn)();if(a){const l=null===a||void 0===a?void 0:a.find((e=>(null===t||void 0===t?void 0:t.code)===(null===e||void 0===e?void 0:e.code)));if(l){const[a]=await(0,d.L8k)({ids:[l.id]});if(a){const l={...a,default_val:{...a.default_val,value:null===t||void 0===t?void 0:t.value}};(0,d.Tnc)(l)&&e((0,s.w)(!1))}}}},Q=async t=>{const a=t.UIParams.runningStatus,l=t.ProcessID.split("_")[1];if(e({type:C.f,param:{projectId:l,status:a}}),e({type:w.mn,param:a}),Number(g.A.getState().project.projectId)===Number(l))if(a){const e=(0,b.bE)();let t=g.A.getState().project.optSample;"#FFFFFF"===t.color&&(await(0,d.IQN)({id:t.key,color:e,status:a?o.$y.RUNNING:void 0}),t={...t,color:e}),U(!1),re(t)}else re(null)},F=async(e,t)=>{var a;const l=null===e||void 0===e||null===(a=e.UIParams)||void 0===a?void 0:a.type;if(l)if(l===A.Bz)j.send([o.t6.UI_ACTION_DIALOG,t]);else if(Object.values(A.s8).includes(l))P({type:l});else if(Object.values(v.tI).includes(l))ee({...e,UIParams:{shortcutCode:l}});else{const[e,t]=l.split("___");t===h.sr.\u5f39\u7a97?P({type:A.pX,data:e}):t===h.sr.\u9884\u89c8\u7a97\u53e3?J(e):T(e)}},J=e=>{const t=g.A.getState().template.pageData.find((t=>t.id===e)),{contentWidth:a,contentHeight:l}=JSON.parse(null===t||void 0===t?void 0:t.window_property);k({projectId:(0,m.HN)(),pageId:e,width:a,height:l})},Y=async(e,t)=>{const{hostId:a}=e,l=`${a}_Station_STATUS`;j.send([l,t])},K=async(e,t)=>{const{Code:l,InstCode:n,Value:i,Index:o,Error:r,ErrorMessage:d}=e;j.send([l,t]),a({sampleCode:n,resultCode:l,newValue:i,index:o,error:r,errorMessage:d})},H=async(e,t)=>{const{Code:a,Value:l}=e;j.send([a,t]),W(e)},W=e=>{var a,l,n,i,o;const r=null===g.A||void 0===g.A||null===(a=g.A.getState())||void 0===a||null===(l=a.global)||void 0===l||null===(n=l.unitList)||void 0===n?void 0:n.find((t=>t.code===(null===e||void 0===e?void 0:e.Dimension)));t({code:e.Code,is_feature:!1===e.IsCheck?0:1,default_val:{value:e.Value,isConstant:!1===e.IsConstant?0:1,unitType:null===r||void 0===r?void 0:r.id,unit:null===r||void 0===r||null===(i=r.units)||void 0===i||null===(o=i.find((t=>t.code===(null===e||void 0===e?void 0:e.Unit))))||void 0===o?void 0:o.id,type:e.Mode}},void 0,!0)},G=async(e,t)=>{const{InstCode:a,Code:l}=e;j.send([`${a}-${l}`,t])},z=t=>{e({type:w.vH,param:t})},X=t=>{e({type:w.UU,param:t})},Z=async(e,t)=>{j.send([o.t6.LOG_CONTROL_DATA,t])},q=t=>{e({type:w.W5,param:{...t,create_time:(0,b.Ln)()}})},$=e=>{e.UICmd===o.zx.SHORTCUT_DATA?ee(e):ee({...e,UIParams:{shortcutCode:E[e.UICmd]}})},ee=t=>{e({type:w.MY,param:t})},te=t=>{e({type:w.J9,param:t})},ae=t=>{e({type:w.Kx,param:t})},le=t=>{e({type:w.yi,param:t})},ne=t=>{e({type:w.rt,param:t})},ie=t=>{let a=t;const l=g.A.getState().subTask.subTaskStatus||[],n=l.findIndex((e=>(null===e||void 0===e?void 0:e.SubTaskID)===(null===t||void 0===t?void 0:t.SubTaskID)));a=[...l],n>-1?a[n]={...t,create_time:a[n].create_time,update_time:(0,b.Ln)()}:a.push({...t,create_time:(0,b.Ln)()}),e({type:w.oA,param:a})},oe=t=>{const a=g.A.getState().subTask.subProcessStatus||[],l=[...a],n=a.findIndex((e=>(null===e||void 0===e?void 0:e.ProcessID)===(null===t||void 0===t?void 0:t.ProcessID)));n>-1?l[n]=t:l.push(t),e({type:w.qM,param:l})},re=t=>{e({type:w.Cf,param:t})},de=t=>{e({type:w.SO,param:t})},se=e=>{const{subTaskNextStep:t=[]}=g.A.getState().subTask,a=t.findIndex((t=>t.key===e.SubTaskID)),l=[...t];-1===a?l.push(e):l[a]={...l[a],flag:e.UIParams.flag},de(l)};return{initPair:()=>{try{_||(_=new S.Pair,_.connect("tcp://localhost:5561"),(()=>{if(!performance.memory)return void console.warn("\u5f53\u524d\u6d4f\u89c8\u5668\u4e0d\u652f\u6301\u5185\u5b58\u76d1\u63a7API\uff0c\u65e0\u6cd5\u68c0\u6d4b\u5185\u5b58\u9608\u503c");let e=!1,t=!1;setInterval((()=>{const{usedJSHeapSize:a,jsHeapSizeLimit:l}=performance.memory,n=a/l*100;if(n<=50)return t&&(i.Ay.destroy("last70"),i.Ay.success({key:"last70",message:"\u5185\u5b58\u68c0\u6d4b",description:"\u5185\u5b58\u4f7f\u7528\u7387\u4e0b\u964d\u5230\u6b63\u5e38\u503c\uff0c\u5df2\u6062\u590d\u4e8c\u7ef4\u6570\u7ec4\u548cdaq\u6d88\u606f\u7684\u663e\u793a\u5904\u7406",placement:"topRight",duration:null}),(0,d.hYk)({ClassName:`project_${(0,m.HN)()}`,Significance:"\u9ad8",Type:"\u5185\u5b58\u68c0\u6d4b",Grade:"Error",Content:"\u5185\u5b58\u4f7f\u7528\u7387\u4e0b\u964d\u5230\u6b63\u5e38\u503c\uff0c\u5df2\u6062\u590d\u4e8c\u7ef4\u6570\u7ec4\u548cdaq\u6d88\u606f\u7684\u663e\u793a\u5904\u7406"}),N.current=!1),e=!1,void(t=!1);n>=50&&n<70&&(e||(i.Ay.destroy("over50"),i.Ay.warning({key:"over50",message:"\u5185\u5b58\u68c0\u6d4b",description:"\u8b66\u544a\uff1a\u5185\u5b58\u4f7f\u7528\u7387\u5df2\u8fbe\u5230\u8b66\u544a\u503c",placement:"topRight",duration:null}),(0,d.hYk)({ClassName:`project_${(0,m.HN)()}`,Significance:"\u9ad8",Type:"\u5185\u5b58\u68c0\u6d4b",Grade:"Error",Content:"\u8b66\u544a\uff1a\u5185\u5b58\u4f7f\u7528\u7387\u5df2\u8fbe\u5230\u8b66\u544a\u503c"})),e=!0,console.warn(`\u8b66\u544a\uff1a\u5185\u5b58\u4f7f\u7528\u7387\u5df2\u8fbe\u5230${n.toFixed(1)}%`)),n>=70&&(t||(i.Ay.destroy("over70"),i.Ay.error({key:"over70",message:"\u5185\u5b58\u68c0\u6d4b",description:"\u5185\u5b58\u4f7f\u7528\u7387\u5df2\u8fbe\u5230\u9608\u503c\uff0c\u505c\u6b62\u5bf9\u4e8c\u7ef4\u6570\u7ec4\u548cdaq\u6d88\u606f\u7684\u663e\u793a\u5904\u7406\uff0c\u8bd5\u9a8c\u6b63\u5e38\u8fd0\u884c\uff0c\u8bf7\u53ca\u65f6\u5904\u7406",placement:"topRight",duration:null}),(0,d.hYk)({ClassName:`project_${(0,m.HN)()}`,Significance:"\u9ad8",Type:"\u5185\u5b58\u68c0\u6d4b",Grade:"Error",Content:"\u5185\u5b58\u4f7f\u7528\u7387\u5df2\u8fbe\u5230\u9608\u503c\uff0c\u505c\u6b62\u5bf9\u4e8c\u7ef4\u6570\u7ec4\u548cdaq\u6d88\u606f\u7684\u663e\u793a\u5904\u7406\uff0c\u8bd5\u9a8c\u6b63\u5e38\u8fd0\u884c\uff0c\u8bf7\u53ca\u65f6\u5904\u7406"})),t=!0,console.warn(`\u5185\u5b58\u4f7f\u7528\u7387\u8fbe\u5230${n.toFixed(1)}%\uff0c\u5df2\u8fbe\u523070%\u9608\u503c\uff0c\u505c\u6b62\u5bf9\u4e8c\u7ef4\u6570\u7ec4\u548cdaq\u6d88\u606f\u7684\u663e\u793a\u5904\u7406`),N.current=!0)}),2e3)})(),B())}catch(e){console.log(e)}},close:()=>{var e,t;null===(e=_)||void 0===e||e.close(),null===(t=R)||void 0===t||t.close(),_=void 0,R=void 0},send:async e=>{try{await R.send(e)}catch(t){console.log("error",t)}},clear:()=>{e({type:w.Vc,param:[]}),de([]),e({type:w.oA,param:g.A.getState().subTask.subTaskStatus.filter((e=>{var t;return"start-running"===(null===e||void 0===e||null===(t=e.UIParams)||void 0===t?void 0:t.status)}))})},submitSubTaskSample:re,submitIsFinishMain:function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e({type:w.mB,param:t})},submitSubTaskNextStep:de,initPublisher:async()=>{try{if(!j){const t=new S.Publisher;try{await t.bind("tcp://*:10247")}catch(e){console.log("error",e)}j=new I(t)}}catch(e){console.log(e)}},useSubscriber:async e=>{const t=new S.Subscriber;try{return t.connect("tcp://localhost:10247"),t.subscribe(e),t}catch(a){console.log(a)}return t},submitSubTaskStatus:ie,submitStartValidation:ae,submitSubTaskVideoRecording:te,submitSampleInstStateChanged:le,submitNextStepChanged:ne,initUiPair:()=>{try{R||(R=new S.Pair,R.connect("tcp://localhost:5560"))}catch(e){console.log(e)}},handleMsg:O}}},45333:(e,t,a)=>{a.d(t,{JN:()=>s,ZE:()=>A,eq:()=>u,tI:()=>c});const l="SHORTCUT/HOME",n="SHORTCUT/SAVE",i="SHORTCUT/SAVEAS",o="SHORTCUT/SETTINGS",r="SHORTCUT/NEXT",d="SHORTCUT/CLOSE",s={"\u9996\u9875":l,"\u5f00\u59cb":"SHORTCUT/START","\u6682\u505c":"SHORTCUT/PAUSE","\u6062\u590d":"SHORTCUT/RECOVER","\u7ec8\u6b62":"SHORTCUT/STOP","\u4fdd\u5b58":n,"\u53e6\u5b58\u4e3a":i,"\u4e0b\u4e00\u6b65":r,"\u9000\u51fa":"SHORTCUT/QUIT","\u5173\u95ed":d,"\u6a21\u677f\u914d\u7f6e":o},c={"\u9996\u9875":l,"\u4fdd\u5b58":n,"\u53e6\u5b58\u4e3a":i,"\u4e0b\u4e00\u6b65":r,"\u6a21\u677f\u914d\u7f6e":o,"\u6253\u5370\u62a5\u544a":"SHORTCUT/PRINT","\u5173\u95ed":d},A={SAVE_AS_TEMPLATE:1,SAVE_AS_PROJECT:2},u={CANCEL:"cancel",NORMAL:"normal",INDEPENDENT:"independent"}},46085:(e,t,a)=>{a.d(t,{et:()=>j,im:()=>ae,RN:()=>te});var l=a(65043),n=a(74117),i=a(89073),o=a(4554),r=a(75440),d=a(28369),s=a(45591),c=a(51869),A=(a(32913),a(72957),a(30454),a(56603)),u=a.n(A),p=a(81143),v=a(68374);const m=p.Ay.div`
    display: flex;
    flex-direction: column;
    height: 100%;

    .top {
        flex: 4;

        display: flex;
        gap: 10px;
        overflow: hidden;

        .left {
            height: 100%;
            flex: 1;
            overflow: hidden;
        }
        
        .right {
            height: 100%;
            flex: 3;
            overflow: hidden;
        }
    }

    .bottom{
        flex:${e=>e.expandCheck?8:1};

        overflow: hidden;
        position: relative;

        .expand{
            position: absolute;
            right: 21px;
            top: 8px;
            cursor: pointer;

            font-size: 22px;
        }
    }
`,h=p.Ay.div`
    height: 100%;
`,g=p.Ay.div`
    height: 100%;
    overflow: hidden;

    display: flex;
    flex-direction: column;

    .title{
       margin: ${(0,v.D0)("12px")} 0;
       display: flex;
       align-items: center;
    }

    .content{
        flex: 1;

       border-radius: ${(0,v.D0)("6px")};
       border: 1px solid ${v.o$.borderGray};
       padding: ${(0,v.D0)("8px")};

       overflow-y: auto;

       p{
           margin-bottom: 3px;
       }
    }

`,b=p.Ay.div`
    height: 100%;
    overflow: hidden;

    display: flex;
    flex-direction:column;

    .ant-tabs-content{
        height: 100%;
        overflow-y: hidden;

        .ant-tabs-tabpane{
            height: 100%;
            overflow-y: hidden;
        }
    }

   .search{
    margin-bottom: 10px;
   }


   .tableContainer{
        flex: 1;
        overflow: auto;
        height: 100%;
   }
`;var y=a(70579);const x={getCompletions(e,t,a,l,n){n(null,[{name:"Value",value:"Value",meta:"InputVar"}])}},f=(e,t)=>{let{script:a,onChange:i,...o}=e;const{t:r}=(0,n.Bd)(),d=(0,l.useRef)();(0,l.useEffect)((()=>{try{const e=u().require("ace/ext/language_tools");e&&e.addCompleter&&e.addCompleter(x)}catch(e){console.warn("Failed to add custom completer:",e)}}),[]);const s=(0,l.useRef)({col:0,row:0});(0,l.useImperativeHandle)(t,(()=>({insert:e=>{const{editor:t}=d.current,a=t.getSession(),l=t.getSelectionRange();a.replace(l,e.replace(/@@/g,""));const n=e.indexOf("@@"),i=e.lastIndexOf("@@")-2;if(-1!==n){const e=a.doc.positionToIndex(l.start),o=a.doc.indexToPosition(e+n),r=a.doc.indexToPosition(e+i);t.selection.setRange({start:o,end:r})}}})));return(0,y.jsx)(h,{children:(0,y.jsx)(c.Ay,{ref:d,mode:"csharp",theme:"xcode",fontSize:14,showPrintMargin:!0,height:"100%",width:"100%",showGutter:!0,highlightActiveLine:!0,setOptions:{enableBasicAutocompletion:!0,enableLiveAutocompletion:!0,enableSnippets:!0,showLineNumbers:!0,tabSize:2},onMouseDown:e=>e.preventDefault(),value:a,onChange:(e,t)=>{i(e)},onCursorChange:e=>{s.current={col:e.cursor.column,row:e.cursor.row}},...o})})},C=(0,l.forwardRef)(f);a(93950);var w=a(16569),E=a(95206),S=a(56543),_=a(67208);const R=(0,l.createContext)(),j={"\u7ed3\u679c\u53d8\u91cf":"ResultVar","\u865a\u62df\u4fe1\u53f7\u53d8\u91cf":"SignalVar","\u8bd5\u6837\u811a\u672c":"SampleInst","\u5b50\u4efb\u52a1\u811a\u672c(\u901a\u7528)":"Normal","\u8f93\u5165\u53d8\u91cf":"InputVar"},I=e=>{let{script:t,module:a=j["\u5b50\u4efb\u52a1\u811a\u672c(\u901a\u7528)"]}=e;const{t:i}=(0,n.Bd)(),o=(0,l.useContext)(R),[r,d]=(0,l.useState)(""),s=async e=>{var t;return await(0,_.jah)({code:e,module:a,result_type:null!==(t=null===o||void 0===o?void 0:o.type)&&void 0!==t?t:S.Jt.BOOL})};return(0,y.jsxs)(g,{children:[(0,y.jsx)("div",{className:"title",children:(0,y.jsx)(E.Ay,{onClick:async()=>{if(!t)return void w.Ay.error("\u8bf7\u5148\u8f93\u5165\u811a\u672c");null===S.a3||void 0===S.a3||S.a3.find((e=>null===t||void 0===t?void 0:t.includes(e)));const e=await s(t);d(String(e))},children:i("\u6821\u9a8c")})}),(0,y.jsx)("div",{className:"content",children:(c=r,(0,y.jsx)("div",{children:c.split("\n").map(((e,t)=>(0,y.jsx)("p",{children:e},t)))}))})]});var c};var T=a(8918),P=a(80077),k=a(9339),D=a(29977);const U="__ABC",N={"\u8f93\u5165\u53d8\u91cf":"input_","\u4fe1\u53f7\u53d8\u91cf":"signal_","\u7ed3\u679c\u53d8\u91cf":"result_"},B=e=>{let{t:t,type:a}=e;return[{title:t("\u540d\u79f0"),dataIndex:"name",key:"name"},{title:t("\u6807\u8bc6\u7b26"),dataIndex:"code",key:"code"}]};var O=a(62058),M=a(83720),L=a(36497);const V=p.Ay.div`
    display: flex;
    align-items: center;
    gap: 8px;

    margin-bottom: 8px;
`,Q=p.Ay.div`
    height: 100%;
    
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .tableContainer{
        flex: 1;
        overflow-y: auto;
    }
`,F=e=>{let{onSearch:t}=e;const[a,n]=(0,l.useState)(),[i,o]=(0,l.useState)(),r=()=>{t({name:a,type:i})};return(0,y.jsxs)(V,{children:[(0,y.jsx)(M.A,{value:a,placeholder:"\u540d\u79f0\u6216\u6807\u8bc6\u7b26\u67e5\u8be2",allowClear:!0,onChange:e=>n(e.target.value),onPressEnter:r}),(0,y.jsx)(L.A,{value:i,placeholder:"\u7c7b\u578b",allowClear:!0,style:{width:"100%"},options:Object.entries(N).map((e=>{let[t,a]=e;return{label:t,value:a}})),onChange:e=>o(e)}),(0,y.jsx)(E.Ay,{type:"primary",icon:(0,y.jsx)(O.A,{}),style:{width:"100px"},onClick:r})]})},J=e=>{let{variable_type:t,code:a}=e;return`Model.GetVarByName<${t===S.ps.\u65f6\u95f4\u65e5\u671f?S.ps.\u6587\u672c:t}InputVar>("${a}")`},Y=e=>{let{code:t}=e;return`Model.CurrentInst.ResultVars["${t}"]`},K=e=>{let{code:t}=e;return`Model.SignalVars["${t}"]`},H=e=>{let{onDoubleClick:t}=e;const{t:a}=(0,n.Bd)(),i=(0,D.A)(),o=(0,P.d4)((e=>e.template.signalList)),r=(0,P.d4)((e=>e.template.resultData)),[d,s]=(0,l.useState)(),c=(0,l.useMemo)((()=>[...i.map((e=>({id:e.id,name:e.name,code:e.code,insertValue:J(e),[U]:N.\u8f93\u5165\u53d8\u91cf}))),...o.map((e=>({id:e.signal_variable_id,name:e.variable_name,code:e.code,insertValue:K(e),[U]:N.\u4fe1\u53f7\u53d8\u91cf}))),...r.map((e=>({id:e.result_variable_id,name:e.variable_name,code:e.code,insertValue:Y(e),[U]:N.\u7ed3\u679c\u53d8\u91cf})))]),[o,i,r]),A=(0,l.useMemo)((()=>{if(!d)return c;const{name:e,type:t}=d;return c.filter((t=>!e||-1!==t.name.toLowerCase().indexOf(e.toLowerCase())||-1!==t.code.toLowerCase().indexOf(e.toLowerCase()))).filter((e=>!t||e[U]===t))}),[c,d]);return(0,y.jsxs)(Q,{children:[(0,y.jsx)(F,{onSearch:e=>{s(e)}}),(0,y.jsx)("div",{className:"tableContainer",children:(0,y.jsx)(k.A,{onRow:e=>({onDoubleClick:()=>t((null===e||void 0===e?void 0:e.insertValue)||"")}),rowKey:"code",size:"small",pagination:!1,columns:B({t:a}),dataSource:A})})]})},{Search:W}=M.A;var G=a(35964);const z=[{title:"\u5f53\u524d\u8bd5\u6837",key:"0-0",children:[{title:"\u5f53\u524d\u8bd5\u6837\u7684\u7c7b\u578b\u540d\u79f0",key:"0-0-0",insertValue:"Model.CurrentInst.Name"},{title:"\u5f53\u524d\u8bd5\u6837\u7684\u67d0\u4e2a\u53c2\u6570\u7684\u503c",key:"0-0-1",insertValue:'Model.CurrentInst.Parameters["@@\u53c2\u6570\u7684code@@"].Value'}]},{title:"\u76ee\u6807\u8bd5\u6837",key:"0-1",children:[{title:"\u76ee\u6807\u8bd5\u6837\u6570\u7ec4\u521d\u59cb\u5316",key:"0-1-0",insertValue:"Model.TargetSampleInsts = Model.SelectedInsts;"},{title:"\u76ee\u6807\u8bd5\u6837\u5faa\u73af\u8d4b\u503c",key:"0-1-1",insertValue:"Model.IterateTargetSampleInsts();"},{title:"\u76ee\u6807\u8bd5\u6837\u7c7b\u578b\u540d\u79f0",key:"0-1-2",insertValue:"Model.TargetSampleInst.Name"},{title:"\u76ee\u6807\u8bd5\u6837\u53c2\u6570",key:"0-1-3",insertValue:'Model.TargetSampleInst.Parameters["@@\u53c2\u6570\u7684code@@"].Value'}]}],X=e=>{let{nodeData:t,onDoubleClick:a}=e;return(0,y.jsx)("div",{onDoubleClick:()=>{null!==t&&void 0!==t&&t.insertValue&&a(null===t||void 0===t?void 0:t.insertValue)},children:t.title})},Z=e=>{let{onDoubleClick:t}=e;const{sampleList:a}=(0,P.d4)((e=>e.project));console.log("sampleList",a);const n=(0,l.useMemo)((()=>a.map((e=>{var t;return{key:null===e||void 0===e?void 0:e.code,title:null===e||void 0===e?void 0:e.sample_name,children:null===e||void 0===e||null===(t=e.parameters)||void 0===t?void 0:t.map((t=>{let{code:a,parameter_name:l}=t;return{key:e.code+a,title:l,insertValue:a}}))}}))),[a]);return(0,y.jsxs)("div",{style:{width:"100%",height:"100%",display:"flex",flexDirection:"column",gap:"8px",overflow:"auto"},children:[(0,y.jsx)(G.A,{selectable:!1,treeData:z,titleRender:e=>(0,y.jsx)(X,{nodeData:e,onDoubleClick:t})}),(0,y.jsx)(G.A,{selectable:!1,treeData:n,titleRender:e=>(0,y.jsx)(X,{nodeData:e,onDoubleClick:t})})]})},q=e=>{let{onSelected:t}=e;const{t:a}=(0,n.Bd)(),l=e=>{t(e)},i=[{key:"variable",label:a("\u53d8\u91cf"),children:(0,y.jsx)(H,{onDoubleClick:l})},{key:"sample",label:a("\u8bd5\u6837"),children:(0,y.jsx)(Z,{onDoubleClick:l})}];return(0,y.jsx)(b,{children:(0,y.jsx)(T.A,{type:"card",style:{height:"100%"},items:i})})},$=(e,t)=>{let{initialFunc:a,module:n,showCheck:i=!0}=e;const[o,r]=(0,l.useState)(),[c,A]=(0,l.useState)(!1),u=(0,l.useRef)();(0,l.useEffect)((()=>{"string"===typeof a&&r(a)}),[a]),(0,l.useImperativeHandle)(t,(()=>({set:e=>{r(e)},get:()=>o})));return(0,y.jsxs)(m,{expandCheck:c,children:[(0,y.jsxs)("div",{className:"top",children:[(0,y.jsx)("div",{className:"left",children:(0,y.jsx)(q,{onSelected:e=>{var t;null===(t=u.current)||void 0===t||t.insert(e)}})}),(0,y.jsx)("div",{className:"right",children:(0,y.jsx)(C,{ref:u,script:o,module:n,onChange:e=>{r(e)}})})]}),i&&(0,y.jsxs)("div",{className:"bottom",children:[(0,y.jsx)("div",{className:"expand",onClick:()=>A(!c),children:c?(0,y.jsx)(d.A,{}):(0,y.jsx)(s.A,{})}),(0,y.jsx)(I,{module:n,script:o})]})]})},ee=(0,l.forwardRef)($),te=e=>{let{open:t,script:a,module:i,showCheck:o,onOk:d,onCancel:s}=e;const{t:c}=(0,n.Bd)(),A=(0,l.useRef)();(0,l.useEffect)((()=>{t&&A.current.set(a)}),[t]);return(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(r.A,{title:c("\u811a\u672c\u7f16\u8f91\u5668"),open:t,onOk:()=>{const e=A.current.get();d(e)},width:"85vw",contentHeight:"70vh",onCancel:s,style:{top:"3vh"},children:(0,y.jsx)(ee,{ref:A,module:i,showCheck:o})})})},ae=e=>{let{value:t="",onChange:a,disabled:r,module:d,showCheck:s,type:c,...A}=e;const{t:u}=(0,n.Bd)(),[p,v]=(0,l.useState)(!1);return(0,y.jsx)(R.Provider,{value:{type:c},children:(0,y.jsx)(i.A,{style:{maxWidth:"100%"},size:"small",extra:!r&&(0,y.jsx)(o.A,{size:"small",onClick:()=>{v(!0)},children:u("\u7f16\u8f91\u5668")}),children:(0,y.jsx)(te,{open:p,script:t,module:d,showCheck:s,onOk:e=>{a(e),v(!1)},onCancel:()=>{v(!1)}})})})}},49952:(e,t,a)=>{a.d(t,{A:()=>o});var l=a(80077),n=a(15637),i=a(67208);a(36950);const o=()=>{const e=(0,l.wA)(),t=async()=>{try{const t=await(0,i.o1W)();t&&e({type:n.jd,param:t})}catch(t){console.error(t)}};return{initDynamicCurveList:t,saveDynamicCurve:async e=>{try{await(0,i.bF_)(e)&&t()}catch(a){console.error(a)}},getHistorySamplesByTotal:async(e,t)=>({})}}},54962:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(81143),n=a(68374);const i=l.Ay.div`
    background: ${n.o$.pageBack};
    box-shadow: 2px 0px 7px 0px rgba(3,36,71,0.08);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    .page-title {
        background: #FFFFFF;
        box-shadow: 0px 2px 4px 0px rgba(7,26,112,0.1);
        padding: ${(0,n.D0)("4px")} ${(0,n.D0)("8px")};
        font-size: 14px;
        font-weight: 550;
        color: #0838B9;
        min-height: 20px;
        box-sizing: content-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
 
`;var o=a(70579);const r=e=>{const{children:t,title:a,operate:l}=e;return(0,o.jsxs)(i,{children:[(a||l)&&(0,o.jsxs)("div",{className:"page-title",children:[(0,o.jsx)("div",{children:a}),l]}),t]})}},55505:(e,t,a)=>{a.d(t,{In:()=>d,K$:()=>r,Vw:()=>v,__:()=>u,f3:()=>s,yx:()=>c});a(65043);var l=a(6051),n=a(92882),i=a(17990),o=a(70579);const r="mainProcess",d="0",s=e=>{let{t:t}=e;return[{label:t("\u5185\u7f6e\u52a8\u4f5c"),value:"internal"},{label:t("\u5feb\u6377\u65b9\u5f0f\u52a8\u4f5c"),value:"shortcut"}]},c={ADD:1,EDIT:2,DELETE:3,HELP:4},A={ready:"\u51c6\u5907",paused:"\u6682\u505c",running:"\u8fd0\u884c\u4e2d",finished:"\u5df2\u505c\u6b62",aborted:"\u5df2\u53d6\u6d88"},u={"\u4ece\u65b0\u5f00\u59cb":"restart","\u6682\u505c":"pause","\u6062\u590d":"resume","\u505c\u6b62":"abort"},p=e=>{let{record:t,actionStatus:a,executeAction:l,abortAction:n}=e;return t.action_category===r?(0,o.jsx)(o.Fragment,{}):"running"===a[t.action_id]?(0,o.jsx)("a",{onClick:()=>n(t.action_id),children:"\u505c\u6b62"}):(0,o.jsx)("a",{onClick:()=>l(t.action_id),children:"\u6267\u884c"})},v=e=>{let{t:t,copy:a,actionStatus:d,executeAction:c,abortAction:u}=e;return[{title:t("\u6807\u8bc6\u7b26"),dataIndex:"action_code",key:"action_code",render:(e,t)=>(0,o.jsx)("span",{style:{cursor:"pointer"},onClick:e=>a(null===t||void 0===t?void 0:t.action_id),children:(0,o.jsx)(i.A,{text:e})})},{title:t("\u52a8\u4f5c\u540d\u79f0"),dataIndex:"action_name",key:"action_name",render:(e,t)=>(0,o.jsx)(i.A,{text:e})},{title:t("\u5206\u7c7b"),dataIndex:"action_category",key:"action_category",render:e=>e===r?t("\u4e3b\u6d41\u7a0b"):s({t:t}).find((t=>t.value===e)).label},{title:t("\u6253\u5f00\u9879\u76ee\u65f6\u6267\u884c"),dataIndex:"run_on_startup",key:"run_on_startup",render:e=>(0,o.jsx)("div",{style:{textAlign:"center"},children:e?(0,o.jsx)(n.A,{twoToneColor:"#52c41a"}):(0,o.jsx)(o.Fragment,{})})},{title:t("\u5173\u95ed\u9879\u76ee\u65f6\u6267\u884c"),dataIndex:"run_on_stopup",key:"run_on_stopup",render:e=>(0,o.jsx)("div",{style:{textAlign:"center"},children:e?(0,o.jsx)(n.A,{twoToneColor:"#52c41a"}):(0,o.jsx)(o.Fragment,{})})},{title:t("\u72b6\u6001"),dataIndex:"action_id",key:"action_id",width:80,render:e=>"finished"===(null===d||void 0===d?void 0:d[e])?"\u51c6\u5907":(null===A||void 0===A?void 0:A[null===d||void 0===d?void 0:d[e]])||"\u51c6\u5907"},{title:t("\u64cd\u4f5c"),dataIndex:"action_id",key:"action_id",width:80,render:(e,t)=>(0,o.jsx)(l.A,{size:"middle",children:p({record:t,actionStatus:d,executeAction:c,abortAction:u})})}]}},59551:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(65043),n=a(80077),i=a(32099);const o=()=>(0,i.Mz)([e=>e.inputVariable.inputVariableMap,e=>e.inputVariable.bufferInputCodeList],((e,t)=>t.map((t=>e.get(t))))),r=()=>{const e=(0,l.useMemo)(o,[]);return(0,n.d4)((t=>e(t)))}},60881:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(65043),n=a(80077);const i={root:"index",yjglq:"\u7b2c\u4e00\u7ae0/\u786c\u4ef6\u7ba1\u7406\u5668"};var o=a(45303);const r=function(){let{startListener:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(0,n.wA)(),{isHelpDocOpen:a}=(0,n.d4)((e=>e.dialog)),r=(0,l.useRef)();(0,l.useEffect)((()=>{const e=e=>{r.current=e.target};return document.addEventListener("keydown",d),document.addEventListener("mousemove",e),()=>{document.removeEventListener("keydown",d),document.removeEventListener("mousemove",e)}}),[e]);const d=e=>{e.stopPropagation();const{key:t}=e;"F1"===t&&s(r.current)},s=e=>{var t;const a=A(e);c(null!==(t=null===i||void 0===i?void 0:i[a])&&void 0!==t?t:i.root)},c=function(){const e=`../../../book/${arguments.length>0&&void 0!==arguments[0]?arguments[0]:i.root}.html`;t({type:o.WO,param:e})},A=e=>e?e.id&&i[e.id]?e.id:A(e.parentNode):i.root;return{isHelpDocOpen:a,openHelpDocByDom:s,openHelpDocByDomID:e=>{var t;c(null!==(t=null===i||void 0===i?void 0:i[e])&&void 0!==t?t:i.root)},openHelpDocByMdTag:c}}},63189:(e,t,a)=>{a.d(t,{A:()=>s});var l=a(65043),n=a(36497),i=a(97914),o=a(74117),r=a(70579);const{Option:d}=n.A,s=e=>{let{id:t,value:a,onChange:s,addonAfter:c,...A}=e;const[u,p]=(0,l.useState)(),[v,m]=(0,l.useState)("px"),{t:h}=(0,o.Bd)();(0,l.useEffect)((()=>{if(a){let t;if("string"===typeof c)t=a.slice(0,-c.length);else if((null===c||void 0===c?void 0:c.constructor)===Object){const{options:l,...n}=c;if(a){var e;const n=(null===(e=l.find((e=>{let{label:t,value:l}=e;return a.slice(-l.length)===l})))||void 0===e?void 0:e.value)||"";n!==v&&m(n),t=a.slice(0,-n.length)}}else t=a;t!==u&&p(t)}}),[a]);const g=e=>{let{iv:t,av:a}=e;const l=null!==t&&void 0!==t?t:u,n=null!==a&&void 0!==a?a:v;void 0!==l&&null!==l&&("string"===typeof c?s(l+c):(null===c||void 0===c?void 0:c.constructor)===Object?n&&s(l+n):s(l))},b=e=>{m(e),g({av:e})};return(0,r.jsx)(i.A,{value:u,addonAfter:(()=>{if("string"===typeof c)return c;if((null===c||void 0===c?void 0:c.constructor)===Object){const{options:e,...t}=c;return(0,r.jsx)(n.A,{...t,value:v,onChange:b,children:e.map(((e,t)=>{let{label:a,value:l}=e;return(0,r.jsx)(d,{value:l,children:h(a)},t)}))})}return!1})(),onChange:e=>{p(e),g({iv:e})},...A})}},64798:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(65043),n=a(80077),i=a(32099);const o=()=>(0,i.Mz)([e=>e.inputVariable.inputVariableMap,e=>e.inputVariable.numberCodeList],((e,t)=>t.map((t=>e.get(t))))),r=()=>{const e=(0,l.useMemo)(o,[]);return(0,n.d4)((t=>e(t)))}},64981:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(80077),n=a(36950),i=a(15637),o=a(67208);const r=()=>{const e=(0,l.wA)();return{initHeaderData:async()=>{try{const t=await(0,o.C3B)();t&&e({type:i.fX,param:t.map((e=>{var t,a,l,i,o;e.data=null!==(t=e.in_process_tab)&&void 0!==t&&t?null!==(a=e.in_process_tab)&&void 0!==a?a:[]:null!==(l=e.not_in_process_tab)&&void 0!==l?l:[];const r=(0,n.cN)(e);return{...r,list:null!==(i=null===(o=r.data)||void 0===o?void 0:o.map((e=>null===e||void 0===e?void 0:e.id)))&&void 0!==i?i:[]}}))})}catch(t){console.log(t)}return null}}}},65175:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(74117),n=a(75440),i=a(30212),o=a(70579);const r=e=>{let{open:t,setOpen:a,editId:r,handleOk:d}=e;const{t:s}=(0,l.Bd)(),c=()=>{a(!1)};return(0,o.jsx)(o.Fragment,{children:t&&(0,o.jsx)(n.A,{open:t,title:s("\u7ed3\u679c\u53d8\u91cf"),width:"80vw",onCancel:c,footer:null,children:(0,o.jsx)(i.A,{isEdit:!!r,result_variable_id:r,resultIsModal:!0,handleCancel:c,handleOk:d})})})}},65913:(e,t,a)=>{a.d(t,{A:()=>s});a(65043);var l=a(20790),n=a(74117),i=(a(80077),a(81143)),o=a(68374);const r=i.Ay.div`
    display: flex;
    width: 100vw;
    height: 100vh;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1001;
    background: rgba(0, 0, 0,.3);

    .loading-center {
        margin: auto;
        text-align: center;
    }

    .text {
        font-size: ${(0,o.D0)("28px")};
        color: ${o.o$.white};
        font-weight: 500;
    }

    .lds-ring {
        display: inline-block;
        position: relative;
        width: 80px;
        height: 80px;
    }
    .lds-ring div {
        box-sizing: border-box;
        display: block;
        position: absolute;
        width: 64px;
        height: 64px;
        margin: 8px;
        border: 8px solid ${o.o$.white};
        border-radius: 50%;
        animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
        border-color: ${o.o$.white} transparent transparent transparent;
    }
    .lds-ring div:nth-child(1) {
        animation-delay: -0.45s;
    }
    .lds-ring div:nth-child(2) {
        animation-delay: -0.3s;
    }
    .lds-ring div:nth-child(3) {
        animation-delay: -0.15s;
    }
    @keyframes lds-ring {
        0% {
        transform: rotate(0deg);
        }
        100% {
        transform: rotate(360deg);
        }
    }
`;var d=a(70579);const s=e=>{const{t:t}=(0,n.Bd)();return(0,d.jsx)(l.A,{children:(0,d.jsx)(r,{children:(0,d.jsxs)("div",{className:"loading-center",children:[(0,d.jsxs)("div",{className:"lds-ring",children:[(0,d.jsx)("div",{}),(0,d.jsx)("div",{}),(0,d.jsx)("div",{})]}),(0,d.jsx)("div",{className:"text",children:t(e.text)||`${t("\u52a0\u8f7d\u4e2d")}...`})]})})})}},67299:(e,t,a)=>{a.d(t,{A:()=>i});var l=a(80077),n=a(14463);const i=()=>{const e=(0,l.wA)(),t=async t=>{e({type:n.of,param:t})},a=async t=>{e({type:n.n$,param:t})},i=async t=>{var a;const l=t?null===t||void 0===t||null===(a=t.split("edit-"))||void 0===a?void 0:a.at(-1):null;e({type:n.qR,param:l})},o=async t=>{e({type:n.Kf,param:t})};return{subMenu:t,subContextMenuId:a,subConfigMain:o,subCurrentDomId:i,clearMenuData:()=>{i(),a(),t(null),o(!0)}}}},67998:(e,t,a)=>{a.d(t,{A:()=>d});var l=a(65043),n=a(36497),i=a(74117),o=a(29977),r=a(70579);const d=e=>{let{inputVariableType:t,...a}=e;const d=(0,o.A)(),{t:s}=(0,i.Bd)(),c=(0,l.useMemo)((()=>d.filter((e=>!t||e.variable_type===t)).map((e=>{let{name:t,code:a}=e;return{label:s(t),value:a}}))),[d,t]);return(0,r.jsx)(n.A,{options:c,...a})}},68130:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=a(67208),i=a(63612);const o=()=>{const e=(0,l.wA)(),t=(0,l.d4)((e=>e.global.optStation)),a=async()=>{const t=await(0,n.IdJ)();t&&e({type:i.tA,param:t})},o=async function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:t;const l=await(0,n.BxM)();if(l&&e({type:i.YS,param:l}),a&&null!==a&&void 0!==a&&a.id){const t=l.find((e=>e.id===a.id));null!==t&&void 0!==t&&t.id&&e({type:i.h5,param:t})}},r=async()=>{const{cfgData:t}=await(0,n.XJm)();t&&e({type:i._c,param:t})};return{initStationInfo:async e=>{await Promise.all([a(),o(e),r()])},updateOptStationGroup:t=>{e({type:i.Mi,param:t})}}}},68945:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(81143);a(68374);const n=l.Ay.div`
  .min-layout {
    transform: scale(0.7);
    transform-origin: bottom;
  }

`;var i=a(70579);const o=e=>{let{sub:t,sup:a,text:l}=e;return t?(0,i.jsx)("sub",{children:l.replace(/{lo|}/g,"")}):a?(0,i.jsx)("sup",{children:l.replace(/{hi|}/g,"")}):l},r=e=>{let{text:t,variables:a=[]}=e;return(0,i.jsx)(n,{children:t.split(/({[^}]+})/).map((e=>{const t=e.includes("{lo"),a=e.includes("{hi"),l=e;return(0,i.jsx)("span",{children:(0,i.jsx)(o,{sub:t,sup:a,text:l})},l||crypto.randomUUID())}))})}},69581:(e,t,a)=>{a.d(t,{A:()=>n});var l=a(80077);const n=(e,t)=>{const a=(0,l.d4)((t=>t.inputVariable.inputVariableMap.get(e)));return null!==a&&void 0!==a?a:t}},70916:(e,t,a)=>{a.d(t,{A:()=>f});var l=a(91688),n=a(80077),i=a(34458),o=a(67208),r=a(3138),d=a(45303),s=a(65694),c=a(74390),A=a(67299),u=a(96181),p=a(44409),v=a(84),m=a(21256),h=a(50540),g=a(36950),b=a(68130),y=a(63612),x=a(754);const f=()=>{const e=(0,n.wA)(),t=(0,l.zy)(),a=(0,l.W6)(),{openDialog:f}=(0,v.A)(),{clearMenuData:C}=(0,A.A)(),{clearTab:w}=(0,u.A)(),{batchWidgetStatus:E}=(0,m.A)(),{submitSubTaskSample:S}=(0,p.A)(),{initRedux:_,clearRedux:R,clearTemplateRedux:j,initSystem:I}=(0,r.A)(),T=(0,n.d4)((e=>e.global.optStation)),P=(0,n.d4)((e=>e.global.stationList)),{initStationInfo:k}=(0,b.A)(),D=()=>{(0,i.xW)(""),e({type:s.wb,param:""}),(0,i.Ql)(""),(0,i.iY)(),C(),w(),j(),I(),S(null),e({type:h.mn,param:!1})},U=async(e,a)=>{const{pageId:l,pageName:n,optStation:r=T}=a||{},d=await(0,o.Anu)({project_id:Number(e)});if(await(0,o.U8W)({project_id:Number(e)})){const a=(0,i.ug)();if(!((null===P||void 0===P?void 0:P.length)>1&&t.pathname===c.ROUTERS.\u9996\u9875.path)){const t=r;(0,i.xL)([{...d,recently_time:(0,g.Ln)(),use_name:a.name},...(0,i.NB)(t).filter((t=>t.project_id!==e))],t)}await N({info:d,isInitRedux:!0,pageId:l||void 0,pageName:n})}},N=async t=>{var a,l,n,o,r,d;let{info:c,isInitRedux:A=!0,pageId:u,pageName:p}=t;const v=!(null===(a=null===c||void 0===c?void 0:c.temporary_flag)||void 0===a||!a);(0,i.xW)(null!==(l=null===c||void 0===c?void 0:c.project_id)&&void 0!==l?l:""),(0,i.WI)(v),(0,i.iY)(c),A&&R(),e({type:s.wb,param:null!==(n=null===c||void 0===c?void 0:c.project_id)&&void 0!==n?n:""});const m=((null===(o=x.A.getState().system)||void 0===o?void 0:o.projectsOpenStatus)||{})[null===c||void 0===c?void 0:c.project_id]||!1;e({type:h.mn,param:m});const g=null!==(r=!(null!==(d=(0,i.Wb)())&&void 0!==d&&d.some((e=>e===(null===c||void 0===c?void 0:c.project_id)))))&&void 0!==r&&r;A&&await _({isNewOpen:g,pageId:u,pageName:p}),(0,i.G7)([...new Set([...(0,i.Wb)(),null===c||void 0===c?void 0:c.project_id])])};return{onOpenProject:U,onOpenTemplate:async e=>{const t=Number(e),a=await(0,o.$Fq)({template_id:t});if(a){const e=(0,i.ug)(),l=await(0,o.Cqh)({template_id:t});(0,i.W5)([{...l,recently_time:(0,g.Ln)(),use_name:e.name,use_id:e.id},...(0,i.IW)().filter((e=>e.template_id!==l.template_id))]),await U(null===a||void 0===a?void 0:a.project_id)}},updateCurrentTest:N,closeProject:async function(l){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:T;const s=Number((0,i.HN)()),A=null!==l&&void 0!==l?l:s;if(await(0,o.ayG)({project_id:Number(A)}),(0,i.G7)((0,i.Wb)().filter((e=>e!==Number(A)))),P.length<=1){const e=null===P||void 0===P?void 0:P.find((e=>e.projectId===Number(A)));null!==e&&void 0!==e&&e.id&&(await(0,o.p9B)({projectId:A}),await(0,o.YZe)({project_id:A}),k(r))}if(P.length>1)if(null!==r&&void 0!==r&&r.id)await(0,o.p9B)({projectId:A}),await(0,o.YZe)({project_id:A}),k(r);else{await(0,o.p9B)({projectId:A});const t=await(0,o._lq)();e({type:y.d_,param:t}),k(r)}if(A===s){if(D(),!n)return;var u;if(P.length<=1)null!==t&&void 0!==t&&null!==(u=t.state)&&void 0!==u&&u.isDataPage?f({type:d.T1}):f({type:d.nI}),a.push({pathname:c.ROUTERS.\u9996\u9875.path});(null===P||void 0===P?void 0:P.length)>1&&t.pathname===c.ROUTERS.\u7075\u6d3b\u5e03\u5c40.path&&a.push({pathname:c.ROUTERS.\u9996\u9875.path}),(0,i.WI)(void 0)}},quitProject:async function(){let{goHomepage:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,i.HN)()?(await E(),D(),e&&((null===P||void 0===P?void 0:P.length)<=1&&a.push({pathname:c.ROUTERS.\u9996\u9875.path}),(null===P||void 0===P?void 0:P.length)>1&&t.pathname===c.ROUTERS.\u7075\u6d3b\u5e03\u5c40.path&&a.push({pathname:c.ROUTERS.\u9996\u9875.path}))):(null===P||void 0===P?void 0:P.length)>1&&t.pathname===c.ROUTERS.\u7075\u6d3b\u5e03\u5c40.path&&a.push({pathname:c.ROUTERS.\u9996\u9875.path})},temporaryProject:async()=>{const e=await(0,o.Anu)();e&&await N({info:e,isInitRedux:!1})}}}},71424:(e,t,a)=>{a.d(t,{A:()=>n});var l=a(69581);const n=(e,t)=>{var a,n;const i=(0,l.A)(e);return null!==(a=null===i||void 0===i||null===(n=i.default_val)||void 0===n?void 0:n.value)&&void 0!==a?a:t}},72295:(e,t,a)=>{a.d(t,{A:()=>v});var l=a(65043),n=a(80077),i=a(19853),o=a.n(i),r=a(56543),d=a(754),s=a(13313),c=a(15637),A=a(41459),u=a(67208);const p={isVisible:r.Jt.BOOL,unit:r.Jt.STRING,isDisabled:r.Jt.BOOL,mode:r.Jt.STRING,isCheck:r.Jt.BOOL,isCheckDisabled:r.Jt.BOOL,onExceedMax:r.Jt.BOOL,onExceedMin:r.Jt.BOOL},v=()=>{const e=(0,n.wA)(),t=function(t,a){let{code:l,...n}=t,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{if(l){const t=d.A.getState().inputVariable.inputVariableMap.get(l);if(!t)return void console.warn("\u672a\u627e\u5230\u8f93\u5165\u53d8\u91cf, code",l);let s;if(a&&(s=a),n&&Object.keys(n).length>0&&(s=Object.keys(n).reduce(((e,t)=>"object"===typeof e[t]?{...e,[t]:{...e[t],...n[t]}}:{...e,[t]:n[t]}),t)),o()(s,t))return;window.isDebug&&console.log("\u4fee\u6539\u8f93\u5165\u53d8\u91cf,code:",l),s.type===r.Ih.GENERAL&&i&&s.is_feature!==t.is_feature&&(0,u.Tnc)(s),e({type:A.j7,param:{code:l,newVariable:s}})}}catch(s){console.log(s)}},a=(0,l.useCallback)((async e=>{var t;let{code:a,value:l}=e;const n=null===d.A||void 0===d.A||null===(t=d.A.getState())||void 0===t?void 0:t.inputVariable.inputVariableMap.get(a);if(!n)return;const i={...n,default_val:{...null===n||void 0===n?void 0:n.default_val,value:l}};await(0,u.Tnc)(i)}),[]);return{updateInputVariableValueDB:(0,l.useCallback)((async e=>{var a;let{code:l,value:n,shouldAsync:i=!0}=e;const o=null===d.A||void 0===d.A||null===(a=d.A.getState())||void 0===a?void 0:a.inputVariable.inputVariableMap.get(l);if(!o)return;const r={...o,default_val:{...null===o||void 0===o?void 0:o.default_val,value:n}};i?await(0,u.Tnc)(r):(0,u.Tnc)(r),t({code:l},r)}),[a]),updateInputVariable:t,updateInputVariableValue:a,correlationVariables:async t=>{e({type:c.Rg,param:t})},subCorrelationVariables:async function(){let e=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;const l=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map((t=>{let{code:l,program_tab:n={}}=t;const i=Object.entries(p).reduce(((t,l)=>{let[i,o]=l;return n[i]&&![s.g.onChange,s.g.onExceedMax,s.g.onExceedMin].includes(n[i])&&(t[i]=function(t){return{script:t,"result-type":arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.Jt.BOOL,subTaskId:e,is_action:a}}(n[i],o)),t}),{});return Object.keys(i).length?{[l]:i}:null})).filter(Boolean);try{if(l&&l.length>0){const e=await(0,u.MPO)({scripts:l,action_context:null===t||void 0===t?void 0:t.scheduler_context});if(e)return e}}catch(n){console.log(n)}return null},subTaskCorrelationVariables:async function(){let e=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0;const a=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map((a=>{let{code:l,program_tab:n={}}=a;const i=Object.entries(p).reduce(((a,l)=>{let[i,o]=l;return n[i]&&(a[i]=function(a){return{script:a,"result-type":arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.Jt.BOOL,subtask_id:e,action_id:t}}(n[i],o)),a}),{});return Object.keys(i).length?{[l]:i}:null})).filter(Boolean);try{if(a&&a.length>0){const e=await(0,u.P_$)({scripts:a});if(e)return e}}catch(l){console.log(l)}return null}}}},72838:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=(a(34458),a(15637)),i=a(67208);const o=()=>{const e=(0,l.wA)();return{initShortcutData:async()=>{try{const t=await(0,i.xeQ)();t&&e({type:n.Yd,param:t})}catch(t){console.log(t)}},clearShortcutData:()=>{e({type:n.Yd,param:[]})},updateShortcut:async t=>{try{await(0,i.sQi)(t),e({type:n.Yd,param:null===t||void 0===t?void 0:t.shortcuts}),Promise.resolve()}catch(a){Promise.reject(a)}}}}},73154:(e,t,a)=>{a.d(t,{A:()=>o});var l=a(80077),n=a(15637),i=a(67208);const o=()=>{const e=(0,l.wA)();return{initGuideData:async()=>{try{const t=await(0,i.Ol6)();t&&e({type:n.gD,param:t})}catch(t){console.log(t)}}}}},73206:(e,t,a)=>{a.d(t,{A:()=>c});var l=a(80077),n=a(16569),i=a(67208),o=(a(45303),a(70916)),r=(a(34458),a(68130)),d=a(15701),s=(a(8237),a(36950),a(33013));a(36581);const c=()=>{(0,l.wA)();const{onOpenProject:e}=(0,o.A)(),t=(0,l.d4)((e=>e.global.stationList)),a=(0,l.d4)((e=>e.global.cfgList)),c=(0,l.d4)((e=>e.global.optStation)),{initStationInfo:A}=((0,l.d4)((e=>e.global.globalMonitoringProjectID)),(0,r.A)()),{initGlobalProjectID:u}=(0,d.A)(),{initProjectList:p}=(0,s.A)(),v=async e=>{let{cfgId:t,projectId:a,stationId:l}=e;await(0,i.C8J)({cfgId:t,projectId:Number(a)}),await(0,i.c2h)({stationId:l,cfgId:t,projectId:Number(a)}),A(c)};return{bingStationProject:async e=>{var l,o;let{optStation:r=c,projectId:d}=e;const{cfgData:s}=await(0,i.XJm)({stationId:null===r||void 0===r?void 0:r.id});if((null===s||void 0===s?void 0:s.length)<=0)throw n.Ay.error("\u5f53\u524d\u7ad9\u672a\u8bbe\u7f6ecfg\uff0c\u8bf7\u5148\u914d\u7f6ecfg"),Error("");const A=null!==(l=null===r||void 0===r?void 0:r.defaultCfgId)&&void 0!==l?l:null===s||void 0===s||null===(o=s[0])||void 0===o?void 0:o.cfgId;await v({cfgId:A,projectId:d,stationId:null===r||void 0===r?void 0:r.id});const u=a.find((e=>e.cfgId===A));n.Ay.success(`\u4e0e\u7ad9:${t[0].stationName}\u4e0a\u7684cfg:${u.cfgName}\u5339\u914d\u6210\u529f,\u5df2\u81ea\u52a8\u7ed1\u5b9a\u6620\u50cf`),p()},bingMonitoringRelationProject:async e=>{let{optStation:t=c,projectId:a}=e;await(0,i.TP4)({projectId:Number(a)}),await u(),A(t)}}}},74125:(e,t,a)=>{a.d(t,{Rg:()=>n,TB:()=>r,YD:()=>o,uO:()=>i});var l=a(97475);const n={"\u4f4d\u79fb":"0","\u8d1f\u8377":"1","\u53d8\u5f62":"2"},i={"\u659c\u6ce2":"RampWave","\u4fdd\u6301":"HoldWave","\u4f59\u5f26":"CosineWave","\u4e09\u89d2":"TriangleWave","\u68af\u5f62\u6ce2":"TrapezoidWave","\u65b9\u6ce2":"SquareWave","\u590d\u6742\u6ce2":"ComplexWave","\u9ad8\u9891":"HighFrequencyWave"},o={"\u9009\u62e9\u5668":"select"},r=[{name:"\u659c\u6ce2",code:i.\u659c\u6ce2,params:[{name:"\u901f\u5ea6",code:"RampWave_Speed",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u76ee\u6807\u503c",code:"RampWave_Target",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u4fdd\u6301\u65f6\u95f4",code:"RampWave_Time",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}}],saveRules:[{name:"\u659c\u6ce2\u6bb5",code:"RampWave",params:[{name:"\u659c\u6ce2\u6bb5",code:"saveDataMode",renderType:l.Q.\u9009\u62e9\u5668,options:[{name:"\u6309\u65f6\u95f4\u95f4\u9694(s)",code:"time",attachParams:[{name:"\u65f6\u95f4\u95f4\u9694",code:"timeInterval",renderType:l.Q.\u6570\u5b57\u8f93\u5165\u6846}]},{name:"\u6309\u63a7\u5236\u5668\u91c7\u96c6\u95f4\u9694",code:"all"}]}]},{name:"\u4fdd\u6301\u6bb5",code:"HoldWave",renderType:l.Q.\u9009\u62e9\u5668,params:[{name:"\u4fdd\u6301\u6bb5",code:"saveDataMode",renderType:l.Q.\u9009\u62e9\u5668,options:[{name:"\u6309\u65f6\u95f4\u95f4\u9694",code:"time",attachParams:[{name:"\u65f6\u95f4\u95f4\u9694",code:"timeInterval",renderType:l.Q.\u6570\u5b57\u8f93\u5165\u6846}]},{name:"\u6309\u63a7\u5236\u5668\u91c7\u96c6\u95f4\u9694",code:"all"}]}]}]},{name:"\u4fdd\u6301",code:i.\u4fdd\u6301,params:[{name:"\u4fdd\u6301\u65f6\u95f4",code:"HoldWave_Time",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}}],saveRules:[{name:"\u6ce2\u6bb5",code:"WaveBand",renderType:l.Q.\u9009\u62e9\u5668,params:[{name:"\u51c6\u5219",code:"saveDataMode",renderType:l.Q.\u9009\u62e9\u5668,options:[{name:"\u6309\u65f6\u95f4\u95f4\u9694",code:"time",attachParams:[{name:"\u65f6\u95f4\u95f4\u9694",code:"timeInterval",renderType:l.Q.\u6570\u5b57\u8f93\u5165\u6846}]},{name:"\u6309\u63a7\u5236\u5668\u91c7\u96c6\u95f4\u9694",code:"all"}]}]}]},{name:"\u4f59\u5f26",code:i.\u4f59\u5f26,params:[{name:"\u9891\u7387",code:"CosineWave_Freq",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u5e45\u503c",code:"CosineWave_Ampl",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u5747\u503c",code:"CosineWave_Mean",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u6b21\u6570",code:"CosineWave_Count"}],saveRules:[{name:"\u6ce2\u6bb5",code:"WaveBand",renderType:l.Q.\u9009\u62e9\u5668,params:[{name:"\u51c6\u5219",code:"saveDataMode",renderType:l.Q.\u9009\u62e9\u5668,options:[{name:"\u6309\u6ce2\u5f62\u95f4\u9694",code:"wave",attachParams:[{name:"\u6ce2\u5f62\u6b21\u6570",code:"Number",renderType:l.Q.\u6570\u5b57\u8f93\u5165\u6846}]},{name:"\u6309\u5cf0\u8c37\u503c",code:"target"}]}]}]},{name:"\u4e09\u89d2",code:i.\u4e09\u89d2,params:[{name:"\u9891\u7387",code:"TriangleWave_Freq",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u5e45\u503c",code:"TriangleWave_Ampl",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u5747\u503c",code:"TriangleWave_Mean",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u6b21\u6570",code:"TriangleWave_Count"}],saveRules:[{name:"\u6ce2\u6bb5",code:"WaveBand",renderType:l.Q.\u9009\u62e9\u5668,params:[{name:"\u51c6\u5219",code:"saveDataMode",renderType:l.Q.\u9009\u62e9\u5668,options:[{name:"\u6309\u6ce2\u5f62\u95f4\u9694",code:"wave",attachParams:[{name:"\u6ce2\u5f62\u6b21\u6570",code:"Number",renderType:l.Q.\u6570\u5b57\u8f93\u5165\u6846}]},{name:"\u6309\u5cf0\u8c37\u503c",code:"target"}]}]}]},{name:"\u68af\u5f62\u6ce2",code:i.\u68af\u5f62\u6ce2,params:[{name:"\u901f\u5ea6",code:"TrapezoidWave_Speed",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u76ee\u6807",code:"TriangleWave_Target",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u4fdd\u6301\u65f6\u95f4",code:"TrapezoidWave_Time",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u901f\u5ea61",code:"TrapezoidWave_Speed1",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u76ee\u68071",code:"TriangleWave_Target1",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u4fdd\u6301\u65f6\u95f41",code:"TrapezoidWave_Time1",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u6b21\u6570",code:"TriangleWave_Count"}],saveRules:[{name:"\u6ce2\u6bb5",code:"WaveBand",renderType:l.Q.\u9009\u62e9\u5668,params:[{name:"\u51c6\u5219",code:"saveDataMode",renderType:l.Q.\u9009\u62e9\u5668,options:[{name:"\u6309\u6ce2\u5f62\u95f4\u9694",code:"wave",attachParams:[{name:"\u6ce2\u5f62\u6b21\u6570",code:"Number",renderType:l.Q.\u6570\u5b57\u8f93\u5165\u6846}]},{name:"\u6309\u5cf0\u8c37\u503c",code:"target"}]}]}]},{name:"\u65b9\u6ce2",code:i.\u65b9\u6ce2,params:[{name:"\u9891\u7387",code:"SquareWave_Freq",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u5e45\u503c",code:"SquareWave_Ampl",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u5747\u503c",code:"SquareWave_Mean",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u6b21\u6570",code:"SquareWave_Count"}],saveRules:[{name:"\u6ce2\u6bb5",code:"WaveBand",renderType:l.Q.\u9009\u62e9\u5668,params:[{name:"\u51c6\u5219",code:"saveDataMode",renderType:l.Q.\u9009\u62e9\u5668,options:[{name:"\u6309\u6ce2\u5f62\u95f4\u9694",code:"wave",attachParams:[{name:"\u6ce2\u5f62\u6b21\u6570",code:"Number",renderType:l.Q.\u6570\u5b57\u8f93\u5165\u6846}]},{name:"\u6309\u5cf0\u8c37\u503c",code:"target"}]}]}]},{name:"\u590d\u6742\u6ce2",code:i.\u590d\u6742\u6ce2,params:[{name:"\u901f\u5ea6",code:"ComplexWave_Speed",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u76ee\u6807\u63a7\u5236\u65b9\u5f0f",code:"ComplexWave_Target_Contorl_Mode",renderType:o.\u9009\u62e9\u5668,options:Object.entries(n).map((e=>{let[t,a]=e;return{label:t,value:a}}))},{name:"\u76ee\u6807\u503c",code:"ComplexWave_Target",targetContorlModeCode:"ComplexWave_Target_Contorl_Mode",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}},{name:"\u4fdd\u6301\u65f6\u95f4",code:"ComplexWave_Time",dimension:{[n.\u4f4d\u79fb]:"",[n.\u8d1f\u8377]:"",[n.\u53d8\u5f62]:""}}],saveRules:[{name:"\u6ce2\u6bb5",code:"WaveBand",renderType:l.Q.\u9009\u62e9\u5668,params:[{name:"\u51c6\u5219",code:"saveDataMode",renderType:l.Q.\u9009\u62e9\u5668,options:[{name:"\u6309\u65f6\u95f4\u95f4\u9694",code:"time",attachParams:[{name:"\u65f6\u95f4\u95f4\u9694",code:"timeInterval",renderType:l.Q.\u6570\u5b57\u8f93\u5165\u6846}]},{name:"\u6309\u63a7\u5236\u5668\u91c7\u96c6\u95f4\u9694",code:"all"}]}]}]}]},74390:(e,t,a)=>{a.r(t),a.d(t,{MainRouter:()=>U,ROUTERS:()=>D});var l=a(65043),n=a(91688),i=a(16569),o=a(12069),r=a(95620),d=a(74117),s=a(80077),c=a(86178),A=a.n(c),u=a(79889),p=a.n(u),v=a(84),m=a(45303),h=a(67208);const g=()=>{const e=(0,n.zy)(),{openDialog:t}=(0,v.A)(),a=()=>{e.pathname!==D.\u5207\u6362\u7528\u6237.path&&(0,h.wHv)().then((e=>{e&&(e.need_today_inspect_count>0||e.need_recent_inspect_count>0)&&t({type:m.hT})}))};(0,l.useEffect)((()=>{(()=>{const e=localStorage.getItem("globalLastSpotCheckDate"),t=(new Date).toDateString();e!==t&&(a(),localStorage.setItem("globalLastSpotCheckDate",t))})();const e=setTimeout((()=>{a();const e=setInterval(a,864e5);return()=>clearInterval(e)}),(()=>{const e=new Date;return new Date(e.getFullYear(),e.getMonth(),e.getDate()+1,1,30,0)-e})());return()=>clearTimeout(e)}),[])};var b=a(60881),y=a(34458),x=a(42225),f=a(4178),C=a(36950),w=a(65913),E=a(70579);const S=()=>{const e=(0,n.zy)(),{verifyDog:t}=(0,x.A)(),a=(0,n.W6)(),i=(0,s.wA)(),{updateTitle:o}=(0,f.A)(),r=(0,s.d4)((e=>e.template.currentPageId)),c=(0,s.d4)((e=>e.template.pageData)),u=(0,s.d4)((e=>e.global.loading)),v=(0,s.d4)((e=>e.global.loadingName)),h=(0,s.d4)((e=>e.global.pageLoading)),{t:S}=(0,d.Bd)();g(),(0,b.A)({startListener:!0}),(0,l.useEffect)((()=>{console.log("\u76d1\u542c\u8def\u7531\u53d8\u5316..."),t()}),[e.pathname]),(0,l.useEffect)((()=>{console.log("\u76d1\u542c\u52a0\u5bc6\u72d7\xb7\xb7\xb7"),R();const l=setInterval((()=>{t()}),3e5);return e.pathname.startsWith("/dialog")||(a.push("/login"),i({type:m.ok})),()=>clearInterval(l)}),[]),(0,l.useEffect)((()=>{_()}),[(0,y.HN)(),(0,y.SU)(),r,c,JSON.stringify((0,y.wB)()),localStorage.getItem("lang")]);const _=(0,l.useCallback)(p()((async()=>{console.log("title\u53d8\u5316...");const e=await(0,C.Fk)(S);o(e)}),2e3),[]),R=()=>{(0,y.ZO)()||(0,y.nJ)(A()())};return(0,E.jsx)(E.Fragment,{children:(u||h)&&(0,E.jsx)(w.A,{text:v})})},_=(0,l.memo)(S);i.Ay.config({top:200});const R=l.lazy((()=>a.e(2605).then(a.bind(a,32605)))),j=l.lazy((()=>a.e(4828).then(a.bind(a,24828)))),I=l.lazy((()=>Promise.all([a.e(9478),a.e(5239),a.e(9372),a.e(8114),a.e(3821),a.e(7326),a.e(2535),a.e(1238),a.e(8185),a.e(1648),a.e(1634),a.e(1043),a.e(8490),a.e(1234),a.e(4917),a.e(4607),a.e(113),a.e(4376),a.e(3481),a.e(2097),a.e(1434),a.e(53),a.e(5575),a.e(1496)]).then(a.bind(a,61496)))),T=l.lazy((()=>Promise.all([a.e(4376),a.e(9954)]).then(a.bind(a,69954)))),P=l.lazy((()=>Promise.all([a.e(9478),a.e(5239),a.e(9372),a.e(8114),a.e(1234),a.e(4917),a.e(113),a.e(3481),a.e(2097),a.e(1785)]).then(a.bind(a,39282)))),k=l.lazy((()=>Promise.all([a.e(1029),a.e(4376),a.e(9755),a.e(991)]).then(a.bind(a,30991)))),D={"\u9996\u9875":{path:"/",title:"\u7cfb\u7edf\u9996\u9875",component:l.lazy((()=>Promise.all([a.e(9755),a.e(973)]).then(a.bind(a,90973))))},"\u7075\u6d3b\u5e03\u5c40":{path:"/dynamicSplit",title:"\u52a8\u6001\u5e03\u5c40",component:T},"\u5207\u6362\u7528\u6237":{path:"/login",title:"\u5207\u6362\u7528\u6237",component:R},"\u5168\u5c40\u76d1\u63a7":{path:"/GlobalMonitoring",component:k}},U=()=>{const e=(0,n.zy)();return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(r.T8,{}),(0,E.jsxs)(r.ku,{isTemplate:e.pathname===D.\u7075\u6d3b\u5e03\u5c40.path,children:[(0,E.jsx)(_,{}),(0,E.jsx)(l.Suspense,{fallback:(0,E.jsx)(E.Fragment,{}),children:(0,E.jsx)(I,{})}),(0,E.jsx)(r.jZ,{children:(0,E.jsx)(o.default,{isHidden:!0})}),(0,E.jsxs)(l.Suspense,{fallback:(0,E.jsx)(E.Fragment,{}),children:[(0,E.jsx)(j,{}),(0,E.jsx)(P,{})]}),(0,E.jsx)(n.dO,{children:Object.entries(D).map((e=>{let[t,a]=e;return(0,E.jsx)(n.qh,{path:a.path,exact:"/"===a.path,render:e=>(0,E.jsx)(l.Suspense,{fallback:(0,E.jsx)(E.Fragment,{}),children:(0,E.jsx)(a.component,{...e})})},t)}))})]})]})}},75122:(e,t,a)=>{a.d(t,{A:()=>c});var l=a(65043),n=a(74117),i=a(56543),o=a(81143);a(68374);const r=o.Ay.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    width: 150px;
    height: 150px;
    cursor: pointer;
    img {
        width: 100px;
        height: 100px;
    }
    .view-name {
        font-size: 14px;
        white-space: nowrap;
    }
    .view-type {
        font-size: 12px;
        color: grey;
    }
`;var d=a(70579);const s=e=>{let{imgObj:t,observer:a,index:o,...s}=e;const{t:c}=(0,n.Bd)(),A=(0,l.useRef)(null);return console.log(t,"imgObj"),(0,l.useEffect)((()=>{A.current&&A.current.children[0]&&a&&(null===a||void 0===a||a.observe(A.current))}),[A]),(0,d.jsxs)(r,{ref:A,"data-index":o,...s,children:[(0,d.jsx)("img",{className:"img","data-src":`${i.mF}${t.id}?t=${(new Date).getTime()}`}),(0,d.jsx)("span",{className:"view-name",children:c(t.name)}),(0,d.jsx)("span",{className:"view-type",children:c(t.category)})]})},c=l.memo(s)},75440:(e,t,a)=>{a.d(t,{A:()=>h});var l=a(65043),n=a(63942),i=a(57297),o=a(34677),r=a.n(o),d=a(682),s=a(81929),c=a.n(s),A=a(81143),u=a(68374);A.Ay.div`
    /* .ant-modal {
        .ant-modal-close {
            top: 15px;
            z-index: 0;
        }
        .ant-modal-header {
            background: rgba(255,255,255,0.8);
            box-shadow: 0px 1px 4px 0px rgba(70,120,254,0.16);
            padding: 15px;
            margin-bottom: 0;
        }
        .ant-modal-content {
            border-radius: 2px;
            background-color: ${u.o$.modalBack} !important;
            padding: 0;
            .ant-modal-body {
                padding: 15px ;
            }
            .ant-modal-footer {
                padding: 15px;
            } 
        }
    }    */

`;const p=A.Ay.div`
    display: flex;
    align-items: center;
    
    .modal-title-icon {
        margin-right: ${(0,u.D0)("12px")};
    }
`;var v=a(70579);const m=(e,t)=>{let{contentHeight:a,...o}=e;const{children:s,showBack:A=!1,onBack:u=()=>{}}=o,[m,h]=(0,l.useState)(!0),[g,b]=(0,l.useState)({left:0,top:0,bottom:0,right:0}),[y,x]=(0,l.useState)({x:0,y:0}),f=(0,l.useRef)(null);return(0,v.jsx)(n.A,{width:"70vw",maskClosable:!1,...o,style:r()({top:"5vw"},o.style),onCancel:()=>{b({left:0,top:0,bottom:0,right:0}),x({x:0,y:0}),h(!0),null===o||void 0===o||o.onCancel()},mask:!1,title:(0,v.jsxs)(p,{children:[A&&(0,v.jsx)("div",{className:"modal-title-icon",children:(0,v.jsx)(d.A,{onClick:()=>u(!1)})}),(0,v.jsx)("div",{style:{width:"100%",cursor:"move"},onMouseOver:()=>{m&&h(!1)},onMouseOut:()=>{h(!0)},children:null===o||void 0===o?void 0:o.title})]}),ref:t,modalRender:e=>(0,v.jsx)(c(),{disabled:m,bounds:g,position:y,onStart:(e,t)=>((e,t)=>{var a;const{clientWidth:l,clientHeight:n}=window.document.documentElement,i=null===(a=f.current)||void 0===a?void 0:a.getBoundingClientRect();i&&b({left:-i.left+t.x,right:l-(i.right-t.x),top:-i.top+t.y,bottom:n-(i.bottom-t.y)})})(0,t),onStop:(e,t)=>((e,t)=>{x({x:t.x,y:t.y})})(0,t),children:(0,v.jsx)("div",{ref:f,children:e})}),children:(0,v.jsx)(i.A,{content:["\u6d4b\u8bd5\u7248\u672c2025/08/01"],inherit:!0,gap:[150,150],font:{fontSize:20,color:"rgba(0,0,0,0.1)"},children:(0,v.jsx)("div",{style:{height:a},children:s})})})},h=(0,l.forwardRef)(m)},78178:(e,t,a)=>{a.d(t,{A:()=>Bt});var l=a(65043),n=a(16569),i=a(63942),o=a(8918),r=a(6051),d=a(83720),s=a(25055),c=a(36497),A=a(32513),u=a(74117),p=a(18650),v=a(97588),m=a(97292),h=a(80077),g=a(33981),b=a(36950),y=a(21043),x=a(56543),f=a(81143),C=a(68374);const w=f.Ay.div`
    display: flex;
    height: 60vh;
    justify-content: center;
    margin-top: 10px;
    overflow: auto;
`;var E=a(34154),S=a(70579);const{TextArea:_}=d.A,R=e=>{var t,a;const{t:n}=(0,u.Bd)(),[i]=s.A.useForm(),[o,r]=(0,l.useState)("horizontal"),[f,C]=(0,l.useState)(null===e||void 0===e?void 0:e.data),[R,j]=(0,l.useState)(!1),I=(0,l.useRef)(),T=(0,h.d4)((e=>e.global.unitList)),{copy:P}=(0,g.A)(I);(0,l.useEffect)((()=>{C(null===e||void 0===e?void 0:e.data)}),[null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.select_tab]),(0,l.useEffect)((()=>{var t,a;null!==e&&void 0!==e&&null!==(t=e.param)&&void 0!==t&&t.variableType&&D("variable_type",null===e||void 0===e||null===(a=e.param)||void 0===a?void 0:a.variableType)}),[e.param.variableType]);const k=e=>{j(!1)},D=(t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");f[e[0]][e[1]]=l}else f[t]=l;let n=f;switch(t){case"is_enable":n={...f,is_feature:!1},C(n);break;case"variable_type":n=B(l),C(n);break;default:C(n)}e.toData(e.parentFieldName,n)},U=()=>e.param.related_result_variable_id?m.O.RESULT_INPUT:e.param.notService?m.O.CONTROL_INPUT:m.O.INPUT,N=e=>"\u65e0"===e?"":e,B=e=>{var t,a,l,n,i,o,r;if(e===x.ps.\u5e03\u5c14\u578b)return{...f,default_val:{value:!1,isConstant:(null===f||void 0===f||null===(a=f.default_val)||void 0===a?void 0:a.isConstant)||0}};if(e===x.ps.\u6570\u5b57\u578b){var d,s,c,A;const{number_tab:e}=f,t=null!==(d=null===(s=T.find((t=>{var a;return t.id===(null===(a=e.unit)||void 0===a?void 0:a.unitType)})))||void 0===s?void 0:s.units)&&void 0!==d?d:[],a=null===t||void 0===t?void 0:t.find((t=>{var a;return t.id===(null===(a=e.unit)||void 0===a?void 0:a.unit)}));return{...f,default_val:{value:0,type:N(e.channel.channel),unit:N(e.unit.unit),unitType:N(e.unit.unitType),proportion:null!==(c=null===a||void 0===a?void 0:a.proportion)&&void 0!==c?c:1,calculate:[],isConstant:(null===(A=f.default_val)||void 0===A?void 0:A.isConstant)||0}}}if([x.ps.\u81ea\u5b9a\u4e49\u6570\u7ec4].includes(e))return{...f,default_val:{value:[],data:[],isConstant:(null===(l=f.default_val)||void 0===l?void 0:l.isConstant)||0}};if(e===x.ps.\u9009\u62e9){var u;const{select_tab:e}=f;return{...f,default_val:{value:(0,b.PM)(e.selection),value_type:(0,b.AZ)(e.selection),isConstant:(null===(u=f.default_val)||void 0===u?void 0:u.isConstant)||0}}}return e===x.ps.\u6587\u672c?{...f,default_val:{value:"",isConstant:(null===(n=f.default_val)||void 0===n?void 0:n.isConstant)||0}}:e===x.ps.Control?{...f,default_val:(0,y.U1)((0,y.Sq)(f.control_tab).groups,{value:[],isConstant:(null===(i=f.default_val)||void 0===i?void 0:i.isConstant)||0})}:e===x.ps.\u4e8c\u7ef4\u6570\u7ec4?{...f,default_val:{value:[],isConstant:(null===(o=f.default_val)||void 0===o?void 0:o.isConstant)||0}}:e===x.ps.\u65f6\u95f4\u65e5\u671f?{...f,default_val:{value:"",isConstant:(null===(r=f.default_val)||void 0===r?void 0:r.isConstant)||0}}:{...f,default_val:{value:"",isConstant:(null===f||void 0===f||null===(t=f.default_val)||void 0===t?void 0:t.isConstant)||0}}};return(0,S.jsx)(w,{children:(0,S.jsxs)(s.A,{...E.Gt,labelAlign:"left",layout:o,form:i,initialValues:{layout:o},children:["edit"===e.mode?(0,S.jsx)(s.A.Item,{hidden:!0,label:n("id"),children:(0,S.jsx)(d.A,{name:"id",readOnly:!0,value:null===f||void 0===f?void 0:f.id,placeholder:n("")})}):null,(0,S.jsx)(s.A.Item,{label:n("\u540d\u79f0"),required:!0,children:(0,S.jsx)(d.A,{name:"name",required:!0,value:null===f||void 0===f?void 0:f.name,placeholder:n(""),onChange:e=>D("name",e),maxLength:50})}),(0,S.jsx)(s.A.Item,{label:n("\u5185\u90e8\u540d"),required:!0,onClick:t=>"edit"===e.mode&&P(`${U()+(null===f||void 0===f?void 0:f.code)}`),children:(0,S.jsx)(d.A,{disabled:"edit"===e.mode,title:n(`${U()}${null===f||void 0===f?void 0:f.code}`),name:"code",prefix:U(),value:null===f||void 0===f?void 0:f.code,placeholder:n(""),onChange:e=>D("code",e)})}),(0,S.jsx)(s.A.Item,{label:n("\u7c7b\u578b"),children:(0,S.jsxs)(c.A,{disabled:"edit"===e.mode||!(null===e||void 0===e||null===(a=e.param)||void 0===a||!a.variableType),name:"variable_type",value:null===f||void 0===f?void 0:f.variable_type,onChange:e=>D("variable_type",e),children:[(0,S.jsx)(c.A.Option,{name:"type",value:"Number",children:n("\u6570\u5b57\u578b")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Text",children:n("\u6587\u672c")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Select",children:n("\u9009\u62e9")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Boolean",children:n("\u5e03\u5c14\u578b")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Buffer",children:n("Buffer")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Control",children:n("\u52a0\u51cf\u63a7\u4ef6")}),(0,S.jsx)(c.A.Option,{name:"type",value:"DateTime",children:n("\u65f6\u95f4\u65e5\u671f")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Label",children:n("Label")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Button",children:n("\u6309\u94ae")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Array",children:n("\u81ea\u5b9a\u4e49\u6570\u7ec4")}),(0,S.jsx)(c.A.Option,{name:"type",value:"Picture",children:n("PIC")}),(0,S.jsx)(c.A.Option,{name:"type",value:"DoubleArray",children:n("\u4e8c\u7ef4\u6570\u7ec4")}),(0,S.jsx)(c.A.Option,{name:"type",value:"DoubleArrayList",children:n("\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408")})]})}),![x.ps.\u4e8c\u7ef4\u6570\u7ec4,x.ps.\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408].includes(null===f||void 0===f?void 0:f.variable_type)&&(0,S.jsxs)(S.Fragment,{children:[(null===f||void 0===f?void 0:f.variable_type)!==x.ps.\u5e03\u5c14\u578b&&(0,S.jsx)(s.A.Item,{label:n("\u53c2\u6570\u53ef\u4ee5\u4f7f\u7528"),children:(0,S.jsx)(A.A,{name:"is_enable",checked:null===f||void 0===f?void 0:f.is_enable,onChange:e=>D("is_enable",e)})}),(0,S.jsx)(s.A.Item,{label:n("\u53ef\u4ee5fx/#\u6309\u94ae"),children:(0,S.jsx)(A.A,{name:"is_fx",checked:null===f||void 0===f?void 0:f.is_fx,onChange:e=>D("is_fx",e)})}),(0,S.jsx)(s.A.Item,{label:n("\u56fe\u7247"),valuePropName:"fileList",children:(0,S.jsx)(v.A,{src:(null===f||void 0===f?void 0:f.pic)||p.Np,btnCLick:()=>{j(!0)},btnTitle:n("\u9009\u62e9\u56fe\u7247"),open:R,onCancel:k,onChange:t=>{f.pic=t,C({...f}),e.toData(e.parentFieldName,f),k()},modalTitle:n("\u9009\u62e9\u56fe\u7247")})})]}),(0,S.jsx)(s.A.Item,{label:n("\u5168\u5c40\u53d8\u91cf"),children:(0,S.jsx)(A.A,{name:"is_overall",checked:null===f||void 0===f?void 0:f.is_overall,onChange:e=>D("is_overall",e)})}),(0,S.jsx)(s.A.Item,{label:n("\u63cf\u8ff0"),children:(0,S.jsx)(_,{name:"description",rows:3,value:null===f||void 0===f?void 0:f.description,placeholder:n(""),onChange:e=>D("description",e),maxLength:30})})]})})};var j=a(97914),I=a(47419),T=a(11645);const P=f.Ay.div`
    display: flex;
    height: 60vh;
    justify-content: center;
    margin-top: 10px;
    .form-container {
      width: 40%;
    }

`,k=e=>{const{t:t,i18n:a}=(0,u.Bd)(),n=(0,h.d4)((e=>e.global.unitList)),[i]=s.A.useForm(),[o,r]=(0,l.useState)("horizontal"),[d,A]=(0,l.useState)(null===e||void 0===e?void 0:e.data),p=(0,l.useMemo)((()=>{var t,a,l;const i=n.find((t=>t.id===e.defaultVal.unitType));return null!==(t=null===i||void 0===i||null===(a=i.units)||void 0===a||null===(l=a.find((e=>e.id===i.default_unit_id)))||void 0===l?void 0:l.name)&&void 0!==t?t:""}),[e,n]);(0,l.useEffect)((()=>{d&&i.setFieldsValue(d)}),[d]);const v=(t,a)=>{var l;let n=a;if(null!=(null===(l=n)||void 0===l?void 0:l.target)&&(n="checkbox"===n.target.type?a.target.checked:null!=n.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");d[e[0]][e[1]]=n}else d[t]=n;A({...d}),a!==x.Me.REASONABLE_MAX_MIN&&"defaultVal"!==t||m({value:"defaultVal"===t?a:d.defaultVal}),e.toData(e.parentFieldName,d)},m=t=>{e.toData(e.parentFieldKey,{...e.defaultVal,...t})};return(0,S.jsx)(P,{children:(0,S.jsxs)(s.A,{...E.Gt,labelAlign:"left",layout:o,form:i,initialValues:{layout:o},className:"form-container",children:[(0,S.jsx)(s.A.Item,{label:t("\u53d8\u91cf\u540d\u79f0"),children:(0,S.jsxs)(c.A,{name:"reasonableType",value:null===d||void 0===d?void 0:d.reasonableType,onChange:e=>v("reasonableType",e),children:[(0,S.jsx)(c.A.Option,{name:"reasonableType",value:x.Me.EMPTY,children:t("\u4e0d\u8bbe\u7f6e\u5408\u7406\u503c")}),(0,S.jsx)(c.A.Option,{name:"reasonableType",value:x.Me.MAX_MIN,children:t("\u6700\u5927/\u6700\u5c0f\u503c")}),(0,S.jsx)(c.A.Option,{name:"reasonableType",value:x.Me.REASONABLE_MAX_MIN,children:t("\u5408\u7406\u503c\u4e0a\u4e0b\u9650")})]})}),(null===d||void 0===d?void 0:d.reasonableType)===x.Me.REASONABLE_MAX_MIN?(0,S.jsx)(s.A.Item,{label:t("\u9ed8\u8ba4\u503c"),children:(0,S.jsx)(j.A,{style:{width:"100%"},placeholder:t(""),name:"defaultVal",addonAfter:p,value:null===d||void 0===d?void 0:d.defaultVal,onChange:e=>v("defaultVal",e)})}):null,(null===d||void 0===d?void 0:d.reasonableType.indexOf("deviation"))>-1?(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(s.A.Item,{label:"-",children:(0,S.jsx)(j.A,{style:{width:"100%"},min:0,suffix:(null===d||void 0===d?void 0:d.reasonableType.indexOf("%"))>-1?"%":"",placeholder:t(""),name:"minRange",value:null===d||void 0===d?void 0:d.minRange,onChange:e=>v("minRange",e)})})}),(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(s.A.Item,{label:"+",children:(0,S.jsx)(j.A,{style:{width:"100%"},min:0,suffix:(null===d||void 0===d?void 0:d.reasonableType.indexOf("%"))>-1?"%":"",placeholder:t(""),name:"maxRange",value:null===d||void 0===d?void 0:d.maxRange,onChange:e=>v("maxRange",e)})})})]}):null,(null===d||void 0===d?void 0:d.reasonableType)===x.Me.MAX_MIN||(null===d||void 0===d?void 0:d.reasonableType)===x.Me.REASONABLE_MAX_MIN?(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(s.A.Item,{name:"maxParam",label:t("\u6700\u5927\u503c"),children:(0,S.jsx)(j.A,{style:{width:"100%"},name:"maxParam",value:(null===d||void 0===d?void 0:d.maxParam)||(null===d||void 0===d?void 0:d.MaxParam),onChange:e=>v("maxParam",e),addonAfter:p})})}),(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(s.A.Item,{name:"minParam",label:t("\u6700\u5c0f\u503c"),children:(0,S.jsx)(j.A,{style:{width:"100%"},name:"minParam",value:null===d||void 0===d?void 0:d.minParam,onChange:e=>v("minParam",e),addonAfter:p})})})]}):null]})})};var D=a(54962),U=a(63390),N=a(46085),B=a(4554);const O=f.Ay.div`
    display: flex;
    justify-content: space-between;
    height: 56.5vh;
    margin-top: 10px;
    .list-layout {
        width: 12vw;
        display: flex;
        padding: 10px;
        flex-direction: column;
        height: 56.5vh;
        overflow-y: auto;
        cursor: pointer;
        .program {
            padding: 5px;
            display: flex;
            justify-content: space-between;
        }
        .item {
            background: #F5F7FF;
            border-radius: 4px;
        }
        
    }
    .content {
        width:45vw;
    }

`;var M=a(13313);const{TextArea:L}=d.A,V=e=>{var t;const{t:a,i18n:n}=(0,u.Bd)(),[i,o]=(0,l.useState)(),[r,d]=(0,l.useState)(!1),[s,c]=(0,l.useState)(),[A,p]=(0,l.useState)(M.E),[v,m]=(0,l.useState)(null===e||void 0===e?void 0:e.data);(0,l.useEffect)((()=>{h(e.type)}),[e.type]);const h=e=>{var t,a;switch(e){case x.ps.\u6570\u5b57\u578b:b(null===M.E||void 0===M.E?void 0:M.E[0]);break;case x.ps.\u5e03\u5c14\u578b:case x.ps.\u9009\u62e9:case x.ps.\u4e8c\u7ef4\u6570\u7ec4:case x.ps.\u6587\u672c:case x.ps.Control:case x.ps.\u65f6\u95f4\u65e5\u671f:p(M.E.filter((e=>![M.g.mode,M.g.unit].includes(e.id)))),b(null===(t=M.E.filter((e=>![M.g.mode,M.g.unit].includes(e.id))))||void 0===t?void 0:t[0]);break;case x.ps.Label:case x.ps.Picture:p(M.E.filter((e=>![M.g.mode,M.g.isDisabled,M.g.isCheck,M.g.unit].includes(e.id)))),b(null===(a=M.E.filter((e=>![M.g.mode,M.g.isDisabled,M.g.isCheck,M.g.unit].includes(e.id))))||void 0===a?void 0:a[0])}},g=(t,a)=>{if(null==t)return;let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");v[e[0]][e[1]]=l}else v[t]=l;m({...v}),e.toData(e.parentFieldName,v)},b=e=>{console.log(e),c(v[e.varname]),o(e)};return(0,S.jsxs)(O,{children:[(0,S.jsx)(D.A,{title:a("\u7a0b\u5e8f\u5217\u8868"),operate:(0,S.jsx)(B.A,{size:"small",onClick:()=>{d(!0)},children:a("\u7f16\u8f91\u5668")}),children:(0,S.jsx)("div",{className:"list-layout",children:A.map((e=>{var t;return(0,S.jsxs)("div",{className:(null===i||void 0===i?void 0:i.id)===e.id?"program item":"program",onClick:()=>b(e),children:[(0,S.jsx)("div",{children:e.title}),(null===(t=v[e.varname])||void 0===t?void 0:t.trim().length)>0&&(0,S.jsx)(U.A,{})]},e.id)}))})}),(0,S.jsx)(D.A,{title:a("\u7a0b\u5e8f"),children:(0,S.jsx)("div",{className:"content",children:(0,S.jsx)(N.im,{height:"50vh",value:null!==(t=v[null===i||void 0===i?void 0:i.varname])&&void 0!==t?t:"",module:N.et.\u8f93\u5165\u53d8\u91cf,onChange:e=>g(null===i||void 0===i?void 0:i.varname,e)})})}),(0,S.jsx)(N.RN,{open:r,module:N.et.\u8f93\u5165\u53d8\u91cf,script:s,onOk:e=>{c(e),g(null===i||void 0===i?void 0:i.varname,e),d(!1)},onCancel:()=>{d(!1),c("")}})]})};var Q=a(96603);const F=f.Ay.div`
    display: flex;
    height: 58vh;
    justify-content: center;
    margin-top: 10px;
    overflow: auto;
    
`,J=e=>{const{t:t,i18n:a}=(0,u.Bd)(),n=(0,h.d4)((e=>e.template.actionList)),[i]=s.A.useForm(),[o,m]=(0,l.useState)(null===e||void 0===e?void 0:e.data),[g,b]=(0,l.useState)(!1);(0,l.useEffect)((()=>{null!==o&&void 0!==o&&o.content||y("isEnable",!1)}),[null===o||void 0===o?void 0:o.content]);const y=(t,a)=>{let l=null!==a&&void 0!==a?a:{target:null};if(l&&l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");o[e[0]][e[1]]=l}else o[t]=l;m({...o}),e.toData(e.parentFieldName,o)},x=e=>{b(!1)};return(0,S.jsx)(F,{children:(0,S.jsxs)(s.A,{...E.Gt,labelAlign:"left",layout:"horizontal",form:i,initialValues:{layout:"horizontal"},children:[(0,S.jsx)(s.A.Item,{label:t("\u542f\u7528\u6309\u94ae"),children:(0,S.jsx)(A.A,{name:"isEnable",checked:null===o||void 0===o?void 0:o.isEnable,onChange:e=>y("isEnable",e),disabled:""===(null===o||void 0===o?void 0:o.content)})}),(0,S.jsx)(s.A.Item,{label:t("\u6309\u94ae\u6587\u672c"),children:(0,S.jsx)(d.A,{maxLength:4,placeholder:t("\u6700\u591a\u8f93\u51654\u4e2a\u5b57\u7b26"),style:E.qw,name:"content",value:null===o||void 0===o?void 0:o.content,onChange:e=>y("content",e)})}),(0,S.jsx)(s.A.Item,{label:t("\u6309\u94ae\u56fe\u6807"),children:(0,S.jsx)(v.A,{src:(null===o||void 0===o?void 0:o.pic)||p.Np,btnCLick:()=>{b(!0)},btnTitle:t("\u9009\u62e9\u56fe\u7247"),open:g,onCancel:x,onChange:t=>{o.pic=t,m({...o}),e.toData(e.parentFieldName,o),x()},modalTitle:t("\u9009\u62e9\u56fe\u7247")})}),(0,S.jsx)(s.A.Item,{label:t("\u52a8\u4f5c\u6e90"),children:(0,S.jsxs)(r.A,{direction:"vertical",children:[(0,S.jsx)(Q.Ay.Group,{defaultValue:"action_lib",value:null===o||void 0===o?void 0:o.source,onChange:e=>y("source",e),options:[{label:t("\u52a8\u4f5c\u5e93"),value:"action_lib"},{label:t("\u7f16\u8f91\u5668"),value:"editor"}]}),"editor"===(null===o||void 0===o?void 0:o.source)?(0,S.jsx)(N.im,{value:null===o||void 0===o?void 0:o.function,width:"11vw",height:"10vw",module:N.et.\u8f93\u5165\u53d8\u91cf,onChange:e=>y("function",e)}):(0,S.jsx)(c.A,{showSearch:!0,allowClear:!0,optionFilterProp:"action_name",options:n,fieldNames:{label:"action_name",value:"action_id"},style:{width:"12vw"},name:"actionId",value:null===o||void 0===o?void 0:o.actionId,onChange:e=>y("actionId",e)})]})})]})})};var Y=a(41445);const K=f.Ay.div`
    display: flex;
    align-items: space-between;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0 1vw 1vh 1vw;
    .content {
        display: flex;
        /* align-items: center; */
        justify-content: center;
        width: 27.5vw;
        height: 27vh;
        background: #FFF;
        overflow: auto;
        margin-top: 1.8vh;
        box-shadow: 0px 0px 5px 0px rgba(0,16,58,0.07);
        border-radius: 4px;
        .ant-form {
            margin-top: 10px;
        }
        .form-content {
            padding: 10px;
        }

    }

`,H=e=>{var t,a,i,o,r,p,v,m,g,y,f,C,w,E,_,R,I,T,P,k,D,U,N,B,O,M,L,V,Q,F,J;const{t:H,i18n:W}=(0,u.Bd)(),G=(0,h.d4)((e=>e.global.unitList)),z=(0,h.d4)((e=>e.template.signalGroups)),[X]=s.A.useForm(),[Z,q]=(0,l.useState)("horizontal"),[$,ee]=(0,l.useState)([]),te=(0,l.useRef)(null),[ae,le]=(0,l.useState)(null===e||void 0===e?void 0:e.data),[ne,ie]=(0,l.useState)([]);(0,l.useEffect)((()=>{ae.multipleMeasurements.measurementCounts&&X.setFieldsValue({measurementCounts:ae.multipleMeasurements.measurementCounts})}),[G]),(0,l.useEffect)((()=>{var e,t;const a=[...z,{group_name:"\u65e0",group_id:"\u65e0"}];ie(a);const l=null===a||void 0===a||null===(e=a.find((e=>{var t;return e.group_id===(null===(t=ae.channel)||void 0===t?void 0:t.channelType)})))||void 0===e?void 0:e.variable_ids;l&&"\u65e0"!==(null===(t=ae.channel)||void 0===t?void 0:t.channelType)?ee(l):ee(l?[...l,{variable_name:"\u65e0",code:"\u65e0"}]:[{variable_name:"\u65e0",code:"\u65e0"}])}),[z,null===(t=ae.channel)||void 0===t?void 0:t.channelType]);const oe=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,a=0;if(ae.multipleMeasurements.measurementCounts>1){const l=t;l===x.Hv.MAX&&(a=Math.max(...e)),l===x.Hv.MIN&&(a=Math.min(...e)),l===x.Hv.AVG&&(a=e.reduce(((e,t)=>e+t))/e.length),l===x.Hv.MID&&(a=(e=>{const t=e.slice().sort(((e,t)=>e-t)),a=t.length;return a%2===1?t[Math.floor(a/2)]:(t[a/2-1]+t[a/2])/2})(e))}return a},re=(t,a)=>{var l;let n=a;if(null!=(null===(l=n)||void 0===l?void 0:l.target)&&(n="checkbox"===n.target.type?a.target.checked:null!=n.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");ae[e[0]][e[1]]=n}else ae[t]=n;var i,o;(le({...ae}),["format.numberRequire","format.formatType","format.pointPosition","format.beforePoint","format.afterPoint","format.significantDigits","format.amendmentInterval"].some((e=>e===t)))&&de({value:(0,b.jq)(null===(i=ae.format)||void 0===i?void 0:i.formatType,e.defaultVal.value,(0,b.Vl)(ae))});"unit.unitType"===t&&de({unitType:a,unit:null===(o=G.filter((e=>e.id===a)))||void 0===o?void 0:o.default_unit_id});"unit.unit"===t&&de({unit:a}),"multipleMeasurements.measurementType"===t&&de({value:oe(e.defaultVal.calculate||[],a)}),"channel.channel"===t&&de({type:a}),e.toData(e.parentFieldName,ae)},de=t=>{e.toData(e.parentFieldKey,{...e.defaultVal,...t})},se=()=>{ae.unit.unitType="",ae.unit.unit="",ae.channel.channel="",de({type:""}),ae.unit.lockChannels=[]},ce=()=>{const e=G.filter((e=>{var t;return e.id===(null===(t=$.find((e=>e.code===ae.channel.channel)))||void 0===t?void 0:t.dimension_id)}));return e&&0===e.length?[...G,{name:"\u65e0",id:"\u65e0",units:[{name:"\u65e0",id:"\u65e0"}]}]:[...e,{name:"\u65e0",id:"\u65e0",units:[{name:"\u65e0",id:"\u65e0"}]}]},Ae=()=>{var e;const t=ce();return t&&0!==(null===t||void 0===t?void 0:t.length)?null===(e=t.find((e=>e.id===ae.unit.unitType)))||void 0===e?void 0:e.units:[]},ue={labelCol:{span:16},wrapperCol:{span:12}},pe={width:"8vw"};return(0,S.jsxs)(K,{children:[(0,S.jsx)("div",{className:"content",children:(0,S.jsxs)(s.A,{...ue,labelAlign:"left",form:X,initialValues:{layout:Z},children:[(0,S.jsx)(s.A.Item,{label:H("\u6570\u503c\u8981\u6c42"),children:(0,S.jsxs)(c.A,{name:"format.numberRequire",value:null===ae||void 0===ae||null===(a=ae.format)||void 0===a?void 0:a.numberRequire,onChange:e=>re("format.numberRequire",e),style:pe,children:[(0,S.jsx)(c.A.Option,{value:x.Uk.ANY,children:H("\u4efb\u610f")}),(0,S.jsx)(c.A.Option,{value:x.Uk.NOT_0,children:H("!=0(\u4e0d\u7b49\u4e8e\u96f6)")}),(0,S.jsx)(c.A.Option,{value:x.Uk.GT_0,children:H(">0(\u5927\u4e8e\u96f6)")}),(0,S.jsx)(c.A.Option,{value:x.Uk.GE_0,children:H("\u22650(\u5927\u4e8e\u7b49\u4e8e\u96f6)")})]})}),(0,S.jsx)(s.A.Item,{label:H("\u683c\u5f0f"),children:(0,S.jsxs)(c.A,{name:"format.formatType",value:null===ae||void 0===ae||null===(i=ae.format)||void 0===i?void 0:i.formatType,onChange:e=>re("format.formatType",e),style:pe,children:[(0,S.jsx)(c.A.Option,{value:x.Ho.AUTO,children:H("\u81ea\u52a8")}),(0,S.jsx)(c.A.Option,{value:x.Ho.POWER,children:H("\u5e42\u6570\u578b")}),(0,S.jsx)(c.A.Option,{value:x.Ho.CUSTOM,children:H("\u5ba2\u6237\u5b9a\u4e49")}),(0,S.jsx)(c.A.Option,{value:x.Ho.VALID,children:H("\u6709\u6548\u5b9a\u4e49")}),(0,S.jsx)(c.A.Option,{value:x.Ho.ROUNDING,children:H("\u4fee\u7ea6\u533a\u95f4")}),(0,S.jsx)(c.A.Option,{value:x.Ho.NUMBER_COUNT,children:H("\u6570\u5b57\u4e2a\u6570")})]})}),(null===ae||void 0===ae||null===(o=ae.format)||void 0===o?void 0:o.formatType)===x.Ho.POWER?(0,S.jsx)(s.A.Item,{label:H("\u5c0f\u6570\u6570\u4f4d"),children:(0,S.jsx)(j.A,{min:0,style:pe,placeholder:H(""),name:"format.pointPosition",value:null===ae||void 0===ae||null===(r=ae.format)||void 0===r?void 0:r.pointPosition,onChange:e=>re("format.pointPosition",e)})}):null,(null===ae||void 0===ae||null===(p=ae.format)||void 0===p?void 0:p.formatType)===x.Ho.CUSTOM?(0,S.jsx)("div",{children:(0,S.jsx)(s.A.Item,{label:H("\u5c0f\u6570\u70b9\u540e(A)"),children:(0,S.jsx)(d.A,{style:pe,maxLength:1,name:"format.afterPoint",value:null===ae||void 0===ae||null===(v=ae.format)||void 0===v?void 0:v.afterPoint,onChange:e=>re("format.afterPoint",e)})})}):null,(null===ae||void 0===ae||null===(m=ae.format)||void 0===m?void 0:m.formatType)===x.Ho.VALID?(0,S.jsx)(s.A.Item,{label:H("\u6709\u6548\u6570\u5b57(G)"),children:(0,S.jsx)(j.A,{style:pe,min:1,name:"format.significantDigits",value:null===ae||void 0===ae||null===(g=ae.format)||void 0===g?void 0:g.significantDigits,onChange:e=>re("format.significantDigits",e)})}):null,(null===ae||void 0===ae||null===(y=ae.format)||void 0===y?void 0:y.formatType)===x.Ho.NUMBER_COUNT?(0,S.jsx)(s.A.Item,{label:H("\u6570\u5b57\u4e2a\u6570"),children:(0,S.jsx)(j.A,{style:pe,min:1,name:"format.numberCount",value:null===ae||void 0===ae||null===(f=ae.format)||void 0===f?void 0:f.numberCount,onChange:e=>re("format.numberCount",e)})}):null,(null===ae||void 0===ae||null===(C=ae.format)||void 0===C?void 0:C.formatType)===x.Ho.ROUNDING?(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(s.A.Item,{label:H("\u4fee\u7ea6\u65b9\u5f0f"),hidden:!0,tooltip:(0,S.jsx)(Y.qv,{t:H}),children:(0,S.jsx)(c.A,{name:"format.roundMode",value:null===ae||void 0===ae||null===(w=ae.format)||void 0===w?void 0:w.roundMode,onChange:e=>re("format.roundMode",e),options:Y.$e,style:pe})}),(0,S.jsx)(s.A.Item,{label:H("\u9608\u503c(1)"),hidden:!0,children:(0,S.jsx)(j.A,{style:pe,name:"format.threshold1",value:null===ae||void 0===ae||null===(E=ae.format)||void 0===E?void 0:E.threshold1,onChange:e=>re("format.threshold1",e)})}),(0,S.jsx)(s.A.Item,{label:H("\u9608\u503c(2)"),hidden:!0,children:(0,S.jsx)(j.A,{style:pe,name:"format.threshold2",value:null===ae||void 0===ae||null===(_=ae.format)||void 0===_?void 0:_.threshold2,onChange:e=>re("format.threshold2",e)})}),(0,S.jsx)(s.A.Item,{label:H("\u4fee\u7ea6\u4f4d\u6570"),children:(0,S.jsx)(c.A,{style:pe,options:Y.cx,name:"format.roundType1",value:null===ae||void 0===ae||null===(R=ae.format)||void 0===R?void 0:R.roundType1,onChange:e=>re("format.roundType1",e)})}),(0,S.jsx)(s.A.Item,{label:H("\u4fee\u7ea6\u4f4d\u6570(2)"),hidden:!0,children:(0,S.jsx)(c.A,{style:pe,options:Y.cx,name:"format.roundType2",value:null===ae||void 0===ae||null===(I=ae.format)||void 0===I?void 0:I.roundType2,onChange:e=>re("format.roundType2",e)})}),(0,S.jsx)(s.A.Item,{label:H("\u4fee\u7ea6\u4f4d\u6570(3)"),hidden:!0,children:(0,S.jsx)(c.A,{style:pe,options:Y.cx,name:"format.roundType3",value:null===ae||void 0===ae||null===(T=ae.format)||void 0===T?void 0:T.roundType3,onChange:e=>re("format.roundType3",e)})})]}):null]})}),(0,S.jsx)("div",{className:"content",children:(0,S.jsxs)(s.A,{...ue,labelAlign:"left",layout:Z,form:X,initialValues:{layout:Z},children:[(0,S.jsx)(s.A.Item,{name:"measurementCounts",label:H("\u6d4b\u91cf\u503c\u4e2a\u6570"),children:(0,S.jsx)(j.A,{placeholder:H(""),style:pe,max:9,min:1,onInput:e=>{0===Number(e)&&(n.Ay.warning(H("\u6d4b\u91cf\u503c\u4e2a\u6570\u4e0d\u80fd\u7b49\u4e8e0")),X.setFieldsValue({measurementCounts:1}),ae.multipleMeasurements.measurementCounts=1),Number(e)<0&&(n.Ay.warning(H("\u6d4b\u91cf\u503c\u4e2a\u6570\u4e0d\u80fd\u5c0f\u4e8e0")),X.setFieldsValue({measurementCounts:1}),ae.multipleMeasurements.measurementCounts=1)},ref:te,name:"multipleMeasurements.measurementCounts",value:ae.multipleMeasurements.measurementCounts,onChange:t=>{var a,l;const n=t||0;re("multipleMeasurements.measurementCounts",t);const i=(null===(a=Array.from({length:n}))||void 0===a?void 0:a.map(((t,a)=>{var l,n;return(null===e||void 0===e||null===(l=e.defaultVal)||void 0===l||null===(n=l.calculate)||void 0===n?void 0:n[a])||1})))||[],o=oe(i,null===(l=ae.multipleMeasurements)||void 0===l?void 0:l.measurementType)||1;if(ae.multipleMeasurements.measurementCounts>1&&"\u65e0"!==ae.channel.channelType){ae.channel.channelType="\u65e0",ae.channel.channel="\u65e0";const e=0===ce().length?"":ce()[0].id;ae.unit.unitType=e;const t=0===Ae().length?"":Ae()[0].id;ae.unit.unit=t,de({unit:t,unitType:e,type:"",calculate:i,value:o})}de({calculate:i,value:o})}})}),(0,S.jsx)(s.A.Item,{label:H("\u53d6\u503c\u65b9\u5f0f"),children:(0,S.jsxs)(c.A,{disabled:1===(null===ae||void 0===ae||null===(P=ae.multipleMeasurements)||void 0===P?void 0:P.measurementCounts),name:"multipleMeasurements.measurementType",value:null===ae||void 0===ae||null===(k=ae.multipleMeasurements)||void 0===k?void 0:k.measurementType,onChange:e=>re("multipleMeasurements.measurementType",e),style:pe,children:[(0,S.jsx)(c.A.Option,{value:x.Hv.MIN,children:H("\u6700\u5c0f\u503c")}),(0,S.jsx)(c.A.Option,{value:x.Hv.MAX,children:H("\u6700\u5927\u503c")}),(0,S.jsx)(c.A.Option,{value:x.Hv.AVG,children:H("\u5e73\u5747\u503c")}),(0,S.jsx)(c.A.Option,{value:x.Hv.MID,children:H("\u4e2d\u503c")})]})})]})}),(0,S.jsx)("div",{className:"content",children:(0,S.jsx)(s.A,{...ue,labelAlign:"left",layout:Z,form:X,initialValues:{layout:Z},style:{height:"25vh"},children:(0,S.jsxs)("div",{className:"form-content",children:[(0,S.jsx)(s.A.Item,{label:H("\u7c7b\u578b"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"group_name",name:"channel.channelType",style:pe,value:null===ae||void 0===ae||null===(D=ae.channel)||void 0===D?void 0:D.channelType,fieldNames:{label:"group_name",value:"group_id"},options:ne,onChange:t=>{re("channel.channelType",t);const a=null===ne||void 0===ne?void 0:ne.find((e=>e.group_id===t)),l=null===a||void 0===a?void 0:a.variable_ids;var n,i,o,r,d,s;l?(ee(l),de({type:null===l||void 0===l||null===(n=l[0])||void 0===n?void 0:n.code,unit:null===l||void 0===l||null===(i=l[0])||void 0===i?void 0:i.unit_id,unitType:null===l||void 0===l||null===(o=l[0])||void 0===o?void 0:o.dimension_id}),ae.channel.channel=null===l||void 0===l||null===(r=l[0])||void 0===r?void 0:r.code,ae.unit.unitType=null===l||void 0===l||null===(d=l[0])||void 0===d?void 0:d.dimension_id,ae.unit.unit=null===l||void 0===l||null===(s=l[0])||void 0===s?void 0:s.unit_id):(ee([{variable_name:"\u65e0",code:"\u65e0"}]),ae.channel.channel="\u65e0",de({type:"\u65e0",unitType:"",unit:""}),ae.unit.unitType="",ae.unit.unit="");"\u65e0"!==ae.channel.channelType&&(ae.multipleMeasurements.measurementCounts=1,X.setFieldsValue({measurementCounts:1})),le({...ae}),e.toData(e.parentFieldName,ae)}})}),(0,S.jsx)(s.A.Item,{label:H("\u901a\u9053"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"variable_name",style:pe,name:"channel.channel",fieldNames:{label:"variable_name",value:"code"},value:null===ae||void 0===ae||null===(U=ae.channel)||void 0===U?void 0:U.channel,onChange:e=>{var t,a;re("channel.channel",e);const l=(null===ne||void 0===ne||null===(t=ne.find((e=>e.group_id===ae.channel.channelType)))||void 0===t||null===(a=t.variable_ids)||void 0===a?void 0:a.find((t=>t.code===e)))||"\u65e0";ae.unit.unitType=l.dimension_id,ae.unit.unit=l?l.unit_id:"",de({unit:l?l.unit_id:"",unitType:l.dimension_id})},options:null===$||void 0===$?void 0:$.filter((e=>{var t;return!(null!==(t=ae.channel.lockChannels)&&void 0!==t&&t.includes(null===e||void 0===e?void 0:e.code))}))})}),(0,S.jsx)(s.A.Item,{label:H("\u9501\u5b9a"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"variable_name",mode:"multiple",disabled:!(null!==ae&&void 0!==ae&&null!==(N=ae.channel)&&void 0!==N&&N.isUserConversion),style:pe,value:null===ae||void 0===ae||null===(B=ae.channel)||void 0===B?void 0:B.lockChannels,fieldNames:{label:"variable_name",value:"code"},onChange:e=>{re("channel.lockChannels",e);const t=null===$||void 0===$?void 0:$.filter((t=>!e.includes(t.code)));0===t.length||e.includes(ae.channel.channel)?se():(e.includes(ae.channel.channel)&&(se(),ae.channel.channel=t[0].code,de({type:t[0].code})),0!==ae.channel.lockChannels.length&&(ae.channel.channel=t[0].code,de({type:t[0].code})))},options:$})}),(0,S.jsx)(s.A.Item,{label:H("\u7528\u6237\u53ef\u4ee5\u8f6c\u6362"),valuePropName:"checked",children:(0,S.jsx)(A.A,{name:"channel.isUserConversion",checked:null===ae||void 0===ae||null===(O=ae.channel)||void 0===O?void 0:O.isUserConversion,onChange:e=>re("channel.isUserConversion",e)})})]})})}),(0,S.jsx)("div",{className:"content",children:(0,S.jsx)(s.A,{...ue,labelAlign:"left",layout:Z,form:X,initialValues:{layout:Z},style:{height:"25vh"},children:(0,S.jsxs)("div",{className:"form-content",children:[(0,S.jsx)(s.A.Item,{label:H("\u91cf\u7eb2"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"name",name:"unit.unitType",value:null===ae||void 0===ae||null===(M=ae.unit)||void 0===M?void 0:M.unitType,fieldNames:{label:"name",value:"id"},onChange:e=>{var t,a;re("unit.unitType",e);const l=null===(t=ce())||void 0===t||null===(a=t.find((t=>t.id===e)))||void 0===a?void 0:a.default_unit_id;ae.unit.unit=l,de({unit:l,unitType:e})},options:ce(),style:pe})}),(0,S.jsx)(s.A.Item,{label:H("\u5355\u4f4d"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"name",name:"unit.unit",value:null===ae||void 0===ae||null===(L=ae.unit)||void 0===L?void 0:L.unit,fieldNames:{label:"name",value:"id"},onChange:e=>re("unit.unit",e),options:null===(V=Ae())||void 0===V?void 0:V.filter((e=>!ae.unit.lockChannels.includes(e.id))),style:pe})}),(0,S.jsx)(s.A.Item,{label:H("\u9501\u5b9a"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"name",mode:"multiple",disabled:!(null!==ae&&void 0!==ae&&null!==(Q=ae.unit)&&void 0!==Q&&Q.isUserConversion),style:pe,fieldNames:{label:"name",value:"id"},placeholder:H(""),value:null===ae||void 0===ae||null===(F=ae.unit)||void 0===F?void 0:F.lockChannels,onChange:e=>{var t;re("unit.lockChannels",e);const a=null===(t=Ae())||void 0===t?void 0:t.filter((e=>!ae.unit.lockChannels.includes(e.id)));0===a.length?(ae.unit.unit="",de({unit:""})):(ae.unit.unit=a[0].id,de({unit:a[0].id}))},options:Ae()})}),(0,S.jsx)(s.A.Item,{label:H("\u7528\u6237\u53ef\u4ee5\u8f6c\u6362"),valuePropName:"checked",children:(0,S.jsx)(A.A,{name:"unit.isUserConversion",checked:null===ae||void 0===ae||null===(J=ae.unit)||void 0===J?void 0:J.isUserConversion,onChange:e=>re("unit.isUserConversion",e)})})]})})})]})};var W=a(20691),G=a(79806),z=a(74125),X=a(95206);const Z=e=>{let{unitList:t,onSelectDimension:a,resetDimension:l}=e;return[{title:"\u63a7\u5236\u65b9\u5f0f",dataIndex:"controlMode",key:"controlMode",render:e=>Object.entries(z.Rg).find((t=>{let[a,l]=t;return l===e}))[0]},{title:"\u91cf\u7eb2",dataIndex:"dimensionId",key:"dimensionId",render:e=>{var a;return e?(0,S.jsx)(S.Fragment,{children:(null===t||void 0===t||null===(a=t.find((t=>t.id===e)))||void 0===a?void 0:a.name)||"\u7cfb\u7edf\u4e0d\u5b58\u5728\u8be5\u91cf\u7eb2"}):(0,S.jsx)(S.Fragment,{children:"--"})}},{title:"\u64cd\u4f5c",dataIndex:"operation",key:"operation",render:(e,t)=>(0,S.jsxs)(r.A,{size:"middle",children:[(0,S.jsx)(X.Ay,{type:"link",onClick:()=>a(t),children:"\u9009\u62e9\u91cf\u7eb2"}),(0,S.jsx)(X.Ay,{type:"link",disabled:!t.dimensionId,onClick:()=>l(t),children:"\u91cd\u7f6e"})]})}]},q=e=>{let{data:t,handleSelectDimension:a,handleResetDimension:n}=e;const i=(0,h.d4)((e=>e.global.unitList)),o=(0,l.useMemo)((()=>Object.entries(t.dimension).map((e=>{let[t,a]=e;return{controlMode:t,dimensionId:a}}))),[t.dimension]);return(0,S.jsx)(G.A,{bordered:!0,rowKey:"controlMode",columns:Z({unitList:i,onSelectDimension:e=>{a({paramCode:t.code,controlMode:e.controlMode})},resetDimension:e=>{n({paramCode:t.code,controlMode:e.controlMode})}}),dataSource:o,pagination:!1})},$=e=>{let{data:t,handleSelectDimension:a,handleResetDimension:l}=e;const{copy:n}=(0,g.A)(),{t:i}=(0,u.Bd)(),o=[{title:"\u53c2\u6570",dataIndex:"name",key:"name"},{title:"code",dataIndex:"code",key:"code",render:e=>(0,S.jsx)("span",{onClick:()=>n(e),children:e})}];return(0,S.jsx)(G.A,{bordered:!0,rowKey:"code",columns:o,dataSource:(null===t||void 0===t?void 0:t.params)||[],pagination:!1,expandable:{expandedRowRender:e=>(0,S.jsx)(q,{data:e,handleSelectDimension:e=>{let{paramCode:l,controlMode:n}=e;return a({typeCode:t.code,paramCode:l,controlMode:n})},handleResetDimension:e=>{let{paramCode:a,controlMode:n}=e;return l({typeCode:t.code,paramCode:a,controlMode:n})}}),defaultExpandedRowKeys:["0"],rowExpandable:e=>e.dimension}})},ee=e=>{let{onSelect:t}=e;return[{title:"\u91cf\u7eb2",dataIndex:"name",key:"name"},{title:"\u64cd\u4f5c",key:"action",render:(e,a)=>(0,S.jsx)(r.A,{size:"middle",children:(0,S.jsx)("a",{onClick:()=>t(a),children:"\u9009\u62e9"})})}]},te=e=>{let{record:t}=e;return(0,S.jsx)(G.A,{rowKey:"code",columns:[{title:"\u5355\u4f4d",dataIndex:"name",key:"name"},{title:"code",dataIndex:"code",key:"code"}],dataSource:t.units,pagination:!1})},ae=(e,t)=>{let{onSelectedDimension:a}=e;const n=(0,h.d4)((e=>e.global.unitList)),[o,r]=(0,l.useState)(!1);(0,l.useImperativeHandle)(t,(()=>({open:()=>{r(!0)}})));const d=()=>{r(!1)};return(0,S.jsx)(i.A,{title:"\u9009\u62e9\u91cf\u7eb2",open:o,onOk:d,onCancel:d,children:(0,S.jsx)(G.A,{rowKey:"code",expandable:{expandedRowRender:e=>(0,S.jsx)(te,{record:e}),defaultExpandedRowKeys:["0"]},columns:ee({onSelect:e=>{a(e.id),d()}}),dataSource:n})})},le=(0,l.forwardRef)(ae),ne=e=>{let{id:t,value:a,onChange:n,initalData:i=z.TB}=e;const o=(0,l.useRef)(),r=(0,l.useRef)(),{copy:d}=(0,g.A)(),{t:s}=(0,u.Bd)(),c=[{title:"\u6ce2\u5f62",dataIndex:"name",key:"name"},{title:"\u5185\u90e8\u540d",dataIndex:"code",key:"code",render:e=>(0,S.jsx)("span",{onClick:()=>d(e),children:e})}],A=e=>{let{typeCode:t,paramCode:a,controlMode:l}=e;r.current={typeCode:t,paramCode:a,controlMode:l},o.current.open()},p=e=>{let{typeCode:t,paramCode:l,controlMode:o}=e;const r=(a||i).map((e=>(null===e||void 0===e?void 0:e.code)!==t?e:{...e,params:null===e||void 0===e?void 0:e.params.map((e=>e.code!==l?e:{...e,dimension:{...e.dimension,[o]:""}}))}));n(r)};return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)(W.A,{column:2,size:"small",children:[(0,S.jsx)(W.A.Item,{span:1,label:s("\u63a7\u5236\u65b9\u5f0f\u5185\u90e8\u540d"),children:(0,S.jsx)("span",{onClick:()=>d("WaveControlPattern"),children:"kzms"})}),(0,S.jsx)(W.A.Item,{span:2,label:s("\u63a7\u5236\u6a21\u5f0f\u5185\u90e8\u540d"),children:(0,S.jsx)("span",{onClick:()=>d("WaveControlMode"),children:"kzfs"})})]}),(0,S.jsx)(G.A,{bordered:!0,columns:c,expandable:{expandedRowRender:e=>(0,S.jsx)($,{data:e,handleSelectDimension:A,handleResetDimension:p}),defaultExpandedRowKeys:["0"]},rowKey:"code",dataSource:a||i,pagination:!1}),(0,S.jsx)(le,{ref:o,onSelectedDimension:e=>{const t=a||i,{typeCode:l,paramCode:o,controlMode:d}=r.current,s=t.map((t=>(null===t||void 0===t?void 0:t.code)!==l?t:{...t,params:null===t||void 0===t?void 0:t.params.map((t=>t.code!==o?t:{...t,dimension:{...t.dimension,[d]:e}}))}));n(s)}})]})},ie=f.Ay.div`
    overflow: hidden;
    .top-handle{
      text-align: right;
      margin-bottom: 8px;
        >img{
          cursor: pointer;  
        }
    }
    .handle-box{
        display: flex;
        align-items: center;
        >img{
            margin-left: 8px;
            cursor: pointer;
            &:first-child{
                margin-left: 0;
            }
        }
    }
`,oe=(e,t)=>{let{open:a,list:o=[],itemModeData:r,onOk:c,onCancel:A}=e;const{t:p}=(0,u.Bd)(),[v]=s.A.useForm();return(0,l.useEffect)((()=>{r&&v.setFieldsValue({name:r.name,code:r.code})}),[]),(0,S.jsx)(i.A,{width:"500px",title:p(r?"\u7f16\u8f91\u6a21\u5f0f":"\u6dfb\u52a0\u6a21\u5f0f"),open:a,onOk:async()=>{try{const e=await v.validateFields();if(o.filter((e=>e.id!==(null===r||void 0===r?void 0:r.id))).some((t=>t.name===e.name||t.code===e.code)))return void n.Ay.error(p("\u6a21\u5f0f\u540d\u79f0\u4e0e\u5185\u90e8\u540d\u4e0d\u53ef\u91cd\u590d"));c({...r,id:(null===r||void 0===r?void 0:r.id)||crypto.randomUUID(),listData:(null===r||void 0===r?void 0:r.listData)||[],name:e.name,code:e.code})}catch(e){console.error(e)}},onCancel:A,children:(0,S.jsxs)(s.A,{form:v,labelCol:{span:6},wrapperCol:{span:14},children:[(0,S.jsx)(s.A.Item,{label:p("\u6a21\u5f0f\u540d\u79f0"),name:"name",rules:[{required:!0,message:p("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(d.A,{placeholder:p("\u8bf7\u8f93\u5165")})}),(0,S.jsx)(s.A.Item,{label:p("\u5185\u90e8\u540d"),name:"code",rules:[{required:!0,message:p("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(d.A,{disabled:!1,placeholder:p("\u8bf7\u8f93\u5165")})})]})})},re=(0,l.forwardRef)(oe),de="param_",se=(e,t)=>{let{open:a,list:o=[],item:r,onOk:c,onCancel:A}=e;const{t:p}=(0,u.Bd)(),[v]=s.A.useForm();return(0,l.useEffect)((()=>{r&&v.setFieldsValue({name:r.name,code:r.code.replace(de,"")})}),[]),(0,S.jsx)(S.Fragment,{children:(0,S.jsx)(i.A,{width:"500px",title:p(r?"\u7f16\u8f91\u53c2\u6570":"\u6dfb\u52a0\u53c2\u6570"),open:a,onOk:async()=>{try{const e=await v.validateFields();if(o.filter((e=>e.id!==(null===r||void 0===r?void 0:r.id))).some((t=>t.name===e.name||t.code===de+e.code)))return void n.Ay.error(p("\u53c2\u6570\u540d\u79f0\u4e0e\u5185\u90e8\u540d\u4e0d\u53ef\u91cd\u590d"));c({...r,id:(null===r||void 0===r?void 0:r.id)||crypto.randomUUID(),listData:(null===r||void 0===r?void 0:r.listData)||[],name:e.name,code:de+e.code})}catch(e){console.error(e)}},onCancel:A,children:(0,S.jsxs)(s.A,{form:v,labelCol:{span:6},wrapperCol:{span:14},children:[(0,S.jsx)(s.A.Item,{label:p("\u53c2\u6570\u540d\u79f0"),name:"name",rules:[{required:!0,message:p("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(d.A,{placeholder:p("\u8bf7\u8f93\u5165")})}),(0,S.jsx)(s.A.Item,{label:p("\u5185\u90e8\u540d"),name:"code",rules:[{required:!0,message:p("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(d.A,{prefix:de,disabled:!1,placeholder:p("\u8bf7\u8f93\u5165")})})]})})})},ce=(0,l.forwardRef)(se);var Ae=a(8361),ue=a(26815),pe=a(19853),ve=a.n(pe),me=a(88483);const he=(e,t)=>{let{open:a,channelOptions:o=[],list:r=[],item:d,onOk:c,onCancel:A}=e;const{t:p}=(0,u.Bd)(),[v]=s.A.useForm();return(0,l.useEffect)((()=>{d&&v.setFieldsValue({channel:d.channel,dimensionId:d.dimensionId})}),[]),(0,S.jsx)(S.Fragment,{children:(0,S.jsx)(i.A,{width:"500px",title:p(d?"\u7f16\u8f91\u901a\u9053":"\u6dfb\u52a0\u901a\u9053"),open:a,onOk:async()=>{try{const e=await v.validateFields();if(r.filter((e=>e.id!==(null===d||void 0===d?void 0:d.id))).some((t=>ve()(t.channel,e.channel))))return void n.Ay.error(p("\u901a\u9053\u4e0d\u53ef\u91cd\u590d"));c({...d,id:(null===d||void 0===d?void 0:d.id)||crypto.randomUUID(),channel:e.channel,dimensionId:e.dimensionId})}catch(e){console.error(e)}},onCancel:A,children:(0,S.jsxs)(s.A,{form:v,labelCol:{span:6},wrapperCol:{span:14},children:[(0,S.jsx)(s.A.Item,{label:p("\u901a\u9053"),name:"channel",rules:[{required:!0,message:p("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(ue.A,{options:o})}),(0,S.jsx)(s.A.Item,{label:p("\u91cf\u7eb2"),name:"dimensionId",rules:[{required:!0,message:p("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(me.A,{})})]})})})},ge=(0,l.forwardRef)(he),be=e=>{let{data:t=[],onChange:a}=e;const{t:n}=(0,u.Bd)(),{getSelectOptions:i}=(0,Ae.Ay)(),o=i({selection:x.P$.\u6620\u50cf\u6570\u636e\u6e90.value,selectLayer:x.Q1.\u901a\u9053}),r=(0,h.d4)((e=>e.global.unitList)),[d,s]=(0,l.useState)(!1),[c,A]=(0,l.useState)(),v=(e,t)=>{if(!t||0===t.length)return[];const[a,...l]=t,n=e.find((e=>e.value===a));if(!n)return[];const i=[n.label];return n.children&&n.children.length>0&&i.push(...v(n.children,l)),i},m=[{title:n("\u901a\u9053"),dataIndex:"channel",ellipsis:!0,key:"channel",render:(e,t)=>{const a=v(o,e);return(0,S.jsx)("div",{children:a.join("/")})}},{title:n("\u91cf\u7eb2"),dataIndex:"dimensionId",ellipsis:!0,key:"dimensionId",render:(e,t)=>{const a=r.find((t=>t.id===e))||{};return(0,S.jsx)("div",{children:null===a||void 0===a?void 0:a.name})}},{title:n("\u64cd\u4f5c"),dataIndex:"\u64cd\u4f5c",ellipsis:!0,key:"\u64cd\u4f5c",width:60,render:(e,t)=>(0,S.jsxs)("div",{className:"handle-box",children:[(0,S.jsx)("img",{onClick:()=>{g("edit",t)},src:p.G8,alt:n("\u7f16\u8f91"),title:n("\u7f16\u8f91")}),(0,S.jsx)("img",{onClick:()=>{g("del",t)},src:p.pU,alt:n("\u5220\u9664"),title:n("\u5220\u9664")})]})}],g=(e,l)=>{"edit"===e&&(A(l),s(!0)),"del"===e&&(null===a||void 0===a||a(t.filter((e=>e.id!==l.id))))},b=()=>{s(!1),A(void 0)};return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)("div",{style:{marginInlineStart:"33px"},children:[(0,S.jsx)("div",{style:{textAlign:"right",marginBottom:"8px",marginRight:"7px"},children:(0,S.jsx)("img",{style:{cursor:"pointer"},onClick:()=>{s(!0)},src:p.d7,alt:"\u6dfb\u52a0",title:"\u6dfb\u52a0"})}),(0,S.jsx)(G.A,{bordered:!0,rowKey:"code",columns:m,dataSource:t||[],pagination:!1})]}),d?(0,S.jsx)(ge,{channelOptions:o,list:t,item:c,open:d,onOk:e=>{let l=[];l=c?null===t||void 0===t?void 0:t.map((t=>t.id===e.id?e:t)):[...t,e],null===a||void 0===a||a(l),b()},onCancel:b}):null]})},ye={name:"\u9ad8\u9891",code:"HighFrequencyWave",id:"HighFrequencyWave",listData:[{name:"\u4ea4\u53d8\u8d1f\u8377",code:"param_HighFrequencyWave_Ampl",id:"HighFrequencyWave_Ampl",listData:[]},{name:"\u5e73\u5747\u8d1f\u8377",code:"param_HighFrequencyWave_Mean",id:"HighFrequencyWave_Mean",listData:[]},{name:"\u6b21\u6570",code:"param_HighFrequencyWave_Count",id:"HighFrequencyWave_Count",listData:[]}]},xe=e=>{let{data:t=[],onChange:a}=e;const{t:n}=(0,u.Bd)(),[i,o]=(0,l.useState)(!1),[r,d]=(0,l.useState)(),{copy:s}=(0,g.A)(),c=[{title:n("\u53c2\u6570"),dataIndex:"name",ellipsis:!0,key:"name"},{title:n("\u5185\u90e8\u540d"),dataIndex:"code",ellipsis:!0,key:"code",render:e=>(0,S.jsx)("span",{style:{cursor:"pointer"},onClick:t=>s(e),children:e})},{title:n("\u64cd\u4f5c"),dataIndex:"\u64cd\u4f5c",ellipsis:!0,key:"\u64cd\u4f5c",width:60,render:(e,t)=>(0,S.jsxs)("div",{className:"handle-box",children:[(0,S.jsx)("img",{onClick:()=>{A("edit",t)},src:p.G8,alt:n("\u7f16\u8f91"),title:n("\u7f16\u8f91")}),(0,S.jsx)("img",{onClick:()=>{A("del",t)},src:p.pU,alt:n("\u5220\u9664"),title:n("\u5220\u9664")})]})}],A=(e,l)=>{"edit"===e&&(d(l),o(!0)),"del"===e&&(null===a||void 0===a||a(t.filter((e=>e.id!==l.id))))},v=()=>{o(!1),d(void 0)},m=(e,l)=>{const n=((e,a)=>t.map((t=>t.id===e?{...t,listData:a}:t)))(l.id,e);null===a||void 0===a||a(n)};return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)("div",{style:{marginInlineStart:"33px"},children:[(0,S.jsx)("div",{style:{textAlign:"right",marginBottom:"8px",marginRight:"7px"},children:(0,S.jsx)("img",{style:{cursor:"pointer"},onClick:()=>{o(!0)},src:p.d7,alt:"\u6dfb\u52a0",title:"\u6dfb\u52a0"})}),(0,S.jsx)(G.A,{bordered:!0,rowKey:"code",columns:c,dataSource:t||[],pagination:!1,expandable:{expandedRowRender:e=>(0,S.jsx)(be,{data:null===e||void 0===e?void 0:e.listData,onChange:t=>m(t,e)})}})]}),i?(0,S.jsx)(ce,{list:t,item:r,open:i,onOk:e=>{let l=[];l=r?null===t||void 0===t?void 0:t.map((t=>t.id===e.id?e:t)):[...t,e],null===a||void 0===a||a(l),v()},onCancel:v}):null]})},fe=e=>{let{mode:t,value:a=[],onChange:n}=e;const{t:i}=(0,u.Bd)(),[o,r]=(0,l.useState)(!1),[d,s]=(0,l.useState)(),{copy:c}=(0,g.A)(),A=[{title:i("\u6a21\u5f0f"),dataIndex:"name",ellipsis:!0,key:"name"},{title:i("\u5185\u90e8\u540d"),dataIndex:"code",ellipsis:!0,key:"code",render:e=>(0,S.jsx)("span",{style:{cursor:"pointer"},onClick:t=>c(e),children:e})},{title:i("\u64cd\u4f5c"),dataIndex:"\u64cd\u4f5c",ellipsis:!0,key:"\u64cd\u4f5c",width:60,render:(e,t)=>(0,S.jsxs)("div",{className:"handle-box",children:[(0,S.jsx)("img",{onClick:()=>{v("edit",t)},src:p.G8,alt:i("\u7f16\u8f91"),title:i("\u7f16\u8f91")}),(0,S.jsx)("img",{onClick:()=>{v("del",t)},src:p.pU,alt:i("\u5220\u9664"),title:i("\u5220\u9664")})]})}],v=(e,t)=>{-1!==a.findIndex((e=>e.id===t.id))&&("edit"===e&&(r(!0),s(t)),"del"===e&&(null===n||void 0===n||n(a.filter((e=>e.id!==t.id)))))},m=()=>{r(!1),s(void 0)},h=(e,t)=>{const l=((e,t)=>a.map((a=>a.id===e?{...a,listData:t}:a)))(t.id,e);n(l)};return(0,l.useEffect)((()=>{"add"!==t||a.some((e=>"HighFrequencyWave"===e.id))||n([ye,...a])}),[]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)(ie,{children:[(0,S.jsx)("div",{className:"top-handle",children:(0,S.jsx)(X.Ay,{type:"primary",onClick:()=>{r(!0),s(void 0)},children:"\u6dfb\u52a0"})}),(0,S.jsx)(G.A,{bordered:!0,columns:A,expandable:{expandedRowRender:e=>(0,S.jsx)(xe,{data:null===e||void 0===e?void 0:e.listData,onChange:t=>h(t,e)})},rowKey:"id",dataSource:a,pagination:!1})]}),o?(0,S.jsx)(re,{list:a,itemModeData:d,open:o,onOk:e=>{let t=[...a];t=d?a.map((t=>t.id===e.id?e:t)):[...a,e],null===n||void 0===n||n(t),m()},onCancel:m}):null]})},Ce=f.Ay.div`
    height: 60vh;
    overflow-y: auto;
`,{useForm:we,Item:Ee}=s.A,Se=e=>{let{mode:t="",data:a,defaultVal:n,toData:i,parentFieldName:o,parentFieldKey:r}=e;const[d]=we(),A=s.A.useWatch("useType",d);(0,l.useEffect)((()=>{d.setFieldsValue(a)}),[a]),(0,l.useEffect)((()=>{i(o,d.getFieldsValue())}),[A]);return(0,S.jsx)(Ce,{children:(0,S.jsxs)(s.A,{form:d,labelCol:{span:4},wrapperCol:{span:20},style:{padding:"10px"},autoComplete:"off",onValuesChange:(e,t)=>{"useType"in e||i(o,t)},children:[(0,S.jsx)(Ee,{label:"\u7528\u9014",name:"useType",children:(0,S.jsx)(c.A,{style:{width:"200px"},options:[{value:"followComp",label:"\u8ddf\u968f\u63a7\u4ef6"},{value:"customWaveform",label:"\u81ea\u5b9a\u4e49\u6ce2\u5f62"},{value:"programmableParameters",label:"\u7a0b\u63a7\u53c2\u6570"}]})}),(0,S.jsx)(Ee,{shouldUpdate:!0,noStyle:!0,children:e=>{let{getFieldValue:a}=e;switch(a("useType")){case"customWaveform":return(0,S.jsx)(Ee,{label:"\u81ea\u5b9a\u4e49\u6ce2\u5f62",name:"customWaveform",children:(0,S.jsx)(ne,{})});case"programmableParameters":return(0,S.jsx)(Ee,{label:"\u81ea\u5b9a\u4e49\u7a0b\u63a7",name:"programmableParameters",children:(0,S.jsx)(fe,{mode:t})});default:return(0,S.jsx)(S.Fragment,{})}}})]})})},_e=f.Ay.div`
    width: 100%;
    padding: 10px ;

    .func_title{
        margin-bottom: 8px;

        display: flex;
        justify-content: space-between;

        .func_list{
            display: flex;
            gap: 8px;
        }
    }

    .seleted_area{
        border: 1px solid #ccc;
        min-height: 50px;
        padding: 3px;

        .tag_item{
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;

        }
    }

.transfer{
    background-color: red;
    .ant-transfer-list{
        flex: 1 !important;
    }

}
`;var Re=a(38134),je=a(24236),Ie=a(88655),Te=a(56434),Pe=a.n(Te);const ke=f.Ay.div`
    .col-list-header{
        display: flex;
        justify-content: end;
        gap: 8px;

        &>div{
            cursor: pointer;
        }
    }

    .col-list-container{
        width: 100%;
        height: 200px;
        border: 1px solid #ccc;
        overflow-y: scroll;
        padding: 3px;

        .col-list-wrapper{

            .col-list-item{
                height: 20px;
                cursor: pointer;
            }

            .col-list-item-selected{
                background: #ccc;
            }

        }
    }
`,De=e=>{let{id:t,value:a=[],onChange:l,optIndex:i,setOptIndex:o}=e;const{t:r,i18n:d}=(0,u.Bd)(),s=e=>"col-list-item "+(i===e?"col-list-item-selected":""),c=()=>{var e;null!==a&&void 0!==a&&null!==(e=a.filter((e=>e.showName)))&&void 0!==e&&e.some(((e,t)=>{var l;return e.code===(null===a||void 0===a||null===(l=a[i])||void 0===l?void 0:l.code)&&t!==i}))&&n.Ay.error(r("\u5185\u90e8\u540d\u4e0d\u80fd\u91cd\u590d"))};return(0,S.jsxs)(ke,{children:[(0,S.jsxs)("div",{className:"col-list-header",children:[(0,S.jsx)("div",{onClick:()=>{c();let e=a.length+1;const t=e=>a.some((t=>t.code===r(`\u5217${e}`)));for(;t(e);)e+=1;l([...a,{...Pe()(Re.G1),showName:r(`\u5217${e}`),code:r(`\u5217${e}`)}])},children:(0,S.jsx)(je.A,{})}),(0,S.jsx)("div",{onClick:()=>{const e=a.filter(((e,t)=>t!==i));i>=e.length&&o(null),setTimeout((()=>{l(e)}),0)},children:(0,S.jsx)(Ie.A,{})})]}),(0,S.jsx)("div",{className:"col-list-container",children:(0,S.jsx)("div",{className:"col-list-wrapper",children:null===a||void 0===a?void 0:a.map(((e,t)=>(0,S.jsx)("div",{className:s(t),onClick:()=>(e=>{c(),o(e)})(t),children:null===e||void 0===e?void 0:e.showName},t)))})})]})};var Ue=a(29977);const Ne={"\u8f93\u5165\u53d8\u91cf":"input","\u4fe1\u53f7\u53d8\u91cf":"signal","\u7ed3\u679c\u53d8\u91cf":"result","\u52a8\u4f5c":"action"},Be=e=>{let{id:t,value:a,onChange:n,disabled:i=!1}=e;const{t:o,i18n:r}=(0,u.Bd)(),d=(0,Ue.A)(),s=(0,h.d4)((e=>e.template.signalList)),c=(0,h.d4)((e=>e.template.resultData)),A=(0,h.d4)((e=>e.template.actionList)),p=(0,l.useMemo)((()=>({label:o("\u8f93\u5165\u53d8\u91cf"),value:Ne.\u8f93\u5165\u53d8\u91cf,children:null===d||void 0===d?void 0:d.filter((e=>e.type===x.Ih.GENERAL)).map((e=>({value:e.code,label:o(e.name)})))})),[d]),v=(0,l.useMemo)((()=>({label:o("\u4fe1\u53f7\u53d8\u91cf"),value:Ne.\u4fe1\u53f7\u53d8\u91cf,children:s.map((e=>({value:e.code,label:o(e.variable_name)})))})),[s]),m=(0,l.useMemo)((()=>({label:o("\u7ed3\u679c\u53d8\u91cf"),value:Ne.\u7ed3\u679c\u53d8\u91cf,children:c.map((e=>({value:e.code,label:o(e.variable_name)})))})),[c]),g=(0,l.useMemo)((()=>({label:o("\u52a8\u4f5c"),value:Ne.\u52a8\u4f5c,children:A.filter((e=>!!e.action_code)).map((e=>({value:e.action_code,label:o(e.action_name)})))})),[A]),b=(0,l.useMemo)((()=>[p,v,m,g].filter((e=>0!==e.children.length))),[p,v,m,g]),y=(0,l.useMemo)((()=>{if(a)return[Object.values(Ne).find((e=>a.startsWith(e))),a]}),[a]);return(0,S.jsx)(ue.A,{value:y,options:b,onChange:function(){let[e,t]=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];n(t)},disabled:i})},{Item:Oe}=s.A,Me=(e,t)=>{let{option:a,handleSubmit:n}=e;const o=(0,l.useRef)(),[r,c]=(0,l.useState)(!1);(0,l.useEffect)((()=>{var e,t;r&&(a?null===o||void 0===o||null===(e=o.current)||void 0===e||e.setFieldsValue({...a}):null===o||void 0===o||null===(t=o.current)||void 0===t||t.resetFields())}),[a,r]),(0,l.useImperativeHandle)(t,(()=>({open:()=>{c(!0)}})));return(0,S.jsx)(i.A,{title:"\u9009\u9879\u8bbe\u7f6e",width:"30vw",open:r,onOk:async()=>{const e=await o.current.validateFields();n({id:a?a.id:(new Date).getTime(),...e}),c(!1)},onCancel:()=>{c(!1)},children:(0,S.jsxs)(s.A,{ref:o,name:"option",autoComplete:"off",labelCol:{span:5},children:[(0,S.jsx)(Oe,{label:"\u9009\u9879\u540d\u79f0",name:"label",children:(0,S.jsx)(d.A,{})}),(0,S.jsx)(Oe,{label:"\u503c",name:"value",children:(0,S.jsx)(d.A,{})})]})})},Le=(0,l.forwardRef)(Me),Ve=e=>{let{handleEditOption:t,handleDeleteOption:a,disabled:l}=e;return[{title:"\u9009\u9879\u540d",dataIndex:"label",key:"label"},{title:"\u503c",dataIndex:"value",key:"value"},{title:"\u64cd\u4f5c",key:"action",render:(e,n)=>(0,S.jsxs)(r.A,{size:"middle",children:[(0,S.jsx)("a",{disabled:l,onClick:()=>l||t(n),children:"\u7f16\u8f91"}),(0,S.jsx)("a",{disabled:l,onClick:()=>l||a(n),children:"\u5220\u9664"})]})}]},Qe=e=>{let{id:t,value:a,onChange:n,disabled:i}=e;const[o,r]=(0,l.useState)(),d=(0,l.useRef)();return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(X.Ay,{onClick:()=>{r(null),d.current.open()},disabled:i,children:"\u6dfb\u52a0\u4e00\u9879"}),(0,S.jsx)(G.A,{bordered:!0,dataSource:a,columns:Ve({handleEditOption:e=>{r(e),d.current.open()},handleDeleteOption:e=>{n(a.filter((t=>t.id!==e.id)))},disabled:i}),pagination:!1}),(0,S.jsx)(Le,{ref:d,option:o,handleSubmit:e=>{n(o?null===a||void 0===a?void 0:a.map((t=>t.id===e.id?{...e}:t)):[...a,e])}})]})};var Fe=a(12159);const{Item:Je}=s.A,Ye=e=>{let{optIndex:t,onValuesChange:a}=e;const{t:l}=(0,u.Bd)(),n=(0,h.d4)((e=>e.global.unitList)),i=s.A.useFormInstance(),o=s.A.useWatch(["columns",t,"typeParam","dimensionId"],i);return(0,S.jsx)(Je,{label:l("\u7c7b\u578b\u53c2\u6570"),children:(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(Je,{label:l("\u91cf\u7eb2"),name:["columns",t,"typeParam","dimensionId"],children:(0,S.jsx)(me.A,{onChange:e=>{const l=n.find((t=>t.id===e));setTimeout((()=>{i.setFieldValue(["columns",t,"typeParam","unitId"],l.default_unit_id),null===a||void 0===a||a({},{...i.getFieldsValue()})}))}})})}),(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(Je,{label:l("\u5355\u4f4d"),name:["columns",t,"typeParam","unitId"],children:(0,S.jsx)(Fe.A,{dimensionId:o})})})]})})},Ke=e=>{let{optIndex:t,form:a,onValuesChange:l}=e;const{t:n}=(0,u.Bd)();switch(s.A.useWatch(["columns",t,"type"],a)){case Re.J$.\u9009\u62e9:return(0,S.jsx)(Je,{label:n("\u7c7b\u578b\u53c2\u6570"),name:["columns",t,"typeParam","options"],children:(0,S.jsx)(Qe,{optIndex:t})});case Re.J$.\u6570\u5b57:return(0,S.jsx)(Ye,{optIndex:t,onValuesChange:l});default:return(0,S.jsx)(S.Fragment,{})}},{Item:He,useForm:We}=s.A,Ge=e=>{let{data:t,defaultVal:a,toData:n,parentFieldName:i,parentFieldKey:o}=e;const{t:r,i18n:p}=(0,u.Bd)(),[v]=We(),[m,h]=(0,l.useState)(null),g=s.A.useWatch(["columns",m,"openCorrelation"],v);(0,l.useEffect)((()=>{v.setFieldsValue(t)}),[t]);const b=(e,t)=>{n(i,t)};return(0,S.jsx)(_e,{children:(0,S.jsxs)(s.A,{form:v,labelCol:{span:8},wrapperCol:{span:16},onValuesChange:b,children:[(0,S.jsx)(I.A,{children:(0,S.jsx)(T.A,{span:6,children:(0,S.jsx)(He,{label:r("\u884c\u6570"),name:"rowNumber",children:(0,S.jsx)(j.A,{min:0})})})}),(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{span:6,children:(0,S.jsx)(He,{label:r("\u5217\u6570"),name:"columns",children:(0,S.jsx)(De,{optIndex:m,setOptIndex:h})})}),null!==m&&(0,S.jsxs)(T.A,{span:17,offset:1,children:[(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{span:10,children:(0,S.jsx)(He,{label:r("\u663e\u793a\u540d\u79f0"),name:["columns",m,"showName"],children:(0,S.jsx)(d.A,{disabled:null===m})})}),(0,S.jsx)(T.A,{span:14,children:(0,S.jsx)(He,{label:r("\u5185\u90e8\u540d"),name:["columns",m,"code"],rules:[{required:!0,message:"\u8bf7\u8f93\u5165\u5185\u90e8\u540d"},{validator:(e,t)=>{var a;const l=((null===(a=v.getFieldValue("columns"))||void 0===a?void 0:a.filter((e=>e.showName)))||[]).filter((e=>e.code===t)).length;return l>1?Promise.reject(new Error("\u5185\u90e8\u540d\u4e0d\u80fd\u91cd\u590d")):Promise.resolve()}}],children:(0,S.jsx)(d.A,{disabled:null===m})})})]}),(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{span:10,children:(0,S.jsx)(He,{label:r("\u7c7b\u578b"),name:["columns",m,"type"],children:(0,S.jsx)(c.A,{disabled:null===m,options:Object.entries(Re.J$).map((e=>{let[t,a]=e;return{label:t,value:a}}))})})}),(0,S.jsx)(T.A,{span:14,children:(0,S.jsx)(Ke,{optIndex:m,onValuesChange:b})})]}),(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{span:6,offset:2,children:(0,S.jsx)(He,{name:["columns",m,"openCorrelation"],valuePropName:"checked",children:(0,S.jsx)(A.A,{disabled:null===m,children:"\u4f34\u968f\u53d8\u91cf"})})}),(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(He,{label:"",name:["columns",m,"correlationCode"],children:(0,S.jsx)(Be,{disabled:!g||null===m})})})]})]})]})]})})};var ze=a(67998);const Xe=f.Ay.div`
    width: 100%;
    padding: 10px ;

    .func_title{
        margin-bottom: 8px;

        display: flex;
        justify-content: space-between;

        .func_list{
            display: flex;
            gap: 8px;
        }
    }

    .seleted_area{
        border: 1px solid #ccc;
        min-height: 50px;
        padding: 3px;

        .tag_item{
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;

        }
    }

.transfer{
    background-color: red;
    .ant-transfer-list{
        flex: 1 !important;
    }

}
`,{Item:Ze,useForm:qe}=s.A,$e=e=>{let{data:t,defaultVal:a,toData:n,parentFieldName:i,parentFieldKey:o}=e;const{t:r,i18n:d}=(0,u.Bd)(),[c]=qe();(0,l.useEffect)((()=>{c.setFieldsValue(t)}),[t]);return(0,S.jsx)(Xe,{children:(0,S.jsxs)(s.A,{form:c,labelCol:{span:8},wrapperCol:{span:16},onValuesChange:(e,t)=>{n(i,t)},children:[(0,S.jsx)(I.A,{children:(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(Ze,{label:r("\u96c6\u5408\u6765\u6e90"),name:"dataSourceCode",children:(0,S.jsx)(ze.A,{inputVariableType:x.ps.\u4e8c\u7ef4\u6570\u7ec4})})})}),(0,S.jsx)(I.A,{children:(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(Ze,{label:r("\u6570\u91cf"),name:"number",children:(0,S.jsx)(j.A,{min:0})})})})]})})},et=f.Ay.div`
    display: flex;
    height: 60vh;
    justify-content: center;
    margin-top: 10px;


`,tt=e=>{const{t:t,i18n:a}=(0,u.Bd)(),[n]=s.A.useForm(),[i,o]=(0,l.useState)("horizontal"),{TextArea:r}=d.A,[A,p]=(0,l.useState)(null===e||void 0===e?void 0:e.data),v=(t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");A[e[0]][e[1]]=l}else A[t]=l;p({...A}),e.toData(e.parentFieldName,A)};return(0,S.jsx)(et,{children:(0,S.jsxs)(s.A,{...E.Gt,labelAlign:"left",layout:i,form:n,initialValues:{layout:i},children:[(0,S.jsx)(s.A.Item,{label:t("\u683c\u5f0f"),children:(0,S.jsxs)(c.A,{name:"format",style:E.qw,value:null===A||void 0===A?void 0:A.format,onChange:e=>v("format",e),children:[(0,S.jsx)(c.A.Option,{value:"single",children:t("\u5355\u884c")}),(0,S.jsx)(c.A.Option,{value:"single_with_text",children:t("\u5355\u884c\uff0c\u5e26\u8bbe\u5b9a\u7684\u6587\u5b57")}),(0,S.jsx)(c.A.Option,{value:"multiple",children:t("\u591a\u884c")}),(0,S.jsx)(c.A.Option,{value:"func",children:t("\u7f16\u8f91\u5668")})]})}),"single_with_text"===(null===A||void 0===A?void 0:A.format)?(0,S.jsx)(s.A.Item,{label:t("\u6587\u5b57\u8bbe\u7f6e"),children:(0,S.jsx)(r,{style:E.qw,name:"content",value:null===A||void 0===A?void 0:A.content,onChange:e=>v("content",e),rows:4})}):null,"func"===(null===A||void 0===A?void 0:A.format)?(0,S.jsx)(s.A.Item,{label:t("\u811a\u672c\u7c7b\u578b"),children:(0,S.jsx)(c.A,{name:"return_type",style:E.qw,value:null===A||void 0===A?void 0:A.return_type,onChange:e=>v("return_type",e),options:Object.entries(x.Jt).map((e=>{let[t,a]=e;return{label:t,value:a}}))})}):null]})})};var at=a(67208),lt=a(75440);const nt=f.Ay.div`
    display: flex;
    width: 100%;
    flex-direction: column;
    .btn-content {
        display: flex;
        justify-content: flex-end;
        .btn-layout {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: vw;
            font-size: ${(0,C.D0)("12px")};
            color: rgba(0,0,0,0.45);
            line-height: 14px;
            >img {
                width: ${(0,C.D0)("20px")};
                height: ${(0,C.D0)("20px")};
                margin-bottom: 2px;
            }
        }
    }

    .list-layout {
        width: 100%;
        height: 15vh;
        background: #FFFFFF;
        border-radius: 4px;
        overflow-y: auto;
        border: 1px solid #D7E2FF;
        .title {
            border-bottom: 1px solid #D7E2FF;
            padding: ${(0,C.D0)("10px")};
            font-size: ${(0,C.D0)("16px")};
            color: rgba(0,0,0,0.84);
            cursor: pointer;
        }
        .opt {
            background: #F5F7FF;
            border-radius: 4px;
        }
    }
`,it=e=>{const{t:t,i18n:a}=(0,u.Bd)(),[n,i]=(0,l.useState)(),[o,s]=(0,l.useState)(e.data),c=t=>{s(t);const a=[];t.forEach((e=>{a.push({id:e.id,label:e.label,value:e.value})})),console.log("ArrayOptionsEditor",a),e.onChange(a)};return(0,S.jsxs)(nt,{children:[(0,S.jsx)("div",{className:"btn-content",children:(0,S.jsxs)(r.A,{children:[(0,S.jsxs)("div",{onClick:()=>{const e=[];o.forEach((t=>e.push(t)));const t=new Set(e.map((e=>e.label)));let a=o.length,l=`text ${a}`;for(;t.has(l);)a+=1,l=`text ${a}`;e.push({id:(new Date).getTime()+Math.floor(1e4*Math.random()),value:(0,b.vx)(12),label:(0,b.p9)(e.map((e=>e.label)))}),c(e)},className:"btn-layout",children:[(0,S.jsx)("img",{src:p.d7,alt:""}),(0,S.jsx)("div",{children:t("\u6dfb\u52a0")})]}),(0,S.jsxs)("div",{onClick:()=>{if(0!==o.length&&n){const e=[];let t=-1;for(let d=0;d<o.length;d+=1)o[d].id===n.id&&(t=d),e.push(o[d]);const a=t,l=t-1;if(l<0)return;const i=o[a],r=o[l];e[a]=r,e[l]=i,c(e)}},className:"btn-layout",children:[(0,S.jsx)("img",{src:p.NA,alt:""}),(0,S.jsx)("div",{children:t("\u4e0a\u79fb")})]}),(0,S.jsxs)("div",{onClick:()=>{if(0!==o.length&&n){const e=[];let t=-1;for(let d=0;d<o.length;d+=1)o[d].id===n.id&&(t=d),e.push(o[d]);const a=t,l=t+1;if(l>o.length-1)return;const i=o[a],r=o[l];e[a]=r,e[l]=i,c(e)}},className:"btn-layout",children:[(0,S.jsx)("img",{src:p.K2,alt:""}),(0,S.jsx)("div",{children:t("\u4e0b\u79fb")})]}),(0,S.jsxs)("div",{onClick:()=>{if(n){const t=[];o.forEach((e=>{e.id!==n.id&&t.push(e)})),c(t),i(),e.selectedChange&&e.selectedChange({})}},className:"btn-layout",children:[(0,S.jsx)("img",{src:p.pU,alt:""}),(0,S.jsx)("div",{children:t("\u5220\u9664")})]})]})}),(0,S.jsx)("div",{className:"list-layout",children:null===o||void 0===o?void 0:o.map((t=>(0,S.jsx)("div",{className:(null===n||void 0===n?void 0:n.id)===t.id?"title opt":"title",onClick:()=>{i(t),null!=e.selectedChange&&e.selectedChange(t)},children:t.label},null===t||void 0===t?void 0:t.id)))}),!0===e.showSelectEditor?(0,S.jsx)(d.A,{placeholder:t(""),value:null===n||void 0===n?void 0:n.label,onChange:e=>{n&&(i({label:e.target.value,value:n.value,id:n.id}),c(o.map((t=>t.id===n.id?{...t,label:e.target.value}:t))))}}):null]})},ot=f.Ay.div`
    height:58vh;
    margin-top: 10px;
    margin-left: 5vw;
    overflow: auto;
`,rt=Object.keys(Ae.yr).map((e=>Ae.yr[e])),dt=e=>{const{t:t,i18n:a}=(0,u.Bd)(),n=(0,h.d4)((e=>e.template.signalGroups)),i=(0,h.d4)((e=>e.template.mappingData)),[o]=s.A.useForm(),[r,A]=(0,l.useState)("horizontal"),[p,v]=(0,l.useState)({}),[m,g]=(0,l.useState)(null===e||void 0===e?void 0:e.data),y=(0,Ae.F$)(i,null===m||void 0===m?void 0:m.selectLayer),[f,C]=(0,l.useState)([]),w=[{value:"selector",label:t("\u9009\u62e9\u5668")},{value:"boxSelection",label:t("\u9009\u6846")}];(0,l.useEffect)((()=>{var e;const t=(null===y||void 0===y||null===(e=y.find((e=>e.value===(null===m||void 0===m?void 0:m.hardwareCategory))))||void 0===e?void 0:e.children)||[];C(t)}),[null===m||void 0===m?void 0:m.hardwareCategory]);const _=(t,a)=>{var l;let n=a;if(null!=n.target&&(n="checkbox"===n.target.type?a.target.checked:null!=n.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");p[e[0]][e[1]]=n}else p[t]=n;v(p);const i={...m,items:m.items.map((e=>{const a={...e};return a.id===p.id&&(a[t]=n),a}))};g(i);const o=m.items.map((e=>e.value)),r=i.items.map((e=>e.value)),d=r.filter((e=>!o.includes(e)))[0],s=o.filter((e=>!r.includes(e)))[0];var c;"multiple_multiple"===(null===m||void 0===m?void 0:m.selection)&&j({value:null===e||void 0===e||null===(c=e.defaultVal)||void 0===c?void 0:c.value.map((e=>e===s?d:e))});(null===e||void 0===e||null===(l=e.defaultVal)||void 0===l?void 0:l.value)===s&&j({value:d}),e.toData(e.parentFieldName,i)},R=(t,a)=>{var l;let i=a;var o,r;null!=(null===(l=i)||void 0===l?void 0:l.target)&&(i="checkbox"===(null===(o=i)||void 0===o?void 0:o.target.type)?null===a||void 0===a?void 0:a.target.checked:null!=(null===(r=i)||void 0===r?void 0:r.target.value)?null===a||void 0===a?void 0:a.target.value:a);if(t.indexOf(".")>-1){const e=t.split(".");m[e[0]][e[1]]=i}else m[t]=i;if(g({...m}),"items"===t&&((null===a||void 0===a?void 0:a.length)>0&&j({value:(null===m||void 0===m?void 0:m.selection)===x.P$.\u5e26\u591a\u9009\u6846\u7684\u591a\u9009\u9879.value?[null===a||void 0===a?void 0:a[0].value]:null===a||void 0===a?void 0:a[0].value}),j({value:(null===m||void 0===m?void 0:m.selection)===x.P$.\u5e26\u591a\u9009\u6846\u7684\u591a\u9009\u9879.value?[]:"",id:null})),"group_id"===t){const e=n.find((e=>e.group_id===a)).variable_ids;e&&(null===e||void 0===e?void 0:e.length)>0&&j({value:e[0].code})}"selection"===t&&j({value:(0,b.PM)(a),key:"",id:null,name:"",value_type:(0,b.AZ)(a)}),"selectLayer"!==t&&"hardwareCategory"!==t||(m.axisSelection=void 0,g({...m,axisSelection:void 0})),"hardwareCategory"!==t&&"axisSelection"!==t||j({value:(0,b.PM)(a),key:"",id:null,name:"",value_type:(0,b.AZ)(a)}),e.toData(e.parentFieldName,m)},j=t=>{e.toData(e.parentFieldKey,{...e.defaultVal,...t})},[P,k]=(0,l.useState)(!1),D={span:8,offset:2};return(0,S.jsx)(ot,{children:(0,S.jsxs)(s.A,{labelCol:{span:5},wrapperCol:{span:12},labelAlign:"left",layout:r,form:o,initialValues:{layout:r},children:[(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u9009\u62e9"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"label",name:"selection",value:null===m||void 0===m?void 0:m.selection,onChange:e=>R("selection",e),style:E.qw,children:Object.values(x.P$).map((e=>(0,S.jsx)(c.A.Option,{value:e.value,label:e.label,children:t(e.label)},e.value)))})})}),(null===m||void 0===m?void 0:m.selection)!==x.P$.\u6620\u50cf\u6570\u636e\u6e90.value&&(null===m||void 0===m?void 0:m.selection)!==x.P$.\u4e8c\u7ef4\u6570\u7ec4\u5217\u6570\u636e\u6e90.value?(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u663e\u793a\u5f62\u6001"),children:(0,S.jsx)(c.A,{name:"show_type",value:null===m||void 0===m?void 0:m.show_type,options:w,onChange:e=>R("show_type",e),style:E.qw})})}):null,(()=>{switch(null===m||void 0===m?void 0:m.selection){case x.P$.\u5e03\u5c14\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case x.P$.\u6570\u5b57\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case x.P$.\u52a8\u4f5c\u6570\u636e\u6e90.value:case x.P$.\u52a8\u4f5c\u5185\u90e8\u540d\u6570\u636e\u6e90.value:case x.P$.\u4e8c\u7ef4\u6570\u7ec4\u5217\u6570\u636e\u6e90.value:case x.P$.\u7a97\u4f53\u6570\u636e\u6e90.value:case x.P$.\u7279\u6b8a\u52a8\u4f5c\u6570\u636e\u6e90.value:case x.P$.\u6620\u50cf\u8f74\u6570\u636e\u6e90.value:case x.P$.\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90.value:case x.P$["\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90(\u591a\u9009)"].value:case x.P$["\u53ef\u5206\u6790\u8bd5\u6837(\u591a\u9009)"].value:case x.P$.Buffer\u6570\u636e\u6e90.value:case x.P$.\u97f3\u9891\u6570\u636e\u6e90.value:case x.P$.\u7ed3\u679c\u6570\u636e\u6e90.value:case x.P$["Buffer\u4fe1\u53f7\u53d8\u91cf"].value:return(0,S.jsx)(S.Fragment,{});case x.P$.\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:return(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u7c7b\u578b"),children:(0,S.jsx)(c.A,{value:null===m||void 0===m?void 0:m.inputVariableType,allowClear:!0,options:Object.entries(x.ps).map((e=>{let[t,a]=e;return{label:t,value:a}})),onChange:e=>{R("inputVariableType",e)},style:E.qw})})});case x.P$.\u6620\u50cf\u6570\u636e\u6e90.value:return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u5c42\u7ea7"),children:(0,S.jsx)(c.A,{value:null===m||void 0===m?void 0:m.selectLayer,options:Object.entries(x.Q1).map((e=>{let[t,a]=e;return{label:t,value:a}})).filter((e=>"sensor"!==e.value)),onChange:e=>{R("selectLayer",e)},style:E.qw})})}),(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u786c\u4ef6\u5927\u7c7b"),children:(0,S.jsx)(c.A,{value:null===m||void 0===m?void 0:m.hardwareCategory,options:rt,onChange:e=>{R("hardwareCategory",e)},style:E.qw,allowClear:!0})})}),(null===m||void 0===m?void 0:m.selectLayer)===x.Q1["\u901a\u9053"]?(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u8f74\u9009\u62e9"),children:(0,S.jsx)(c.A,{value:null===m||void 0===m?void 0:m.axisSelection,options:f,onChange:e=>R("axisSelection",e),style:E.qw,allowClear:!0})})}):null]});case x.P$.\u6570\u636e\u6e90.value:return(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u7c7b\u578b"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"group_name",name:"selection",value:null===m||void 0===m?void 0:m.group_id,fieldNames:{label:"group_name",value:"group_id"},options:n,onChange:e=>R("group_id",e),style:E.qw})})});default:return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u6587\u5b57"),children:(0,S.jsx)(d.A,{placeholder:t(""),style:E.qw,value:p.label,onChange:e=>_("label",e)})})}),(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u6807\u8bc6\u7b26"),children:(0,S.jsx)(d.A,{placeholder:t(""),style:E.qw,name:"value",value:p.value,onChange:e=>_("value",e)})})})]})}})()]}),(()=>{switch(null===m||void 0===m?void 0:m.selection){case x.P$.\u6570\u636e\u6e90.value:case x.P$.\u52a8\u4f5c\u6570\u636e\u6e90.value:case x.P$.\u52a8\u4f5c\u5185\u90e8\u540d\u6570\u636e\u6e90.value:case x.P$.\u4e8c\u7ef4\u6570\u7ec4\u5217\u6570\u636e\u6e90.value:case x.P$.\u7a97\u4f53\u6570\u636e\u6e90.value:case x.P$.\u7279\u6b8a\u52a8\u4f5c\u6570\u636e\u6e90.value:case x.P$["Buffer\u4fe1\u53f7\u53d8\u91cf"].value:case x.P$.\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90.value:case x.P$["\u4fe1\u53f7\u53d8\u91cf\u6570\u636e\u6e90(\u591a\u9009)"].value:case x.P$.\u6620\u50cf\u8f74\u6570\u636e\u6e90.value:case x.P$.\u6620\u50cf\u6570\u636e\u6e90.value:case x.P$.Buffer\u6570\u636e\u6e90.value:case x.P$.\u97f3\u9891\u6570\u636e\u6e90.value:case x.P$.\u7ed3\u679c\u6570\u636e\u6e90.value:case x.P$.\u5e03\u5c14\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case x.P$.\u6570\u5b57\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case x.P$.\u8f93\u5165\u53d8\u91cf\u6570\u636e\u6e90.value:case x.P$["\u53ef\u5206\u6790\u8bd5\u6837(\u591a\u9009)"].value:return(0,S.jsx)(S.Fragment,{});default:return(0,S.jsx)(I.A,{children:(0,S.jsx)(T.A,{...D,children:(0,S.jsx)(s.A.Item,{label:t("\u5217\u8868"),children:(0,S.jsx)("div",{style:E.qw,children:(0,S.jsx)(it,{data:null===m||void 0===m?void 0:m.items,currentItem:p,showSelectEditor:!1,selectedChange:e=>v(e),onChange:e=>R("items",e)})})})})})}})()]})})};var st=a(92941);const ct=f.Ay.div`
    display: flex;
    height: 59vh;
    justify-content: space-between;
    padding: 10px;
    .content {
       display: flex;
       justify-content: space-between;
       height: 100%;
       width: 20vw;
       .top-content {
         height: 21vh;
         padding: 10px;
       }
       .left-content {
            height: 34.5vh;
            padding: 10px;
            overflow: auto;
       }
    }
    .right-content {
            width: 35.8vw;
            height: 100%;
            overflow: auto;
            padding: 10px;
            .table-title-layout {
                display: flex;
                .table-title {
                    width: 8vw;
                    white-space: nowrap;
                    flex-shrink: 0;
                    display: flex;
                    justify-content: center;
                    margin-left: 0.3vw;
                    font-weight: 600;
                }
            }
            .table-content {
               margin-top: 0.5vw;
               margin-left: -0.5vw;
               display: flex;
               .table-col-content {
                    margin-left: 0.5vw;
               }
            }

       }
`,At=e=>{const{t:t,i18n:a}=(0,u.Bd)(),[n]=s.A.useForm(),[i,o]=(0,l.useState)("horizontal"),[p,v]=(0,l.useState)(null===e||void 0===e?void 0:e.data),m=[];for(let l=1;l<20;l+=1)m.push({label:l,value:l});const h=(t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");p[e[0]][e[1]]=l}else p[t]=l;"rowCounts"===t&&(p.columnData=function(e){const t=[e];for(let a=0;a<e;a+=1)a<=p.columnData.length-1?t[a]=p.columnData[a]:t[a]=new Array(p.columnCount);return t}(l),p.rowDefinition=(e=>{const t=[e];for(let a=0;a<e;a+=1)a<p.rowDefinition.length?t[a]=p.rowDefinition[a]:t[a]={title:"",type:"text",options:[]};return t})(l)),"columnCount"===t&&(p.columnData=(e=>{const t=[p.columnData.length];for(let a=0;a<p.columnData.length;a+=1){t[a]=new Array(e);for(let l=0;l<e;l+=1)l<=p.columnData[a].length-1?t[a][l]=p.columnData[a][l]:t[a][l]=""}return t})(l),p.columnDefinition=(e=>{const t=[e];for(let a=0;a<e;a+=1)a<p.columnDefinition.length?t[a]=p.columnDefinition[a]:t[a]={title:"",type:"text",options:[]};return t})(l)),v({...p}),e.toData(e.parentFieldName,p)},g=(t,a,l)=>{let n=l;null!=n.target&&(n="checkbox"===n.target.type?l.target.checked:null!=n.target.value?l.target.value:l),p.columnDefinition[t][a]=n,v({...p}),e.toData(e.parentFieldName,p)},b=(t,a,l)=>{let n=l;null!=n.target&&(n="checkbox"===n.target.type?l.target.checked:null!=n.target.value?l.target.value:l),p.rowDefinition[t][a]=n,v({...p}),e.toData(e.parentFieldName,p)},y=(t,a,l)=>{let n=l;null!=n.target&&(n="checkbox"===n.target.type?l.target.checked:null!=n.target.value?l.target.value:l),p.columnData[t][a]=n,e.toData(e.parentFieldName,p)},x={width:"8vw"};return(0,S.jsxs)(ct,{children:[(0,S.jsx)("div",{className:"content",children:(0,S.jsxs)(r.A,{direction:"vertical",size:8,children:[(0,S.jsx)(D.A,{children:(0,S.jsx)("div",{className:"top-content",children:(0,S.jsx)(s.A,{labelAlign:"left",layout:i,form:n,initialValues:{layout:i},children:(0,S.jsxs)(I.A,{children:[(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(s.A.Item,{label:t("\u884c\u8868\u5934\u5360\u4f4d"),children:(0,S.jsx)(A.A,{style:E.qw,name:"rowHeaderPlace",checked:p.rowHeaderPlace,onChange:e=>h("rowHeaderPlace",e)})})}),(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(s.A.Item,{label:t("\u884c\u6570"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"label",options:m,style:{width:"4vw"},name:"rowCounts",value:p.rowCounts,onChange:e=>h("rowCounts",e)})})}),(0,S.jsx)(T.A,{span:12,children:(0,S.jsx)(s.A.Item,{label:t("\u5217\u8868\u5934\u5360\u4f4d"),children:(0,S.jsx)(A.A,{name:"columnHeaderPlace",checked:p.columnHeaderPlace,onChange:e=>h("columnHeaderPlace",e)})})}),(0,S.jsx)(s.A.Item,{label:t("\u5217\u6570"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"label",options:m,name:"columnCount",style:{width:"4vw"},value:p.columnCount,onChange:e=>h("columnCount",e)})}),(0,S.jsx)(s.A.Item,{label:t("\u5217\u8868\u5c5e\u6027"),children:(0,S.jsxs)(Q.Ay.Group,{name:"radiogroup",value:null===p||void 0===p?void 0:p.isRowType,onChange:e=>h("isRowType",e),children:[(0,S.jsx)(Q.Ay,{value:!1,children:t("\u5217\u5c5e\u6027")}),(0,S.jsx)(Q.Ay,{value:!0,children:t("\u884c\u5c5e\u6027")})]})})]})})})}),(0,S.jsx)(D.A,{children:(0,S.jsx)("div",{className:"left-content",children:(0,S.jsx)(s.A,{layout:i,form:n,initialValues:{layout:i},children:(e=>{let{definition:a,onChange:l,isRowType:n}=e;return a.map(((e,i)=>(0,S.jsxs)(s.A.Item,{label:`${t(n?"\u884c":"\u5217")}${i+1}`,children:[(0,S.jsx)(I.A,{children:(0,S.jsx)(T.A,{span:12,children:(0,S.jsxs)(c.A,{name:`type${i+1}`,value:a[i].type,onChange:e=>l(i,"type",e),children:[(0,S.jsx)(c.A.Option,{value:"text",children:t("\u6587\u672c")}),(0,S.jsx)(c.A.Option,{value:"select",children:t("\u9009\u62e9")})]})})}),"select"===a[i].type?(0,S.jsx)("div",{className:"table-layout",children:(0,S.jsx)(it,{data:a[i].options,showSelectEditor:!0,onChange:e=>l(i,"options",e)})}):null]})))})({isRowType:null===p||void 0===p?void 0:p.isRowType,definition:null!==p&&void 0!==p&&p.isRowType?p.rowDefinition:p.columnDefinition,onChange:null!==p&&void 0!==p&&p.isRowType?b:g})})})})]})}),(0,S.jsx)(D.A,{title:t("\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c(\u8bf7\u5728\u884c\u5217\u4e2d\u6839\u636e\u9700\u8981\u5f55\u5165\u521d\u59cb\u503c)"),children:(0,S.jsx)("div",{className:"right-content",children:(0,S.jsx)(st.A,{columns:null===p||void 0===p?void 0:p.columnDefinition.map(((e,a)=>({title:(0,S.jsx)("div",{children:(0,S.jsx)(d.A,{placeholder:t("\u5217\u540d"),style:x,value:e.title,onChange:e=>g(a,"title",e)})}),dataIndex:"name",width:"8vw",render:(t,l,n)=>(e=>{let{type:t,record:a,renderIndex:l,index:n,options:i}=e;return"select"===t?(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"label",style:x,value:a[n],options:i,onChange:e=>y(l,n,e)}):(0,S.jsx)(d.A,{value:a[n],onChange:e=>y(l,n,e),style:x})})({type:null!==p&&void 0!==p&&p.isRowType?null===p||void 0===p?void 0:p.rowDefinition[n].type:e.type,record:l,renderIndex:n,index:a,options:null!==p&&void 0!==p&&p.isRowType?null===p||void 0===p?void 0:p.rowDefinition[n].options:null===p||void 0===p?void 0:p.columnDefinition[a].options})}))),rowData:p.rowDefinition,dataSource:p.columnData,colHead:p.columnHeaderPlace,rowHead:p.rowHeaderPlace,rowHeadRender:(e,a,l,n)=>{var i;return(0,S.jsx)(d.A,{value:null===(i=n[l])||void 0===i?void 0:i.title,placeholder:t("\u884c\u540d"),onChange:e=>b(l,"title",e),style:x})},scroll:{x:!0,y:"30vh"}})})})]})},ut=f.Ay.div`
    display: flex;
    height: 60vh;
    justify-content: center;
    margin-top: 10px;
`,pt=e=>{const{t:t}=(0,u.Bd)(),[a]=s.A.useForm(),n=(0,h.d4)((e=>e.template.actionList)),[i,o]=(0,l.useState)(null===e||void 0===e?void 0:e.data);(0,l.useEffect)((()=>{null!==i&&void 0!==i&&i.content||r("isEnable",!1)}),[null===i||void 0===i?void 0:i.content]);const r=(t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");i[e[0]][e[1]]=l}else i[t]=l;o(i),e.toData(e.parentFieldName,i)};return(0,S.jsx)(ut,{children:(0,S.jsxs)(s.A,{layout:"horizontal",form:a,style:{width:"60%"},children:[(0,S.jsx)(s.A.Item,{label:t("\u542f\u7528\u6309\u94ae"),children:(0,S.jsx)(A.A,{name:"isEnable",checked:null===i||void 0===i?void 0:i.isEnable,onChange:e=>r("isEnable",e),disabled:""===(null===i||void 0===i?void 0:i.content)})}),(0,S.jsx)(s.A.Item,{label:t("\u6309\u94ae\u6587\u672c"),children:(0,S.jsx)(d.A,{maxLength:4,placeholder:t("\u6700\u591a\u8f93\u51654\u4e2a\u5b57\u7b26"),name:"content",value:null===i||void 0===i?void 0:i.content,onChange:e=>r("content",e)})}),(0,S.jsx)(s.A.Item,{label:t("\u6309\u94ae\u4f4d\u7f6e"),children:(0,S.jsx)(Q.Ay.Group,{defaultValue:"left",value:null===i||void 0===i?void 0:i.position,onChange:e=>r("position",e),options:(()=>{const a=e.type===x.ps.\u9009\u62e9?"\u9009\u62e9\u6846":"\u8f93\u5165\u6846";return[{label:t(`${a}\u524d`),value:"left"},{label:t(`${a}\u540e`),value:"right"}]})()})}),(0,S.jsx)(s.A.Item,{label:t("\u6309\u94ae\u7c7b\u578b"),children:(0,S.jsx)(Q.Ay.Group,{defaultValue:"left",value:null===i||void 0===i?void 0:i.buttonType,style:{width:"15vw"},onChange:e=>r("buttonType",e),options:[{label:t("\u52a8\u4f5c"),value:E.NR.\u52a8\u4f5c},{label:t("\u811a\u672c"),value:E.NR.\u811a\u672c}]})}),i.buttonType===E.NR.\u52a8\u4f5c?(0,S.jsx)(s.A.Item,{label:t("\u6309\u94ae\u52a8\u4f5c"),children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"action_name",options:n,name:"actionId",fieldNames:{label:"action_name",value:"action_id"},value:null===i||void 0===i?void 0:i.actionId,onChange:e=>r("actionId",e)})}):(0,S.jsx)(N.im,{height:"30vh",value:i.script,module:N.et.\u8f93\u5165\u53d8\u91cf,onChange:e=>r("script",e)})]})})};var vt=a(26851),mt=a(36069);const ht=f.Ay.div`
    display: flex;
    height: 60vh;
    justify-content: center;
    margin-top: 10px;
    .img-list {
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,0.15);
            display: flex;
            background: rgba(255,255,255,0.8);
            box-shadow: 2px 0px 7px 0px rgba(3,36,71,0.08);
            padding: 0.4vw;
           >img {
                width: 6vw; 
                height: 6vw;
           }
        }

`,gt=e=>{const{t:t,i18n:a}=(0,u.Bd)(),[n]=s.A.useForm(),[i,o]=(0,l.useState)("horizontal"),[c,v]=(0,l.useState)(null===e||void 0===e?void 0:e.data),[m,h]=(0,l.useState)(!1),[g,y]=(0,l.useState)(),f=(t,a)=>{var l;c.src=t,c.path=null===a||void 0===a?void 0:a.path,c.name=null===(l=null===a||void 0===a?void 0:a.name)||void 0===l?void 0:l.substring(0,null===l||void 0===l?void 0:l.indexOf(".")),v({...c}),e.toData(e.parentFieldName,c)},C=async e=>{try{const t=await(0,b.ZJ)(e);return f(t,e),!1}catch(t){console.log(t,"\u538b\u7f29\u56fe\u7247\u5f02\u5e38")}return!1};return(0,S.jsxs)(ht,{children:[(0,S.jsxs)(s.A,{...E.Gt,labelAlign:"left",form:n,initialValues:{layout:i},children:[(0,S.jsx)(s.A.Item,{label:t("\u663e\u793a\u540d\u79f0"),children:(0,S.jsx)(A.A,{name:"showName",checked:null===c||void 0===c?void 0:c.showName,onChange:t=>((t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");c[e[0]][e[1]]=l}else c[t]=l;v({...c}),e.toData(e.parentFieldName,c)})("showName",t)})}),(0,S.jsx)(s.A.Item,{label:t("\u6dfb\u52a0\u56fe\u7247"),children:(0,S.jsxs)(r.A,{children:[(0,S.jsx)(X.Ay,{onClick:()=>{h(!0)},children:t("\u5e93\u4e2d\u9009\u62e9")}),(0,S.jsx)(vt.A,{name:"avatar",accept:".png, .jpg",beforeUpload:C,showUploadList:!1,children:(0,S.jsx)(X.Ay,{children:t("\u672c\u5730\u4e0a\u4f20")})})]})}),(0,S.jsx)(s.A.Item,{label:t("\u56fe\u7247"),children:(0,S.jsx)(vt.A,{name:"avatar",accept:".png, .jpg",beforeUpload:C,disabled:!0,showUploadList:!1,children:(0,S.jsx)("div",{className:"img-list",children:(0,S.jsx)("img",{src:null!==c&&void 0!==c&&c.src?c.src.startsWith("data:")||c.src.includes(x.mF)?c.src:`${x.mF}${c.src}`:p.Np,alt:""})})})}),(0,S.jsx)(s.A.Item,{label:t("\u56fe\u7247\u8def\u5f84"),children:(0,S.jsx)(d.A,{disabled:!0,name:"showName",value:null===c||void 0===c?void 0:c.path,title:null===c||void 0===c?void 0:c.path})})]}),(0,S.jsx)(lt.A,{title:t("\u9009\u62e9\u56fe\u7247"),width:"80vw",height:"50vw",open:m,onCancel:()=>h(!1),footer:null,children:(0,S.jsx)(mt.A,{handleClick:e=>{f(e),h(!1)}})})]})},bt=f.Ay.div`
    display: flex;
    height: 60vh;
    justify-content: center;
    margin-top: 10px;


`,{TextArea:yt}=d.A,xt=e=>{const{t:t,i18n:a}=(0,u.Bd)(),[n]=s.A.useForm(),[i,o]=(0,l.useState)("horizontal"),[r,A]=(0,l.useState)(null===e||void 0===e?void 0:e.data),p=(t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");r[e[0]][e[1]]=l}else r[t]=l;A({...r}),e.toData(e.parentFieldName,r)};return(0,S.jsx)(bt,{children:(0,S.jsxs)(s.A,{...E.Gt,labelAlign:"left",layout:i,form:n,initialValues:{layout:i},children:[(0,S.jsx)(s.A.Item,{label:t("\u6587\u672c\u884c\u6570"),children:(0,S.jsxs)(c.A,{name:"format",value:null===r||void 0===r?void 0:r.format,onChange:e=>p("format",e),style:E.qw,children:[(0,S.jsx)(c.A.Option,{value:"single",children:t("\u5355\u884c")}),(0,S.jsx)(c.A.Option,{value:"multiple",children:t("\u591a\u884c")})]})}),(0,S.jsx)(s.A.Item,{label:t("\u6587\u672c\u5185\u5bb9"),children:"single"===(null===r||void 0===r?void 0:r.format)?(0,S.jsx)(d.A,{name:"content",value:null===r||void 0===r?void 0:r.content,onChange:e=>p("content",e),style:E.qw}):(0,S.jsx)(yt,{name:"content",value:null===r||void 0===r?void 0:r.content,onChange:e=>p("content",e),rows:3,style:{...E.qw,maxWidth:"initial"}})}),(0,S.jsx)(s.A.Item,{label:t("\u663e\u793a\u5b57\u53f7"),children:(0,S.jsxs)(c.A,{name:"fontSize",value:null===r||void 0===r?void 0:r.fontSize,onChange:e=>p("fontSize",e),style:E.qw,children:[(0,S.jsx)(c.A.Option,{value:"8",children:"8"}),(0,S.jsx)(c.A.Option,{value:"9",children:"9"}),(0,S.jsx)(c.A.Option,{value:"10",children:"10"}),(0,S.jsx)(c.A.Option,{value:"12",children:"12"}),(0,S.jsx)(c.A.Option,{value:"14",children:"14"}),(0,S.jsx)(c.A.Option,{value:"16",children:"16"}),(0,S.jsx)(c.A.Option,{value:"18",children:"18"}),(0,S.jsx)(c.A.Option,{value:"20",children:"20"})]})}),(0,S.jsx)(s.A.Item,{label:t("\u6587\u5b57\u989c\u8272"),children:(0,S.jsx)(d.A,{type:"color",style:{width:"3vw"},name:"fore",value:null===r||void 0===r?void 0:r.fore,onChange:e=>p("fore",e)})})]})})};var ft=a(22);const Ct=f.Ay.div`
    display: flex;
    flex-direction: column;
    height: 60vh;
    overflow-y: auto;
    padding: 20px;
    >pre {
        width: 100%
    } 
`,wt=e=>{let{data:t,toData:a}=e;return(0,S.jsx)(Ct,{children:(0,S.jsx)(ft.A,{variable:t,onChange:e=>{a("",e)}})})};var Et=a(12624);const St=f.Ay.div`
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
        height: 35vh;
        overflow: auto;
        .info {
            padding: 5px;
            cursor: pointer;
            &:hover {
               background: rgba(215, 226, 255, 0.2);
            }
        }
        .select {
            padding: 5px;
            cursor: pointer;
            background: rgba(215, 226, 255, 1);
        }
    }

    .btn-layout {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        >img {
            width: ${(0,C.D0)("26px")};
            height: ${(0,C.D0)("26px")};
        }
        >div {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(0,0,0,0.45);
            margin-top: 2px;
            line-height: 14px;
            
        }
    }

`,_t=e=>{const{t:t,i18n:a}=(0,u.Bd)(),n=(0,Ue.A)(),[i]=s.A.useForm(),o=s.A.useWatch("dialog_type",i),r=s.A.useWatch("type",i),[A,p]=(0,l.useState)(null===e||void 0===e?void 0:e.data);(0,l.useEffect)((()=>{i.setFieldsValue(null===e||void 0===e?void 0:e.data)}),[null===e||void 0===e?void 0:e.data]);const v=(t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");A[e[0]][e[1]]=l}else A[t]=l;p({...A}),e.toData(e.parentFieldName,A),e.toData(e.parentFieldKey,(0,y.U1)((0,y.Sq)(A).groups,{value:[],data:[],isConstant:0,groups:[]}))};return(0,S.jsx)(St,{children:(0,S.jsxs)(s.A,{form:i,labelCol:{span:8},wrapperCol:{span:16},children:[(0,S.jsx)(s.A.Item,{label:t("\u63a7\u4ef6\u7c7b\u578b"),name:"type",rules:[{required:!0,message:t("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(Q.Ay.Group,{style:{width:"18vw"},onChange:e=>v("type",e),options:(0,Y.Yz)({t:t})})}),r===Y.ze.NOT_CUSTOM?(0,S.jsx)(s.A.Item,{label:t("\u5f39\u51fa\u7a97\u53e3"),name:"dialog_type",rules:[{required:!0,message:t("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"label",options:Y.vU,onChange:e=>v("dialog_type",e)})}):(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(s.A.Item,{label:t("\u63a7\u4ef6\u540d\u79f0"),name:"control_name",rules:[{required:!0,message:t("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(d.A,{onChange:e=>v("control_name",e)})}),(0,S.jsx)(s.A.Item,{label:t("\u5185\u90e8\u540d\u79f0"),name:"code",rules:[{required:!0,message:t("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(d.A,{onChange:e=>v("code",e)})}),(0,S.jsx)(s.A.Item,{label:t("\u521d\u59cb\u5316\u540d\u79f0"),name:"default_name",rules:[{required:!0,message:t("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(d.A,{onChange:e=>v("default_name",e)})})]}),(0,S.jsx)(s.A.Item,{label:t("\u5173\u8054\u8f93\u5165\u53d8\u91cf"),name:"related_variables",rules:[{required:(null===A||void 0===A?void 0:A.type)===Y.ze.CUSTOM,message:t("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"name",options:n,onChange:e=>v("related_variables",e),mode:"multiple",fieldNames:{label:"name",value:"id"}})}),o===Y.YM.SIGNAL&&(0,S.jsx)(s.A.Item,{label:t("\u662f\u5426\u9009\u62e9DAQ"),name:"is_daq",valuePropName:"checked",rules:[{required:!0,message:t("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(Et.A,{checkedChildren:t("\u5f00\u542f"),unCheckedChildren:t("\u5173\u95ed"),onChange:e=>v("is_daq",e)})})]})})},Rt=f.Ay.div`
    display: flex;
    height: 60vh;
    justify-content: center;
    margin-top: 10px;
`,jt=e=>{const{t:t,i18n:a}=(0,u.Bd)(),[n]=s.A.useForm(),[i,o]=(0,l.useState)("horizontal"),[r,d]=(0,l.useState)(null===e||void 0===e?void 0:e.data);return(0,S.jsx)(Rt,{children:(0,S.jsx)(s.A,{...E.Gt,labelAlign:"left",layout:i,form:n,initialValues:{layout:i},children:(0,S.jsx)(s.A.Item,{label:t("\u683c\u5f0f"),children:(0,S.jsx)(c.A,{name:"showType",style:E.qw,value:null===r||void 0===r?void 0:r.showType,onChange:t=>((t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");r[e[0]][e[1]]=l}else r[t]=l;d({...r}),e.toData(e.parentFieldName,r)})("showType",t),options:[{label:t("\u52fe\u9009"),value:"checkbox"},{label:t("\u5f00\u5173"),value:"switch"}]})})})})},It=f.Ay.div`
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
        height: 35vh;
        overflow: auto;
        .info {
            padding: 5px;
            cursor: pointer;
            &:hover {
               background: rgba(215, 226, 255, 0.2);
            }
        }
        .select {
            padding: 5px;
            cursor: pointer;
            background: rgba(215, 226, 255, 1);
        }
    }

    .btn-layout {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        >img {
            width: ${(0,C.D0)("26px")};
            height: ${(0,C.D0)("26px")};
        }
        >div {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(0,0,0,0.45);
            margin-top: 2px;
            line-height: 14px;
            
        }
    }

`,Tt=e=>{const{t:t}=(0,u.Bd)(),a=(0,h.d4)((e=>e.template.signalList)),[n]=s.A.useForm(),[i,o]=(0,l.useState)(null===e||void 0===e?void 0:e.data),r=(0,l.useMemo)((()=>a.map((e=>({label:`${e.variable_name}(${e.code})`,value:e.code})))),[a]);(0,l.useEffect)((()=>{n.setFieldsValue(null===e||void 0===e?void 0:e.data)}),[null===e||void 0===e?void 0:e.data]);const d=(t,a)=>{let l=a;if(null!=l.target&&(l="checkbox"===l.target.type?a.target.checked:null!=l.target.value?a.target.value:a),t.indexOf(".")>-1){const e=t.split(".");i[e[0]][e[1]]=l}else i[t]=l;o({...i}),e.toData(e.parentFieldName,i),e.toData(e.parentFieldKey,{value:[],isConstant:0,groups:[]})};return(0,S.jsx)(It,{children:(0,S.jsxs)(s.A,{form:n,labelCol:{span:10},wrapperCol:{span:14},children:[(0,S.jsx)(s.A.Item,{label:t("buffer\u7c7b\u578b"),name:"buffer_type",rules:[{required:!0,message:t("\u8bf7\u9009\u62e9")}],children:(0,S.jsx)(c.A,{showSearch:!0,optionFilterProp:"label",options:(0,Y.r1)({t:t}),style:{width:"20vw"},onChange:e=>d("buffer_type",e)})}),(0,S.jsx)(s.A.Item,{label:t("\u4fe1\u53f7\u53d8\u91cf"),name:"signals",children:(0,S.jsx)(c.A,{mode:"multiple",style:{width:"20vw"},onChange:e=>d("signals",e),options:r})}),(null===i||void 0===i?void 0:i.buffer_type)!==Y.yw.NOT&&(0,S.jsx)(s.A.Item,{label:t("buffer\u5927\u5c0f"),name:"size",rules:[{required:!0,validator:(e,a)=>null===a||!Number.isInteger(a)||a<=0?Promise.reject(new Error(t("\u8bf7\u8f93\u5165\u6b63\u6574\u6570"))):Promise.resolve()}],children:(0,S.jsx)(j.A,{onChange:e=>d("size",e)})}),(null===i||void 0===i?void 0:i.buffer_type)===Y.yw.ARRAY_QUEUE&&(0,S.jsx)(s.A.Item,{label:t("\u5b58\u6ee1\u540e\u64cd\u4f5c"),name:"size_expand",rules:[{required:!0,message:t("\u8bf7\u8f93\u5165")}],children:(0,S.jsx)(c.A,{showSearch:!0,style:{width:"20vw"},optionFilterProp:"label",options:(0,Y.hX)({t:t}),onChange:e=>d("size_expand",e)})})]})})};var Pt=a(754);const kt=f.Ay.div`
    display: flex;
    height: 65vh;
    justify-content: space-between;
    
    .left-info {
        width: 58vw;
        height: 65vh;
        background: rgba(255,255,255,0.8);
        box-shadow: 2px 0px 7px 0px rgba(3,36,71,0.08);
        border-radius: 4px;

        .ant-tabs-top {
            .ant-tabs-nav-wrap {
                padding-left: ${(0,C.D0)("10px")}; 
            }
            
            >.ant-tabs-nav {
                margin: 0;
                &::before {
                    box-shadow: 0px 2px 4px 0px rgba(7,26,112,0.1);
                }
            }
            
        }

    }
    .right-button {
        display: flex;
        width: 9vw;
        height: 64vh;
        flex-direction: column;
        justify-content: space-between;
    }
`,Dt=e=>{const{t:t}=(0,u.Bd)(),[a,d]=(0,l.useState)({activeTabKey:"/tabGeneral"}),[s,c]=(0,l.useState)(e.data),[A,p]=n.Ay.useMessage();(0,l.useEffect)((()=>(c(v(e.data)),d({activeTabKey:"/tabGeneral"}),()=>{})),[null===e||void 0===e?void 0:e.data]);const v=t=>e.related_result_variable_id?(0,b.qD)(t,m.O.RESULT_INPUT,!0):e.notService?(0,b.qD)(t,m.O.CONTROL_INPUT,!0):(0,b.qD)(t,m.O.INPUT,!0),h=async()=>{if(!y())return;let a=await g(s);if(T("tabDoubleArray")){var l,i,o;const e=(null===(l=a)||void 0===l||null===(i=l.double_array_tab)||void 0===i||null===(o=i.columns)||void 0===o?void 0:o.filter((e=>e.showName)))||[],r=new Set;let d=!1;const s=e.some((e=>e.code&&""!==e.code.trim()?r.has(e.code)?(n.Ay.error(t("\u5217\u5185\u90e8\u540d\u4e0d\u80fd\u91cd\u590d")),d=!0,!0):(r.add(e.code),!1):(n.Ay.error(t("\u5217\u5185\u90e8\u540d\u4e0d\u80fd\u4e3a\u7a7a")),d=!0,!0)));if(s||d)return}if(T("tabReasonable")){var r,d;const{defaultVal:e,maxParam:l,minParam:i,reasonableType:o}=null===(r=a)||void 0===r?void 0:r.reasonable_val_tab,{value:s}=null===(d=a)||void 0===d?void 0:d.default_val;if(o===x.Me.MAX_MIN||o===x.Me.REASONABLE_MAX_MIN){if(l<i)return void n.Ay.error(t("\u5408\u7406\u503c\u4e2d\u6700\u5927\u503c\u4e0d\u80fd\u5c0f\u4e8e\u6700\u5c0f\u503c"));if(s>l||s<i)return void n.Ay.error(t("\u9884\u89c8\u4e2d\u503c\u5fc5\u987b\u5728\u6700\u5c0f\u503c\u548c\u6700\u5927\u503c\u4e4b\u95f4"))}if(o===x.Me.REASONABLE_MAX_MIN&&(e>l||e<i))return void n.Ay.error(t("\u5408\u7406\u503c\u4e2d\u9ed8\u8ba4\u503c\u5fc5\u987b\u5728\u6700\u5c0f\u503c\u548c\u6700\u5927\u503c\u4e4b\u95f4"))}if(T("tabBuffer")){const{buffer_tab:e}=a;if(!Number.isInteger(null===e||void 0===e?void 0:e.size)||(null===e||void 0===e?void 0:e.size)<=0)return void n.Ay.error(t("buffer\u5927\u5c0f\u5fc5\u987b\u4e3a\u6b63\u6574\u6570"))}try{a=e.related_result_variable_id?await f(a):e.notService?C(a):await w(a),e.onOk&&e.onOk(a)}catch(c){console.error(c)}},g=t=>("edit"!==e.mode||(t.variable_type,x.ps.\u6570\u5b57\u578b),t),y=()=>null===s.name||""===s.name?(A.warning(t("\u8bf7\u8f93\u5165\u540d\u79f0")),!1):null!==s.code&&""!==s.code||(A.warning(t("\u8bf7\u8f93\u5165\u6807\u8bc6\u7b26")),!1),f=async t=>{const a=(0,b.qD)(t,m.O.RESULT_INPUT);return"edit"===e.mode?await(0,at.xOw)({...a,related_result_variable_id:null===e||void 0===e?void 0:e.related_result_variable_id}):await(0,at.b5n)({...a,related_result_variable_id:null===e||void 0===e?void 0:e.related_result_variable_id}),a},C=t=>{const a=(0,b.qD)(t,m.O.CONTROL_INPUT);return"edit"!==e.mode&&(a.id=crypto.randomUUID()),a},w=async a=>{const l=(0,b.qD)(a,m.O.INPUT);if(l.variable_type===x.ps.\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408){var i,o,r,d;if(null===(i=(0,Pt.B)("inputVariable","inputVariableMap").get(null===l||void 0===l||null===(o=l.double_array_list_tab)||void 0===o?void 0:o.dataSourceCode))||void 0===i||!i.double_array_tab)throw n.Ay.error(t("\u672a\u8f93\u5165\u4fe1\u606f\uff0c\u8bf7\u9009\u62e9\u96c6\u5408\u6765\u6e90")),Error("");l.double_array_list_tab.dataSourceConfig=null===(r=(0,Pt.B)("inputVariable","inputVariableMap").get(null===l||void 0===l||null===(d=l.double_array_list_tab)||void 0===d?void 0:d.dataSourceCode))||void 0===r?void 0:r.double_array_tab}return"edit"===e.mode?await(0,at.Tnc)(l):await(0,at.Lz$)(l),l},E=(0,l.useCallback)((()=>{const e={Number:["tabNumber","tabReasonable","tabProgram","tabButtonType","tabPreview"],Text:["tabText","tabProgram","tabButtonType","tabPreview"],Select:["tabSelect","tabProgram","tabButtonType","tabPreview"],Boolean:["tabBoolean","tabProgram","tabPreview"],DateTime:["tabProgram","tabPreview"],TwoDigitArray:["twoDigitArray","tabProgram","tabPreview"],Label:["tabLabel","tabProgram","tabButtonType","tabPreview"],Button:["tabButton","tabPreview"],Control:["tabControl","tabProgram","tabPreview"],Buffer:["tabBuffer"],Picture:["tabPicture","tabProgram","tabPreview"],Array:["tabCustomArray","tabPreview"],DoubleArray:["tabDoubleArray"],DoubleArrayList:["tabDoubleArrayList"]};return e}),[]),[_,j]=(0,l.useState)(E()),I=(e,t)=>{if(""===e)c(t);else{const a={};a[e]=t;const l=Object.assign(s,a);c(l)}j(E())},T=e=>{const t=_[null===s||void 0===s?void 0:s.variable_type];return t&&t.includes(e)},P=[{key:"/tabGeneral",label:t("\u5e38\u89c4"),children:(0,S.jsx)(R,{data:v(s),toData:I,parentFieldName:"",mode:e.mode,param:e})},T("tabDoubleArrayList")&&{key:"/tabDoubleArrayList",label:t("\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408"),children:(0,S.jsx)($e,{data:null===s||void 0===s?void 0:s.double_array_list_tab,defaultVal:(null===s||void 0===s?void 0:s.default_val)||{value:0,unit:""},toData:I,parentFieldName:"double_array_list_tab",parentFieldKey:"default_val"})},T("tabDoubleArray")&&{key:"/tabDoubleArray",label:t("\u4e8c\u7ef4\u6570\u7ec4"),children:(0,S.jsx)(Ge,{data:null===s||void 0===s?void 0:s.double_array_tab,defaultVal:(null===s||void 0===s?void 0:s.default_val)||{value:0,unit:""},toData:I,parentFieldName:"double_array_tab",parentFieldKey:"default_val"})},T("tabCustomArray")&&{key:"/tabCustomArray",label:t("\u81ea\u5b9a\u4e49\u6570\u7ec4"),children:(0,S.jsx)(Se,{mode:e.mode,data:null===s||void 0===s?void 0:s.custom_array_tab,defaultVal:(null===s||void 0===s?void 0:s.default_val)||{value:0,unit:""},toData:I,parentFieldName:"custom_array_tab",parentFieldKey:"default_val"})},T("tabNumber")&&{key:"/tabNumber",label:t("\u6570\u5b57\u578b"),children:(0,S.jsx)(H,{data:null===s||void 0===s?void 0:s.number_tab,defaultVal:(null===s||void 0===s?void 0:s.default_val)||{value:0,unit:""},toData:I,parentFieldName:"number_tab",parentFieldKey:"default_val"})},T("tabLabel")&&{key:"/tabLabel",label:t("Label"),children:(0,S.jsx)(xt,{data:null===s||void 0===s?void 0:s.label_tab,toData:I,parentFieldName:"label_tab"})},T("tabText")&&{key:"/tabText",label:t("\u6587\u672c"),children:(0,S.jsx)(tt,{data:null===s||void 0===s?void 0:s.text_tab,toData:I,parentFieldName:"text_tab"})},T("tabPicture")&&{key:"/tabPicture",label:t("PIC"),children:(0,S.jsx)(gt,{data:null===s||void 0===s?void 0:s.picture_tab,toData:I,parentFieldName:"picture_tab"})},T("tabSelect")&&{key:"/tabSelect",label:t("\u9009\u62e9"),children:(0,S.jsx)(dt,{data:null===s||void 0===s?void 0:s.select_tab,defaultVal:(null===s||void 0===s?void 0:s.default_val)||{value:"",unit:""},toData:I,parentFieldName:"select_tab",parentFieldKey:"default_val"})},T("tabButtonType")&&{key:"/tabButtonType",label:t("\u6309\u94ae"),children:(0,S.jsx)(pt,{data:null===s||void 0===s?void 0:s.button_variable_tab,type:null===s||void 0===s?void 0:s.variable_type,toData:I,parentFieldName:"button_variable_tab"})},T("twoDigitArray")&&{key:"/twoDigitArray",label:t("\u4e8c\u7ef4\u6570\u7ec4"),children:(0,S.jsx)(At,{data:null===s||void 0===s?void 0:s.two_digit_array_tab,toData:I,parentFieldName:"two_digit_array_tab"})},T("tabReasonable")&&{key:"/tabReasonable",label:t("\u5408\u7406\u503c"),children:(0,S.jsx)(k,{data:null===s||void 0===s?void 0:s.reasonable_val_tab,defaultVal:(null===s||void 0===s?void 0:s.default_val)||{value:"",unit:""},parentFieldKey:"default_val",toData:I,parentFieldName:"reasonable_val_tab"})},T("tabControl")&&{key:"/tabControl",label:t("\u52a0\u51cf\u63a7\u4ef6"),children:(0,S.jsx)(_t,{data:null===s||void 0===s?void 0:s.control_tab,toData:I,parentFieldName:"control_tab",parentFieldKey:"default_val"})},T("tabBoolean")&&{key:"/tabBoolean",label:t("\u5e03\u5c14\u578b"),children:(0,S.jsx)(jt,{data:null===s||void 0===s?void 0:s.boolean_tab,toData:I,parentFieldName:"boolean_tab",parentFieldKey:"default_val"})},T("tabProgram")&&{key:"/tabProgram",label:t("\u7a0b\u5e8f"),children:(0,S.jsx)(V,{data:null===s||void 0===s?void 0:s.program_tab,type:null===s||void 0===s?void 0:s.variable_type,toData:I,parentFieldName:"program_tab"})},T("tabButton")&&{key:"/tabButton",label:t("\u6309\u94ae"),children:(0,S.jsx)(J,{data:null===s||void 0===s?void 0:s.button_tab,type:s.variable_type,toData:I,parentFieldName:"button_tab"})},T("tabBuffer")&&{key:"/tabBuffer",label:t("buffer"),children:(0,S.jsx)(Tt,{data:null===s||void 0===s?void 0:s.buffer_tab,toData:I,parentFieldName:"buffer_tab",parentFieldKey:"default_val"})},T("tabPreview")&&{key:"/tabPreview",label:t("\u9884\u89c8"),children:(0,S.jsx)(wt,{data:s,toData:I,parentFieldName:"default_val"})}].filter(Boolean);return(0,S.jsxs)(lt.A,{destroyOnClose:!0,title:"add"===e.mode?t("\u65b0\u5efa\u53d8\u91cf"):t("\u4fee\u6539\u53d8\u91cf"),width:"70vw",open:e.open,onOk:h,onCancel:e.onCancel,footer:null,children:[p,(0,S.jsxs)(kt,{children:[(0,S.jsx)("div",{className:"left-info",children:(0,S.jsx)(o.A,{onTabClick:e=>{let l=e;"/tabGeneral"===a.activeTabKey&&"/tabGeneral"!==e&&(null!=s.name&&""!==s.name&&null!=s.code&&""!==s.code||(l="/tabGeneral",i.A.error({title:t("\u63d0\u793a"),content:t("\u6bcf\u4e2a\u53c2\u6570\u5fc5\u987b\u5177\u6709\u53c2\u6570\u540d")}))),d({...a,activeTabKey:l})},destroyInactiveTabPane:!0,activeKey:a.activeTabKey,items:P})}),(0,S.jsxs)("div",{className:"right-button",children:[(0,S.jsxs)(r.A,{direction:"vertical",children:[(0,S.jsx)(B.A,{block:!0,onClick:()=>h(),children:t("\u786e\u8ba4")}),(0,S.jsx)(B.A,{block:!0,onClick:()=>e.onCancel(),children:t("\u53d6\u6d88")})]}),(0,S.jsx)(r.A,{direction:"vertical",children:(0,S.jsx)(B.A,{block:!0,type:"else",children:t("\u5e2e\u52a9")})})]})]})]})},Ut=e=>{const[t,a]=(0,l.useState)(!1);(0,l.useEffect)((()=>{d()}),[e.modalIndex]);const i=t=>({name:"",code:"",group_category:"",id:"",variable_type:"Number",default_val:{value:0,isConstant:0,value_type:"string",unitType:null,unit:null},createdBy:"",f1_index:"",pic:"",is_enable:0,is_feature:0,is_overall:0,is_fx:0,number_tab:{format:{numberRequire:"any",formatType:"auto",afterPoint:null,beforePoint:null,significantDigits:1,amendmentInterval:0,pointPosition:0,roundMode:0,threshold1:1,threshold2:1,roundType1:1,roundType2:1,roundType3:1},channel:{channelType:"\u65e0",channel:"\u65e0",isUserConversion:!1,lockChannels:[]},multipleMeasurements:{measurementCounts:1,measurementType:"min"},unit:{unitType:"\u65e0",unit:"\u65e0",isUserConversion:!1,lockChannels:[]}},reasonable_val_tab:{reasonableType:"empty",values:[0,0],defaultVal:10,minParam:10,maxParam:10,isToResultList:!1},button_variable_tab:{isEnable:!1,content:"",position:"left",actionId:"",function:"f(x)",pic:"",buttonType:E.NR.\u52a8\u4f5c,script:""},button_tab:{isEnable:!1,content:"",position:"left",actionId:"",function:"",source:"action_lib",pic:"",type:"action",method:"post"},program_tab:{numericFormat:"",unit:"",isVisible:"",isDisabled:"",mode:"",isCheck:""},boolean_tab:{showType:"checkbox"},text_tab:{content:"",format:"single",return_type:x.Jt.BOOL,canUseText:!1},select_tab:{selection:"list_single",group_id:"",selectLayer:x.Q1.\u8f74,items:[],format:"",comment:"",show_type:"selector"},two_digit_array_tab:{rowCounts:1,columnCount:1,rowHeaderPlace:!0,columnHeaderPlace:!0,isRowType:!1,rowDefinition:[{title:"\u884c1",type:"text",options:[]}],columnDefinition:[{title:"\u52171",type:"text",options:[]}],columnData:[[""]]},custom_array_tab:{useType:null!==e&&void 0!==e&&e.isSetProgrammableParameters?"programmableParameters":"followComp"},control_tab:{type:"not_custom",dialog_type:"variable",control_name:"",code:"",default_name:"",related_variables:[],title:"",variables:[],signals:[],is_daq:!1},buffer_tab:{buffer_type:"ArrayQueue",size:1,size_expand:"expand",signals:[]},label_tab:{format:"",content:"",fontSize:12,fore:""},picture_tab:{src:"",showName:!1,path:"",name:""},related_var_tab:{vars:[]},double_array_tab:{rowNumber:0,columns:[]},double_array_list_tab:{dataSourceCode:"",number:0}}),[o,r]=(0,l.useState)(i()),d=async()=>{a(!1),n.Ay.open({key:"AddModal",type:"loading",content:"Loading..."}),r(i()),a(!0),n.Ay.destroy("AddModal")};return t?(0,S.jsx)(Dt,{data:o,mode:"add",open:e.open,onOk:e.onOk,variableType:null===e||void 0===e?void 0:e.variableType,isSetProgrammableParameters:e.isSetProgrammableParameters,onCancel:e.onCancel,related_result_variable_id:e.related_result_variable_id,notService:e.notService,controlLibraryInputVars:null===e||void 0===e?void 0:e.controlLibraryInputVars}):null},Nt=e=>{const[t,a]=(0,l.useState)(e.data),[i,o]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{e.editId&&(async()=>{if(o(!1),n.Ay.open({key:"EditModal",type:"loading",content:"Loading..."}),e.varEditDetail)a(Pe()(e.varEditDetail));else{const t=await(0,at.L8k)({ids:[e.editId]});null===t[0].default_val&&(t[0].default_val={value:""}),a(t[0])}o(!0),n.Ay.destroy("EditModal")})()}),[e.editId]),i?(0,S.jsx)("div",{children:(0,S.jsx)(Dt,{isSetProgrammableParameters:e.isSetProgrammableParameters,data:t,mode:"edit",open:e.open,onOk:e.onOk,onCancel:e.onCancel,related_result_variable_id:e.related_result_variable_id,notService:e.notService,controlLibraryInputVars:e.controlLibraryInputVars})}):null},Bt=e=>"add"===e.mode?(0,S.jsx)(Ut,{modalIndex:e.modalIndex,open:e.open,onOk:e.onOk,onCancel:e.onCancel,variableType:e.variableType,isSetProgrammableParameters:e.isSetProgrammableParameters,related_result_variable_id:e.related_result_variable_id,notService:e.notService,controlLibraryInputVars:null===e||void 0===e?void 0:e.controlLibraryInputVars}):(0,S.jsx)(Nt,{isSetProgrammableParameters:e.isSetProgrammableParameters,varEditDetail:e.varEditDetail,editId:e.editId,open:e.open,onOk:e.onOk,onCancel:e.onCancel,related_result_variable_id:e.related_result_variable_id,notService:e.notService,controlLibraryInputVars:null===e||void 0===e?void 0:e.controlLibraryInputVars})},78583:(e,t,a)=>{a.d(t,{A:()=>c});var l=a(65043),n=a(80077),i=a(34458),o=a(36950),r=a(65694),d=a(67208),s=a(754);const c=()=>{const e=(0,n.wA)();return{initProjectHistoryData:async()=>{try{if((0,i.HN)()){const t=await(0,d.wBI)({TemplateName:(0,o.n1)()});t&&e({type:r.cs,param:t})}}catch(t){console.log(t)}},syncResult:(0,l.useCallback)((t=>{let{sampleCode:a,resultCode:l,newValue:n,index:i,error:o,errorMessage:d}=t;const c=Object.fromEntries(Object.entries(s.A.getState().project.resultHistoryData).map((e=>{let[t,r]=e;if(t===a){const e=r.findIndex((e=>e.code===l)),a={code:l,error:o,errorMessage:d,index:i,value:n};return-1===e?[t,[...r,a]]:[t,r.map(((t,l)=>l===e?a:t))]}return[t,r]})));null!==c&&void 0!==c&&c[a]||(c[a]=[{code:l,error:o,errorMessage:d,index:i,value:n}]),e({type:r.cs,param:c})}),[])}}},80231:(e,t,a)=>{a.d(t,{A:()=>w});var l=a(65043),n=a(80077),i=a(97950),o=a(67299),r=a(84),d=a(8237),s=a(45303),c=a(74117),A=a(14463),u=a(50540),p=a(45333),v=a(34458),m=a(81143);a(68374);const h=m.Ay.div`
    position: fixed;
    z-index: 100000;
    user-select: none;
    display: none;
    background: #FFF;
    min-width: 120px;
    border: 1px solid rgba(220 ,220, 220,1);
    box-shadow: 5px 5px 3px -2px rgba(0, 0, 0,0.4);
    font-size: 14px;
    >:hover {
        background: rgba(20, 115, 245,0.4)
    }
    .unique-content {
        padding:  3px 5px !important;

    }
    .disabled {
        cursor:no-drop ;
        color: rgba(224,224,224);
        pointer-events: none;
        
    }
    .j-layout {
        display: flex;
        justify-content: space-between;
    }
    .line {
        border-bottom: 2px solid #d8d8d8;
    }

`,g=m.Ay.div`
    position: absolute;
    z-index: 100000;
    user-select: none;
    display: none;
    background: #FFF;
    min-width: 120px;
    border: 1px solid rgba(220 ,220, 220,1);
    box-shadow: 5px 5px 3px -2px rgba(0, 0, 0,0.4);
    font-size: 14px;
    >:hover {
        background: rgba(20, 115, 245,0.4)
    }
    .unique-content {
        padding:  3px 5px;
    }
    .line {
        border-bottom: 2px solid #d8d8d8;
    }
    .feature {
        cursor:no-drop ;
        color: rgba(224,224,224);
        pointer-events: none;
    }
    
`;var b=a(70579);const y=e=>{const{children:t,...a}=e;return(0,b.jsx)(g,{...a,children:t})},x=m.Ay.div`
   padding:  3px 5px;
   position: relative;
   ${e=>{var t;return!(null===(t=e.feature)||void 0===t||t)&&"\n        cursor:no-drop ;\n        color: rgba(224,224,224);\n        pointer-events: none;\n   "}}
     ${e=>{var t;return null!==(t=e.line)&&void 0!==t&&t&&"\n        border-bottom: 2px solid #d8d8d8;\n   "}}
   img {
      width: 12px;
      height: 12px;
      object-fit: contain;
   }
    
`,f=e=>{const{children:t,label:a,...n}=e,i=(0,l.useRef)();return(0,b.jsx)(x,{...n,className:"",ref:i,onMouseEnter:e=>(e=>{const t=e.clientX,a=e.clientY,l=i.current,n=l.lastElementChild;if(n){n.style.display="block",n.style.left=`${l.clientWidth}px`;const e=window.innerWidth,i=window.innerHeight,o=e-(t+l.clientWidth)>=l.clientWidth?l.clientWidth:-l.clientWidth,r=i-a>=n.clientHeight?0:-(n.clientHeight-(i-a));n.style.left=`${o}px`,n.style.top=`${r}px`}})(e),onMouseLeave:e=>(()=>{var e;const t=i.current;t.lastElementChild&&null!==(e=t.lastElementChild)&&void 0!==e&&e.id&&(t.lastElementChild.style.display="none")})(),children:t})};let C=null;const w=e=>{var t,a;let{children:m,domId:g,onBefore:x,onClose:w,isVisible:E=!0,options:S=[],layoutConfig:_,capture:R=!1}=e;const j=(0,n.wA)(),{t:I}=(0,c.Bd)(),{openDialog:T}=(0,r.A)(),{subContextMenuId:P,subConfigMain:k,subMenu:D}=(0,o.A)(),U=(0,n.d4)((e=>e.global.readOnly)),N=((0,n.d4)((e=>e.global.globalMonitoringProjectID)),(0,n.d4)((e=>e.global.userIsAdmin))),B=((0,n.d4)((e=>e.project.projectId)),(0,n.d4)((e=>e.template.defaultPageId))),O=(0,n.d4)((e=>e.global.stationList)),M=(0,n.d4)((e=>e.global.optStation)),L=null===g||void 0===g||null===(t=g.split("edit-"))||void 0===t?void 0:t.at(-1),V=(0,l.useRef)(),Q=(0,l.useRef)();(0,l.useEffect)((()=>{if(U)return()=>{};if(Q.current=document.getElementById(L),!Q.current)return()=>{Q.current=null};const e=e=>{if(x&&(0,i.flushSync)((()=>{x(e)})),document.getElementsByName("menu").forEach((e=>{e.style.display="none"})),C&&(C.style.border="none"),C=Q.current,e.preventDefault(),e.stopPropagation(),V.current){var t,a,l,n;let i=0,o=0;const r=((e,t,a)=>{let l=e;for(;l;){var n,i;if(l.style&&(a?(null===(n=l.style)||void 0===n?void 0:n[t])===a:null===(i=l.style)||void 0===i?void 0:i[t]))break;l=l.parentNode}return l})(e.target,"transform");if(r&&null!==r&&void 0!==r&&null!==(t=r.style)&&void 0!==t&&null!==(a=t.transform)&&void 0!==a&&a.includes("translate")&&(null===r||void 0===r||!r.className.includes("ant-table-tbody-virtual-holder-inner"))&&(null===r||void 0===r||!r.className.includes("drag-container-box"))){const e=r.getBoundingClientRect();i=e.left,o=e.top}let d=e.clientX,s=e.clientY;const c=window.innerWidth,A=window.innerHeight,u=null===(l=V.current)||void 0===l?void 0:l.offsetWidth,p=null===(n=V.current)||void 0===n?void 0:n.offsetHeight;d=c-u>=d?d:c-u,s=A-p>=s?s:A-p,V.current.style.display="block",V.current.style.top=s-o+"px",V.current.style.left=d-i+"px",s+V.current.offsetHeight>A&&(V.current.style.top=s-V.current.offsetHeight+"px"),d+V.current.offsetWidth>c&&(V.current.style.left=d-V.current.offsetWidth+"px"),"process"!==C.dataset.name&&(C.style.border="5px solid #197aff");const v=()=>{try{C&&(C.style.border="none",C=null),V.current&&(V.current.style.display="none"),D(null),w&&w(e)}catch(t){console.log("err",t)}window.removeEventListener("click",v)};window.addEventListener("click",v)}};return Q.current.addEventListener("contextmenu",e,R),()=>{Q.current.removeEventListener("contextmenu",e)}}),[L,U]);const F=[{onClick:()=>{T({type:s.TS}),j({type:A.NS,param:_}),P(L)},label:(0,l.useMemo)((()=>I("\u9009\u62e9\u754c\u9762\u5185\u5bb9")),[]),visible:!!N,feature:!0},{onClick:()=>{j({type:A.NS,param:_}),T({type:s.a2})},label:(0,l.useMemo)((()=>I("\u7f16\u8f91\u5f53\u524d\u5e03\u5c40")),[]),visible:!!N,feature:!0,line:!0},{onClick:()=>{j({type:u.MY,param:{UIParams:{shortcutCode:p.JN.\u4fdd\u5b58}}})},label:(0,l.useMemo)((()=>I("\u4fdd\u5b58")),[]),visible:!0,feature:!0},{onClick:()=>{j({type:u.MY,param:{UIParams:{shortcutCode:p.JN.\u53e6\u5b58\u4e3a}}})},label:(0,l.useMemo)((()=>I("\u53e6\u5b58\u4e3a")),[]),visible:!0,feature:!0},{onClick:()=>{j({type:u.MY,param:{UIParams:{shortcutCode:p.JN.\u9000\u51fa}}})},label:(0,l.useMemo)((()=>I("\u56de\u9996\u9875")),[]),visible:(()=>{let e=M||{};return 1===O.length&&(e=null===O||void 0===O?void 0:O[0]),B!==d.CH.\u7ed3\u679c\u9875\u9762&&(!e.projectId||e.projectId===Number((0,v.HN)()))})(),feature:!0},{onClick:()=>{j({type:u.MY,param:{UIParams:{shortcutCode:p.JN.\u5173\u95ed}}})},label:(0,l.useMemo)((()=>I("\u5173\u95ed")),[]),visible:!!N,feature:!0},{label:(0,l.useMemo)((()=>I("\u6a21\u677f\u914d\u7f6e")),[]),visible:!!N,feature:!0,children:[{onClick:()=>T({type:s.P6}),label:(0,l.useMemo)((()=>I("\u8f93\u5165\u53d8\u91cf\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.Um}),label:(0,l.useMemo)((()=>I("\u4fe1\u53f7\u53d8\u91cf\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.vY}),label:(0,l.useMemo)((()=>I("\u7ed3\u679c\u53d8\u91cf\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0,line:!0},{onClick:()=>T({type:s.IL}),label:(0,l.useMemo)((()=>I("\u52a8\u4f5c\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.kd}),label:(0,l.useMemo)((()=>I("\u5355\u4f4d\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.GS}),label:(0,l.useMemo)((()=>I("\u8bd5\u6837\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.hB}),label:(0,l.useMemo)((()=>I("\u8f85\u52a9\u7ebf\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.bp}),label:(0,l.useMemo)((()=>I("\u89c6\u9891\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.i2}),label:(0,l.useMemo)((()=>I("\u6620\u50cf\u7ba1\u7406\u5668")),[]),visible:B===d.CH.\u9ed8\u8ba4\u9875\u9762&&!(O.length>1&&(null===M||void 0===M||!M.id)),feature:!0},{onClick:()=>T({type:s.RQ}),label:(0,l.useMemo)((()=>I("\u5bfc\u51fa\u914d\u7f6e\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0},{onClick:()=>{T({type:s._}),P(null),k(!0)},label:(0,l.useMemo)((()=>I("\u5e03\u5c40\u7ba1\u7406")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.Q6}),label:(0,l.useMemo)((()=>I("\u5bf9\u8bdd\u6846\u7ba1\u7406")),[]),visible:!0,feature:!0},{onClick:()=>T({type:s.YX}),label:(0,l.useMemo)((()=>I("\u76d1\u63a7\u5173\u8054\u7ba1\u7406\u5668")),[]),visible:!0,feature:!0}].filter((e=>null===e||void 0===e?void 0:e.visible))}].filter((e=>null===e||void 0===e?void 0:e.visible)),J=e=>`unique-content ${null!==e&&void 0!==e&&e.line?"line":""} ${null!==e&&void 0!==e&&e.feature?"":"feature"} `,Y=e=>`subMenu_${e.label}`;return(0,b.jsxs)(h,{ref:V,name:"menu",children:[m,E&&(0,b.jsx)(b.Fragment,{children:null===(a=[...S,...F])||void 0===a?void 0:a.map((e=>{var t,a;return(0,l.createElement)(f,{...e,key:e.label},(0,b.jsxs)("div",{className:"j-layout",children:[(0,b.jsx)("div",{children:I(e.label)}),(null===e||void 0===e?void 0:e.children)&&(null===e||void 0===e||null===(t=e.children)||void 0===t?void 0:t.length)>0&&(0,b.jsx)("div",{children:"\uff1e"})]}),(null===e||void 0===e?void 0:e.children)&&(null===e||void 0===e||null===(a=e.children)||void 0===a?void 0:a.length)>0&&(0,b.jsx)(y,{id:Y(e),children:e.children.map((e=>(0,b.jsx)("div",{onClick:null===e||void 0===e?void 0:e.onClick,className:J(e),children:I(null===e||void 0===e?void 0:e.label)},Y(e))))},Y(e)))}))})]})}},81077:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=a(15637),i=a(67208);const o=()=>{const e=(0,l.wA)(),t=async t=>{try{const a=await(0,i.SnP)(t);a&&e({type:n.N1,param:a})}catch(a){console.log(a)}};return{initModuleData:t,saveModuleDataData:async e=>{try{return!!await(0,i.TVx)(e)&&(t(),!0)}catch(a){console.log(a)}return!1}}}},84665:(e,t,a)=>{a.d(t,{A:()=>s});a(65043);var l=a(18650),n=a(74117),i=a(81143),o=a(68374);const r=i.Ay.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    .img-name {
        font-size: ${(0,o.D0)("16px")};
        font-weight: 400;
        color: #A2A6B2;
        line-height: 22px;
    }
`;var d=a(70579);const s=e=>{const{t:t}=(0,n.Bd)();return(0,d.jsxs)(r,{children:[(0,d.jsx)("img",{src:l.Lt,alt:""}),(0,d.jsx)("div",{className:"img-name",children:t("\u6682\u65e0\u56fe\u7247")})]})}},84856:(e,t,a)=>{a.d(t,{A:()=>s});var l=a(80077),n=a(41086),i=a(14463),o=a(15637),r=a(67208),d=a(12847);const s=()=>{const e=(0,l.wA)(),{initPageData:t,saveDefaultId:a}=(0,d.A)(),s=(0,l.d4)((e=>e.template.defaultPageId)),c=async(e,l)=>{try{const i=await t();if(i){let t=null===i||void 0===i?void 0:i.find((t=>(null===t||void 0===t?void 0:t.id)===e||t.name===l));var n;return t||(t=i.find((e=>(null===e||void 0===e?void 0:e.id)===s))),t?(A(null===(n=t)||void 0===n?void 0:n.layout),a(t.id),u(t.id),t.id):s}return s}catch(i){return console.log(i),s}},A=t=>{e({type:i.hf,param:t?(0,n.K6)(t):null})},u=async t=>{try{e({type:o.qK,param:t})}catch(a){console.log(a)}};return{initTemplateLayout:c,subTemplateLayout:async(e,t)=>{try{await(0,r.OIg)((0,n.gT)(e,null))&&c(t)}catch(a){console.log(a)}},saveLayoutData:A,subCurrentPageId:u,saveLayout:async e=>{try{await(0,r.OIg)((0,n.gT)(e,null))&&t()}catch(a){console.log(a)}}}}},87011:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(59266),n=a(36282),i=a(30092),o=a(70579);const r=e=>{let{data:t,usable:a}=e;return(0,o.jsx)(o.Fragment,{children:a?(0,o.jsx)(n.A,{placement:"bottom",content:(0,o.jsx)(i.default,{dialogId:t,showImg:!1}),trigger:"click",children:(0,o.jsx)(l.A,{})}):(0,o.jsx)(l.A,{style:{cursor:"no-drop"}})})}},88359:(e,t,a)=>{a.d(t,{w:()=>o});var l=a(67208),n=a(41459),i=a(56543);const o=e=>async(t,a)=>{try{const a=await(0,l.Eqn)(void 0,e);if(a){const e=new Map,l=[],o=[],r=[],d=[],s=[],c=[],A=[],u=[],p=[],v=[];a.forEach((t=>{const{code:a,type:n,variable_type:m}=t;e.set(a,t),p.push(a),n===i.Ih.GENERAL&&(l.push(a),m===i.ps.Buffer&&o.push(a),m===i.ps.\u4e8c\u7ef4\u6570\u7ec4&&r.push(a),m===i.ps.\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408&&d.push(a),m===i.ps.\u6570\u5b57\u578b&&s.push(a),m===i.ps.\u9009\u62e9&&c.push(a),m===i.ps.\u5e03\u5c14\u578b&&A.push(a),m===i.ps.\u6587\u672c&&u.push(a)),n===i.Ih.CONTROL_LIBRARY&&v.push(a)})),t({type:n.ir,param:{inputVariableMap:e,allCodeList:p,inputVariableCodeList:l,controlInputCodeList:v,bufferInputCodeList:o,doubleArrayCodeList:r,doubleArrayListCodeList:d,numberCodeList:s,selectCodeList:c,booleanCodeList:A,textCodeList:u}})}return a}catch(o){return!1}}},88483:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(36497),n=a(80077),i=a(70579);const o=e=>{const t=(0,n.d4)((e=>e.global.unitList));return(0,i.jsx)(l.A,{fieldNames:{label:"name",value:"id"},options:t,...e})}},89800:(e,t,a)=>{a.d(t,{A:()=>o});a(65043);var l=a(80077),n=a(15637),i=a(67208);const o=()=>{const e=(0,l.wA)();return{initSignalGroupData:async()=>{try{const t=await(0,i.gA1)();if(t){const a=t.sort(((e,t)=>new Date(t.created_time)-new Date(e.created_time)));e({type:n.SN,param:a})}}catch(t){console.log(t)}},initSignalData:async()=>{try{const t=await(0,i.JR7)();if(t){const a=t.sort(((e,t)=>new Date(t.created_time)-new Date(e.created_time)));return e({type:n.c8,param:a}),a}}catch(t){console.log(t)}return null}}}},92676:(e,t,a)=>{a.d(t,{L1:()=>i});var l=a(56434),n=a.n(l);a(70887);const i=e=>{const t=n()(e);return delete t.created_time,delete t.created_user_id,delete t.updated_time,delete t.updated_user_id,delete t.delete_flag,e.block_tag_array||(t.block_tag_array=[]),e.test_res_datas||(t.test_res_datas=[]),e.line_tag_array||(t.line_tag_array=[]),t.line_tag_flag=!!e.line_tag_flag,t.legend_flag=!!e.legend_flag,t.block_tag_flag=!!e.block_tag_flag,t.apply_point_count=!!e.apply_point_count,t.coordinate_source_flag=!!e.coordinate_source_flag,t.curve_nama_setting_enabl=!!e.curve_nama_setting_enabl,t.x_log=!!e.x_log,t.x_grid_line=!!e.x_grid_line,t.x_zero_line=!!e.x_zero_line,t.x_shadow=!!e.x_shadow,t.y_log=!!e.y_log,t.y_shadow=!!e.y_shadow,t.y_grid_line=!!e.y_grid_line,t.y_zero_line=!!e.y_zero_line,t.y2_log=!!e.y2_log,t.y2_grid_line=!!e.y2_grid_line,t.y2_zero_line=!!e.y2_zero_line,t.y2_shadow=!!e.y2_shadow,t}},92941:(e,t,a)=>{a.d(t,{A:()=>r});a(65043);var l=a(9339),n=a(81143);a(68374);const i=n.Ay.div`
    .ant-table-thead {
        display: ${e=>e.colHead?"contents":"none"}
    }
    .table-first {
        background: rgba(215, 226, 255, 1);
    }
    .ant-table-tbody {
            >tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td:first-child {
                background: ${e=>e.rowHead?"rgba(215, 226, 255, 1) !important":"transparent"}; 
            }
        }
`;var o=a(70579);const r=e=>{let{dataSource:t=[],columns:a=[],colHead:n=!0,rowHead:r=!1,rowHeadRender:d=null,rowData:s=[],...c}=e;return(0,o.jsx)(i,{colHead:n,rowHead:r,children:(0,o.jsx)(l.A,{columns:(()=>{const e={title:" ",width:"10vw",dataIndex:"key",fixed:"left",className:"table-first"};return r&&d&&(e.render=(e,t,a)=>d(e,t,a,s)),r?[e,...a]:a})(),bordered:!0,size:"small",dataSource:t,pagination:!1,scroll:{x:!0,y:"30vh"},...c})})}},94817:(e,t,a)=>{a.d(t,{A:()=>o});var l=a(24768),n=(a(65043),a(81143));a(68374);n.Ay.div`
    .split {
        display: flex;
        flex-direction: row;
        height: 100%;
        width: 100%;
    }

    .gutter {
        /* background-color: #eee; */
        /* background-repeat: no-repeat; */
        /* background-position: 50%; */
    }

    .gutter.gutter-horizontal {
        /* background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg=='); */
        cursor: col-resize;
    }

`;var i=a(70579);const o=e=>{const{children:t}=e;return(0,i.jsx)(l.A,{className:"split-horizontal",gutterSize:4,minSize:200,sizes:[20,80],...e,children:t})}},95620:(e,t,a)=>{a.d(t,{T8:()=>o,jZ:()=>r,ku:()=>i});var l=a(81143),n=a(18650);const i=l.Ay.div`
 width: 100vw;
 height: 100vh;
 background-size: 100% 100% !important;
 background: ${e=>e.isTemplate?`url(${n.k8})`:"white"};
 &::after {
    content: "";
    height: 0;
    width: 0;
    background-image: url(${n.k8});
}
`,o=l.Ay.div`
    background-image: url(${n.pl}) ;
    &::after {
        content: "";
        height: 0;
        width: 0;
        background-image: url(${n.pl}) ;
}
`,r=l.Ay.div`
    display:none;
`},96181:(e,t,a)=>{a.d(t,{A:()=>o});var l=a(80077),n=a(67208),i=a(14463);const o=()=>{const e=(0,l.wA)(),t=async t=>{e({type:i.e3,param:t})},a=t=>{e({type:i.p4,param:t})};return{subTab:t,clearTab:()=>{t(null),a(null)},subTabOpt:a,saveSingleTabLayout:async t=>{const[a]=await(0,n.PXE)({binder_ids:[t.binder_id]});await(0,n.Kv3)({binders:[{...a,layout:t}]}),console.log("changedBinderId","\u4f60\u8be5\u66f4\u65b0\u4e86",null===a||void 0===a?void 0:a.binder_id),e({type:i.EH,param:a.binder_id})}}}},97292:(e,t,a)=>{a.d(t,{O:()=>l});const l={FUNC:"func_",INPUT:"input_",RESULT_INPUT:"result_input_",CONTROL_INPUT:"control_input_",DIMENSION:"dimension_",UNIT:"unit_",SAMPLE_TYPE:"sampleType_",SAMPLE_PARAM:"sampleParam_",SAMPLE:"sample_",SAMPLE_ELSE:"sampleElse_",RESULT:"result_",SIGNAL:"signal_",ACTION:"action_"}},97475:(e,t,a)=>{a.d(t,{A:()=>u,Q:()=>d});a(65043);var l=a(25055),n=a(36497),i=a(74117),o=a(63189),r=a(70579);const d={"\u9009\u62e9\u5668":"select","\u6570\u5b57\u8f93\u5165\u6846":"input_number"},{Item:s}=l.A,c=e=>{let{render_type:t,options:a,...l}=e;const{t:s}=(0,i.Bd)();switch(t){case d.\u6570\u5b57\u8f93\u5165\u6846:return(0,r.jsx)(o.A,{style:{width:"100%"},...l});case d.\u9009\u62e9\u5668:return(0,r.jsx)(n.A,{style:{minWidth:"150px"},...l,options:null===a||void 0===a?void 0:a.map((e=>({...e,label:s(e.label)})))});default:return(0,r.jsx)(r.Fragment,{})}},A=e=>{let{upperName:t,params:a,getFieldValue:l}=e;return null===a||void 0===a?void 0:a.map((e=>{var a;let{name:n,code:o,renderType:d,options:u}=e;const p=[...t,o],v=l(p),m=null===u||void 0===u||null===(a=u.find((e=>e.code===v)))||void 0===a?void 0:a.attachParams,{t:h}=(0,i.Bd)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s,{label:h(n),name:p,rules:[{required:!0}],children:(0,r.jsx)(c,{render_type:d,options:u,fieldNames:{label:"name",value:"code"}})},o),m&&(0,r.jsx)(A,{params:m,upperName:t,getFieldValue:l})]})}))},u=e=>{let{saveRules:t,getFieldValue:a,upperName:l="saveRules"}=e;return(0,r.jsx)(r.Fragment,{children:t.map((e=>{let{name:t,code:n,params:i}=e;return(0,r.jsx)(A,{upperName:[l,n],params:i,getFieldValue:a})}))})}},97588:(e,t,a)=>{a.d(t,{A:()=>h});var l=a(65043),n=a(74117),i=a(89330),o=a(61966),r=a(4554),d=a(36950),s=a(67208),c=a(36069),A=a(75440),u=a(18650),p=a(81143);a(68374);const v=p.Ay.div`
    display: flex;
    margin-top: 0.3vw;
    .img-layout {
        display: flex;
        .img-list {
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,0.15);
            display: flex;
            background: rgba(255,255,255,0.8);
            box-shadow: 2px 0px 7px 0px rgba(3,36,71,0.08);
            padding: 0.4vw;
            margin-left: 0.5vw;
            position: relative;
           >img {
                width: 4vw; 
                height: 4vw;
           }
            .del-icon{
                position: absolute;
                top: 2px;
                right: 2px;
                line-height: 1;
                color: red;
                border-radius: 5px;
                display: none;
                cursor: pointer;
            }
            
            &:hover{
                .del-icon{
                    display: block;
                }
            }
            
        }
    }

`;var m=a(70579);const h=e=>{let{value:t,src:a=u.Np,btnCLick:p,btnTitle:h="\u9009\u62e9\u56fe\u7247",open:g,onCancel:b,modalTitle:y="\u9009\u62e9\u56fe\u7247",disabled:x=!1,onChange:f,btn:C={type:"default"},showDel:w=!1}=e;const[E,S]=(0,l.useState)(t),{t:_}=(0,n.Bd)();(0,l.useEffect)((()=>{S(t||a)}),[t,a]);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(v,{children:(0,m.jsxs)("div",{className:"img-layout",children:[(0,m.jsx)(r.A,{...C,disabled:x,icon:(0,m.jsx)(i.A,{}),size:"small",onClick:p,children:_(h)}),(0,m.jsxs)("div",{className:"img-list",children:[(0,m.jsx)("img",{src:E||u.Np,alt:""}),E&&w?(0,m.jsx)("div",{className:"del-icon",onClick:()=>{S(""),null===f||void 0===f||f("")},children:(0,m.jsx)(o.A,{})}):null]})]})}),(0,m.jsx)(A.A,{title:_(y),width:"80vw",open:g,onCancel:b,footer:null,children:(0,m.jsx)(c.A,{updateImage:S,handleClick:async e=>{const t=await(0,s.QpR)(e),a=await(0,d.ZJ)(new File([t],"test.png"));S(a),f&&f(a),b()}})})]})}}}]);
//# sourceMappingURL=8787.b5237e70.chunk.js.map