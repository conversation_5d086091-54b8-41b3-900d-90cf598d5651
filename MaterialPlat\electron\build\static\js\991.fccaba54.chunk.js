"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[991],{30991:(e,t,o)=>{o.r(t),o.d(t,{default:()=>Ie});var n=o(65043),i=o(80077),a=o(98964),r=o(99905),l=o(682),d=o(15701),s=(o(53025),o(5520)),c=o(35964),p=o(50849),u=o(81903),g=o(24459),f=o(96416),m=o(74117),v=o(18650),h=o(67208),x=o(68130),j=o(70916),y=o(36581),I=o(98171),b=o(8237),w=o(34458),S=o(81143);const A=S.Ay.div`
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;


    .top_global_btn{
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom: 4px solid #D1D1D1;
        font-size: 20px;
        font-weight: 700;
        
        cursor: pointer;
        &.active{
            background: #e6f4ff;
        }
    }

    .bottom_station_container{
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .group_operate{
            background: #F2F2F2;
            height: 25px;
            display: flex;
            justify-content: space-between;
            padding: 0 5px;
            .group_operate_left{
                height: 100%;
                font-size: 14px;
                display: flex;
                justify-content: center;
                align-items: center;
                .group_create_container{
                    cursor: pointer;
                    margin-right: 12px;
                    img{
                        width: 20px;
                    }
                }
                .font-size-select{
                    cursor: pointer;
                }
            }
            

            .group_view{
                display: flex;
                align-items: center;
                gap: 5px;

                .selected{
                    color: #1598DC;
                }
            }
        }

        .station_tree_container{
            flex: 1;
            overflow: auto;
            padding-top: 4px;
            .draggable-tree-list{
                .ant-tree-switcher-noop {
                    width: 4px;
                }
            }
            .draggable-tree{
                .ant-tree-node-content-wrapper{
                    overflow: hidden;
                }
            }
        }
    }
`,_=S.Ay.div`
    display: flex;
    justify-content: space-between;

    .node_title{
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .node_func{
        display: flex;
        justify-content: center;
        align-items: center;
        white-space: nowrap;
        img{
            width: 18px;
            height: 18px;
        }
    }
`,C=S.Ay.div`
    
`;var k=o(25055),N=o(16569),H=o(60647),D=o(83720),P=o(70579);const{Item:R}=k.A,E=e=>{let{groupData:t,handleSuccess:o}=e;const i=(0,n.useRef)(),[a,r]=(0,n.useState)(!1);(0,n.useEffect)((()=>{var e;null===i||void 0===i||null===(e=i.current)||void 0===e||e.setFieldsValue({groupName:t.groupName,color:t.color||"#ffffff"})}),[t,a]);return(0,P.jsxs)("div",{onClick:e=>{e.stopPropagation()},children:[(0,P.jsx)(H.A,{open:a,icon:null,onConfirm:async()=>{var e;const n=null===i||void 0===i||null===(e=i.current)||void 0===e?void 0:e.getFieldsValue();await(0,h.ei1)({...t,...n})&&(N.Ay.success("\u4fee\u6539\u6210\u529f"),r(!1),o())},onCancel:()=>{r(!1)},description:(0,P.jsx)(C,{children:(0,P.jsx)(k.A,{ref:i,name:"groupEdit",labelCol:{span:6},wrapperCol:{span:16},children:(0,P.jsx)(R,{label:"\u7ec4\u540d\u79f0",name:"groupName",rules:[{required:!0,message:"\u8bf7\u586b\u5199\u7ec4\u540d"}],children:(0,P.jsx)(D.A,{})})})}),children:(0,P.jsx)("img",{src:v.fu,alt:"",onClick:()=>r(!0)})}),(0,P.jsx)("img",{src:v.En,alt:"",onClick:async()=>{await(0,h.xEu)({id:t.id})&&(r(!1),o())}})]})},M=e=>{const{initStationInfo:t}=(0,x.A)(),{projectList:o}=(0,i.d4)((e=>e.system)),a=(0,n.useMemo)((()=>{const t=o.find((t=>t.project_id===e.projectId));return t?` - (${t.project_name})`:""}),[e,o]),{t:r}=(0,m.Bd)();return(0,P.jsxs)(_,{children:[(0,P.jsx)("div",{className:"node_title",children:e.stationCode?`${r(e.stationName)} ${a}`:r(e.groupName)}),(0,P.jsx)("div",{className:"node_func",children:!e.stationCode&&"1"!==e.id&&(0,P.jsx)(E,{groupData:e,handleSuccess:t})})]})};var O=o(91688),z=o(63612),G=o(74390);const F=e=>{let{id:t,station:o,onSelectTree:n}=e;const a=(0,O.W6)(),r=(0,i.wA)(),{onOpenProject:l,closeProject:s}=(0,j.A)(),{projectId:c}=(0,i.d4)((e=>e.project)),{quitProject:p}=((0,i.d4)((e=>e.global.stationList)),(0,j.A)()),{selectOptStation:u}=(0,d.A)(),{openProject:g}=(0,y.A)(),{standbyConfig:m}=(0,I.A)();return(0,P.jsxs)(f.W1,{id:t,animation:!1,children:[(0,P.jsx)(f.q7,{hidden:!o,disabled:!(null!==o&&void 0!==o&&o.projectId)||(null===o||void 0===o?void 0:o.projectId)&&c&&String(o.projectId)===String(c),onClick:()=>(async()=>{n([],{node:o})})(),children:"\u6253\u5f00\u9879\u76ee"}),(0,P.jsx)(f.q7,{hidden:!o,disabled:!(null!==o&&void 0!==o&&o.projectId&&(0,w.Wb)().some((e=>e===o.projectId))),onClick:()=>(async e=>{await s(e,!0,o)})(null===o||void 0===o?void 0:o.projectId),children:"\u5173\u95ed\u9879\u76ee"}),(0,P.jsx)(f.q7,{onClick:()=>{r({type:z.X8,param:{station:o,isGlobalMonitoring:!o}})},disabled:null===o||void 0===o?void 0:o.projectId,children:"\u5207\u6362\u9879\u76ee"}),(0,P.jsx)(f.q7,{hidden:!!o,onClick:async()=>{r({type:z.X8,param:null}),await p({goHomepage:!1}),a.push({pathname:G.ROUTERS.\u9996\u9875.path})},children:"\u56de\u7cfb\u7edf\u9996\u9875"})]})},K="global-monitoring-menu",T=()=>{(0,i.wA)();const{quitProject:e}=(0,j.A)(),{openGlobalProject:t,fontSizeData:o,setFontSizeHandle:a,setGroupTypeHandle:r,selectOptStation:l}=(0,d.A)(),S=(0,i.d4)((e=>e.global.stationGroupList)),_=(0,i.d4)((e=>e.global.optStation)),C=(0,i.d4)((e=>e.global.globalMonitoringGroupFontSize)),k=(0,i.d4)((e=>e.global.globalMonitoringGroupType)),{initStationInfo:N,updateOptStationGroup:H}=(0,x.A)(),D=(0,i.d4)((e=>e.global.globalMonitoringProjectID)),{openProject:R}=(0,y.A)(),{standbyConfig:E}=(0,I.A)(),{projectList:O}=(0,i.d4)((e=>e.system)),{t:z}=(0,m.Bd)(),[G,T]=(0,n.useState)(),{show:q}=(0,f.EF)({id:K});(0,n.useEffect)((()=>{N()}),[]);const B=async(t,o)=>{let{node:n}=o;null!==n&&void 0!==n&&n.stationCode&&(l(n),n.projectId?(!(0,w.HN)()||(0,w.HN)()&&Number((0,w.HN)())!==n.projectId)&&((0,w.HN)()&&await e({goHomepage:!1}),await R(n.projectId,{pageId:E.mode===I.M.\u4e3b\u673a?b.CH.\u9ed8\u8ba4\u9875\u9762:b.CH.\u5907\u673a\u9875\u9762,optStation:n})):(0,w.HN)()&&await e({goHomepage:!1}))},L=e=>e.map((e=>{var t;const o={...e,title:e.stationName||e.groupName||""};return Array.isArray(e.children)&&(null===(t=e.children)||void 0===t?void 0:t.length)>0&&(o.children=L(e.children)),o})),W=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=[];return e.forEach((e=>{0!==t&&o.push(e),e.children&&e.children.length>0&&(o=o.concat(W(e.children,t+1)))})),o},$=(0,n.useMemo)((()=>{const e=L(S);return"list"===k?W(e):e}),[S,k]),X=e=>{r(e)},J=(0,n.useMemo)((()=>{const e=O.find((e=>e.project_id===D));return e?` - (${e.project_name})`:""}),[D,O]);return(0,P.jsxs)(A,{children:[(0,P.jsxs)("div",{className:"top_global_btn "+(_&&_.id?"":"active"),onClick:async()=>{l(null),D?(!(0,w.HN)()||(0,w.HN)()&&Number((0,w.HN)())!==D)&&(await e({goHomepage:!1}),await R(D,{pageId:E.mode===I.M.\u4e3b\u673a?b.CH.\u9ed8\u8ba4\u9875\u9762:b.CH.\u5907\u673a\u9875\u9762,optStation:null})):(0,w.HN)()&&await e({goHomepage:!1})},onContextMenu:e=>{T(),q({event:e})},children:[z("\u5168\u5c40\u76d1\u63a7")," ",J]}),(0,P.jsxs)("div",{className:"bottom_station_container",children:[(0,P.jsxs)("div",{className:"group_operate",children:[(0,P.jsxs)("div",{className:"group_operate_left",children:["tree"===k?(0,P.jsx)("div",{className:"group_create_container",onClick:async()=>{await(0,h.ei1)({groupName:"\u65b0\u5efa\u5206\u7ec4"})&&N()},children:(0,P.jsx)("img",{src:v.kE,alt:""})}):null,(0,P.jsx)(s.A,{menu:{items:o,selectedKeys:[C],onClick:a},children:(0,P.jsx)("div",{className:"font-size-select",children:(0,P.jsx)(p.A,{})})})]}),(0,P.jsxs)("div",{className:"group_view",children:[z("\u89c6\u56fe"),":",(0,P.jsx)(u.A,{className:"tree"===k?"selected":"",onClick:()=>X("tree")}),(0,P.jsx)(g.A,{className:"list"===k?"selected":"",onClick:()=>X("list")})]})]}),(0,P.jsx)("div",{className:"station_tree_container",children:$&&(0,P.jsx)(c.A,{style:{fontSize:`${C}px`},className:""+("list"===k?"draggable-tree draggable-tree-list":"draggable-tree"),selectedKeys:[null===_||void 0===_?void 0:_.id],defaultExpandAll:!0,autoExpandParent:!0,draggable:{icon:!1},blockNode:!0,onDrop:async e=>{let{event:t,node:o,dragNode:n,dragNodesKeys:i}=e;if(n.stationCode){let e;if(o.stationCode){const t=S.find((e=>e.id===o.groupId));if(o.groupId===n.groupId){const i=t.children.map((e=>e.id)).filter((e=>e!==n.id));i.splice(i.findIndex((e=>e===o.id)),0,n.id),e={groupId:t.id,stationIds:i,groupColor:t.color}}else{const i=t.children.map((e=>e.id));i.splice(i.findIndex((e=>e===o.id)),0,n.id),e={groupId:t.id,stationIds:i,groupColor:t.color}}}else e={groupId:o.id,stationIds:[...o.children.map((e=>e.id)),n.id],groupColor:o.color};await(0,h.m6_)(e),N()}},fieldNames:{key:"id"},titleRender:e=>(0,P.jsx)(M,{...e}),treeData:$,onSelect:B,onRightClick:async e=>{let{event:t,node:o}=e;o.stationCode&&(T(o),q({event:t}))}})})]}),(0,P.jsx)(F,{id:K,station:G,onSelectTree:B})]})};var q=o(41291),B=o(69954);const L="globalMonitoring-2",W="globalMonitoring-3",$=()=>{const e=(0,i.d4)((e=>e.global.optStation)),{projectId:t}=(0,i.d4)((e=>e.project)),o=e?W:L;return(0,P.jsx)(a.A,{children:t?(0,P.jsx)(B.default,{}):(0,P.jsx)(q.A,{homeType:o})})},X=S.Ay.div`
    width: 100%;
    height: 100%;
    .global_layout{
        height: 100%;
        gap: 8px;

        background-color: #ececec;
    }
`,J=S.Ay.div`
    width: 100%;
    height: 100%;
    background: #DDE8F9;
    clip-path: polygon(100% 10%,100% 90%,0 100%,0 0);
    display: flex;
    justify-content: center;
    align-items: center;
    .icon{
        color: #2879DA;
        font-size: 15px;
    }
`;var V=o(75440),U=o(33013);const Q=S.Ay.div`
    height: 50vh;
    display: flex;
    overflow: hidden;

    .left{
        flex: 1;
        height: 100%;
    }
    .right{
        flex: 3;
        height: 100%;
    }
`;var Y=o(8918),Z=o(32371),ee=o(17990);const te=e=>{let{t:t,moduleDataSource:o}=e;return[{title:t("\u6a21\u677f\u540d\u79f0"),dataIndex:"template_name",key:"template_name"},{title:t("\u6a21\u677f\u7c7b\u578b"),dataIndex:"template_type",key:"template_type",render:e=>{var t;return(0,P.jsx)(ee.A,{text:null===o||void 0===o||null===(t=o.find((t=>(null===t||void 0===t?void 0:t.id)===Number(e))))||void 0===t?void 0:t.name})}},{title:t("\u6a21\u677f\u6807\u51c6"),dataIndex:"standard_number",key:"standard_number"},{title:t("\u521b\u5efa\u8005"),dataIndex:"name",key:"name",render:e=>(0,P.jsx)(ee.A,{text:e})},{title:t("\u5b58\u50a8\u4f4d\u7f6e"),dataIndex:"template_directory",key:"template_directory",render:e=>(0,P.jsx)(ee.A,{text:e})},{title:t("\u521b\u5efa\u65f6\u95f4"),dataIndex:"created_time",key:"created_time",render:e=>(0,P.jsx)(ee.A,{text:e})}]},oe=(e,t)=>{const{t:o}=(0,m.Bd)(),a=(0,i.d4)((e=>e.template.moduleDataSource)),[r,l]=(0,n.useState)(""),[d,s]=(0,n.useState)([]);(0,n.useEffect)((()=>{c()}),[]),(0,n.useImperativeHandle)(t,(()=>({getSelectedId:()=>r})));const c=async()=>{try{const e=await(0,h.n5E)();e&&s(e.data)}catch(e){console.log(e)}},p={selectedRowKeys:[r],onChange:e=>{let[t]=e;l(t)},type:"radio"};return(0,P.jsx)(Z.A,{rowSelection:{...p},rowKey:e=>e.template_id,columns:te({t:o,moduleDataSource:a}),scroll:{y:"39vh"},dataSource:d,pagination:!1,onRow:e=>({onClick:()=>{l(e.template_id)}})})},ne=(0,n.forwardRef)(oe),ie=e=>{let{t:t}=e;return[{title:t("\u9879\u76ee\u540d\u79f0"),dataIndex:"project_name",key:"project_name"},{title:t("\u6a21\u677f\u540d\u79f0"),dataIndex:"template_name",key:"template_name"},{title:t("\u6a21\u677f\u7c7b\u578b"),dataIndex:"template_type",key:"template_type"},{title:t("\u9879\u76ee\u6807\u51c6"),dataIndex:"standard_number",key:"standard_number"},{title:t("\u521b\u5efa\u8005"),dataIndex:"name",key:"name"},{title:t("\u5b58\u50a8\u4f4d\u7f6e"),dataIndex:"project_directory",key:"project_directory",render:e=>(0,P.jsx)(ee.A,{text:e})},{title:t("\u521b\u5efa\u65f6\u95f4"),dataIndex:"created_time",key:"created_time",render:e=>(0,P.jsx)(ee.A,{text:e})}]},ae=(e,t)=>{const{t:o}=(0,m.Bd)(),a=(0,i.d4)((e=>e.global.stationList)),r=(0,i.d4)((e=>e.global.optStation)),l=(0,i.d4)((e=>e.global.globalMonitoringProjectID)),d=(0,i.d4)((e=>e.global.waitingBindInfo)),[s,c]=(0,n.useState)(""),[p,u]=(0,n.useState)([]);(0,n.useEffect)((()=>{g()}),[]),(0,n.useImperativeHandle)(t,(()=>({setCurrentId:e=>{c(e)},getSelectedId:()=>s})));const g=async()=>{try{const e=await(0,h.wD6)();e&&u(e)}catch(e){console.log(e)}},f={selectedRowKeys:[s],onChange:e=>{let[t]=e;c(t)},type:"radio"};console.log(d);const v=(0,n.useMemo)((()=>{if(1===a.length){const e=null===a||void 0===a?void 0:a[0];return null===p||void 0===p?void 0:p.filter((t=>(null===t||void 0===t?void 0:t.id)!==(null===e||void 0===e?void 0:e.projectId)))}const t=(null===e||void 0===e?void 0:e.currentStation)||(null===r||void 0===r?void 0:r.id),o=a.filter((e=>(null===e||void 0===e?void 0:e.projectId)&&(null===e||void 0===e?void 0:e.id)!==(null===t||void 0===t?void 0:t.id))).map((e=>e.projectId));return l&&null!==r&&void 0!==r&&r.id&&o.push(l),null===p||void 0===p?void 0:p.filter((e=>!o.includes(null===e||void 0===e?void 0:e.project_id)))}),[p,a,r,null===e||void 0===e?void 0:e.currentStation,l,d]);return(0,P.jsx)(Z.A,{rowSelection:{...f},rowKey:e=>e.project_id,columns:ie({t:o}),scroll:{y:"39vh"},dataSource:v,pagination:!1,onRow:e=>({onClick:()=>{c(e.project_id)}})})},re=(0,n.forwardRef)(ae);var le=o(48229);const de=(e,t)=>{const[o,i]=(0,n.useState)(le.S.\u6a21\u677f),a=(0,n.useRef)(),r=(0,n.useRef)();(0,n.useImperativeHandle)(t,(()=>({setCurrentProjectId:e=>{var t;null===(t=r.current)||void 0===t||t.setCurrentId(e)},getSelectedId:()=>{if(o===le.S.\u6a21\u677f){const e=a.current.getSelectedId();return e?{type:le.S.\u6a21\u677f,id:e}:(N.Ay.error("\u672a\u9009\u62e9\u6a21\u677f"),!1)}if(o===le.S.\u9879\u76ee){const e=r.current.getSelectedId();return e?{type:le.S.\u9879\u76ee,id:e}:(N.Ay.error("\u672a\u9009\u62e9\u9879\u76ee"),!1)}return!1}})));const l=[{label:"\u6a21\u677f",key:le.S.\u6a21\u677f,forceRender:!0,children:(0,P.jsx)(ne,{ref:a})},{label:"\u9879\u76ee",key:le.S.\u9879\u76ee,forceRender:!0,children:(0,P.jsx)(re,{ref:r,currentStation:null===e||void 0===e?void 0:e.currentStation})}];return(0,P.jsx)(Y.A,{style:{width:"100%"},activeKey:o,type:"card",items:l,onChange:e=>{i(e)}})},se=(0,n.forwardRef)(de),ce=e=>{let{open:t,onOk:o,projectId:i,onCancel:a,currentStation:r}=e;const{t:l}=(0,m.Bd)(),d=(0,n.useRef)(),{initProjectList:s}=(0,U.A)();(0,n.useEffect)((()=>{var e;(s(),i&&t)&&(null===(e=d.current)||void 0===e||e.setCurrentProjectId(i))}),[i,t]);return(0,P.jsx)(V.A,{title:l("\u7ed1\u5b9a"),open:t,onOk:()=>(()=>{var e;const t=null===(e=d.current)||void 0===e?void 0:e.getSelectedId();t&&o(t)})(),onCancel:a,children:(0,P.jsx)(Q,{children:(0,P.jsx)(se,{ref:d,currentStation:r})})})},pe=e=>{let{t:t}=e;return[{title:t("CFG\u540d\u79f0"),dataIndex:"cfgName",key:"cfgName"}]},ue=(e,t)=>{let{stationId:o}=e;const{t:i}=(0,m.Bd)(),[a,r]=(0,n.useState)(),[l,d]=(0,n.useState)();(0,n.useImperativeHandle)(t,(()=>({getCfgId:()=>l||(N.Ay.error("\u672a\u9009\u62e9cfg"),!1)}))),(0,n.useEffect)((()=>{o&&s()}),[o]);const s=async()=>{try{const{cfgData:e,defaultCfgId:t}=await(0,h.XJm)({stationId:o});r(e)}catch(e){console.log(e)}},c={selectedRowKeys:[l],onChange:(e,t)=>{let[o]=e;d(o)},type:"radio"};return(0,P.jsx)(Z.A,{rowSelection:{...c},rowKey:e=>e.cfgId,columns:pe({t:i}),scroll:{y:"47vh"},dataSource:a,pagination:!1})},ge=(0,n.forwardRef)(ue),fe=S.Ay.div`
    height: 50vh;
    display: flex;
    overflow: hidden;

    .left{
        flex: 1;
        height: 100%;
    }
    .right{
        flex: 3;
        height: 100%;
    }
`,me=e=>{let{open:t,setOpen:o,stationId:i,onOk:a}=e;const{t:r}=(0,m.Bd)(),l=(0,n.useRef)();return(0,P.jsx)(V.A,{title:r("\u9009\u62e9cfg"),width:500,open:t,onOk:()=>{const e=l.current.getCfgId();e&&a(e)},onCancel:()=>{o(!1)},children:(0,P.jsx)(fe,{children:(0,P.jsx)(ge,{ref:l,stationId:i})})})},ve=()=>{var e,t;const o=(0,i.wA)(),a=(0,i.d4)((e=>e.global.waitingBindInfo)),{initStationInfo:r}=((0,i.d4)((e=>e.global.stationList)),(0,x.A)()),{onOpenProject:l,onOpenTemplate:s}=(0,j.A)(),{initGlobalProjectID:c,openGlobalProject:p}=(0,d.A)(),u=(0,i.d4)((e=>e.global.globalMonitoringProjectID)),[g,f]=(0,n.useState)(null),[m,v]=(0,n.useState)(!1),[S,A]=(0,n.useState)(!1),{selectOptStation:_}=(0,d.A)(),C=(0,n.useRef)({}),{quitProject:k}=(0,j.A)(),{openProject:H}=(0,y.A)(),{standbyConfig:D}=(0,I.A)();(0,n.useEffect)((()=>(a&&R(),()=>{O()})),[a]);const R=()=>{const{station:e,id:t,type:o,isGlobalMonitoring:n}=a;if(C.current=a,f(e),!n){var i;if(null===(i=(0,w.Wb)())||void 0===i?void 0:i.some((t=>t===(null===e||void 0===e?void 0:e.projectId))))return void N.Ay.error("\u5f53\u524d\u7ad9\u4e0a\u9879\u76ee\u6b63\u5728\u8fd0\u884c\uff0c\u8bf7\u5148\u505c\u6b62\u9879\u76ee")}if(n&&u){var r;if(null===(r=(0,w.Wb)())||void 0===r?void 0:r.some((e=>e===u)))return void N.Ay.error("\u5f53\u524d\u7ad9\u4e0a\u9879\u76ee\u6b63\u5728\u8fd0\u884c\uff0c\u8bf7\u5148\u505c\u6b62\u9879\u76ee")}(e&&!t&&!o||n)&&E(),e&&void 0!==t&&o&&M()},E=()=>{v(!0)},M=async e=>{const{station:t,id:o,type:n,isGlobalMonitoring:i}=C.current;if(i)(0,w.HN)()&&await k({goHomepage:!1}),await H(o,{pageId:D.mode===I.M.\u4e3b\u673a?b.CH.\u9ed8\u8ba4\u9875\u9762:b.CH.\u5907\u673a\u9875\u9762,optStation:{}}),_({});else if("project"===n&&((0,w.HN)()&&await k({goHomepage:!1}),await H(o,{pageId:D.mode===I.M.\u4e3b\u673a?b.CH.\u9ed8\u8ba4\u9875\u9762:b.CH.\u5907\u673a\u9875\u9762,optStation:t}),_(t)),"temp"===n){(0,w.HN)()&&await k({goHomepage:!1});const{cfgData:e}=await(0,h.XJm)({stationId:null===t||void 0===t?void 0:t.id});if((null===e||void 0===e?void 0:e.length)<=0)return void N.Ay.error("\u5f53\u524d\u7ad9\u672a\u8bbe\u7f6ecfg");await s(o),_(t)}},O=()=>{o({type:z.X8,param:null})};return(0,P.jsxs)(P.Fragment,{children:[m&&(0,P.jsx)(ce,{currentStation:g,open:m,onOk:e=>{C.current={...C.current,id:e.id,type:e.type},v(!1),M()},projectId:null===a||void 0===a||null===(e=a.station)||void 0===e?void 0:e.projectId,onCancel:()=>{O(),v(!1)}}),S&&(0,P.jsx)(me,{destroyOnClose:!0,open:S,setOpen:A,stationId:null===a||void 0===a||null===(t=a.station)||void 0===t?void 0:t.id,onOk:e=>M(e)})]})},{Header:he,Sider:xe,Content:je}=a.A,ye=e=>{let{collapsed:t}=e;return(0,P.jsx)(J,{children:t?(0,P.jsx)(r.A,{className:"icon"}):(0,P.jsx)(l.A,{className:"icon"})})},Ie=()=>{const{globalMonitoringProjectID:e,selectOptStation:t}=(0,d.A)(),[o,r]=((0,i.d4)((e=>e.global.stationList)),(0,i.d4)((e=>e.global.optStation)),(0,n.useState)(!1));return(0,P.jsxs)(X,{children:[(0,P.jsxs)(a.A,{className:"global_layout",children:[(0,P.jsx)(xe,{collapsible:!0,onCollapse:(e,t)=>{r(e)},collapsedWidth:0,trigger:(0,P.jsx)(ye,{collapsed:o}),zeroWidthTriggerStyle:{background:"none",color:"#000",width:"15px",height:"150px",top:"calc(50% - 150px)",insetInlineEnd:"-15px"},children:(0,P.jsx)(T,{})}),(0,P.jsx)($,{})]}),(0,P.jsx)(ve,{})]})}},48229:(e,t,o)=>{o.d(t,{S:()=>n});const n={"\u6a21\u677f":"temp","\u9879\u76ee":"project"}},69954:(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var n=o(65043),i=o(16569),a=o(80077),r=o(84856),l=o(94376),d=o(15637),s=(o(53025),o(81143)),c=(o(68374),o(18650));const p=s.Ay.div`
    height: 100vh;
    overflow: hidden;
    background-image: url(${c.k8});
    background-repeat: repeat;
    background-size: 100% 100%;
`;var u=o(70579);const g=()=>{const e=(0,a.wA)(),{subTemplateLayout:t}=(0,r.A)(),o=(0,a.d4)((e=>e.template.currentPageId)),s=(0,a.d4)((e=>e.template.pageData)),[c,g]=(0,n.useState)([]);(0,n.useEffect)((()=>{window.sessionStorage.setItem("logTimer",(new Date).getTime())}),[]),(0,n.useEffect)((()=>{if(0===s.length)return;const t=s.find((e=>e.id===o));var n;t?g(null===t||void 0===t?void 0:t.layout):(i.Ay.error("\u9875\u9762\u4e0d\u5b58\u5728"),e({type:d.qK,param:null===s||void 0===s||null===(n=s[0])||void 0===n?void 0:n.id}))}),[o,s]);return(0,u.jsx)(p,{children:c&&(0,u.jsx)(l.A,{config:c,onResize:e=>{t(e,o)}})})}}}]);
//# sourceMappingURL=991.fccaba54.chunk.js.map