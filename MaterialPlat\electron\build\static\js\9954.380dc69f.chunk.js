"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[9954],{53025:(e,t,r)=>{r(65043)},69954:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var a=r(65043),n=r(16569),i=r(80077),o=r(84856),d=r(94376),s=r(15637),u=(r(53025),r(81143)),l=(r(68374),r(18650));const c=u.Ay.div`
    height: 100vh;
    overflow: hidden;
    background-image: url(${l.k8});
    background-repeat: repeat;
    background-size: 100% 100%;
`;var g=r(70579);const p=()=>{const e=(0,i.wA)(),{subTemplateLayout:t}=(0,o.A)(),r=(0,i.d4)((e=>e.template.currentPageId)),u=(0,i.d4)((e=>e.template.pageData)),[l,p]=(0,a.useState)([]);(0,a.useEffect)((()=>{window.sessionStorage.setItem("logTimer",(new Date).getTime())}),[]),(0,a.useEffect)((()=>{if(0===u.length)return;const t=u.find((e=>e.id===r));var a;t?p(null===t||void 0===t?void 0:t.layout):(n.Ay.error("\u9875\u9762\u4e0d\u5b58\u5728"),e({type:s.qK,param:null===u||void 0===u||null===(a=u[0])||void 0===a?void 0:a.id}))}),[r,u]);return(0,g.jsx)(c,{children:l&&(0,g.jsx)(d.A,{config:l,onResize:e=>{t(e,r)}})})}}}]);
//# sourceMappingURL=9954.380dc69f.chunk.js.map