{"version": 3, "file": "static/js/reactPlayerSoundCloud.04a45bd4.chunk.js", "mappings": "wHAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAqB,CAAC,EAzBXC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAoB,CAC3BK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,QAC/BC,EAAeD,EAAQ,OACvBE,EAAkBF,EAAQ,OAG9B,MAAMP,UAAmBG,EAAaO,UACpCC,WAAAA,GACEC,SAASC,WACTzB,EAAc0B,KAAM,aAAcN,EAAaO,YAC/C3B,EAAc0B,KAAM,WAAY,MAChC1B,EAAc0B,KAAM,cAAe,MACnC1B,EAAc0B,KAAM,iBAAkB,MACtC1B,EAAc0B,KAAM,QAAQ,KAC1BA,KAAKE,UAAU,EAAE,IAEnB5B,EAAc0B,KAAM,UAAU,KACF,OAAtBA,KAAKG,MAAMC,QACbJ,KAAKE,UAAUF,KAAKG,MAAMC,OAC5B,IAEF9B,EAAc0B,KAAM,OAAQK,IAC1BL,KAAKK,OAASA,CAAM,GAExB,CACAC,iBAAAA,GACEN,KAAKG,MAAMI,SAAWP,KAAKG,MAAMI,QAAQP,KAC3C,CACAQ,IAAAA,CAAKC,EAAKC,IACR,EAAIhB,EAAaiB,QAzBL,yCACG,MAwB+BC,MAAMC,IAClD,IAAKb,KAAKK,OACR,OACF,MAAM,KAAES,EAAI,cAAEC,EAAa,MAAEC,EAAK,OAAEC,EAAM,MAAEC,GAAUL,EAAGM,OAAOC,OAC3DV,IACHV,KAAKqB,OAASR,EAAGM,OAAOnB,KAAKK,QAC7BL,KAAKqB,OAAOC,KAAKR,EAAMd,KAAKG,MAAMoB,QAClCvB,KAAKqB,OAAOC,KAAKN,GAAO,KACJhB,KAAKwB,SAAWxB,KAAKyB,YACvB,KAGhBzB,KAAKG,MAAMuB,SAAS,IAEtB1B,KAAKqB,OAAOC,KAAKP,GAAgBY,IAC/B3B,KAAKyB,YAAcE,EAAEC,gBAAkB,IACvC5B,KAAK6B,eAAiBF,EAAEG,cAAc,IAExC9B,KAAKqB,OAAOC,KAAKL,GAAQ,IAAMjB,KAAKG,MAAM4B,YAC1C/B,KAAKqB,OAAOC,KAAKJ,GAAQS,GAAM3B,KAAKG,MAAM6B,QAAQL,MAEpD3B,KAAKqB,OAAOb,KAAKC,EAAK,IACjBT,KAAKG,MAAM8B,OAAOC,QACrBC,SAAUA,KACRnC,KAAKqB,OAAOe,aAAaZ,IACvBxB,KAAKwB,SAAWA,EAAW,IAC3BxB,KAAKG,MAAMkC,SAAS,GACpB,GAEJ,GAEN,CACAC,IAAAA,GACEtC,KAAKC,WAAW,OAClB,CACAsC,KAAAA,GACEvC,KAAKC,WAAW,QAClB,CACAuC,IAAAA,GACA,CACAC,MAAAA,CAAOC,GAA6B,IAApBC,IAAW5C,UAAA6C,OAAA,QAAAC,IAAA9C,UAAA,KAAAA,UAAA,GACzBC,KAAKC,WAAW,SAAoB,IAAVyC,GACrBC,GACH3C,KAAKuC,OAET,CACArC,SAAAA,CAAU4C,GACR9C,KAAKC,WAAW,YAAwB,IAAX6C,EAC/B,CACAV,WAAAA,GACE,OAAOpC,KAAKwB,QACd,CACAuB,cAAAA,GACE,OAAO/C,KAAKyB,WACd,CACAuB,gBAAAA,GACE,OAAOhD,KAAK6B,eAAiB7B,KAAKwB,QACpC,CACAyB,MAAAA,GACE,MAAM,QAAEC,GAAYlD,KAAKG,MACnBgD,EAAQ,CACZC,MAAO,OACPC,OAAQ,OACRH,WAEF,OAAuB7D,EAAaJ,QAAQqE,cAC1C,SACA,CACEC,IAAKvD,KAAKuD,IACVC,IAAK,wCAAwCC,mBAAmBzD,KAAKG,MAAMM,OAC3E0C,QACAO,YAAa,EACbC,MAAO,YAGb,EAEFrF,EAAcY,EAAY,cAAe,cACzCZ,EAAcY,EAAY,UAAWS,EAAgBiE,QAAQC,YAC7DvF,EAAcY,EAAY,eAAe,E", "sources": ["../node_modules/react-player/lib/players/SoundCloud.js"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "SoundCloud_exports", "__export", "target", "all", "name", "default", "SoundCloud", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "setVolume", "props", "volume", "iframe", "componentDidMount", "onMount", "load", "url", "isReady", "getSDK", "then", "SC", "PLAY", "PLAY_PROGRESS", "PAUSE", "FINISH", "ERROR", "Widget", "Events", "player", "bind", "onPlay", "duration", "currentTime", "onPause", "e", "currentPosition", "fractionLoaded", "loadedProgress", "onEnded", "onError", "config", "options", "callback", "getDuration", "onReady", "play", "pause", "stop", "seekTo", "seconds", "keepPlaying", "length", "undefined", "fraction", "getCurrentTime", "getSecondsLoaded", "render", "display", "style", "width", "height", "createElement", "ref", "src", "encodeURIComponent", "frameBorder", "allow", "canPlay", "soundcloud"], "sourceRoot": ""}