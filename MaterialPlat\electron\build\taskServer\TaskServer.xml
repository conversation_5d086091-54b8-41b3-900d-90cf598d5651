<?xml version="1.0"?>
<doc>
    <assembly>
        <name>TaskServer</name>
    </assembly>
    <members>
        <member name="T:TaskServer.ApiServer.JsonConverters.DoubleConverter">
            <summary>
            系统中的double可能未 NAN等特殊值, 所以在处理json的时候做特殊处理
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.JsonConverters.DoubleConverter.Write(System.Text.Json.Utf8JsonWriter,System.Double,System.Text.Json.JsonSerializerOptions)">
            <summary>
            重写double类型 WriteJson
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.JsonConverters.DoubleConverter.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
            <summary>
            正常读double值
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.loading.loadingDlls(System.String)">
            <summary>
            指定目录加载Dll
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.loading.loadingsubTasks">
            <summary>
            指定目录加载子任务
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.loading.LoadHardwareConnecotrs">
            <summary>
            加载硬件
            </summary>
            <returns></returns>
        </member>
        <member name="T:TaskServer.ApiServer.ResultParam">
            <summary>
            C#接口返回结构
            </summary>
            <param name="statusCode">返回的状态码。</param>
            <param name="value">返回的消息</param>
            <param name="contentType">类型</param>
        </member>
        <member name="M:TaskServer.ApiServer.ResultParam.#ctor(System.Int32,System.String,System.Object)">
            <summary>
            C#接口返回结构
            </summary>
            <param name="statusCode">返回的状态码。</param>
            <param name="value">返回的消息</param>
            <param name="contentType">类型</param>
        </member>
        <member name="P:TaskServer.ApiServer.ResultParam.statusCode">
            <summary>返回的状态码。</summary>
        </member>
        <member name="P:TaskServer.ApiServer.ResultParam.value">
            <summary>返回的消息</summary>
        </member>
        <member name="P:TaskServer.ApiServer.ResultParam.contentType">
            <summary>类型</summary>
        </member>
        <member name="T:TaskServer.ApiServer.RestFulResult">
            <summary>
            统一封装返回的结果
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.RestFulResult.OK">
            <summary>
            成功返回，无参数
            </summary>
            <returns>返回一个固定结构。</returns>
        </member>
        <member name="M:TaskServer.ApiServer.RestFulResult.OK(System.Object)">
            <summary>
            成功返回，有数据参数
            </summary>
            <param name="data">需要返回的数据。</param>
            <returns>返回一个固定结构。</returns>
        </member>
        <member name="M:TaskServer.ApiServer.RestFulResult.OK(System.Int32,System.String,System.Object)">
            <summary>
            成功返回，所有参数
            </summary>
            <param name="code">需要返回状态码。</param>
            <param name="msg">需要返回msg消息。</param>
            <param name="data">需要返回的数据。</param>
            <returns>返回一个固定结构。</returns>
        </member>
        <member name="M:TaskServer.ApiServer.RestFulResult.SORRY(System.Int32,System.String)">
            <summary>
            失败返回，所有参数
            </summary>
            <param name="code">需要返回状态码。</param>
            <param name="msg">需要返回失败的msg消息。</param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.RestFulResult.SORRY(System.Int32,System.String,System.Object)">
            <summary>
            失败返回，所有参数
            </summary>
            <param name="code">需要返回失败状态码。</param>
            <param name="msg">需要返回失败msg消息。</param>
            <param name="data">需要返回失败的数据。</param>
            <returns>返回一个固定结构。</returns>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.ConfigInfo.Routes">
            <summary>
            配置信息设置接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.ConfigInfo.Routes.SetAppServerConfigInfo(ScriptEngine.ConfigInfo.AppServerConfigInfoParam)">
            <summary>
            设置AppServer配置信息
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.hardware.HardWareRoutes">
            <summary>
            和硬件连接器有关接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.hardware.HardWareRoutes.GetPorts">
            <summary>
            获取当前硬件管理器端口列表
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.hardware.HardWareRoutes.CreateRealHardware(CreateRealHardwareParam)">
            <summary>
            实例化真实硬件
                因为软件在不同模式见切换需要重启软件, 当前接口期望在软件首次登陆时调用
                实例化真实硬件后不支持修改 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes">
            <summary>
            历史数据接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes.GetRealtimeData(TaskServer.ApiServer.Routes.HistoricalData.GetDataParam)">
            <summary>
            获取运行中的历史数据
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes.GetHistoricalData(TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam)">
            <summary>
            获取历史数据
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes.GetResultsIndex(TaskServer.ApiServer.Routes.HistoricalData.ResultsInfo)">
            <summary>
            获取结果变量
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes.GetResultVars(TaskServer.ApiServer.Routes.HistoricalData.GetResultVarsParam)">
            <summary>
            获取结果变量
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes.SetResultIndex(TaskServer.ApiServer.Routes.HistoricalData.ResultsIndex)">
            <summary>
            设置结果变量索引
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes.GetDataByTime(TaskServer.ApiServer.Routes.HistoricalData.GetDataByTimeParams)">
            <summary>
            设置结果变量索引
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes.GetDataByIndex(TaskServer.ApiServer.Routes.HistoricalData.GetDataByIndexParams)">
            <summary>
            设置结果变量索引
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.HistoricalDataRoutes.SaveDbResultVars(TaskServer.ApiServer.Routes.HistoricalData.SaveResultVarsParam)">
            <summary>
            设置结果变量索引
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam">
            <summary>
            
            </summary>
            <param name="TemplateName"></param>
            <param name="X"></param>
            <param name="Y"></param>
            <param name="BufferCode"></param>
            <param name="Y2"></param>
            <param name="OffsetX"></param>
            <param name="OffsetY"></param>
            <param name="OffsetY2"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.#ctor(System.String,System.String,System.Collections.Generic.List{System.String},System.String,System.Collections.Generic.List{System.String},System.Double,System.Double,System.Double)">
            <summary>
            
            </summary>
            <param name="TemplateName"></param>
            <param name="X"></param>
            <param name="Y"></param>
            <param name="BufferCode"></param>
            <param name="Y2"></param>
            <param name="OffsetX"></param>
            <param name="OffsetY"></param>
            <param name="OffsetY2"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.TemplateName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.X">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.Y">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.BufferCode">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.Y2">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.OffsetX">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.OffsetY">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetDataParam.OffsetY2">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam">
            <summary>
            
            </summary>
            <param name="TemplateName"></param>
            <param name="X"></param>
            <param name="Y"></param>
            <param name="SampleCode"></param>
            <param name="BufferCode"></param>
            <param name="Y2"></param>
            <param name="OffsetX"></param>
            <param name="OffsetY"></param>
            <param name="OffsetY2"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.#ctor(System.String,System.String,System.Collections.Generic.List{System.String},System.String,System.String,System.Collections.Generic.List{System.String},System.Double,System.Double,System.Double,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="TemplateName"></param>
            <param name="X"></param>
            <param name="Y"></param>
            <param name="SampleCode"></param>
            <param name="BufferCode"></param>
            <param name="Y2"></param>
            <param name="OffsetX"></param>
            <param name="OffsetY"></param>
            <param name="OffsetY2"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.TemplateName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.X">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.Y">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.SampleCode">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.BufferCode">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.Y2">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.OffsetX">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.OffsetY">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam.OffsetY2">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.HistoricalData.ResultsInfo">
            <summary>
            请求多个结果变量index 结构
            </summary>
            <param name="TemplateName"></param>
            <param name="Results"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.ResultsInfo.#ctor(System.String,System.Collections.Generic.List{TaskServer.ApiServer.Routes.HistoricalData.Result})">
            <summary>
            请求多个结果变量index 结构
            </summary>
            <param name="TemplateName"></param>
            <param name="Results"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.ResultsInfo.TemplateName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.HistoricalData.ResultsInfo.Results">
            <summary></summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.GetRealtimeData(TaskServer.ApiServer.Routes.HistoricalData.GetDataParam)">
            <summary>
            获取执行过程中Json格式的的历史数据
            </summary>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:TaskServer.ApiServer.Routes.HistoricalData.Service.GetColumnData(System.Collections.Generic.List{System.Collections.Generic.List{System.Double}},System.Int32)" -->
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.GetHistoricalData(TaskServer.ApiServer.Routes.HistoricalData.GetHistoricalDataParam)">
            <summary>
            获取历史数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.GetResultsIndex(TaskServer.ApiServer.Routes.HistoricalData.ResultsInfo)">
            <summary>
            获取历史数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.GetResultsDataIndexs(System.String,System.String)">
            <summary>
            获取结果变量数据的index
            </summary>
            <param name="templateName"></param>
            <param name="sampleCode"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.GetResultData(TaskServer.ApiServer.Routes.HistoricalData.GetResultVarsParam)">
            <summary>
            获取结果变量
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.SetResultIndex(TaskServer.ApiServer.Routes.HistoricalData.ResultsIndex)">
            <summary>
            设置结果变量的 index
            </summary>
            <param name="param"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.GetDataByTime(TaskServer.ApiServer.Routes.HistoricalData.GetDataByTimeParams)">
            <summary>
            根据时间获取数据的index
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.GetDataByIndex(TaskServer.ApiServer.Routes.HistoricalData.GetDataByIndexParams)">
            <summary>
            根据index获取数据的时间
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.HistoricalData.Service.SaveResultVar(TaskServer.ApiServer.Routes.HistoricalData.SaveResultVarsParam)">
            <summary>
            保存结果变量
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.HistoricalData.Service.ResultItem">
            <summary>
            结果变量结构
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Host.HostInstanceParam">
            <summary>
            主机实例化参数
            </summary>
            <param name="Hosts"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Host.HostInstanceParam.#ctor(System.Collections.Generic.List{ScriptEngine.System.HostDTO})">
            <summary>
            主机实例化参数
            </summary>
            <param name="Hosts"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Host.HostInstanceParam.Hosts">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Host.AddUpdateHostInstanceParam">
            <summary>
            新增编辑主机实例化参数
            </summary>
            <param name="Host"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Host.AddUpdateHostInstanceParam.#ctor(ScriptEngine.System.HostDTO)">
            <summary>
            新增编辑主机实例化参数
            </summary>
            <param name="Host"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Host.AddUpdateHostInstanceParam.Host">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Host.HostRoutes">
            <summary>
            主机实例化接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Host.HostRoutes.HostInstance(TaskServer.ApiServer.Routes.Host.HostInstanceParam)">
            <summary>
            主机初始化
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Host.HostRoutes.AddUpdateHostInstance(TaskServer.ApiServer.Routes.Host.AddUpdateHostInstanceParam)">
            <summary>
            新建编辑主机实例化
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.Models.TaskStatusEnum">
            <summary>
            任务状态枚举
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.Models.TaskStatusEnum.Started">
            <summary>
            已启动
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.Models.TaskStatusEnum.Running">
            <summary>
            执行中
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.Models.TaskStatusEnum.Completed">
            <summary>
            已完成
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.Models.TaskStatusEnum.Failed">
            <summary>
            失败
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.Models.TaskStatus">
            <summary>
            任务状态信息
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.TaskId">
            <summary>
            任务ID
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.Status">
            <summary>
            任务状态
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.Progress">
            <summary>
            进度百分比 (0-100)
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.Message">
            <summary>
            状态消息
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.CreatedTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.UpdatedTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.TotalFiles">
            <summary>
            总文件数
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.CompletedFiles">
            <summary>
            已完成文件数
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.TotalRows">
            <summary>
            总行数
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.ProcessedRows">
            <summary>
            已处理行数
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.TaskStatus.ExportPath">
            <summary>
            导出文件路径
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.Models.AsyncExportResponse">
            <summary>
            异步导出响应
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.AsyncExportResponse.TaskId">
            <summary>
            任务ID
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.AsyncExportResponse.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.Models.AsyncExportResponse.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.Routes">
            <summary>
            报表接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.Export(TaskServer.ApiServer.Routes.Report.ExportReportParams)">
            <summary>
            导出报表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.ExportCsv(TaskServer.ApiServer.Routes.Report.ExportCsvParams)">
            <summary>
            导出csv
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.InitExcelExport(TaskServer.ApiServer.Routes.Report.InitExcelExportParams)">
            <summary>
            分页导出Excel - 初始化文件
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.AppendExcelData(TaskServer.ApiServer.Routes.Report.AppendExcelDataParams)">
            <summary>
            分页导出Excel - 追加数据
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.FinalizeExcelExport(TaskServer.ApiServer.Routes.Report.FinalizeExcelExportParams)">
            <summary>
            分页导出Excel - 完成导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.ExportExcelDoubleArry(TaskServer.ApiServer.Routes.Report.ExportDoubleArrayParams)">
            <summary>
            导出报表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.ExportCsvDoubleArray(TaskServer.ApiServer.Routes.Report.ExportDoubleArrayParams)">
            <summary>
            导出报表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.ExportCsvAsync(TaskServer.ApiServer.Routes.Report.ExportCsvParams)">
            <summary>
            异步导出CSV
            </summary>
            <param name="param">导出参数</param>
            <returns>任务信息</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.GetCsvExportStatus(System.String)">
            <summary>
            查询CSV导出任务状态
            </summary>
            <param name="taskId">任务ID</param>
            <returns>任务状态</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.GetCsvExportTasks">
            <summary>
            获取所有CSV导出任务列表
            </summary>
            <returns>任务列表</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Routes.GetTaskStatistics">
            <summary>
            获取任务统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ReportUnitParams">
            <summary>
            报表中的单位参数
            </summary>
            <param name="Code">单位code</param>
            <param name="Factor">单位转换因数</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportUnitParams.#ctor(System.String,System.Double)">
            <summary>
            报表中的单位参数
            </summary>
            <param name="Code">单位code</param>
            <param name="Factor">单位转换因数</param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.ReportUnitParams.Code">
            <summary>单位code</summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.ReportUnitParams.Factor">
            <summary>单位转换因数</summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportUnitParams.Conversion(TaskServer.ApiServer.Routes.Report.ReportUnitParams,System.Int32)">
            <summary>
            int类型数值的单位换算
            </summary>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportUnitParams.Conversion(TaskServer.ApiServer.Routes.Report.ReportUnitParams,System.Double)">
            <summary>
            double类型数值的单位换算
            </summary>
            <returns></returns>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ReportRoundParams">
            <summary>
            修约参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportRoundParams.#ctor(System.Int32,System.Int32,System.Int16,System.Int32,System.Int32,System.Int32)">
            <summary>
            修约参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportRoundParams.RoundOff(System.Double)">
            <summary>
            根据修约计算value的目标格式 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ReportFormatParams">
            <summary>
            报表中的格式参数
            </summary>
            <param name="ExponentialDecimalDigits">幂数型小数位数</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportFormatParams.#ctor(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},TaskServer.ApiServer.Routes.Report.ReportRoundParams)">
            <summary>
            报表中的格式参数
            </summary>
            <param name="ExponentialDecimalDigits">幂数型小数位数</param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.ReportFormatParams.ExponentialDecimalDigits">
            <summary>幂数型小数位数</summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportFormatParams.Format(System.Double)">
            <summary>
            根据格式格式化数值
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportFormatParams.Conversion(TaskServer.ApiServer.Routes.Report.ReportUnitParams,System.Double)">
            <summary>
            double类型数值的单位换算
            </summary>
            <returns></returns>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ReportInputVarParams">
            <summary>
            报表输入变量参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportInputVarParams.#ctor(System.String,System.Object,TaskServer.ApiServer.Routes.Report.ReportUnitParams)">
            <summary>
            报表输入变量参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ReportSampleInstParams">
            <summary>
            导出报表试样实例参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportSampleInstParams.#ctor(System.String,System.String,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportInputVarParams})">
            <summary>
            导出报表试样实例参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ReportResultVarParams">
            <summary>
            导出报表结果变量参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportResultVarParams.#ctor(System.String,TaskServer.ApiServer.Routes.Report.ReportUnitParams,TaskServer.ApiServer.Routes.Report.ReportFormatParams)">
            <summary>
            导出报表结果变量参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ReportSignalVarParams">
            <summary>
            导出报表信号变量参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportSignalVarParams.#ctor(System.String,System.String,TaskServer.ApiServer.Routes.Report.ReportUnitParams)">
            <summary>
            导出报表信号变量参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams">
            <summary>
            导出历史数据参数
            </summary>
            <param name="XAxisCode">x轴信号变量code</param>
            <param name="YAxisCode">y轴信号变量code</param>
            <param name="BufferCode">历史数据保存buffer code</param>
            <param name="SignalVars">信号变量参数</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams.#ctor(System.String,System.String,System.String,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSignalVarParams})">
            <summary>
            导出历史数据参数
            </summary>
            <param name="XAxisCode">x轴信号变量code</param>
            <param name="YAxisCode">y轴信号变量code</param>
            <param name="BufferCode">历史数据保存buffer code</param>
            <param name="SignalVars">信号变量参数</param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams.XAxisCode">
            <summary>x轴信号变量code</summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams.YAxisCode">
            <summary>y轴信号变量code</summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams.BufferCode">
            <summary>历史数据保存buffer code</summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams.SignalVars">
            <summary>信号变量参数</summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ExportReportParams">
            <summary>
            导出报表参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ExportReportParams.#ctor(System.String,System.String,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.StatisticalPattern},TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams)">
            <summary>
            导出报表参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.ExportCsvParams">
            <summary>
            导出CSV参数 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.ExportCsvParams.#ctor(System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            导出CSV参数 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.InitExcelExportParams">
            <summary>
            初始化Excel导出参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.InitExcelExportParams.#ctor(System.String,System.String,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.StatisticalPattern},TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams,System.Int32,System.Nullable{System.Int32})">
            <summary>
            初始化Excel导出参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.AppendExcelDataParams">
            <summary>
            追加Excel数据参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.AppendExcelDataParams.#ctor(System.String,System.Collections.Generic.List{System.String},System.Int32,System.Int32,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.StatisticalPattern},System.String,TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams})">
            <summary>
            追加Excel数据参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.FinalizeExcelExportParams">
            <summary>
            完成Excel导出参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.FinalizeExcelExportParams.#ctor(System.String)">
            <summary>
            完成Excel导出参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.SampleHistoricalDataInfo">
            <summary>
            试样历史数据分页信息
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.SampleHistoricalDataInfo.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            试样历史数据分页信息
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.InitExcelExportResult">
            <summary>
            初始化Excel导出结果
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.InitExcelExportResult.#ctor(System.Int32,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.SampleHistoricalDataInfo},System.String)">
            <summary>
            初始化Excel导出结果
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.Service">
            <summary>
            导出报表Service
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.ReportExport(TaskServer.ApiServer.Routes.Report.ExportReportParams)">
            <summary>
            导出报表
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CsvExportAsync(TaskServer.ApiServer.Routes.Report.ExportCsvParams,System.String)">
            <summary>
            异步导出CSV报表
            </summary>
            <param name="param">导出参数</param>
            <param name="taskId">任务ID</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.WriteCSVFileAsync(System.String,System.String[],Scripting.ITemplate,System.String,System.Int32,System.Int32)">
            <summary>
            异步写入CSV文件
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CsvExport(TaskServer.ApiServer.Routes.Report.ExportCsvParams)">
            <summary>
            导出CSV报表（同步版本，保持兼容性）
            </summary>
            <param name="param"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.InitExcelExport(TaskServer.ApiServer.Routes.Report.InitExcelExportParams)">
            <summary>
            初始化Excel导出
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.AppendExcelData(TaskServer.ApiServer.Routes.Report.AppendExcelDataParams)">
            <summary>
            追加Excel数据
            </summary>
            <param name="param"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.AppendHistoricalData(NPOI.SS.UserModel.IWorkbook,Scripting.ITemplate,TaskServer.ApiServer.Routes.Report.AppendExcelDataParams,System.Int32)">
            <summary>
            追加历史数据到指定试样的sheet
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.FinalizeExcelExport(TaskServer.ApiServer.Routes.Report.FinalizeExcelExportParams)">
            <summary>
            完成Excel导出
            </summary>
            <param name="param"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CreateSheet0(NPOI.SS.UserModel.IWorkbook,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams})">
            <summary>
            创建报表的Sheet0(试样实例*结果变量信息)
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CreateSheet1(NPOI.SS.UserModel.IWorkbook,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.Dictionary{System.String,ScriptEngine.ResultVar.DbResultVar}},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams})">
            <summary>
            创建报表的Sheet1 测试结果 = 试样实例*结果变量
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CreateSheet2(NPOI.SS.UserModel.IWorkbook,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.Dictionary{System.String,ScriptEngine.ResultVar.DbResultVar}},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.StatisticalPattern})">
            <summary>
             创建报表的Sheet2 数据统计 = Sheet2: 结果变量*统计函数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.IndexToColumn(System.Int32)">
            <summary>
            使用列的序号转为列名 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CreateHistoricalDataSheetHeader(NPOI.SS.UserModel.IWorkbook,TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams,System.String)">
            <summary>
            创建历史数据sheet页表头
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.AddChartToHistoricalDataSheet(NPOI.SS.UserModel.ISheet)">
            <summary>
            为历史数据sheet添加图表
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CreateHistoricalDataSheet(NPOI.SS.UserModel.IWorkbook,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSignalVarParams},System.String,System.Collections.Generic.IEnumerable{System.Object[]},System.Boolean)">
            <summary>
            创建历史数据sheet页
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CreateOtherSheets(NPOI.SS.UserModel.IWorkbook,Scripting.ITemplate,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams},TaskServer.ApiServer.Routes.Report.ReportHistoricalDataParams)">
            <summary>
            创建报表的其他Sheet(试样实例*信号变量历史数据)
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CreateSheet1Header(NPOI.SS.UserModel.IWorkbook,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams})">
            <summary>
            创建测试结果sheet的表头
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.CreateSheet2Header(NPOI.SS.UserModel.IWorkbook,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.StatisticalPattern})">
            <summary>
            创建数据统计sheet的表头
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.AppendDataToSheets(NPOI.SS.UserModel.IWorkbook,System.Collections.Generic.List{System.String},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams},System.Int32,System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.StatisticalPattern},System.String)">
            <summary>
            追加数据到各个sheet
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.AppendTestResultData(NPOI.SS.UserModel.ISheet,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.Dictionary{System.String,ScriptEngine.ResultVar.DbResultVar}},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{System.String},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportSampleInstParams},System.Int32)">
            <summary>
            追加测试结果数据
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.AppendStatisticsData(NPOI.SS.UserModel.ISheet,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.Dictionary{System.String,ScriptEngine.ResultVar.DbResultVar}},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.ReportResultVarParams},System.Collections.Generic.List{TaskServer.ApiServer.Routes.Report.StatisticalPattern},System.Int32)">
            <summary>
            追加统计数据
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.ExportCsvDoubleArray(TaskServer.ApiServer.Routes.Report.ExportDoubleArrayParams)">
            <summary>
            导出DoubleArray
            </summary>
            <param name="param"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Service.ExportDoubleArray(TaskServer.ApiServer.Routes.Report.ExportDoubleArrayParams)">
            <summary>
            导出DoubleArray(CSV格式)
            </summary>
            <param name="param"></param>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.StatisticalPattern">
            <summary>
            报表结果文件的数据统计方式
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.StatisticalPattern.AverageValue">
            <summary>
            平均值
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.StatisticalPattern.MaximumValue">
            <summary>
            最大值
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.StatisticalPattern.MinimumValue">
            <summary>
            最小值
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.StatisticalPattern.VarianceYields">
            <summary>
            方差
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.Statistic">
            <summary>
            数据统计 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Statistic.VarianceYields(System.Double[])">
            <summary>
            扩展double[]方差 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Statistic.GetStatisticalResult(TaskServer.ApiServer.Routes.Report.StatisticalPattern,System.Double[])">
            <summary>
            根据统计方式获取统计结果
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.Statistic.GetStatisticalName(TaskServer.ApiServer.Routes.Report.StatisticalPattern)">
            <summary>
            获取统计方式名字
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Report.TaskStatusManager">
            <summary>
            任务状态管理器
            负责管理CSV导出任务的状态信息
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.TaskStatusManager._taskStatuses">
            <summary>
            任务状态存储 - 线程安全
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.TaskStatusManager.MaxTaskCount">
            <summary>
            最大任务数量限制
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.TaskStatusManager.TaskExpirationHours">
            <summary>
            任务过期时间（小时）
            </summary>
        </member>
        <member name="F:TaskServer.ApiServer.Routes.Report.TaskStatusManager._cleanupTimer">
            <summary>
            清理定时器
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.#cctor">
            <summary>
            静态构造函数，初始化清理定时器
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.CreateTask(System.String,System.String)">
            <summary>
            创建新任务
            </summary>
            <param name="taskId">任务ID</param>
            <param name="message">初始消息</param>
            <returns>创建的任务状态</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.UpdateStatus(System.String,TaskServer.ApiServer.Routes.Report.Models.TaskStatusEnum,System.Int32,System.String,System.String)">
            <summary>
            更新任务状态
            </summary>
            <param name="taskId">任务ID</param>
            <param name="status">新状态</param>
            <param name="progress">进度</param>
            <param name="message">消息</param>
            <param name="errorMessage">错误消息</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.UpdateProgress(System.String,System.Int32,System.Int32,System.Int32,System.Int64,System.Int64)">
            <summary>
            更新任务进度
            </summary>
            <param name="taskId">任务ID</param>
            <param name="progress">进度百分比</param>
            <param name="completedFiles">已完成文件数</param>
            <param name="totalFiles">总文件数</param>
            <param name="processedRows">已处理行数</param>
            <param name="totalRows">总行数</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.GetStatus(System.String)">
            <summary>
            获取任务状态
            </summary>
            <param name="taskId">任务ID</param>
            <returns>任务状态，如果不存在返回null</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.GetAllTasks">
            <summary>
            获取所有任务状态
            </summary>
            <returns>所有任务状态列表</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.RemoveTask(System.String)">
            <summary>
            删除任务
            </summary>
            <param name="taskId">任务ID</param>
            <returns>是否删除成功</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.CleanupExpiredTasks(System.Object)">
            <summary>
            清理过期任务
            </summary>
            <param name="state">定时器状态参数</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Report.TaskStatusManager.GetStatistics">
            <summary>
            获取当前任务统计信息
            </summary>
            <returns>任务统计信息</returns>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.Action.Routes">
            <summary>
            实例化试样相关接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Action.Routes.ChangeActionState(TaskServer.ApiServer.Routes.Template.Action.ChangeActionStateParams)">
            <summary>
            修改动作状态
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.ControlSelectedResultVar.ControlSelectedResultVarParam">
            <summary>
            更新控件选中的结果变量数据参数
            </summary>
            <param name="TemplateName">模板名称</param>
            <param name="ResultVarList">选中的结果变量数据</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.ControlSelectedResultVar.ControlSelectedResultVarParam.#ctor(System.String,System.Collections.Generic.List{Scripting.ITemplate.ResultVarData})">
            <summary>
            更新控件选中的结果变量数据参数
            </summary>
            <param name="TemplateName">模板名称</param>
            <param name="ResultVarList">选中的结果变量数据</param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.ControlSelectedResultVar.ControlSelectedResultVarParam.TemplateName">
            <summary>模板名称</summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.ControlSelectedResultVar.ControlSelectedResultVarParam.ResultVarList">
            <summary>选中的结果变量数据</summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.ControlSelectedResultVar.Routes">
            <summary>
            控制选中结果变量相关接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.ControlSelectedResultVar.Routes.UpdateControlSelectedResultVar(TaskServer.ApiServer.Routes.Template.ControlSelectedResultVar.ControlSelectedResultVarParam)">
            <summary>
            更新控件选中的结果变量数据
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.BatchAddGlobalProjectMapping">
            <summary>
            新建实例化的接口参数
            </summary>
            <param name="ClassName"></param>
            <param name="GlobalProjectMappingParams"></param>   
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.BatchAddGlobalProjectMapping.#ctor(System.String,System.Collections.Generic.List{ScriptEngine.InstantiatedTemplate.GlobalProjectMapping.GlobalProjectMappingDTO})">
            <summary>
            新建实例化的接口参数
            </summary>
            <param name="ClassName"></param>
            <param name="GlobalProjectMappingParams"></param>   
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.BatchAddGlobalProjectMapping.ClassName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.BatchAddGlobalProjectMapping.GlobalProjectMappingParams">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.UpdateGlobalProjectMapping">
            <summary>
            更新实例化的接口参数
            </summary>
            <param name="ClassName"></param>
            <param name="GlobalProjectMappingParam"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.UpdateGlobalProjectMapping.#ctor(System.String,ScriptEngine.InstantiatedTemplate.GlobalProjectMapping.GlobalProjectMappingDTO)">
            <summary>
            更新实例化的接口参数
            </summary>
            <param name="ClassName"></param>
            <param name="GlobalProjectMappingParam"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.UpdateGlobalProjectMapping.ClassName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.UpdateGlobalProjectMapping.GlobalProjectMappingParam">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.BatchDeleteGlobalProjectMapping">
            <summary>
            删除实例化的接口参数
            </summary>
            <param name="ClassName"></param>
            <param name="IDs"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.BatchDeleteGlobalProjectMapping.#ctor(System.String,System.Collections.Generic.List{System.Int32})">
            <summary>
            删除实例化的接口参数
            </summary>
            <param name="ClassName"></param>
            <param name="IDs"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.BatchDeleteGlobalProjectMapping.ClassName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.BatchDeleteGlobalProjectMapping.IDs">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.GlobalProjectMapping.Routes">
            <summary>
            监控关联管理器 实例化接口
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.LogControl.QueryLogControlDataParam">
            <summary>
            日志控件查询接口参数
            </summary>
            <param name="ClassName">模板名称</param>
            <param name="StartTime">开始时间</param>
            <param name="EndTime">结束时间</param>
            <param name="HostName">主机名称</param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.LogControl.QueryLogControlDataParam.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            日志控件查询接口参数
            </summary>
            <param name="ClassName">模板名称</param>
            <param name="StartTime">开始时间</param>
            <param name="EndTime">结束时间</param>
            <param name="HostName">主机名称</param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.LogControl.QueryLogControlDataParam.ClassName">
            <summary>模板名称</summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.LogControl.QueryLogControlDataParam.StartTime">
            <summary>开始时间</summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.LogControl.QueryLogControlDataParam.EndTime">
            <summary>结束时间</summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.LogControl.QueryLogControlDataParam.HostName">
            <summary>主机名称</summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.LogControl.Routes">
            <summary>
            日志控件相关接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.LogControl.Routes.QueryLogControlData(TaskServer.ApiServer.Routes.Template.LogControl.QueryLogControlDataParam)">
            <summary>
            查询日志控件数据接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.LogControl.Routes.QueryLogControlData(TaskServer.ApiServer.Routes.Template.LogControl.AddRecordLogData)">
            <summary>
            添加日志控件数据接口
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.Mapping.UpdateHardwareParams">
            <summary>
            修改实例化信号变量入参
            </summary>
            <param name="ClassName"></param>
            <param name="Hardware"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Mapping.UpdateHardwareParams.#ctor(System.String,ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware.HardwareDTO)">
            <summary>
            修改实例化信号变量入参
            </summary>
            <param name="ClassName"></param>
            <param name="Hardware"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.Mapping.UpdateHardwareParams.ClassName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.Mapping.UpdateHardwareParams.Hardware">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.Mapping.Routes">
            <summary>
            实例化映像接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Mapping.Routes.UpdateHardware(TaskServer.ApiServer.Routes.Template.Mapping.UpdateHardwareParams)">
            <summary>
            修改实例化信号变量
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.CloseParams">
            <summary>
            关闭模板接口参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.CloseParams.#ctor(System.String)">
            <summary>
            关闭模板接口参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.Routes">
            <summary>
            模板接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Routes.Close(TaskServer.ApiServer.Routes.Template.CloseParams)">
            <summary>
            关闭模板(在此前需要终止流程图)
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SampleInst.BatchDeleteInputVarParams">
            <summary>
            批量删除输入变量参数 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.BatchDeleteInputVarParams.#ctor(System.String,System.String[])">
            <summary>
            批量删除输入变量参数 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateInputVarParams">
            <summary>
            更新输入变量入参结构
            </summary>
            <param name="ClassName"></param>
            <param name="InputVar"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateInputVarParams.#ctor(System.String,ScriptEngine.InputVar.InputVarDTO)">
            <summary>
            更新输入变量入参结构
            </summary>
            <param name="ClassName"></param>
            <param name="InputVar"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateInputVarParams.ClassName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateInputVarParams.InputVar">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateDoubleArrayInputVarParams">
            <summary>
            修改二维数组单个单元格入参结构
            </summary>
            <param name="ClassName"></param>
            <param name="DoubleArrayCode"></param>
            <param name="DoubleArrayCellParam"></param>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateDoubleArrayInputVarParams.#ctor(System.String,System.String,ScriptEngine.InputVar.InputVars.DoubleArrayCellParam)">
            <summary>
            修改二维数组单个单元格入参结构
            </summary>
            <param name="ClassName"></param>
            <param name="DoubleArrayCode"></param>
            <param name="DoubleArrayCellParam"></param>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateDoubleArrayInputVarParams.ClassName">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateDoubleArrayInputVarParams.DoubleArrayCode">
            <summary></summary>
        </member>
        <member name="P:TaskServer.ApiServer.Routes.Template.SampleInst.UpdateDoubleArrayInputVarParams.DoubleArrayCellParam">
            <summary></summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SampleInst.InputVarRoutes">
            <summary>
            输入变量实例化结构接口
            TODO: 输入变量的创建和修改当前缺少实例化接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.InputVarRoutes.BatchDelete(TaskServer.ApiServer.Routes.Template.SampleInst.BatchDeleteInputVarParams)">
            <summary>
            批量删除实例化输入变量
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.InputVarRoutes.Update(TaskServer.ApiServer.Routes.Template.SampleInst.UpdateInputVarParams)">
            <summary>
            更新输入变量值
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.InputVarRoutes.UpdateDoubleArrayCell(TaskServer.ApiServer.Routes.Template.SampleInst.UpdateDoubleArrayInputVarParams)">
            <summary>
            更新二维数组单元格数据
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SampleInst.BatchDeleteResultVarParams">
            <summary>
            批量删除结果变量参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.BatchDeleteResultVarParams.#ctor(System.String,System.String[])">
            <summary>
            批量删除结果变量参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SampleInst.BatchCreateOrUpdateResultVarParams">
            <summary>
            批量新建或者更新结果变量参数 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.BatchCreateOrUpdateResultVarParams.#ctor(System.String,System.Collections.Generic.Dictionary{System.String,ScriptEngine.ResultVar.ResultVarDTO})">
            <summary>
            批量新建或者更新结果变量参数 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SampleInst.ResultVarRoutes">
            <summary>
            输入变量实例化结构接口
            TODO: 输入变量的创建和修改当前缺少实例化接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.ResultVarRoutes.BatchDelete(TaskServer.ApiServer.Routes.Template.SampleInst.BatchDeleteResultVarParams)">
            <summary>
            批量修改或编辑试样
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SampleInst.ResultVarRoutes.BatchCreateOrUpdate(TaskServer.ApiServer.Routes.Template.SampleInst.BatchCreateOrUpdateResultVarParams)">
            <summary>
            批量创建或编辑结果变量 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SignalVar.BatchCreateOrUpdatePatams">
            <summary>
            批量更新或新建信号变量参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SignalVar.BatchCreateOrUpdatePatams.#ctor(System.String,System.Collections.Generic.List{ScriptEngine.InstantiatedTemplate.SignalVar.CreateOrUpdateParams})">
            <summary>
            批量更新或新建信号变量参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SignalVar.BatchUpdateHwMappingPatams">
            <summary>
            批量更新信号变量硬件映像参数
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SignalVar.BatchUpdateHwMappingPatams.#ctor(System.String,System.Collections.Generic.List{ScriptEngine.InstantiatedTemplate.SignalVar.UpdateHwMappingParams})">
            <summary>
            批量更新信号变量硬件映像参数
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.SignalVar.Routes">
            <summary>
            模板接口
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SignalVar.Routes.BatchCreateOrUpdateSignalVar(TaskServer.ApiServer.Routes.Template.SignalVar.BatchCreateOrUpdatePatams)">
            <summary>
            修改实例化信号变量
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SignalVar.Routes.DeleteSignalVar(TaskServer.ApiServer.Routes.Template.SignalVar.DeletePatams)">
            <summary>
            删除实例化信号变量
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.SignalVar.Routes.BatchCreateOrUpdateSignalVar(TaskServer.ApiServer.Routes.Template.SignalVar.BatchUpdateHwMappingPatams)">
            <summary>
            修改实例化信号变量
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.Subtask.CreateSubtaskParams">
            <summary>
            创建子任务接口参数 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Subtask.CreateSubtaskParams.#ctor(System.String,ScriptEngine.InstantiatedTemplate.SubTask.SubtaskDTO,System.String)">
            <summary>
            创建子任务接口参数 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.Subtask.UpdateSubtaskParams">
            <summary>
            更新子任务接口参数 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Subtask.UpdateSubtaskParams.#ctor(System.String,ScriptEngine.InstantiatedTemplate.SubTask.SubtaskDTO,System.String)">
            <summary>
            更新子任务接口参数 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.Subtask.DeleteSubtaskParams">
            <summary>
            删除子任务接口参数 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Subtask.DeleteSubtaskParams.#ctor(System.String,System.String,System.String)">
            <summary>
            删除子任务接口参数 
            </summary>
        </member>
        <member name="T:TaskServer.ApiServer.Routes.Template.Subtask.RunScriptParams">
            <summary>
            执行子任务脚本接口参数 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Subtask.RunScriptParams.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            执行子任务脚本接口参数 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Subtask.Routes.CreateSubtask(TaskServer.ApiServer.Routes.Template.Subtask.CreateSubtaskParams)">
            <summary>
            修改实例化信号变量
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Subtask.Routes.UpdateSubtask(TaskServer.ApiServer.Routes.Template.Subtask.UpdateSubtaskParams)">
            <summary>
            更新子任务 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Subtask.Routes.DeleteSubtask(TaskServer.ApiServer.Routes.Template.Subtask.DeleteSubtaskParams)">
            <summary>
            删除子任务
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Subtask.Routes.RunScript(TaskServer.ApiServer.Routes.Template.Subtask.RunScriptParams)">
            <summary>
            执行子任务脚本 
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Interface.UISubscriptionController.InitSubscription(ScriptEngine.InputVar.UISubscription.SubscriptionRequest)">
            <summary>
            初始化订阅
            </summary>
            <param name="request">订阅请求</param>
            <returns>订阅结果</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Interface.UISubscriptionController.UpdateSubscription(ScriptEngine.InputVar.UISubscription.SubscriptionRequest)">
            <summary>
            更新订阅
            </summary>
            <param name="request">订阅请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Interface.UISubscriptionController.PauseSubscription(System.String)">
            <summary>
            暂停订阅
            </summary>
            <param name="request">暂停请求</param>
            <returns>暂停结果</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Interface.UISubscriptionController.ResumeSubscription(System.String)">
            <summary>
            恢复订阅
            </summary>
            <param name="request">恢复请求</param>
            <returns>恢复结果</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Interface.UISubscriptionController.CloseSubscription(System.String)">
            <summary>
            关闭订阅
            </summary>
            <param name="request">关闭请求</param>
            <returns>关闭结果</returns>
        </member>
        <member name="M:TaskServer.ApiServer.Routes.Template.Interface.UISubscriptionController.CloseTemplateSubscriptions(System.String)">
            <summary>
            批量关闭模板的所有订阅
            </summary>
            <param name="request">批量关闭请求</param>
            <returns>关闭结果</returns>
        </member>
        <member name="T:TaskServer.ApiServer.Server">
            <summary>
            接口服务
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Server.Init">
            <summary>
            初始化接口服务
            </summary>
        </member>
        <member name="M:TaskServer.ApiServer.Server.Start">
            <summary>
            开启接口服务(占用当前线程 启动服务时最后调用)
            </summary>
        </member>
        <member name="T:CreateRealHardwareParam">
            <summary>
            创建实例化真实硬件参数
            </summary>
        </member>
        <member name="M:CreateRealHardwareParam.#ctor(System.Collections.Generic.Dictionary{System.String,ScriptEngine.InstantiatedTemplate.Hardware.RealHardware.HardwareDTO})">
            <summary>
            创建实例化真实硬件参数
            </summary>
        </member>
        <member name="T:Routes">
            <summary>
            实例化试样相关接口
            </summary>
        </member>
        <member name="M:Routes.BatchCreateOrUpdateSampleInst(BatchCreateOrUpdateParams)">
            <summary>
            批量修改或编辑试样
            </summary>
        </member>
        <member name="M:Routes.SelectSampleInst(SelectParams)">
            <summary>
            修改当前选中试样
            </summary>
        </member>
        <member name="M:Routes.ChangeCurrentInstState(ChangeStateParams)">
            <summary>
            修改当前试样实例状态
            </summary>
        </member>
        <member name="M:Routes.DeleteSampleInst(BatchDeleteParams)">
            <summary>
            批量删除试样
            </summary>
        </member>
        <member name="T:Program">
              多任务管理器通过读取配置文件 subtasks.json 来启动多个HWC进程
              +-------------------------+
              |     Task   Server       |
              ++-----------+------------+
               |           |subTasks.json:
               |           | [http, cmd, data, name, exe]
               | +---------v-------------------+
               | |   Hardware connector 1      |
               | +-----------------------------+
               | [http, cmd, data, name, exe]
               |     +------------------------------+
               +----->  Hardware connector 2        |
               |     +------------------------------+
               | [http, cmd, data, name, exe]
               |          +-------------------------------+
               +---------->  ...                          |
                          +-------------------------------+
            
              TaskServer 通过 Router/Dealer(双向) 下发命令/接受通知
              通过 Push/Pull 接受硬件数据
              +----------------------------------+
              |        Task    Server            |
              +--+----------+-----+-----------+--+
                 |  Router  |     |   Pull    |
                 +-----^----+     +-----^-----+
                       |                |
                       | cmd            | data
                       |                |
                  +----v----+      +----+-----+
                  | Dealer  |      |  Push    |
              +---+---------+------+----------+--+
              |  Hareware Connector 1, 2 n       |
              +----------------------------------+
        </member>
        <member name="T:HardwareConnectorConfig">
            <summary>
            硬件连接器
            </summary>
            <param name="Name">dll的唯一标志</param>
            <param name="Http">port.每个dll唯一</param>
            <param name="Data">数据交互端口，固定7789</param>
            <param name="Cmd">命令交互端口，固定7788</param>
            <param name="ExePath">exe所在路径</param>
            <param name="ConfigDir">配置文件所在路径.一般为".",意味着这当前路径</param>
        </member>
        <member name="M:HardwareConnectorConfig.#ctor(System.String,System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            硬件连接器
            </summary>
            <param name="Name">dll的唯一标志</param>
            <param name="Http">port.每个dll唯一</param>
            <param name="Data">数据交互端口，固定7789</param>
            <param name="Cmd">命令交互端口，固定7788</param>
            <param name="ExePath">exe所在路径</param>
            <param name="ConfigDir">配置文件所在路径.一般为".",意味着这当前路径</param>
        </member>
        <member name="P:HardwareConnectorConfig.Name">
            <summary>dll的唯一标志</summary>
        </member>
        <member name="P:HardwareConnectorConfig.Http">
            <summary>port.每个dll唯一</summary>
        </member>
        <member name="P:HardwareConnectorConfig.Data">
            <summary>数据交互端口，固定7789</summary>
        </member>
        <member name="P:HardwareConnectorConfig.Cmd">
            <summary>命令交互端口，固定7788</summary>
        </member>
        <member name="P:HardwareConnectorConfig.ExePath">
            <summary>exe所在路径</summary>
        </member>
        <member name="P:HardwareConnectorConfig.ConfigDir">
            <summary>配置文件所在路径.一般为".",意味着这当前路径</summary>
        </member>
        <member name="T:Registry">
            <summary>
            加载子任务和硬件连接器
            </summary>
        </member>
        <member name="M:Registry.RegisterHardwareConnectors">
            <summary>
            注册硬件连接器
            </summary>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="T:Registry.HareWareConfig">
             <summary>
            
             </summary>
             <param name="HardwareImplementation">硬件的namespace.classname,dllname</param>
             <param name="DllPath">dll名称</param>
        </member>
        <member name="M:Registry.HareWareConfig.#ctor(System.String,System.String)">
             <summary>
            
             </summary>
             <param name="HardwareImplementation">硬件的namespace.classname,dllname</param>
             <param name="DllPath">dll名称</param>
        </member>
        <member name="P:Registry.HareWareConfig.HardwareImplementation">
            <summary>硬件的namespace.classname,dllname</summary>
        </member>
        <member name="P:Registry.HareWareConfig.DllPath">
            <summary>dll名称</summary>
        </member>
        <member name="T:Registry.SubtaskConfig">
            <summary>
            子任务结构
            TODO 考虑如何处理大小写匹配的问题
            </summary>
            <param name="key"></param>
            <param name="type"></param>
        </member>
        <member name="M:Registry.SubtaskConfig.#ctor(System.String,System.String)">
            <summary>
            子任务结构
            TODO 考虑如何处理大小写匹配的问题
            </summary>
            <param name="key"></param>
            <param name="type"></param>
        </member>
        <member name="P:Registry.SubtaskConfig.key">
            <summary></summary>
        </member>
        <member name="P:Registry.SubtaskConfig.type">
            <summary></summary>
        </member>
        <member name="T:Registry.HwcConfig">
            <summary>
            硬件连接器
            TODO 考虑如何处理大小写匹配的问题
            </summary>
            <param name="name">dll的唯一标志</param>
            <param name="http">port.每个dll唯一</param>
            <param name="data">数据交互端口，固定7789</param>
            <param name="cmd">命令交互端口，固定7788</param>
            <param name="exePath">exe所在路径</param>
            <param name="configDir">配置文件所在路径.一般为".",意味着这当前路径</param>
        </member>
        <member name="M:Registry.HwcConfig.#ctor(System.String,System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            硬件连接器
            TODO 考虑如何处理大小写匹配的问题
            </summary>
            <param name="name">dll的唯一标志</param>
            <param name="http">port.每个dll唯一</param>
            <param name="data">数据交互端口，固定7789</param>
            <param name="cmd">命令交互端口，固定7788</param>
            <param name="exePath">exe所在路径</param>
            <param name="configDir">配置文件所在路径.一般为".",意味着这当前路径</param>
        </member>
        <member name="P:Registry.HwcConfig.name">
            <summary>dll的唯一标志</summary>
        </member>
        <member name="P:Registry.HwcConfig.http">
            <summary>port.每个dll唯一</summary>
        </member>
        <member name="P:Registry.HwcConfig.data">
            <summary>数据交互端口，固定7789</summary>
        </member>
        <member name="P:Registry.HwcConfig.cmd">
            <summary>命令交互端口，固定7788</summary>
        </member>
        <member name="P:Registry.HwcConfig.exePath">
            <summary>exe所在路径</summary>
        </member>
        <member name="P:Registry.HwcConfig.configDir">
            <summary>配置文件所在路径.一般为".",意味着这当前路径</summary>
        </member>
    </members>
</doc>
