{"hardwareRootPath": "../hardwareConnectors", "mainStreamKey": "", "hardwareConnectors": [{"name": "hardware_simulator", "http": 5000, "data": 7789, "cmd": 7788, "exePath": "../../../../HardwareConnector/bin/Debug/net6.0-windows/HardwareConnector.exe", "configDir": "."}], "caculateDllsPath": "..\\..\\..\\..\\dllRoot\\caculateDlls", "caculateDlls": ["..\\..\\..\\..\\dllRoot\\caculateDlls\\jingtai\\cal_SingleSideToleranceCoefficientLookupTableDll.dll", "..\\..\\..\\..\\dllRoot\\caculateDlls\\jingtai\\cal_SoftPlant_Caculation.dll", "..\\..\\..\\..\\dllRoot\\caculateDlls\\rubian\\cal_CreepCalc.dll", "..\\..\\..\\..\\dllRoot\\caculateDlls\\system\\cal_System.Reactive.dll"], "subTasksPath": "..\\..\\..\\..\\dllRoot\\subTasks", "subTasks": [{"key": "start", "type": "SubTasks.SubTaskStart, SubTaskList"}, {"key": "end", "type": "SubTasks.SubTaskEnd, SubTaskList"}, {"key": "SubTaskTimer", "type": "SubTasks.<PERSON><PERSON><PERSON><PERSON><PERSON>r, SubTaskList"}, {"key": "SubTaskVideoRecording", "type": "SubTasks.SubTaskVideoRecording, SubTaskList"}, {"key": "xiebo", "type": "SubTasks.SubTaskCmdRAMP, SubTaskList"}, {"key": "removeYsj", "type": "SubTasks.SubTaskRemoveStrainGauge, SubTaskList"}, {"key": "SubtaskDialogBox", "type": "SubTasks.tasks.userInteractionTask.SubtaskDialogBox, SubTaskList"}, {"key": "SubtaskInputVarDialog", "type": "SubTasks.tasks.userInteractionTask.SubtaskInputVarDialog, SubTaskList"}, {"key": "SubtaskExecuteNextStep", "type": "SubTasks.tasks.userInteractionTask.SubtaskExecuteNextStep, SubTaskList"}, {"key": "SubtaskUIOperations", "type": "SubTasks.tasks.userInteractionTask.SubtaskUIOperations, SubTaskList"}, {"key": "daq", "type": "SubTasks.SubTask<PERSON>Q, SubTaskList"}, {"key": "daqRmc", "type": "SubTasks.SubTaskRmcDAQ, SubTaskList"}, {"key": "daqCycle", "type": "SubTasks.SubTaskCycleCalc<PERSON>Q, SubTaskList"}, {"key": "BoxHeadDAQ", "type": "SubTasks.SubTaskBoxHeadDAQ, SubTaskList"}, {"key": "action", "type": "SubTasks.SubTaskOpenFormAndAction, SubTaskList"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "type": "SubTasks.SubTask<PERSON><PERSON><PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "statusCheck", "type": "SubTasks.SubTask<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "crossbeamMovement", "type": "SubTasks.tasks.SubTaskCrossbeamMovement, SubTaskList"}, {"key": "stop", "type": "SubTasks.tasks.SubTaskStop, SubTaskList"}, {"key": "reset<PERSON>ore", "type": "SubTasks.tasks.SubTaskResetRest<PERSON>, SubTaskList"}, {"key": "BreakageMonitor", "type": "SubTasks.SubTaskBreakageMonitor, SubTaskList"}, {"key": "break", "type": "SubTasks.SubTask<PERSON>reak, SubTaskList"}, {"key": "basicTask", "type": "SubTasks.SubTaskBasicTask, SubTaskList"}, {"key": "periodicWaves", "type": "SubTasks.tasks.controlCommandTask.SubTaskPeriodicWaves, SubTaskList"}, {"key": "randomWave", "type": "SubTasks.tasks.controlCommandTask.SubTaskRandomWave, SubTaskList"}, {"key": "highFrequencyCommand", "type": "SubTasks.tasks.controlCommandTask.SubTaskHightCommand, SubTaskList"}, {"key": "trapezoidalWave", "type": "SubTasks.tasks.controlCommandTask.SubTaskTrapezoidalWave, SubTaskList"}, {"key": "trapezoidalWaveDataGather", "type": "SubTasks.tasks.controlCommandTask.SubTaskTrapezoidalWaveDataGather, SubTaskList"}, {"key": "adjustableSlantControl", "type": "SubTasks.tasks.controlCommandTask.SubTaskAdjustableSlantControl, SubTaskList"}, {"key": "periodicWavesDataGather", "type": "SubTasks.tasks.controlCommandTask.SubTaskPeriodicWavesDataGather, SubTaskList"}, {"key": "sweepAmplitude", "type": "SubTasks.tasks.controlCommandTask.SubTaskSweepAmplitude, SubTaskList"}, {"key": "sweepFrequency", "type": "SubTasks.tasks.controlCommandTask.SubTaskSweepFrequency, SubTaskList"}, {"key": "peakGatherData", "type": "SubTasks.tasks.gatherDataTask.SubTaskPeakGatherData, SubTaskList"}, {"key": "hightGatherData", "type": "SubTasks.tasks.gatherDataTask.SubTaskHightGatherData, SubTaskList"}, {"key": "pointsFilter", "type": "SubTasks.tasks.gatherDataTask.SubTaskP<PERSON>s<PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "logMessageControl", "type": "SubTasks.tasks.experimentalActionTask.SubTaskLogMessageControl, SubTaskList"}, {"key": "runExternalProgram", "type": "SubTasks.tasks.experimentalActionTask.SubTaskRunExternalProgram, SubTaskList"}, {"key": "variableAssignment", "type": "SubTasks.tasks.experimentalActionTask.SubTaskVariableAssignment, SubTaskList"}, {"key": "printReport", "type": "SubTasks.tasks.experimentalActionTask.SubTaskPrintReport, SubTaskList"}, {"key": "writeData", "type": "SubTasks.tasks.experimentalActionTask.SubTaskWriteData, SubTaskList"}, {"key": "readData", "type": "SubTasks.tasks.experimentalActionTask.SubTaskReadData, SubTaskList"}, {"key": "externalDeviceAction", "type": "SubTasks.tasks.experimentalActionTask.SubTaskExternalDeviceAction, SubTaskList"}, {"key": "videoModule", "type": "SubTasks.tasks.experimentalActionTask.SubTaskVideoModule, SubTaskList"}, {"key": "loopResetControl", "type": "SubTasks.tasks.experimentalActionTask.SubTaskLoopResetControl, SubTaskList"}, {"key": "inputDialog", "type": "SubTasks.tasks.userInteractionTask.SubTaskInputDialog, SubTaskList"}, {"key": "pauseWaitUserAction", "type": "SubTasks.tasks.userInteractionTask.SubTaskPauseWaitUserAction, SubTaskList"}, {"key": "customizeMessagePopup", "type": "SubTasks.tasks.userInteractionTask.SubTaskCustomizeMessagePopup, SubTaskList"}, {"key": "outPutBit", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskOutPutBit, SubTaskList"}, {"key": "inPutBit", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskInPutBit, SubTaskList"}, {"key": "writeRmcstr", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskWriteRmcstr, SubTaskList"}, {"key": "performControlEventAction", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskPerformControlEventAction, SubTaskList"}, {"key": "temperatureController", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskTemperatureControl, SubTaskList"}, {"key": "inputSignal", "type": "SubTasks.tasks.eventDetectionTask.SubTaskInput<PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "waitEvent", "type": "SubTasks.tasks.eventDetectionTask.SubTaskWaitEvent, SubTaskList"}, {"key": "peakLimitDetection", "type": "SubTasks.tasks.eventDetectionTask.SubTaskPeakLimitDetection, SubTaskList"}, {"key": "SubTaskComputeVar", "type": "SubTasks.SubTaskComputeVar, SubTaskList"}, {"key": "onlyAction", "type": "SubTasks.tasks.SubTask<PERSON><PERSON>, SubTaskList"}, {"key": "next", "type": "SubTasks.tasks.<PERSON>Task<PERSON><PERSON>t, SubTaskList"}, {"key": "realTimeComputing", "type": "SubTasks.tasks.SubTaskRealTimeComputing, SubTaskList"}, {"key": "manualRemove", "type": "SubTasks.SubTaskManualRemoveStrainGauge, SubTaskList"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "type": "SubTasks.tasks.SubTaskResult<PERSON><PERSON><PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "cmdConnection", "type": "SubTasks.SubTaskCmdConnection, SubTaskList"}, {"key": "suspend", "type": "SubTasks.tasks.SubTaskSuspend, SubTaskList"}, {"key": "combinedWaveStartControl", "type": "SubTasks.tasks.controlCommandTask.SubTaskCombinedWaveStartControl, SubTaskList"}, {"key": "combinedWaveEndControl", "type": "SubTasks.tasks.controlCommandTask.SubTaskCombinedWaveEndControl, SubTaskList"}, {"key": "creepCombinationWave", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepCombinationWave, SubTaskList"}, {"key": "peakValleyCompensation", "type": "SubTasks.tasks.eventDetectionTask.SubTaskPeakValleyCompensation, SubTaskList"}, {"key": "actuator", "type": "SubTasks.tasks.controlCommandTask.SubTaskActuator, SubTaskList"}, {"key": "creepSpecificControl", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepControl, SubTaskList"}, {"key": "creepCombinationInterval", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepCombination<PERSON><PERSON>val, SubTaskList"}, {"key": "heatPreservationTime", "type": "SubTasks.tasks.flowLogicTask.SubTaskHeatPreservationTime, SubTaskList"}, {"key": "creepPauseCommand", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepPauseCommand, SubTaskList"}, {"key": "creepResumeCommand", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepResumeCommand, SubTaskList"}, {"key": "creepLoadTime", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepLoadTime, SubTaskList"}, {"key": "lowerComputer", "type": "SubTasks.tasks.flowLogicTask.SubTaskLowerComputer, SubTaskList"}, {"key": "onSystemMsg", "type": "SubTasks.tasks.SubTaskOnSystemMsg, SubTaskList"}, {"key": "online", "type": "SubTasks.tasks.SubTaskOnline, SubTaskList"}, {"key": "highFrequencyDAQ", "type": "SubTasks.tasks.gatherDataTask.SubTaskHighFrequencyDAQ, SubTaskList"}, {"key": "SubTaskEvalScript", "type": "SubTasks.SubTaskEvalScript, SubTaskList"}, {"key": "creepOfflineRecovery", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepOfflineRecovery, SubTaskList"}, {"key": "creepComputeSignalOffset", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepComputeSignalOffset, SubTaskList"}, {"key": "creepControlCmd", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepControlCmd, SubTaskList"}, {"key": "creepTestStopCheck", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepTestStopCheck, SubTaskList"}, {"key": "creepFinishCheck", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreep<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "creepAlarmCheck", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreep<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "creepDeviceStopMonitor", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepDeviceStopMonitor, SubTaskList"}, {"key": "creepBitInMonitor", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepBitInMonitor, SubTaskList"}, {"key": "creepSaveData", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepSaveData, SubTaskList"}, {"key": "creepRealTimeComputing", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepRealTimeComputing, SubTaskList"}, {"key": "creepSignalCheck", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepS<PERSON><PERSON><PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "subTaskPlayBack", "type": "SubTasks.tasks.SubTaskPlayBack, SubTaskList"}, {"key": "subTaskModify", "type": "SubTasks.tasks.SubTaskModify, SubTaskList"}, {"key": "arrayDataHandler", "type": "SubTasks.tasks.gatherDataTask.SubTaskArray<PERSON><PERSON><PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "subTaskSyncStart", "type": " SubTasks.tasks.controlCommandTask.SubTaskSyncStart, SubTaskList"}, {"key": "subTaskSyncAction", "type": " SubTasks.tasks.controlCommandTask.SubTaskSyncAction, SubTaskList"}, {"key": "creepUpdateControlParams", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepUpdateControlParams, SubTaskList"}, {"key": "globalExecuteAction", "type": "SubTasks.tasks.SubTaskGlobalExecuteAction, SubTaskList"}, {"key": "globalDataCollection", "type": "SubTasks.tasks.SubTaskGlobalDataCollection, SubTaskList"}, {"key": "creepTrendFiltering", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepTrendFiltering, SubTaskList"}, {"key": "appPushData", "type": "SubTasks.tasks.SubTaskAppPushData, SubTaskList"}, {"key": "updateParam", "type": "SubTasks.tasks.userInteractionTask.SubTaskUpdate<PERSON><PERSON><PERSON>, SubTaskList"}, {"key": "highFreqCycleGen", "type": "SubTasks.tasks.gatherDataTask.SubTaskHighFreqCycleGen, SubTaskList"}, {"key": "highFreqJiaoBianDAQ", "type": "SubTasks.tasks.gatherDataTask.SubTaskHighFreqJiaoBianDAQ, SubTaskList"}, {"key": "creepChangeTempParams", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepChangeTempParams, SubTaskList"}, {"key": "HighFrequencySubTask", "type": "SubTaskList.tasks.controlCommandTask.SubTaskHighFrequencyControl, SubTaskList"}, {"key": "SubTaskStartOnline", "type": "SubTaskList.tasks.SubTaskStartOnline, SubTaskList"}, {"key": "subTaskCreepCycleSignalGen", "type": "SubTaskList.Tasks.GatherDataTask.SubTaskCreepCycleSignalGen, SubTaskList"}]}