[{"code": 1, "describe": "预加负荷", "saveData": 1, "alarm": 0, "stopTestState": 0, "testState": ""}, {"code": 2, "describe": "正在升温", "saveData": 1, "alarm": 0, "stopTestState": 0, "testState": ""}, {"code": 3, "describe": "已经加载", "saveData": 1, "alarm": 0, "stopTestState": 0, "testState": ""}, {"code": 4, "describe": "停试卸载", "saveData": 0, "alarm": 0, "stopTestState": 0, "testState": "试验结束"}, {"code": 5, "describe": "停试降温", "saveData": 0, "alarm": 0, "stopTestState": 0, "testState": "试验结束"}, {"code": 6, "describe": "试验停止", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 7, "describe": "试验暂停", "saveData": 0, "alarm": 1, "stopTestState": 0, "testState": ""}, {"code": 8, "describe": "试验继续", "saveData": 1, "alarm": 0, "stopTestState": 0, "testState": ""}, {"code": 9, "describe": "保温阶段", "saveData": 1, "alarm": 0, "stopTestState": 0, "testState": ""}, {"code": 10, "describe": "系统脱机", "saveData": 0, "alarm": 0, "stopTestState": 0, "testState": "试验空闲"}, {"code": 11, "describe": "系统联机", "saveData": 0, "alarm": 0, "stopTestState": 0, "testState": "试验空闲"}, {"code": 31, "describe": "变形超上限", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 32, "describe": "变形超下限", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 33, "describe": "负荷超上限", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 34, "describe": "负荷超下限", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 35, "describe": "位移超上限", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 36, "describe": "位移超下限", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 37, "describe": "变形高于控制量", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 38, "describe": "变形低于控制量", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 39, "describe": "负荷高于控制量", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 40, "describe": "负荷低于控制量", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 41, "describe": "位移高于控制量", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 42, "describe": "位移低于控制量", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 43, "describe": "试样断裂", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 44, "describe": "变形累加", "saveData": 1, "alarm": 1, "stopTestState": 0, "testState": ""}, {"code": 45, "describe": "手动停止试验", "saveData": 0, "alarm": 1, "stopTestState": 1, "testState": "试验结束"}, {"code": 46, "describe": "电偶开路", "saveData": 0, "alarm": 1, "stopTestState": 0, "testState": ""}, {"code": 47, "describe": "电偶短路", "saveData": 0, "alarm": 1, "stopTestState": 0, "testState": ""}, {"code": 99, "describe": "蠕变设备断电", "saveData": 0, "alarm": 1, "stopTestState": 0, "testState": ""}, {"code": 100, "describe": "通讯中断", "saveData": 0, "alarm": 1, "stopTestState": 0, "testState": ""}]