{"subTasks": [{"key": "start", "type": "SubTasks.SubTaskStart"}, {"key": "end", "type": "SubTasks.SubTaskEnd"}, {"key": "SubTaskTimer", "type": "SubTasks.SubTaskTimer"}, {"key": "SubTaskVideoRecording", "type": "SubTasks.SubTaskVideoRecording"}, {"key": "xiebo", "type": "SubTasks.SubTaskCmdRAMP"}, {"key": "removeYsj", "type": "SubTasks.SubTaskRemoveStrainGauge"}, {"key": "SubtaskDialogBox", "type": "SubTasks.tasks.userInteractionTask.SubtaskDialogBox"}, {"key": "SubtaskInputVarDialog", "type": "SubTasks.tasks.userInteractionTask.SubtaskInputVarDialog"}, {"key": "SubtaskExecuteNextStep", "type": "SubTasks.tasks.userInteractionTask.SubtaskExecuteNextStep"}, {"key": "SubtaskUIOperations", "type": "SubTasks.tasks.userInteractionTask.SubtaskUIOperations"}, {"key": "daq", "type": "SubTasks.SubTaskDAQ"}, {"key": "daqRmc", "type": "SubTasks.SubTaskRmcDAQ"}, {"key": "daqCycle", "type": "SubTasks.SubTaskCycleCalcDAQ"}, {"key": "BoxHeadDAQ", "type": "SubTasks.SubTaskBoxHeadDAQ"}, {"key": "action", "type": "SubTasks.SubTaskOpenFormAndAction"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "type": "SubTasks.SubTaskVarCheck"}, {"key": "statusCheck", "type": "SubTasks.SubTaskStatusCheck"}, {"key": "crossbeamMovement", "type": "SubTasks.tasks.SubTaskCrossbeamMovement"}, {"key": "stop", "type": "SubTasks.tasks.SubTaskStop"}, {"key": "reset<PERSON>ore", "type": "SubTasks.tasks.SubTaskResetRestore"}, {"key": "BreakageMonitor", "type": "SubTasks.SubTaskBreakageMonitor"}, {"key": "break", "type": "SubTasks.SubTaskBreak"}, {"key": "basicTask", "type": "SubTasks.SubTaskBasicTask"}, {"key": "periodicWaves", "type": "SubTasks.tasks.controlCommandTask.SubTaskPeriodicWaves"}, {"key": "randomWave", "type": "SubTasks.tasks.controlCommandTask.SubTaskRandomWave"}, {"key": "highFrequencyCommand", "type": "SubTasks.tasks.controlCommandTask.SubTaskHightCommand"}, {"key": "trapezoidalWave", "type": "SubTasks.tasks.controlCommandTask.SubTaskTrapezoidalWave"}, {"key": "trapezoidalWaveDataGather", "type": "SubTasks.tasks.controlCommandTask.SubTaskTrapezoidalWaveDataGather"}, {"key": "adjustableSlantControl", "type": "SubTasks.tasks.controlCommandTask.SubTaskAdjustableSlantControl"}, {"key": "periodicWavesDataGather", "type": "SubTasks.tasks.controlCommandTask.SubTaskPeriodicWavesDataGather"}, {"key": "sweepAmplitude", "type": "SubTasks.tasks.controlCommandTask.SubTaskSweepAmplitude"}, {"key": "sweepFrequency", "type": "SubTasks.tasks.controlCommandTask.SubTaskSweepFrequency"}, {"key": "peakGatherData", "type": "SubTasks.tasks.gatherDataTask.SubTaskPeakGatherData"}, {"key": "hightGatherData", "type": "SubTasks.tasks.gatherDataTask.SubTaskHightGatherData"}, {"key": "pointsFilter", "type": "SubTasks.tasks.gatherDataTask.SubTaskPointsFilter"}, {"key": "logMessageControl", "type": "SubTasks.tasks.experimentalActionTask.SubTaskLogMessageControl"}, {"key": "runExternalProgram", "type": "SubTasks.tasks.experimentalActionTask.SubTaskRunExternalProgram"}, {"key": "variableAssignment", "type": "SubTasks.tasks.experimentalActionTask.SubTaskVariableAssignment"}, {"key": "printReport", "type": "SubTasks.tasks.experimentalActionTask.SubTaskPrintReport"}, {"key": "writeData", "type": "SubTasks.tasks.experimentalActionTask.SubTaskWriteData"}, {"key": "readData", "type": "SubTasks.tasks.experimentalActionTask.SubTaskReadData"}, {"key": "externalDeviceAction", "type": "SubTasks.tasks.experimentalActionTask.SubTaskExternalDeviceAction"}, {"key": "videoModule", "type": "SubTasks.tasks.experimentalActionTask.SubTaskVideoModule"}, {"key": "loopResetControl", "type": "SubTasks.tasks.experimentalActionTask.SubTaskLoopResetControl"}, {"key": "inputDialog", "type": "SubTasks.tasks.userInteractionTask.SubTaskInputDialog"}, {"key": "pauseWaitUserAction", "type": "SubTasks.tasks.userInteractionTask.SubTaskPauseWaitUserAction"}, {"key": "customizeMessagePopup", "type": "SubTasks.tasks.userInteractionTask.SubTaskCustomizeMessagePopup"}, {"key": "outPutBit", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskOutPutBit"}, {"key": "inPutBit", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskInPutBit"}, {"key": "writeRmcstr", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskWriteRmcstr"}, {"key": "performControlEventAction", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskPerformControlEventAction"}, {"key": "temperatureController", "type": "SubTasks.tasks.peripheralInteractionTask.SubTaskTemperatureControl"}, {"key": "inputSignal", "type": "SubTasks.tasks.eventDetectionTask.SubTaskInputSignal"}, {"key": "waitEvent", "type": "SubTasks.tasks.eventDetectionTask.SubTaskWaitEvent"}, {"key": "peakLimitDetection", "type": "SubTasks.tasks.eventDetectionTask.SubTaskPeakLimitDetection"}, {"key": "SubTaskComputeVar", "type": "SubTasks.SubTaskComputeVar"}, {"key": "onlyAction", "type": "SubTasks.tasks.SubTaskAction"}, {"key": "next", "type": "SubTasks.tasks.SubTaskNext"}, {"key": "realTimeComputing", "type": "SubTasks.tasks.SubTaskRealTimeComputing"}, {"key": "manualRemove", "type": "SubTasks.SubTaskManualRemoveStrainGauge"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "type": "SubTasks.tasks.SubTaskResultVarCheck"}, {"key": "cmdConnection", "type": "SubTasks.SubTaskCmdConnection"}, {"key": "suspend", "type": "SubTasks.tasks.SubTaskSuspend"}, {"key": "combinedWaveStartControl", "type": "SubTasks.tasks.controlCommandTask.SubTaskCombinedWaveStartControl"}, {"key": "combinedWaveEndControl", "type": "SubTasks.tasks.controlCommandTask.SubTaskCombinedWaveEndControl"}, {"key": "creepCombinationWave", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepCombinationWave"}, {"key": "peakValleyCompensation", "type": "SubTasks.tasks.eventDetectionTask.SubTaskPeakValleyCompensation"}, {"key": "actuator", "type": "SubTasks.tasks.controlCommandTask.SubTaskActuator"}, {"key": "creepSpecificControl", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepControl"}, {"key": "creepCombinationInterval", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepCombinationInterval"}, {"key": "heatPreservationTime", "type": "SubTasks.tasks.flowLogicTask.SubTaskHeatPreservationTime"}, {"key": "creepPauseCommand", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepPauseCommand"}, {"key": "creepResumeCommand", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepResumeCommand"}, {"key": "creepLoadTime", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepLoadTime"}, {"key": "lowerComputer", "type": "SubTasks.tasks.flowLogicTask.SubTaskLowerComputer"}, {"key": "onSystemMsg", "type": "SubTasks.tasks.SubTaskOnSystemMsg"}, {"key": "online", "type": "SubTasks.tasks.SubTaskOnline"}, {"key": "highFrequencyDAQ", "type": "SubTasks.tasks.gatherDataTask.SubTaskHighFrequencyDAQ"}, {"key": "SubTaskEvalScript", "type": "SubTasks.SubTaskEvalScript"}, {"key": "creepOfflineRecovery", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepOfflineRecovery"}, {"key": "creepComputeSignalOffset", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepComputeSignalOffset"}, {"key": "creepControlCmd", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepControlCmd"}, {"key": "creepTestStopCheck", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepTestStopCheck"}, {"key": "creepFinishCheck", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepFinishCheck"}, {"key": "creepAlarmCheck", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepAlarmCheck"}, {"key": "creepDeviceStopMonitor", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepDeviceStopMonitor"}, {"key": "creepBitInMonitor", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepBitInMonitor"}, {"key": "creepSaveData", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepSaveData"}, {"key": "creepRealTimeComputing", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepRealTimeComputing"}, {"key": "creepSignalCheck", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepSignalCheck"}, {"key": "subTaskPlayBack", "type": "SubTasks.tasks.SubTaskPlayBack"}, {"key": "subTaskModify", "type": "SubTasks.tasks.SubTaskModify"}, {"key": "arrayDataHandler", "type": "SubTasks.tasks.gatherDataTask.SubTaskArrayDataHandler"}, {"key": "subTaskSyncStart", "type": " SubTasks.tasks.controlCommandTask.SubTaskSyncStart"}, {"key": "subTaskSyncAction", "type": " SubTasks.tasks.controlCommandTask.SubTaskSyncAction"}, {"key": "creepUpdateControlParams", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepUpdateControlParams"}, {"key": "globalExecuteAction", "type": "SubTasks.tasks.SubTaskGlobalExecuteAction"}, {"key": "globalDataCollection", "type": "SubTasks.tasks.SubTaskGlobalDataCollection"}, {"key": "creepTrendFiltering", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepTrendFiltering"}, {"key": "appPushData", "type": "SubTasks.tasks.SubTaskAppPushData"}, {"key": "updateParam", "type": "SubTasks.tasks.userInteractionTask.SubTaskUpdateParam"}, {"key": "highFreqCycleGen", "type": "SubTasks.tasks.gatherDataTask.SubTaskHighFreqCycleGen"}, {"key": "highFreqJiaoBianDAQ", "type": "SubTasks.tasks.gatherDataTask.SubTaskHighFreqJiaoBianDAQ"}, {"key": "creepChangeTempParams", "type": "SubTasks.tasks.flowLogicTask.SubTaskCreepChangeTempParams"}, {"key": "HighFrequencySubTask", "type": "SubTaskList.tasks.controlCommandTask.SubTaskHighFrequencyControl"}, {"key": "SubTaskStartOnline", "type": "SubTaskList.tasks.SubTaskStartOnline"}]}