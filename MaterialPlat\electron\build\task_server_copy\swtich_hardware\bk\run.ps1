# copy SignalVar.dll
# cd ..\SignalVar
# dotnet build
# copy .\bin\Debug\net6.0\SignalVar.dll  ..\TaskServer\

# # copy ScriptEngine.dll
# cd ..\ScriptEngine\
# dotnet build
# copy .\bin\Debug\net6.0\ScriptEngine.dll  ..\TaskServer\

# cd ..\HardwareConnector\
# dotnet build

# # copy FuncLibs.dll
# cd ..\FuncLibs\
# dotnet build
# copy ..\FuncLibs\bin\Debug\net6.0\FuncLibs.dll  ..\TaskServer\

# 保存当前目录
$originalDir = Get-Location

try {
    cd ..\TaskServer\bin\Debug\net6.0
    clear
    dotnet run --project ..\..\..\TaskServer.csproj    
}
finally {
    # 无论是否发生错误或中断，都切换回原目录 方便下次执行
    cd $originalDir
    Write-Host "已切换回原始目录: $originalDir"
}

