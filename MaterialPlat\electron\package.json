{"name": "zhongji-experiment-electron", "author": {"name": "zhong<PERSON> experiment"}, "homepage": "", "version": "1.0.4", "private": true, "main": "src/main.js", "dependencies": {"electron-is-dev": "^2.0.0", "electron-log": "^5.0.0", "express": "^4.17.1", "iconv-lite": "^0.6.3", "zeromq": "^6.0.4"}, "devDependencies": {"concurrently": "^7.6.0", "cross-env": "^7.0.3", "electron": "16.0.7", "electron-builder": "23.6.0", "wait-on": "^7.0.1"}, "scripts": {"start": "concurrently \"cd ../react-electron && npm run start\" \"wait-on http://127.0.0.1:3001 && electron .\"", "dev": "electron .", "build": "electron-builder --dir", "build:linux": "electron-builder --dir --linux --arm64", "build:windows": "electron-builder --dir --win", "pack": "electron-builder", "build-react": "cd ../react-electron && npm run build", "copy-react-build": "xcopy /E /I /Y ..\\react-electron\\build .\\build", "copy-package-resources": "xcopy /E /I /Y .\\package-resources\\* .\\build\\", "pack-java": "cd ../clj-backend && lein with-profile uberjar uberjar", "pack-copy-java": "copy ..\\clj-backend\\target\\uberjar\\clj-backend.jar .\\build", "init-java-db": "cd .\\build && .\\jdk11.0.17\\bin\\java.exe -jar clj-backend.jar reset && cd ..", "build-solution": "cd ..\\ && dotnet build", "replace-caculate": "mkdir .\\dist\\win-unpacked\\dllRoot\\caculateDlls\\system && copy ..\\dllRoot\\caculateDlls\\system\\cal_System.Reactive.dll .\\dist\\win-unpacked\\dllRoot\\caculateDlls\\system && mkdir .\\dist\\win-unpacked\\dllRoot\\caculateDlls\\jingtai && copy ..\\dllRoot\\caculateDlls\\jingtai\\cal_SoftPlant_Caculation.dll .\\dist\\win-unpacked\\dllRoot\\caculateDlls\\jingtai && mkdir .\\dist\\win-unpacked\\dllRoot\\caculateDlls\\rubian && copy ..\\dllRoot\\caculateDlls\\rubian\\cal_CreepCalc.dll .\\dist\\win-unpacked\\dllRoot\\caculateDlls\\rubian", "replace-subtask": "mkdir .\\dist\\win-unpacked\\dllRoot\\subTasks\\SubTaskList && move .\\dist\\win-unpacked\\resources\\app\\build\\taskServer\\SubTaskList.dll .\\dist\\win-unpacked\\dllRoot\\subTasks\\SubTaskList && copy .\\package-resources\\task_server_copy\\subTasks.json .\\dist\\win-unpacked\\dllRoot\\subTasks\\SubTaskList", "replace-hardware": "mkdir .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_simulator .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_static .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_dynamic .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_high_frequency .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_creep .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_temperature_control .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_copy .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_yingbian .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_plc .\\dist\\win-unpacked\\dllRoot\\hwcResource && move .\\dist\\win-unpacked\\resources\\app\\build\\hardware_artusb .\\dist\\win-unpacked\\dllRoot\\hwcResource", "pack-hardware": "cd ..\\HardwareConnector && dotnet clean && dotnet msbuild && dotnet publish -r win-x86 -c Release && cd ..\\electron", "pack-hardware-copymnq": "mkdir .\\build\\hardware_simulator && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_simulator", "pack-hardware-copyjt": "mkdir .\\build\\hardware_static && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_static", "pack-hardware-copydt": "mkdir .\\build\\hardware_dynamic && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_dynamic", "pack-hardware-copygp": "mkdir .\\build\\hardware_high_frequency && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_high_frequency", "pack-hardware-copyrb": "mkdir .\\build\\hardware_creep && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_creep", "pack-hardware-copywk": "mkdir .\\build\\hardware_temperature_control && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_temperature_control", "pack-hardware-copyyb": "mkdir .\\build\\hardware_yingbian && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_yingbian", "pack-hardware-copyplc": "mkdir .\\build\\hardware_plc && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_plc", "pack-hardware-copyartusb": "mkdir .\\build\\hardware_artusb && copy ..\\HardwareConnector\\bin\\Release\\net6.0-windows\\win-x86\\publish .\\build\\hardware_artusb", "pack-hardware-copy": "npm run pack-hardware-copymnq && npm run pack-hardware-copyjt && npm run pack-hardware-copydt && npm run pack-hardware-copygp && npm run pack-hardware-copyrb && npm run pack-hardware-copywk && npm run pack-hardware-copyyb && npm run pack-hardware-copyplc && npm run pack-hardware-copyartusb", "pack-else-hardware-copymnq": "copy ..\\..\\中机dll\\模拟器\\* .\\build\\hardware_simulator && copy ..\\Logger\\appsettings.json .\\build\\hardware_simulator && mkdir .\\build\\hardwareConnectors && xcopy /E /I .\\build\\hardware_simulator .\\build\\hardwareConnectors\\hardware_simulator", "pack-else-hardware-copyjt": "copy ..\\..\\中机dll\\静态\\* .\\build\\hardware_static && copy ..\\Logger\\appsettings.json .\\build\\hardware_static", "pack-else-hardware-copydt": "copy ..\\..\\中机dll\\动态\\* .\\build\\hardware_dynamic && copy ..\\Logger\\appsettings.json .\\build\\hardware_dynamic", "pack-else-hardware-copygp": "copy ..\\..\\中机dll\\高频\\* .\\build\\hardware_high_frequency && copy ..\\Logger\\appsettings.json .\\build\\hardware_high_frequency", "pack-else-hardware-copyrb": "copy ..\\..\\中机dll\\蠕变\\* .\\build\\hardware_creep && copy ..\\Logger\\appsettings.json .\\build\\hardware_creep", "pack-else-hardware-copywk": "copy ..\\..\\中机dll\\温控\\* .\\build\\hardware_temperature_control && copy ..\\Logger\\appsettings.json .\\build\\hardware_temperature_control", "pack-else-hardware-copyyb": "xcopy ..\\..\\中机dll\\应变仪\\* .\\build\\hardware_yingbian /E /I /Y && copy ..\\Logger\\appsettings.json .\\build\\hardware_yingbian", "pack-else-hardware-copyplc": "xcopy ..\\..\\中机dll\\PLC\\* .\\build\\hardware_plc /E /I /Y && copy ..\\Logger\\appsettings.json .\\build\\hardware_plc", "pack-else-hardware-copyartusb": "xcopy ..\\..\\中机dll\\AD卡\\* .\\build\\hardware_artusb /E /I /Y && copy ..\\Logger\\appsettings.json .\\build\\hardware_artusb", "pack-else-hardware-copy": "npm run pack-else-hardware-copymnq && npm run pack-else-hardware-copyjt && npm run pack-else-hardware-copydt && npm run pack-else-hardware-copygp && npm run pack-else-hardware-copyrb && npm run pack-else-hardware-copywk && npm run pack-else-hardware-copyyb && npm run pack-else-hardware-copyplc && npm run pack-else-hardware-copyartusb", "pack-task-server": "cd ../TaskServer && dotnet clean && dotnet build && dotnet publish -r win-x64 -c Release && cd ../electron", "pack-task-server-copy": "mkdir .\\build\\taskServer && copy ..\\TaskServer\\bin\\Release\\net6.0\\win-x64\\publish .\\build\\taskServer", "pack-else-task-copy": "copy ..\\TaskServer\\bin\\Debug\\net6.0\\subTasks.json .\\build\\taskServer && copy ..\\TaskServer\\bin\\Debug\\net6.0\\DeviceStatusCode.json .\\build\\taskServer && copy ..\\Logger\\appsettings.json .\\build\\taskServer", "build-all-resources": "npm run build-react && npm run copy-react-build && npm run copy-package-resources && npm run pack-java && npm run pack-copy-java && npm run pack-task-server && npm run pack-task-server-copy && npm run pack-else-task-copy && npm run init-java-db && npm run build-solution && npm run pack-hardware && npm run pack-hardware-copy && npm run pack-else-hardware-copy", "update-packed-time": "node updatePackedTime.js", "copy-doc-file": "copy ..\\..\\中机dll\\多硬件一定参照此文档配置.docx .\\dist\\win-unpacked\\dllRoot\\hwcResource", "build-all": "npm run update-packed-time && del-build.bat && npm run build-all-resources && npm run build && npm run replace-caculate && npm run replace-subtask && npm run replace-hardware && edit_subtaskjson.bat && npm run copy-doc-file", "pack-all": "npm run build-all-resources && npm run pack"}, "build": {"appId": "com.zj.app", "productName": "中机试验", "npmRebuild": true, "asar": false, "mac": {"category": "tools"}, "win": {"target": "nsis", "icon": "assets/icon.png"}, "linux": {"target": ["deb"], "icon": "assets/icon.png"}, "files": ["src/**/*", "assets/**/*", "resources/**/*", "build/**/*", "package.json"], "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true}, "directories": {"output": "dist"}}}