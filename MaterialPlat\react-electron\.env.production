PORT=8013
# 游览器设置
BROWSER=none
REACT_APP_WS_URL=ws://localhost:9876
REACT_APP_API_URL=http://localhost:3000/api/
REACT_APP_HARDWARE_API_URL=http://localhost:5000/api/
REACT_APP_TASK_SERVER_API_URL=http://localhost:5002/api/
REACT_APP_TASK_SERVER_API2_URL=http://localhost:5002/
SKIP_PREFLIGHT_CHECK=true

# MQ连接url taskServer->UI
REACT_APP_MQ_URL="tcp://localhost:5561"

# MQ连接url UI->taskServer
REACT_APP_MQ_UI_URL="tcp://localhost:5560"

# MQPublisher
REACT_APP_MQ_PUB="tcp://*:10247"

# MQSubscriber
REACT_APP_MQ_SUB="tcp://localhost:10247"

# 是否是开发环境
REACT_APP_IS_DEV=false

# # 禁用生产环境source map  但是会无法分析报错 暂时不开启

# GENERATE_SOURCEMAP=false