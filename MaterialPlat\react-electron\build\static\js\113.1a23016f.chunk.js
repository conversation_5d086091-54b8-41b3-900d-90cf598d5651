"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[113,4154],{4494:(i,e,t)=>{t.d(e,{A:()=>s});var l=t(80077),n=t(56543),d=t(8237),o=t(74117),a=t(10866),r=t(36950);const s=()=>{const{t:i}=(0,o.Bd)(),{getSample:e}=(0,a.A)(),t=(0,l.d4)((i=>i.project.sampleData)),s=(0,l.d4)((i=>i.project.optSample)),u=(0,l.d4)((i=>i.project.resultHistoryData)),v=(0,l.d4)((i=>i.template.resultData)),c=(0,l.d4)((i=>i.template.tableConfigData)),m=(0,l.d4)((i=>i.template.resultTestData)),h=(0,l.d4)((i=>i.global.unitList)),p=i=>{var t,l,d;const o=null===c||void 0===c?void 0:c.find((e=>(null===e||void 0===e?void 0:e.id)===i)),a=null===(t=e(s))||void 0===t?void 0:t.data,r=(null===o||void 0===o?void 0:o.param)||[],u=(null===o||void 0===o?void 0:o.setting)||{min_width:100,max_width:200,is_name:!0},p=(null===o||void 0===o?void 0:o.statistics)||[],b=null===o||void 0===o||null===(l=o.sample_param)||void 0===l?void 0:l.map((i=>i.parameter_id)),g=null===a||void 0===a?void 0:a.filter((i=>(null===b||void 0===b?void 0:b.includes(i.parameter_id))&&!i.hidden_flag));var x,f;return{data:null===(x=g,f=null===o||void 0===o?void 0:o.type,d=x&&x.length>0?[...null===v||void 0===v?void 0:v.filter((i=>{var e;return null===i||void 0===i||null===(e=i.display_modes)||void 0===e?void 0:e.includes(f)})).map((i=>{var e,t,l;return{...i,unit_name:null===h||void 0===h||null===(e=h.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.dimension_id))))||void 0===e||null===(t=e.units)||void 0===t||null===(l=t.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.unit_id))))||void 0===l?void 0:l.name}})),...null===x||void 0===x?void 0:x.map((i=>{var e,t,l,d;return{result_variable_id:i.parameter_id,variable_name:i.parameter_name,type:n.l4.RESULT_TYPE,code:null===i||void 0===i?void 0:i.code,abbreviation:null!==(e=i.abbreviation)&&void 0!==e?e:"",unit_id:i.units_id,dimension_id:i.dimension_id,unit_name:null===h||void 0===h||null===(t=h.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.dimension_id))))||void 0===t||null===(l=t.units)||void 0===l||null===(d=l.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.units_id))))||void 0===d?void 0:d.name,value:i.value}}))]:v)||void 0===d?void 0:d.filter((i=>(null===r||void 0===r?void 0:r.includes(i.result_variable_id))&&([n.l4.RESULT_TABLE,n.l4.LABEL].includes(i.type)||(null===m||void 0===m?void 0:m.map((i=>i.id)).includes(i.result_variable_id))))).sort(((i,e)=>(null===r||void 0===r?void 0:r.indexOf(null===i||void 0===i?void 0:i.result_variable_id))-(null===r||void 0===r?void 0:r.indexOf(null===e||void 0===e?void 0:e.result_variable_id)))),setting:u,statistics:p}};return{getColData:p,getTableData:i=>{const e=null===t||void 0===t?void 0:t.flatMap((i=>{const{children:e,name:t}=i;return e.map((i=>({...i,parentName:t})))})).filter((i=>!i.disabled&&i.status!==n.$y.READY)),{data:l,setting:d}=p(i);return{tableData:e.map((i=>{var e;const t={sample_color:i.color,sample_code:i.code,sample_name:i.name,sample_key:i.key},n=null!==(e=null===u||void 0===u?void 0:u[i.code])&&void 0!==e?e:[];return l.reduce(((e,t)=>{var l;const d=t.unit_id||t.units_id;if("display_modes"in t){var o,a;const{format_type:i,format_info:l,code:d,dimension_id:s,unit_id:u}=t,v=null!==(o=null===n||void 0===n?void 0:n.find((i=>i.code===d)))&&void 0!==o?o:{};let c=null===v||void 0===v?void 0:v.value;return"number"===typeof c&&(c=(0,r.jq)(i,(0,r.tJ)(c,s,u),(0,r._q)(i,l))),{...e,...t,result_variable_id:t.result_variable_id,[t.code]:c,errors:[...null!==(a=null===e||void 0===e?void 0:e.errors)&&void 0!==a?a:[],v.error&&{error:v.error,msg:v.errorMessage,code:v.code}].filter(Boolean)}}const s=null!==(l=i.data.find((i=>i.parameter_id===t.result_variable_id)))&&void 0!==l?l:{},u=(0,r.tJ)(null===s||void 0===s?void 0:s.value,s.dimension_id,null===s||void 0===s?void 0:s.units_id);return{...e,...t,result_variable_id:t.result_variable_id,[t.code]:(0,r.tJ)(u,null===s||void 0===s?void 0:s.dimension_id,d,null===s||void 0===s?void 0:s.units_id)}}),t)})),colData:l,setting:d}},getsStatisticTableData:e=>{var t;const{statistics:l,setting:n,data:o}=null!==(t=p(e))&&void 0!==t?t:{};return{colData:null!==o&&void 0!==o?o:[],setting:n,tableData:(0,d.Qu)({t:i}).filter((i=>null===l||void 0===l?void 0:l.includes(i.id))).sort(((i,e)=>(null===l||void 0===l?void 0:l.indexOf(i.id))-(null===l||void 0===l?void 0:l.indexOf(e.id))))}}}}},20113:(i,e,t)=>{t.r(e),t.d(e,{default:()=>O});var l=t(65043),n=t(80077),d=t(74117),o=t(16569),a=t(79889),r=t.n(a),s=t(14463),u=t(65694),v=t(45303),c=t(9339),m=t(43880),h=t(80231),p=t(8237),b=t(36950),g=t(4494),x=t(84),f=t(67299),_=t(75440),y=t(30212),j=t(16133),w=t(81143),k=t(68374);const D=w.Ay.div`
    background: ${k.o$.splitBack};
    /* border-radius: 8px; */
    padding: 10px;
    height: 100%;
    position: relative;
    .hidden-element {
        visibility: hidden;
        position: absolute;
        top: 0;
        width: 100%;
    }
    .table-first {
       background: rgba(66,111,255,0.1);
    }
    .table-layout table {
        font-size: ${i=>i.isFission?"12px":"auto"};
        min-width: auto !important;
        colgroup col:last-child {
            display: none;
        }
    }
    
    .ant-table-wrapper {
        font-size:${(0,k.D0)("14px")};
        .ant-table {
            .ant-table-header {
                font-size: ${i=>i.isFission?"10px":"auto"};
            }
        }

        .ant-table-wrapper .ant-table-thead >tr>th
        .ant-table-cell-scrollbar {
            background: rgba(66,111,255,0.1);
            box-shadow:none
         }
        .ant-table-thead {
            >tr >th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
                display: none;
            }
            >tr >th:first-child {
                background: rgba(215, 226, 255, 1);
            }
            >tr >th  {
                padding: 0 ${(0,k.D0)("4px")};
                overflow: hidden;
                background: rgba(66,111,255,0.1);
            }
        }
        .ant-table-body {
            >tr >td  {
                padding: 0 ${(0,k.D0)("4px")};
                background: rgba(66,111,255,0.1);
            }
        }
    }

    .columns-style {
        background: rgba(66,111,255,0.1);
    }
    .row-style {
        background: rgba(66,111,255,0.1)
    }
    .unit-name {
        opacity: 0.6;
    }
`,A=w.Ay.div`
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 25px;
    min-width: ${i=>i.min_width}px;
    max-width: ${i=>i.max_width}px;
    .circle-layout {
        display: flex;
        align-items: center;
        justify-content: center;
        .name {
            margin-left: 8px;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }
        .circle {
            border-radius: 50%;
            width: 1vh;
            height: 1vh;
        }
    }
`,R=w.Ay.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-width: ${i=>i.min_width-16}px;
    max-width: ${i=>i.max_width}px;
    .line-layout {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
    .omit { 
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

`,N=w.Ay.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-width: ${i=>i.min_width}px;
    max-width: ${i=>i.max_width}px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;

`,$=w.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }
    .disabled {
        cursor: no-drop;
    }
`;t(56543);var C=t(84674),F=t(68945),S=t(70579);const B=i=>{var e,t,l,n;let{setting:o,res:a,isFission:r,setHighlight:s,currentResult:u}=i;const{t:v}=(0,d.Bd)(),c=null!==(e=null===o||void 0===o?void 0:o.is_unit)&&void 0!==e&&e,m=null!==(t=null===o||void 0===o?void 0:o.is_name)&&void 0!==t&&t,h=null!==(l=null===o||void 0===o?void 0:o.is_abbr)&&void 0!==l&&l&&(null===a||void 0===a?void 0:a.abbreviation),p=null!==(n=null===o||void 0===o?void 0:o.is_line)&&void 0!==n&&n,b=function(){let i=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return(0,S.jsxs)(S.Fragment,{children:[p&&i?m&&h?(0,S.jsxs)("div",{className:"line-layout",children:[(0,S.jsx)("div",{className:"omit",children:null===a||void 0===a?void 0:a.variable_name}),(0,S.jsx)("div",{children:"("}),(0,S.jsx)(F.A,{text:null===a||void 0===a?void 0:a.abbreviation,variables:null===a||void 0===a?void 0:a.variables}),(0,S.jsx)("div",{children:")"})]}):m?(0,S.jsx)("div",{className:"omit",children:null===a||void 0===a?void 0:a.variable_name}):h?(0,S.jsx)(F.A,{text:null===a||void 0===a?void 0:a.abbreviation,variables:null===a||void 0===a?void 0:a.variables}):"":(0,S.jsxs)(S.Fragment,{children:[m&&(0,S.jsx)("div",{className:"omit",children:v(a.variable_name)}),h&&(0,S.jsx)("div",{className:"omit",children:(0,S.jsx)(F.A,{text:null===a||void 0===a?void 0:a.abbreviation,variables:null===a||void 0===a?void 0:a.variables})})]}),c&&(0,S.jsx)("div",{className:"unit-name omit",children:v(a.unit_name)}),i?null:(0,S.jsx)("div",{className:"omit",children:v(null===a||void 0===a?void 0:a.description)})]})};return(0,S.jsx)(R,{...o,onMouseMove:()=>{r||u&&(u.current=a)},children:(0,S.jsx)(C.A,{title:b(!1),children:b()})})},E=i=>{let{text:e,row:t,res:n,setting:d,currentResult:o}=i;const a=(0,l.useMemo)((()=>{var i,t,l,o,a,r,s,u;let v=null!==(i=null===d||void 0===d?void 0:d.min_width)&&void 0!==i?i:0;const c=d.is_abbr?null===(t=n.abbreviation)||void 0===t?void 0:t.replace(/{lo|}|{hi|}/g,""):"";let m=d.is_name?n.variable_name:"";d.is_line&&(m=`${m}${c}`);const h=d.is_unit?n.unit_name:"";if(Math.max(null!==(l=null===(o=m)||void 0===o?void 0:o.length)&&void 0!==l?l:0,null!==(a=null===c||void 0===c?void 0:c.length)&&void 0!==a?a:0,null!==(r=null===h||void 0===h?void 0:h.length)&&void 0!==r?r:0)>(null!==(s=null===(u=String(null!==e&&void 0!==e?e:""))||void 0===u?void 0:u.length)&&void 0!==s?s:0)&&!["NaN","0"].includes(String(null!==e&&void 0!==e?e:""))){const i=[m,c,h].reduce(((i,e)=>{const t=String(e);return t.length>i.length?t:i}),"");let e=(0,b.WI)(i);e=e<v?v:e,v=e>d.max_width?d.max_width:e}return v}),[d,e,n]),r=()=>{var i;if("number"===typeof(null!==e&&void 0!==e?e:0))return null!==e&&void 0!==e?e:"--";const l=null===(i=t.errors)||void 0===i?void 0:i.find((i=>(null===i||void 0===i?void 0:i.code)===n.code));return null!==l&&void 0!==l&&l.error?(0,S.jsx)("span",{style:{color:"red"},title:null===l||void 0===l?void 0:l.msg,children:e}):"string"===typeof e?e:""};return(0,S.jsx)(N,{...d,min_width:a,onMouseOver:()=>{o&&(o.current=n)},children:(0,S.jsx)(C.A,{title:r(),children:r()})})},H=i=>{let{currentResult:e}=i;const{t:t}=(0,d.Bd)();return(0,S.jsx)("div",{onMouseOver:()=>{e.current={}},children:t("\u6807\u6ce8")})},T=i=>{let{resDatas:e=[],setHighlight:t,highlight:l,isFission:n,currentResult:d,setting:o}=i;return{columns:[{title:(0,S.jsx)(H,{currentResult:d}),dataIndex:"sample_key",align:"center",className:"table-first",render:(i,e)=>(0,S.jsx)(A,{...o,onMouseOver:()=>{d.current={}},children:(0,S.jsx)(C.A,{title:e.sample_name,children:(0,S.jsxs)("div",{className:"circle-layout",children:[(null===e||void 0===e?void 0:e.sample_color)&&(0,S.jsx)("div",{className:"circle",style:{background:(null===e||void 0===e?void 0:e.sample_color)||"#FFF"}}),(0,S.jsx)("div",{className:"name",children:e.sample_name})]})})})},...e.map((i=>({title:(0,S.jsx)(B,{res:i,isFission:n,setHighlight:t,setting:o,currentResult:d}),dataIndex:i.code,key:i.result_variable_id,align:"center",className:l===i.result_variable_id?"columns-style":"",render:(e,l)=>(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("div",{style:{visibility:"hidden",height:0,padding:"0 2px"},children:(0,S.jsx)(B,{res:i,isFission:n,setHighlight:t,setting:o,currentResult:d})}),(0,S.jsx)(E,{text:e,row:l,res:i,setting:o,currentResult:d})]})})))]}},M=i=>{var e;let{domId:t,layoutConfig:l,handleResult:o}=i;const{openDialog:a}=(0,x.A)(),{t:r}=(0,d.Bd)(),u=(0,n.d4)((i=>i.global.roleHiddenDomClass)),c=(0,n.wA)(),{subContextMenuId:m}=(0,f.A)(),b=null===t||void 0===t||null===(e=t.split("edit-"))||void 0===e?void 0:e.at(-1);return(0,S.jsx)($,{children:(0,S.jsxs)(h.A,{domId:t,layoutConfig:l,children:[(0,S.jsx)("div",{className:`unique-content ${u}`,onClick:()=>o(!0),children:r("\u7f16\u8f91\u7ed3\u679c")}),(0,S.jsx)("div",{className:"unique-content",onClick:()=>{m(b),a({type:v.pn}),c({type:s.Bz,param:p.pz.TABLE_CONTROL})},children:r("\u6d4b\u8bd5\u7ed3\u679c\u8bbe\u7f6e\u8868")})]})})},L=(i,e)=>{let{item:{widget_id:t,data:a},title:s,id:v,isRightClick:h=!0,isFission:p=!1,layoutConfig:x,isPdfPrint:f=!1}=i;const w=(0,n.wA)(),{t:k}=(0,d.Bd)(),A=(0,n.d4)((i=>i.project.highlight)),R=(0,n.d4)((i=>i.project.optSample)),N=(0,n.d4)((i=>i.project.resultHistoryData)),$=(0,n.d4)((i=>i.project.sampleData)),C=(0,n.d4)((i=>i.template.resultData)),F=(0,n.d4)((i=>i.template.widgetData)),B=(0,n.d4)((i=>i.template.tableConfigData)),E=(0,n.d4)((i=>i.template.resultTestData)),{getTableData:H}=(0,g.A)(),{initResultData:L}=(0,j.A)(),[O,z]=(0,l.useState)("100%"),I=(0,l.useRef)(),q=(0,l.useRef)(),P=(0,l.useRef)(),[J,K]=(0,l.useState)([]),[U,Y]=(0,l.useState)(!1),[Q,W]=(0,l.useState)([]),[G,V]=(0,l.useState)({}),X=(0,l.useRef)();(0,l.useEffect)((()=>{const i=document.getElementById(v);return i&&(P.current=new ResizeObserver(r()(Z,800)),P.current.observe(i)),()=>{P.current&&(P.current.unobserve(i),P.current=null)}}),[]),(0,l.useEffect)((()=>{Z()}),[J]),(0,l.useEffect)((()=>{if(p&&a){const{colData:i,tableData:e,setting:t}=a;V(t);const{isPdfSelect:l,sampleList:n}=t;let d=e;f&&l&&(null===n||void 0===n?void 0:n.length)>0&&(d=e.filter((i=>n.includes(i.sample_key))));const{columns:o}=T({resDatas:i,setting:t,setHighlight:ei,highlight:A,currentResult:X,isFission:p});K(o),W(d)}else{const i=(0,b.Rm)(F,"widget_id",t);i&&ii(i)}}),[N,C,F,B,E,$,p,a,f]);const Z=()=>{const i=document.getElementById(v);var e,t,l,n;i&&z((null===i||void 0===i?void 0:i.offsetHeight)-(null===q||void 0===q||null===(e=q.current)||void 0===e?void 0:e.offsetHeight)-(null===I||void 0===I||null===(t=I.current)||void 0===t||null===(l=t.nativeElement)||void 0===l||null===(n=l.querySelector("thead"))||void 0===n?void 0:n.offsetHeight)-30)},ii=async i=>{if(i){const{tableData:e,colData:t,setting:l}=H(null===i||void 0===i?void 0:i.data_source);V(l);const{isPdfSelect:n,sampleList:d}=l;let o=e;f&&n&&(null===d||void 0===d?void 0:d.length)>0&&(o=e.filter((i=>d.includes(i.sample_key))));const{columns:a}=T({resDatas:t,setting:l,currentResult:X,setHighlight:ei,highlight:A,isFission:p});K(a),W(o),Z()}},ei=i=>{w({type:u.q5,param:i})},ti=function(){Y(arguments.length>0&&void 0!==arguments[0]&&arguments[0]),L()};return(0,S.jsxs)(D,{"data-component":"SampleTable",isFission:p,...G,children:[(0,S.jsx)(m.A,{title:s,ref:q}),(0,S.jsx)(c.A,{className:"table-layout",rowKey:(0,l.useCallback)((i=>`${null===i||void 0===i?void 0:i.result_variable_id}-${null===i||void 0===i?void 0:i.sample_code}`),[]),ref:(0,l.useCallback)((i=>{I.current=i,e&&(e.current=i)}),[]),columns:J,size:"small",dataSource:Q.length>0?Q:[{}],bordered:!0,pagination:!1,tableLayout:"fixed",scroll:(0,l.useMemo)((()=>({y:O,x:"max-content"})),[O]),rowClassName:(0,l.useCallback)((i=>(null===i||void 0===i?void 0:i.sampleKey)===(null===R||void 0===R?void 0:R.key)?"row-style":""),[R])}),U&&X.current&&(0,S.jsx)(_.A,{title:k("\u7f16\u8f91\u7ed3\u679c"),open:U,onCancel:()=>ti(),footer:null,children:(0,S.jsx)(y.A,{resultIsModal:U,result_variable_id:X.current.result_variable_id,handleCancel:()=>ti(!1),isEdit:!0})}),h&&(0,S.jsx)(M,{domId:v,layoutConfig:x,handleResult:function(){let i=arguments.length>0&&void 0!==arguments[0]&&arguments[0];null!==X&&void 0!==X&&X.current&&"format_type"in X.current?ti(i):o.Ay.error(k("\u4e0d\u662f\u7ed3\u679c\u53d8\u91cf"))}})]})},O=(0,l.forwardRef)(L)},43880:(i,e,t)=>{t.d(e,{A:()=>u});var l=t(65043),n=t(74117),d=t(81143),o=t(68374);const a=d.Ay.div`
    .title-layout {
        display: flex;
        align-items: center;
        margin-bottom: ${(0,o.D0)("10px")};
        .pillar {
            width: ${(0,o.D0)("8px")};
            height: ${(0,o.D0)("20px")};
            background: #0091FF;
            border-radius: 2px;
        }
        .title {
            margin-left: 0.5vw;
            font-size: 14px;
            font-weight: 600;
            color: #333333;
            line-height: 20px;
        }
    }
`;d.Ay.div`
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction:column ;
    .circle {
        border-radius: 50%;
        width: 1.5vh;
        height: 1.5vh;
    }
`;var r=t(70579);const s=(i,e)=>{let{title:t}=i;const{t:l}=(0,n.Bd)();return(0,r.jsx)(a,{children:(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"title-layout",ref:e,children:[(0,r.jsx)("div",{className:"pillar"}),(0,r.jsx)("div",{className:"title",children:l(t)})]})})})},u=(0,l.forwardRef)(s)},84674:(i,e,t)=>{t.d(e,{A:()=>s});var l=t(65043),n=t(96651),d=t(81143);t(68374);const o=d.Ay.div`

  
`;t(4554);var a=t(70579);const r=(i,e)=>{const{children:t,title:l}=i;return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(o,{children:(0,a.jsx)(n.A,{...i,destroyTooltipOnHide:{keepParent:!1},ref:e,children:t})})})},s=(0,l.forwardRef)(r)}}]);
//# sourceMappingURL=113.1a23016f.chunk.js.map