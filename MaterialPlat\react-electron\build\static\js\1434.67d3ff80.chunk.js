"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[1434],{11434:(e,i,t)=>{t.r(i),t.d(i,{default:()=>Xe});var a=t(65043),l=t(80077),d=t(65239),n=t.n(d),o=t(56434),r=t.n(o),s=t(93950),c=t.n(s),u=t(74117),v=t(16569),p=t(63942),h=t(89073),m=t(35964),f=t(8354),x=t(6051),b=t(76399),g=t(39470),y=(t(70588),t(79806)),A=t(75440),w=t(55505),k=t(70579);const C=e=>{let{t:i,handleSelected:t}=e;return[{title:i("\u52a8\u4f5cid"),dataIndex:"action_id",width:280,key:"action_id"},{title:i("\u52a8\u4f5c\u540d\u79f0"),dataIndex:"action_name",key:"action_name"},{title:"\u64cd\u4f5c",dataIndex:"action_id",key:"action_id",render:(e,i)=>(0,k.jsx)(x.A,{size:"middle",children:(0,k.jsx)("a",{onClick:()=>t(e),children:"\u9009\u62e9"})})}]},j=(e,i)=>{let{handleSelectedAction:t=e=>console.log(e)}=e;const{t:d}=(0,u.Bd)(),n=(0,l.d4)((e=>e.template.actionList)),[o,r]=(0,a.useState)(!1);(0,a.useImperativeHandle)(i,(()=>({open:()=>{r(!0)}})));return(0,k.jsx)(A.A,{open:o,onCancel:()=>{r(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:(0,k.jsx)(y.A,{rowKey:"action_id",columns:C({t:d,handleSelected:e=>{t(e),r(!1)}}),dataSource:n})})},S=(0,a.forwardRef)(j);var T=t(94817),I=t(4554),N=t(20790),E=t(36950),D=t(67208),R=t(18650),_=t(45303),U=t(84),L=t(67299),B=t(34458),F=t(33981),q=t(65913),P=t(80231),M=t(81143),Q=t(68374);const O="38px",V=(M.Ay.div`
    display: flex;
    > div {
        cursor: pointer;
        font-weight: 600;
        color: #6D8DE1;
        line-height: 17px;
    }
    .name {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: ${O};
        width: 10vw;
    }
    .opt {
        font-weight: 600;
        color: #29448B;
        line-height: 17px;
        background: rgba(255,255,255,0.26);
        box-shadow: 0px 2px 4px 0px rgba(7,26,112,0.1);
    }
`,M.Ay.div`
    display: flex;
    height: 100%; 
    .ant-card {
        border-radius: 0; 
        .ant-card-head {
            border-radius: 0; 
        }
    }
    .process {
        height: 100%; 
        .ant-card-small {
            height: 100%;
            box-sizing: border-box;
            border: 0;
            >.ant-card-head {
                padding: 0;
                background: #E9EEFF;
                box-sizing: border-box;
                box-shadow: 0px 2px 4px 0px rgba(7,26,112,0.1);
                height: ${O};
            }
             >.ant-card-body {
                box-sizing: border-box;
                background: #F4F6FF;
                // 减去顶栏的tab高度, 减去scrollbar高度(不知道为什么overlay不生效) 
                /* height: calc(100% - ${O} - 5px); */
                box-shadow: 0px 0px 10px 0px rgba(50,106,255,0.04);
            }
        }
        
    }
    .leftBar {
        >.ant-card-body {
            padding: 0;
            height: calc(100% - ${O})!important;
        }
    }
    .ant-card-small {
        >.ant-card-body {
            padding: 0;
            height: 100%;
        }
    }
    .card-title {
        color: #2D4586;
        font-weight: 600;
    }
    .title-right {
        cursor: pointer;
    }
    .disabled {
        cursor: no-drop;
    }
    .download {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .download-title {
        font-size: 12px;
      }
    }
    .add-icon {
        width: 14px;
        height: 14px;
    }
    
    
    .process-right-card{
        >.ant-card-body{
            flex: 1;
            overflow: auto;
        }
    }
`),Y=M.Ay.div`
    position: sticky;
    top: 0;
    overflow-y: auto;
    height: 100%; 
    /* width: ${"200px"}; */
    box-sizing: border-box;
    

    .item {
        display: inline-block;
    }

    .img-icon {
        width: 14px;
        height: 14px;
    }

    .ant-tree-indent {
        width: 0px;
    }
`,K=M.Ay.div`
    display: flex;
    overflow: overlay;
    justify-content: center;
    height: 100%; 
    /* border-radius: 8px; */
    .execute-process {
        overflow: overlay;
        width: 100%;
        height: 100%;
    }
`,H="90px",W=M.Ay.div`
    position: relative;
    box-sizing: border-box;
    text-align: center;
    display: grid;
    padding: 20px;
    .layout {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    svg.allow {
        /* transform: scaleX(2); */
        path {
            stroke-width: 3;
        }
    }
`,$=M.Ay.div`
    margin-top: 50px;
`,z=(0,M.Ay)($)`
    border-radius: 6px;  
    border: 1px solid #5A78FF;
    height: 65px;
    width: ${H};
    font-size: 14px;
    flex-shrink: 0;
    position: relative;
    outline: 1px solid #5A78FF;
    outline-offset: -1px;
    transition: all .3s linear;

    &.select {
        background: rgba(90,120,255,0.1);
    }
    &.delete {
        background:  rgba(255,140,0,0.6);
    }
    &.running {
        background: 
            linear-gradient(90deg, rgba(50,205,50,0.8) 50%, transparent 0) repeat-x,
            linear-gradient(90deg, rgba(50,205,50,0.8) 50%, transparent 0) repeat-x,
            linear-gradient(0deg, rgba(50,205,50,0.8) 50%, transparent 0) repeat-y,
            linear-gradient(0deg, rgba(50,205,50,0.8) 50%, transparent 0) repeat-y;
            background-size: 4px 2px, 4px 2px, 2px 4px, 2px 4px;
        background-position: 0 0, 0 100%, 0 0, 100% 0;
        // 开启动画消耗比较高
        /* animation: linearGradientMove .3s infinite linear; */
        outline: none;
        border: 1px solid rgba(50,205,50,0.8);
        background-color: rgba(50,205,50,0.1);
    }
    @keyframes linearGradientMove {
        100% {
            background-position: 4px 0, -4px 100%, 0 -4px, 100% 4px;
        }
    }
    &.abort {
        outline: none;
        border: 1px solid red;
        background-color: rgba(255,0,0,0.1);
    }
    &.pause {
        outline: none;
        border: 1px solid #FFA500;
        background-color: rgba(255,165,0,0.1);
    }
    &.finish {
        outline: none;
        border: 1px solid #A9A9A9;
        background-color: rgba(169,169,169,0.1);
    }
    .error {
        color: red;
        font-size: 20px;
        position: absolute;
        top: -12px;
        right: -4px;
    }
    .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .icon {
        img{
            width: 20px;
            height: 20px;
        }
    }
`,G=(0,M.Ay)($)`
    border-radius: 2px;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    >.title-box {
        height: 50px;
        width: ${H};
        border-radius: 6px; 
        border: 1px solid #5A78FF;

        &.if-else {
            height: 80px;
            border: none;
            background-image: url(${"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAABQCAYAAACZM2JkAAAACXBIWXMAAJ17AACdewE8n3fEAAAFf0lEQVR4nO3dZ4hcVRiH8V+KJsjGlkQMKioRxWisaIIiolhAFMGCgqIRP9hL7BqiJoq9rL2AaCAqqFgRJRgDFjDGhhCxRPSDJWIUJBoUSdYPr0Pazu69M+eWmd3n487OOW/+uTn3vc85Mxmx+PdVOogeHImpeAy/VFtOdkZXXUAOtsAZuB4TsDPm4PsKa8rMyKoLyMhEzMStImSYgQewV0U15aITrugdcTXOxqYbvHYstsY1eLfkunJR9yt6Cu7AOTYOucFBeBTHl1VUK9Q56Om4HycbvM4peEhc9bWkjkGPwTF4BIdjRMb3bYd7MUt0J7WibkH34BSxFOwte8gNxuFmcdOclLa09qhT0FvhPLEE7CB/yOtyobi6d01QVxLqEvQ2uA63S/fP/hTxl3ZAovHaog5Bb487cYX2ruL+OEKs9UcmHjc3VQe9G54QT3xFsb8I+9QC5xiUqoIehWl4HkeVMN9k0SperHk/XihVBD0Wx+F1IYfKYqJ4+JmL8SXOi/KD3gxn4iUV/GFFj3417sJOZU5cZtDjhJN4tMQ5mzFDyUKqrKAn4D7MLmm+LBwrbpKHlDFZGUHviPk4q4S58lKakCoy6JHYA6/g6ALnaZdShFRRQW+KQ/GWcBZ1p3AhVUTQY3Ei3sC2BYxfFIUKqdRB9+ACPCNaqU6kECGVMuitcKPoUTud5EIqVdCTRF96eaLx6kBSIZUi6F0wD6clGKtuJBNS7QQ9CgfiOTXQkAWSREi1GnRjX+957Nvq5B1E20KqlaB7hBiaL7achgptCam8QY8XOyEPYnPpd0Q6gRlaEFJ5gt4eN+EGbJJnki4kt5DKGvRuuEfsUg8T5BJSWYKeLpr3k9ooqlvJLKQGCnqM2HJ6BIcZmutxFjIJqWZBj8Pp1i76Ve+W151BhVR/AW4t1uK7hLQfDjk7TYXUhiFOEvt6c7Fl4WV1J/0KqXWDnoxbhBjqVMVZFzYSUo2gp6JXNOPDS0Ua1hNSo8V+3lwhiIZJy2TRUOw6YvHvq/qqrmYoMBJ74puqC+liVmD2SCzFfni72nq6km+Fx765ceP7U5zqfKqqirqQT0Vf/SzrdxirxWmiG8uvqetYiPPxZuMH/bVyc9Tz+Fan8BwuwQfr/rBZzzxPNN3D5OMhsQuzdMMXmgXdh0XYHcuLq6trWClOyjb9EoCBngLX4EvR/n2curIu4kexvXcPfm32S1ket3/DwXg5SVndxVLRvj2OAb/4JKvX+AcniMPkwwTv4SK8mOWX8wikPlyKy/LX1HW8KrJYlPUNrZi6XnEsd6jyJK6U877VStB9Yr2eJp4ohwr/iI9Qz8LXed/cqnteg49E+5d70g5kBa4V51p+bmWAdiT/GvwgBHc3C6ll4knvXvzV6iApdlO6WUgtsfYTDG2RatuqG4XUAmHfFqQYLPX+YLcIqYYY+jDVgEVsxHaykFolNlRnCv2QjCKC7lQhtRy3Cfv2U+rBizpa0GlCapm4v9wkTFxyij7D0QlC6jPRIz9W5CRlHJaps5B6R7ibF4qeqKxTSXUUUq+IziKzGGqHso9/9apeSK0WD1czxbJRCmUHXbWQWom7xY7Id2VOXMWBxqqE1HLxJd/Xi5t0qVR1crRsIbVMXMW94uZcOlUf0S1DSH0ixNDTBc4xKFUHTbFCaiHOlUgMtUMdgm6QWki9IK7kJQnHbJk6BU06IfWwOAbwVYKxklC3oNsVUitFV3GdFreciqJuQdO6kGqcGLoNfxRQV1vUMegGeYTUF9aeGPq3wJpaps5Bk01IvS/HiaGqqHvQDCykXvv/tdrvwndC0A16rS+knsRV4nG+9nTCf+HUoE9cwfuILyZ5RsliqB06KWjiRve5+Lje3xXXkov/AH6TBiA3UhG6AAAAAElFTkSuQmCC"});
            background-repeat: no-repeat;
            background-size: 100% 100%;
            padding-top: 15px;
            background-color: initial!important;
            &.select {
                background-image: url(${"data:image/png;base64,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"});
            }
            &.delete {
                background-image: url(${"data:image/png;base64,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"});
            }
        }
        
        &.loop {
            border: none;
            background: rgba(238, 180, 34, 0.3);
            border-radius: 50%;
        }
        &.parallels {
            border: none;
            background: rgba(50, 205, 50, 0.2);
        }
        &.break {
            border: none;
            background: rgba(255, 20, 147, 0.2);
        }

        &.select {
            background-color: rgba(90,120,255,0.1);
        }
        &.delete {
            background-color: rgba(255,140,0,0.6);
        }
        
    }

    .subprocess {
        display: flex;
        justify-items: center;
    }
`,X=(0,M.Ay)($)`
    border-radius: 2px;
    border: 1px dashed #5A78FF;
    padding: 20px 20px 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    >.title-box {
        width: ${H};
        /* border: 1px solid #000; */
    }

    >.end {
        position: absolute;
        border: 1px solid #000;
        bottom: 0;
        opacity: 0;
    }
    .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
`,Z=M.Ay.div`
    /* width: ${"365px"}; */
    width: 100%;
    //流程图中，选择并行时，右侧内容多时不能滚动
    //height: 100%;
    box-sizing: border-box;
    position: sticky;
    top: 0;
    right: 0;
    /* padding: 10px; */
    
    >.row {
        display: flex;
        margin-top: 16px;
        >.label {
            min-width: 150px;
            &::after {
                content: ':'
            }
        }
    }
`,J=M.Ay.div`
    position: fixed;
    z-index: 100000;
    user-select: none;
    display: none;
    background: #FFF;
    min-width: 120px;
    padding: 5px;
    border: 1px solid rgba(220 ,220, 220,1);
    box-shadow: 5px 5px 3px -2px rgba(0, 0, 0,0.4);
    
    .list {
        border-radius: ${(0,Q.D0)("4px")};
        min-width: ${(0,Q.D0)("100px")};
        overflow: hidden; /* 处理圆角 */
        .item {
            font-size: 14px;
            word-break: keep-all; /* 很重要，否则会换行 */
            cursor: pointer;
            padding: 2px;
            &:hover {
                background-color: rgba(20, 115, 245,0.4);
            }
        }
        .disabled {
          cursor: no-drop;
        }
    }
`,ee=M.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .disabled {
        cursor: no-drop;
    }
    .unique-content {
        padding: 2px;
    }
    .disabled {
          cursor: no-drop;
     }
`,ie=M.Ay.div`
    position: absolute;
    bottom: 10px;
    right: 10px;
    .fixed-content {
        width: 24px;
        min-height: 80px;
        background: rgba(90,120,255,0.27);
        border-radius: 12px;
        border: 1px solid rgba(90,120,255,0.45);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 2px;
        .ant-divider-horizontal {
            margin: 4px 0;
            background-color: rgba(90,120,255,0.43);
        }
        >img {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        .disabled {
            cursor: no-drop;
        }
    }

`,te=e=>{let{domId:i,layoutConfig:t,handleRemoveClick:a,handleSaveAs:d,handleBatchRemoveClick:n,handleBatchSameRemoveClick:o,handleBatchChildRemoveClick:r,handleCopyClick:s,handleOpenSelectActiveDialog:c,handleMainCopyClick:v}=e;const{t:p}=(0,u.Bd)(),{menuData:h}=(0,l.d4)((e=>e.split)),{subTaskSample:m}=(0,l.d4)((e=>e.subTask)),{subMenu:f}=(0,L.A)(),x=()=>{var e;return["start","end"].includes(null===h||void 0===h||null===(e=h.param)||void 0===e?void 0:e.idPrefix)},b=()=>{var e;return["parallels","loop","if-else"].includes(null===h||void 0===h||null===(e=h.param)||void 0===e?void 0:e.idPrefix)};return(0,k.jsx)(ee,{children:(0,k.jsx)(P.A,{domId:i,layoutConfig:t,onClose:()=>f(null),children:h&&(0,k.jsxs)(k.Fragment,{children:[!x()&&(0,k.jsx)("div",{className:"unique-content "+(m?"disabled":""),onClick:()=>{x()||a()},children:p("\u5220\u9664")}),!(b()||x())&&(0,k.jsx)("div",{className:"unique-content "+(m?"disabled":""),onClick:()=>{b()||x()||o()},children:p("\u5220\u9664\u76f8\u540c\u5b50\u4efb\u52a1")}),!x()&&(0,k.jsx)("div",{className:"unique-content "+(m?"disabled":""),onClick:()=>{x()||v()},children:p("copyId")}),!x()&&(0,k.jsx)("div",{className:"unique-content "+(m?"disabled":""),onClick:()=>{x()||s()},children:p("copy\u5b50\u4efb\u52a1Id")})]})})})};var ae=t(74712),le=t(83720),de=t(97326),ne=t(56543),oe=(t(64798),t(54962),t(52136));t(26790);const{Panel:re}=de.A,{Panel:se}=de.A,{Panel:ce}=de.A,{Panel:ue}=de.A,{Panel:ve}=de.A,{Panel:pe}=de.A,{TextArea:he}=le.A,{Panel:me}=de.A;var fe=t(46085);const xe=e=>e?`${e}-${crypto.randomUUID()}`:`${crypto.randomUUID()}`,be=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...ae.QE.\u5b50\u8fdb\u7a0b,name:"\u65b0\u5206\u652f",important:!1,id:xe(ae.QE.\u5b50\u8fdb\u7a0b.preFix),children:[],...e}},ge=e=>{const{type:i,name:t,idPrefix:a,imgUrl:l}=e,[d,n]=i.split(":"),o=xe(a);if(d===ae.ts.\u57fa\u672c\u63a7\u4ef6)return{...e,id:o,code:`${null===e||void 0===e?void 0:e.code}${(0,E.vx)(10)}`};if(d===ae.ts.\u6761\u4ef6\u63a7\u4ef6){if(i===ae.QE.\u6761\u4ef6.type)return{...e,id:o,name:`${t}-${(10*Math.random()).toFixed(1)}`,children:(null===e||void 0===e?void 0:e.children)||[be({name:"true"}),be({name:"false"})]};if(i===ae.QE.\u5e76\u884c.type)return{...e,id:o,name:`${t}-${(10*Math.random()).toFixed(1)}`,params:[],children:(null===e||void 0===e?void 0:e.children)||[be({name:"\u5e76\u884c\u5206\u652f1"}),be({name:"\u5e76\u884c\u5206\u652f2"})]};if(i===ae.QE.\u5faa\u73af.type)return{...e,id:o,name:`${t}-${(10*Math.random()).toFixed(1)}`,children:(null===e||void 0===e?void 0:e.children)||[be({name:"\u5faa\u73af\u5206\u652f"})]};if(i===ae.QE.\u5faa\u73af\u7ec8\u6b62.type)return{...e,id:o,name:`${t}-${(10*Math.random()).toFixed(1)}`};if(i===ae.QE.\u91cd\u65b0\u5f00\u59cb.type)return{...e,id:o,name:`${t}-${(10*Math.random()).toFixed(1)}`}}},ye=(e,i)=>{const{editData:t,handleConfirmChange:l}=e,{t:d}=(0,u.Bd)(),[n,o]=(0,a.useState)([]),[r,s]=(0,a.useState)();(0,a.useEffect)((()=>{s(null===t||void 0===t?void 0:t.name),null!==t&&void 0!==t&&t.params&&o([...t.params])}),[t]);const c=e=>{l({data:{...t,params:null!==e&&void 0!==e?e:n},isDb:!1})},v=()=>{o([...t.params])};return(0,a.useImperativeHandle)(i,(()=>({handleSubmit:c,handleReset:v}))),(0,k.jsxs)(oe.LF,{children:[(0,k.jsx)("div",{className:"title",children:d("\u6761\u4ef6\u7f16\u8f91\u6846")}),(0,k.jsxs)("div",{className:"form",children:[(0,k.jsxs)("div",{className:"row",children:[(0,k.jsx)("div",{className:"label",children:d("\u663e\u793a\u540d\u79f0")}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(le.A,{type:"text",value:r,onChange:e=>{s(e.target.value)},onBlur:()=>{l({data:{...t,name:r},isDb:!1})}})})]}),n.map((e=>(0,k.jsxs)("div",{className:"row",children:[(0,k.jsx)("div",{className:"label",children:e.key}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(fe.im,{value:e.value,width:"20vw",module:fe.et["\u5b50\u4efb\u52a1\u811a\u672c(\u901a\u7528)"],onChange:i=>((e,i)=>{const t=n.map((t=>t.key===i?{...t,value:e}:t));o(t),c(t)})(i,e.key)})})]},e.key)))]})]})},Ae=(0,a.forwardRef)(ye);var we=t(32513),ke=t(92275),Ce=t(97063);const je=e=>{let{targetId:i,containerId:t,positionSum:a}=e;const l=document.querySelector(`[id="${t}"]`).querySelector(`[id="${i}"]`);if(!l)return;const{offsetHeight:d,offsetLeft:n,offsetTop:o,offsetWidth:r,clientLeft:s,clientTop:c}=l,u=(null===a||void 0===a?void 0:a.left)||0,v=(null===a||void 0===a?void 0:a.top)||0,p=(null===a||void 0===a?void 0:a.height)||d,h=(null===a||void 0===a?void 0:a.width)||r;return l.offsetParent.id===t?{topMid:{x:n+h/2+u,y:o+v},bottomMid:{x:n+h/2+u,y:o+p+v}}:je({targetId:l.offsetParent.id,containerId:t,positionSum:{left:u+n+1,top:v+o+1,height:p,width:h}})},Se=e=>{let{containerId:i,sourceDomId:t,targetDomId:a,subProcessId:l,pathStrokeColor:d="#9DD4FF",pathStrokeArrowsColor:n="#000",pathStrokeWidth:o=1,arrowSize:r=3,svgClassNamesArr:s=[(0,ke.Rz)()],connectorPositions:c=["bottom","top"],prefix:u}=e;try{const e=je({targetId:t,containerId:i}),v=je({targetId:a,containerId:i});if(!e||!v)return;const[p,h]=c,m=e.bottomMid.x,f=e.bottomMid.y,x="top"===h?v.topMid.x:v.bottomMid.x,b="top"===h?v.topMid.y:v.bottomMid.y,g=document.createElementNS("http://www.w3.org/2000/svg","svg"),y=document.createElementNS("http://www.w3.org/2000/svg","path"),A=document.createElementNS("http://www.w3.org/2000/svg","path");if(g.style.position="absolute",g.style.overflow="visible",g.style.top=f,g.style.height=b-f,Math.abs(x-m)<=1||m===x){const e=100;g.style.width=e,g.style.left=m-e/2;const i=e/2,t=b-f;y.setAttribute("d",`M ${i} 0\n                L ${i} ${t-r}`),A.setAttribute("d",`M ${i} ${t}\n                L ${i-r} ${t-2*r}\n                L ${i+r} ${t-2*r}\n                Z\n                `)}else{g.style.left=m-o;const e=x-m+o,i=b-f;y.setAttribute("d",`M ${o} 0\n                L ${o} ${i/2}\n                L ${e} ${i/2}\n                L ${e} ${i-r}`),A.setAttribute("d",`M ${e} ${i}\n                L ${e-r} ${i-2*r}\n                L ${e+r} ${i-2*r}\n                Z\n                `),y.setAttribute("reverse",e<0),A.setAttribute("reverse",e<0)}y.setAttribute("fill","none"),y.setAttribute("stroke",d),y.setAttribute("stroke-width",o),A.setAttribute("fill",n),g.classList.add(...s),g.setAttribute(`data-${u}-source-id`,t),g.setAttribute(`data-${u}-target-id`,a),l&&g.setAttribute(`data-${u}-subprocess-id`,l),g.append(y),g.append(A),document.getElementById(i).append(g),g.style.width<y.getBBox().width&&(g.style.width=y.getBBox().width+10)}catch(v){console.error("error happens when draw SVG",v)}},Te=e=>{let{pathStrokeColor:i="#9DD4FF",pathStrokeArrowsColor:t="#000",pathStrokeWidth:a=1,arrowSize:l=3,containerId:d,data:n,sourceDomId:o,targetDomId:r,prefix:s,name:c=(0,ke.UM)()}=e;try{const e=document.createElementNS("http://www.w3.org/2000/svg","svg"),u=document.createElementNS("http://www.w3.org/2000/svg","path"),v=document.createElementNS("http://www.w3.org/2000/svg","path"),p=je({targetId:o,containerId:d}),h=je({targetId:r,containerId:d}),m=p.bottomMid.x,f=p.bottomMid.y,x=h.topMid.x,b=h.topMid.y,g=document.getElementById(d).scrollWidth/2,y=20;e.style.left=m-a-g/2,e.style.width=g;const A=x-m+a,w=b-f;u.setAttribute("d",`M ${g/2+y} 0\n            L ${g/2+y} ${w/2}\n            L ${A+g/2+y} ${w/2}\n            L ${A+g/2+y} ${w-l}`),v.setAttribute("d",`M ${A+g/2+y} ${w}\n            L ${A-l+g/2+y} ${w-2*l}\n            L ${A+l+g/2+y} ${w-2*l}\n            Z\n            `),u.setAttribute("fill","none"),u.setAttribute("stroke",i),u.setAttribute("stroke-width",a),v.setAttribute("fill",t),e.setAttribute("class",(0,ke.Rz)(s)),e.setAttribute("name",c),e.style.position=n.style.position,e.style.top=n.style.top,e.style.marginLeft=`-${y}px`,e.style.height=b-f,e.append(u),e.append(v),document.getElementById(d).append(e)}catch(u){console.error("error happens when draw SVG",u)}};var Ie=t(51399),Ne=t(85643);const Ee=e=>{const{type:i,id:t,description:a,name:d,nameShow:n,isCorrect:o,children:r,imgUrl:s,hideInBar:c,selectId:v,clickBasicFunc:p,clickCombineTitleFunc:h,startOrEndFunc:m,clickFoldFunc:f,isFold:x=!0,prefix:b,propsId:g,clickContextMenuFunc:y,dragStartFunc:A,dragEndFunc:w,ctrlDelData:C,idPrefix:j}=e,[S]=i.split(":"),{subTaskStatus:T}=(0,l.d4)((e=>e.subTask)),{subMenu:I}=(0,L.A)(),{t:N}=(0,u.Bd)(),E=()=>{if(T&&(null===T||void 0===T?void 0:T.length)>0){const i=null===T||void 0===T?void 0:T.find((e=>(null===e||void 0===e?void 0:e.SubTaskID)===t));if(i){var e;const a=t===(null===i||void 0===i?void 0:i.SubTaskID),l=null===i||void 0===i||null===(e=i.UIParams)||void 0===e?void 0:e.status;if((l===ne.Y3.START_RUNNING||l===ne.Y3.RESUME_MSG)&&a)return"running";if(l===ne.Y3.ABORT_MSG&&a)return"abort";if(l===ne.Y3.PAUSE_MSG&&a)return"pause";if(l===ne.Y3.FINISH_MSG&&a)return"finish"}}return""},D=i=>{i.stopPropagation(),I({id:t,param:e})};return S===ae.ts.\u57fa\u672c\u63a7\u4ef6?(0,k.jsxs)(z,{ifElse:j,id:t,onClick:e=>{["start","end"].includes(j)?m(e,t):p(e,n||d)},className:`${t===v?"select":""} ${E()} ${C.includes(t)&&"delete"}`,onMouseUp:D,onContextMenu:i=>c?null:y(i,t,e),onDragStart:i=>A(i,t,e),onDragEnd:i=>w(i,t,e),draggable:!0,title:`${a||d}`,children:[(0,k.jsxs)("div",{className:"title-box",children:[!o&&!["\u5f00\u59cb","\u7ed3\u675f"].includes(d)&&(0,k.jsx)("span",{className:"error",children:"*"}),(0,k.jsx)("div",{className:"title",title:n||d,children:N(n||d)}),(0,k.jsx)("div",{className:"icon",children:["\u5f00\u59cb","\u7ed3\u675f"].includes(d)?(0,k.jsx)("i",{className:s}):(0,k.jsx)("img",{src:s||R.Lt,alt:""})})]}),r]},t):S===ae.ts.\u6761\u4ef6\u63a7\u4ef6?(0,k.jsxs)(G,{id:t,children:[(0,k.jsxs)("div",{id:`${(0,ke.ZW)(b)}${t}`,className:`title-box ${j===ae.QE.\u6761\u4ef6.idPrefix?"if-else":j===ae.QE.\u5faa\u73af.idPrefix?"loop":j===ae.QE.\u5e76\u884c.idPrefix?"parallels":j===ae.QE.\u5faa\u73af\u7ec8\u6b62.idPrefix?"break":j===ae.QE.\u91cd\u65b0\u5f00\u59cb.idPrefix?"restart":""}  ${t===v?"select":""} ${E()} ${C.includes(t)&&"delete"}`,onClick:e=>{h(e,t)},onMouseUp:D,onContextMenu:i=>y(i,t,e),onDragStart:i=>A(i,t,e),onDragEnd:i=>w(i,t,e),draggable:!x,title:`${a||d}`,children:[(0,k.jsx)("div",{className:"title",title:n||d,children:N(n||d)}),![ae.QE.\u5faa\u73af\u7ec8\u6b62.idPrefix,ae.QE.\u91cd\u65b0\u5f00\u59cb.idPrefix].includes(j)&&(0,k.jsx)("span",{onClick:i=>f(i,t,e),children:x?(0,k.jsx)(Ie.A,{}):(0,k.jsx)(Ne.A,{draggable:!0})}),(0,k.jsx)("div",{className:"icon",children:[ae.QE.\u5faa\u73af\u7ec8\u6b62.idPrefix,ae.QE.\u91cd\u65b0\u5f00\u59cb.idPrefix].includes(j)?(0,k.jsx)("img",{src:s||R.Lt,alt:""}):(0,k.jsx)("i",{className:s})})]}),x&&(0,k.jsx)("div",{className:"subprocess",children:r})]},t):(0,k.jsxs)(X,{id:t,children:[(0,k.jsxs)("div",{className:"title-box",id:`${(0,ke.Z)(b)}${t}`,title:`${a||d}`,children:[(0,k.jsx)("div",{className:"title",title:n||d,children:N(n||d)}),(0,k.jsx)("div",{className:"icon",children:(0,k.jsx)("i",{className:s})})]}),r,(0,k.jsx)("div",{className:"end",id:`${(0,ke.Ys)(b)}${t}`})]},t)},De=e=>{let{datas:i,addedData:t,sourceId:a,targetId:l,subprocessId:d}=e;const n=[...i],o=n.findIndex((e=>e.id===a)),r=n.findIndex((e=>e.id===l));return-1!==o?(n.splice(o+1,0,t),n):-1!==r?[t,...n]:n.map((e=>e.children?{...e,children:De({datas:`${e.id}`===d?[t,...e.children]:e.children,addedData:t,sourceId:a,targetId:l,subprocessId:d})}:e))},Re=e=>{const{dataArr:i,...t}=e;return i.map((e=>{var i;return(null===(i=e.children)||void 0===i?void 0:i.length)>0?(0,k.jsx)(Ee,{...e,...t,children:Re({dataArr:e.children,...t})},e.id):(0,k.jsx)(Ee,{...e,...t},e.id)}))},_e=(e,i)=>{for(let l=0;l<e.length;l+=1){var t;const d=e[l];if(d.type!==ae.QE.\u6761\u4ef6.type&&d.type!==ae.QE.\u5faa\u73af.type&&d.type!==ae.QE.\u5e76\u884c.type||d.children.forEach((e=>{Se({containerId:(0,ke.HW)(i),sourceDomId:`${(0,ke.ZW)(i)}${d.id}`,targetDomId:e.id,svgClassNamesArr:[(0,ke.Rz)(i)],prefix:i}),Se({containerId:(0,ke.HW)(i),sourceDomId:e.id,targetDomId:d.id,connectorPositions:["bottom","bottom"],svgClassNamesArr:[(0,ke.Rz)(i)],prefix:i})})),d.type===ae.QE.\u5b50\u8fdb\u7a0b.type){var a;const e=null===(a=d.children)||void 0===a?void 0:a.length;e>0?(Se({containerId:(0,ke.HW)(i),sourceDomId:`${(0,ke.Z)(i)}${d.id}`,targetDomId:`${d.children[0].id}`,svgClassNamesArr:[(0,ke.Rz)(i),(0,ke.$v)(i)],prefix:i}),Se({containerId:(0,ke.HW)(i),sourceDomId:`${d.children[e-1].id}`,targetDomId:`${(0,ke.Ys)(i)}${d.id}`,svgClassNamesArr:[(0,ke.Rz)(i),(0,ke.$v)(i)],prefix:i})):Se({containerId:(0,ke.HW)(i),sourceDomId:`${(0,ke.Z)(i)}${d.id}`,targetDomId:`${(0,ke.Ys)(i)}${d.id}`,subProcessId:d.id,svgClassNamesArr:[(0,ke.Rz)(i),(0,ke.$v)(i)],connectorPositions:["bottom","bottom"],prefix:i})}const n=e[l+1];d.type!==ae.QE.\u5b50\u8fdb\u7a0b.type&&n&&Se({containerId:(0,ke.HW)(i),sourceDomId:d.id,targetDomId:n.id,svgClassNamesArr:[(0,ke.Rz)(i),(0,ke.$v)(i)],prefix:i}),(null===(t=d.children)||void 0===t?void 0:t.length)>0&&_e(d.children,i)}},Ue=function(e){let i,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(const a of t){if(i)break;if(a.id===e){i=a;break}a.children&&(i=Ue(e,a.children))}return i},Le=e=>{let{datas:i,targetId:t,newData:a,targetKey:l,targetVal:d}=e;return i.map((e=>{if(e.id===t){if(a)return{...a};if(l)return{...e,[l]:d}}return e.children?{...e,children:Le({datas:e.children,targetId:t,newData:a,targetKey:l,targetVal:d})}:e}))},Be=(e,i)=>{const{editData:t,handleConfirmChange:l}=e,{t:d}=(0,u.Bd)(),[n,o]=(0,a.useState)([]),[r,s]=(0,a.useState)();(0,a.useEffect)((()=>{s(t.name),null!==t&&void 0!==t&&t.children&&o([...t.children])}),[t]);const c=(e,i,a)=>{const{value:d,checked:r}=e.target,s=Le({datas:n,targetId:i,targetKey:a,targetVal:"name"===a?d:r});o(s),"important"===a&&l({data:{...t,"branch-idxs":s.map(((e,i)=>e.important?i:null)).filter((e=>null!==e)),children:s},isDb:!1})},v=()=>{l({data:{...t,"branch-idxs":n.map(((e,i)=>e.important?i:null)).filter((e=>null!==e)),children:n},isDb:!1})},p=()=>{o([...t.children])};return(0,a.useImperativeHandle)(i,(()=>({handleSubmit:v,handleReset:p}))),(0,k.jsxs)(oe.rZ,{children:[(0,k.jsxs)("div",{className:"row-name",children:[(0,k.jsx)("div",{className:"label",children:d("\u663e\u793a\u540d\u79f0")}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(le.A,{type:"text",style:{width:"88%"},value:r,onChange:e=>{s(e.target.value)},onBlur:()=>{l({data:{...t,name:r},isDb:!1})}})})]}),(0,k.jsx)("div",{className:"title",children:d("\u5206\u652f\u7f16\u8f91\u6846")}),(0,k.jsxs)("div",{className:"form",children:[n.map((e=>(0,k.jsxs)("div",{className:"row",children:[(0,k.jsx)("div",{className:"label",children:d("\u5206\u652f\u540d")}),(0,k.jsx)("div",{className:"input",children:(0,k.jsx)(le.A,{type:"text",value:e.name,style:{width:"88%"},onChange:i=>c(i,e.id,"name"),onBlur:e=>v()})}),(0,k.jsx)("div",{className:"label",children:d("\u4f18\u5148\u7ea7")}),(0,k.jsx)(we.A,{checked:e.important,onChange:i=>c(i,e.id,"important")}),(0,k.jsx)("div",{className:"delete",onClick:()=>(e=>{const i=n.filter((i=>i.id!==e));o(i),l({data:{...t,"branch-idxs":n.map(((e,i)=>e.important?i:null)).filter((e=>null!==e)),children:i},isDb:!1,isLine:!0,branchDataDel:n.find((i=>i.id===e))})})(e.id),children:(0,k.jsx)("i",{className:"fa fa-times","aria-hidden":"true"})})]},e.id))),(0,k.jsx)("div",{className:"row add",onClick:()=>{o([...n,be()]),l({data:{...t,"branch-idxs":n.map(((e,i)=>e.important?i:null)).filter((e=>null!==e)),children:[...n,be()]},isDb:!1,isLine:!0})},children:(0,k.jsx)("i",{className:"fa fa-plus","aria-hidden":"true"})})]})]})},Fe=(0,a.forwardRef)(Be);var qe=t(65508),Pe=t(36497),Me=t(97914);const Qe=(e,i)=>{var t;const{editData:l,handleConfirmChange:d}=e,{t:n}=(0,u.Bd)(),[o,r]=(0,a.useState)([]),[s,c]=(0,a.useState)(),v=null===o||void 0===o||null===(t=o.find((e=>"type"===(null===e||void 0===e?void 0:e.key))))||void 0===t?void 0:t.value;(0,a.useEffect)((()=>{c(l.name),null!==l&&void 0!==l&&l.params&&r([...l.params])}),[l]);const p=e=>{d({data:{...l,params:null!==e&&void 0!==e?e:o},isDb:!1})},h=()=>{r([...l.params])};return(0,a.useImperativeHandle)(i,(()=>({handleSubmit:p,handleReset:h}))),(0,k.jsxs)(oe.LF,{children:[(0,k.jsx)("div",{className:"title",children:n("\u5c5e\u6027\u7f16\u8f91\u6846")}),(0,k.jsxs)("div",{className:"form",children:[(0,k.jsxs)("div",{className:"row",children:[(0,k.jsx)("div",{className:"label",children:n("\u663e\u793a\u540d\u79f0")}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(le.A,{type:"text",value:s,onChange:e=>{c(e.target.value)},onBlur:()=>{d({data:{...l,name:s},isDb:!1})}})})]}),o.map((e=>{var i;return(0,k.jsxs)("div",{className:"row",children:["type"===e.key&&(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("div",{className:"label",children:e.key}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(Pe.A,{value:e.value,options:null===(i=e.options)||void 0===i?void 0:i.map((e=>({...e,label:n(e.label)}))),onChange:i=>((e,i)=>{const t=o.map((t=>t.key===i?{...t,value:e}:t));r(t),d({data:{...l,params:t},isDb:!1})})(i,e.key)})})]}),"expr"===e.key&&"SCRIPT"===v&&(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("div",{className:"label",children:e.key}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(fe.im,{value:e.value,width:"20vw",module:fe.et["\u5b50\u4efb\u52a1\u811a\u672c(\u901a\u7528)"],onChange:i=>((e,i)=>{const t=o.map((t=>t.key===i?{...t,value:e}:t));p(t),r(t)})(i,e.key)})})]}),"times"===e.key&&"NUM"===v&&(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("div",{className:"label",children:e.key}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(Me.A,{min:1,value:e.value,onChange:i=>((e,i)=>{const t=o.map((t=>t.key===i?{...t,value:e}:t));r(t)})(i,e.key),onBlur:i=>(e.key,void p())})})]})]},e.key)}))]})]})},Oe=(0,a.forwardRef)(Qe),Ve=(e,i)=>{const{editData:t,handleConfirmChange:l}=e,{t:d}=(0,u.Bd)(),[n,o]=(0,a.useState)([]),[r,s]=(0,a.useState)();(0,a.useEffect)((()=>{s(null===t||void 0===t?void 0:t.name),null!==t&&void 0!==t&&t.params&&o([...t.params])}),[t]);const c=()=>{l({data:{...t,params:n},isDb:!1})},v=()=>{o([...t.params])};return(0,a.useImperativeHandle)(i,(()=>({handleSubmit:c,handleReset:v}))),(0,k.jsxs)(oe.LF,{children:[(0,k.jsx)("div",{className:"title",children:d("\u7ec8\u6b62\u7f16\u8f91\u6846")}),(0,k.jsxs)("div",{className:"form",children:[(0,k.jsxs)("div",{className:"row",children:[(0,k.jsx)("div",{className:"label",children:d("\u663e\u793a\u540d\u79f0")}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(le.A,{type:"text",value:r,onChange:e=>{s(e.target.value)},onBlur:()=>{l({data:{...t,name:r},isDb:!1})}})})]}),n.map((e=>(0,k.jsxs)("div",{className:"row",children:[(0,k.jsx)("div",{className:"label",children:d("\u5faa\u73af\u7ec8\u6b62id")}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(le.A,{value:e.value,onChange:i=>((e,i)=>{const t=n.map((t=>t.key===i?{...t,value:e}:t));o(t)})(i.target.value,e.key),onBlur:i=>(i.target.value,e.key,void c())})})]},e.key)))]})]})},Ye=(0,a.forwardRef)(Ve),Ke=(e,i)=>{const{editData:t,handleConfirmChange:l}=e,{t:d}=(0,u.Bd)(),[n,o]=(0,a.useState)([]),[r,s]=(0,a.useState)();(0,a.useEffect)((()=>{s(null===t||void 0===t?void 0:t.name),null!==t&&void 0!==t&&t.params&&o([...t.params])}),[t]);const c=e=>{l({data:{...t,params:null!==e&&void 0!==e?e:n},isDb:!1})},v=()=>{o([...t.params])};return(0,a.useImperativeHandle)(i,(()=>({handleSubmit:c,handleReset:v}))),(0,k.jsxs)(oe.LF,{children:[(0,k.jsx)("div",{className:"title",children:d("\u7ed3\u675f")}),(0,k.jsxs)("div",{className:"form",children:[(0,k.jsxs)("div",{className:"row",children:[(0,k.jsx)("div",{className:"label",children:d("\u663e\u793a\u540d\u79f0")}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(le.A,{type:"text",value:r,disabled:!0,onChange:e=>{s(e.target.value)},onBlur:()=>{l({data:{...t,name:r},isDb:!1})}})})]}),n.map((e=>(0,k.jsxs)("div",{className:"row",children:[(0,k.jsx)("div",{className:"label",children:e.key}),(0,k.jsx)("div",{className:"value",children:(0,k.jsx)(fe.im,{value:e.value,width:"20vw",module:fe.et["\u5b50\u4efb\u52a1\u811a\u672c(\u901a\u7528)"],onChange:i=>((e,i)=>{const t=n.map((t=>t.key===i?{...t,value:e}:t));o(t),c(t)})(i,e.key)})})]},e.key)))]})]})},He=(0,a.forwardRef)(Ke),We=(e,i)=>{const{t:t}=(0,u.Bd)(),{editData:a}=e;switch(null===a||void 0===a?void 0:a.type){case ae.jW.BASIC:return(0,k.jsx)(qe.A,{...e,ref:i});case ae.QE.\u6761\u4ef6.type:return(0,k.jsx)(Ae,{...e,ref:i});case ae.QE.\u5e76\u884c.type:return(0,k.jsx)(Fe,{...e,ref:i});case ae.QE.\u5faa\u73af.type:return(0,k.jsx)(Oe,{...e,ref:i});case ae.QE.\u5faa\u73af\u7ec8\u6b62.type:return(0,k.jsx)(Ye,{...e,ref:i});case ae.QE.\u7ed3\u675f.type:case ae.QE.\u5f00\u59cb.type:return(0,k.jsx)(He,{...e,ref:i});default:return(0,k.jsx)(oe.xI,{children:t("\u5c5e\u6027\u7f16\u8f91\u6846")})}},$e=(0,a.forwardRef)(We);var ze=t(16014),Ge=t(60584);const Xe=function(e){const{prefix:i="defaultProcess",defaultFlowData:t=[],flowDataCallback:d,defaultScheduler:o=[],action_id:s="",layoutConfig:y,processType:A=ae.x9.\u9ed8\u8ba4,item:C,copyCallback:j=()=>console.log("copyCallback")}=e,[P,M]=v.Ay.useMessage(),{t:Q}=(0,u.Bd)(),{subMenu:O}=(0,L.A)(),H=(0,a.useRef)(),$=(0,l.d4)((e=>e.global.unitList)),{isControlLibraryOpen:z,isDefaultProcessOpen:G,isActionOpen:X}=(0,l.d4)((e=>e.dialog)),{menuData:ee}=(0,l.d4)((e=>e.split)),{subTaskSample:le}=(0,l.d4)((e=>e.subTask)),{openDialog:de}=(0,U.A)(),[ne,oe]=(0,a.useState)([]),re=(0,a.useRef)(ae.vN),[se,ce]=(0,a.useState)(null),ue=(0,a.useRef)(null),ve=(0,a.useRef)(null),pe=(0,a.useRef)(null),he=(0,a.useRef)(),me=(0,a.useRef)(!0),fe=(0,a.useRef)(!1),[be,ye]=(0,a.useState)([]),Ae=(0,a.useRef)(),we=(0,a.useRef)(),[je,Se]=(0,a.useState)([]),[Ie,Ne]=(0,a.useState)(!1),[Ee,Be]=(0,a.useState)(null),Fe=(0,a.useRef)([]),qe=(0,a.useRef)(),[Pe,Me]=(0,a.useState)([]),[Qe,Oe]=(0,a.useState)("0px"),[Ve,Ye]=(0,a.useState)(ae.U9.STANDARD),[Ke,He]=(0,a.useState)(o),{copy:We}=(0,F.A)(),{saveSubTaskDb:Xe,getSubTaskParam:Ze,putSubTaskDb:Je,batchDelSubTaskDb:ei,batchDelSameSubTaskDb:ii,getBatchSubTaskList:ti}=(0,Ge.A)(),ai=(0,a.useRef)(),li=(0,a.useRef)(),di=(0,l.d4)((e=>e.global.userIsAdmin)),ni=[ae.QE.\u5e76\u884c.idPrefix,ae.QE.\u5faa\u73af.idPrefix,ae.QE.\u6761\u4ef6.idPrefix,ae.QE.\u5faa\u73af\u7ec8\u6b62.idPrefix,ae.QE.\u91cd\u65b0\u5f00\u59cb.idPrefix];(0,a.useEffect)((()=>{window.addEventListener("resize",ci);const e=document.getElementById((0,ke.XR)(i));return e&&(ai.current=new ResizeObserver(c()(si,700)),ai.current.observe(e)),()=>{window.removeEventListener("resize",ci),fe.current=void 0,ai.current&&(ai.current.unobserve(e),ai.current=null)}}),[]),(0,a.useEffect)((()=>{ri()}),[A,JSON.stringify(t),JSON.stringify(o)]),(0,a.useEffect)((()=>{mi()}),[ee]),(0,a.useEffect)((()=>{z||oi()||Fi()}),[z,A]),(0,a.useEffect)((()=>{pi()}),[ne]);const oi=()=>A===ae.x9.\u67e5\u770b,ri=async()=>{await fi(),ui((0,B.sk)()?Number((0,B.sk)()):1),pi()},si=()=>{var e,t;null!==(e=document.getElementById((0,ke.XR)(i)))&&void 0!==e&&e.offsetParent&&(Oe(null===(t=document.getElementById((0,ke.XR)(i)))||void 0===t?void 0:t.offsetHeight),pi(!1))},ci=(0,a.useCallback)(c()((()=>pi(!1)),700),[]),ui=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;const t=document.getElementById((0,ke.HW)(i));t.style.transform=`scale(${e})`,t.style.transformOrigin="0 0",setTimeout((()=>{t.offsetHeight,t.offsetWidth}),100)},vi=e=>{const i=Number((0,B.sk)());e?(0,B.Y0)(i+.1):(0,B.Y0)(i<.3?i:i-.1),ui(Number((0,B.sk)()))},pi=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];console.log("\u753b\u7ebf",i),(e=>{document.querySelectorAll(`.${(0,ke.Rz)(e)}`).forEach((e=>e.remove()))})(i),e?setTimeout((()=>_e(re.current,i)),100):_e(re.current,i)},hi=e=>{re.current=e,oe(e),pi()},mi=()=>{he.current=null===ee||void 0===ee?void 0:ee.id,Ae.current=null===ee||void 0===ee?void 0:ee.param},fi=async()=>{try{if(A===ae.x9.\u9ed8\u8ba4||s===w.In)return void xi();if(A===ae.x9.\u52a8\u4f5c)return void yi();A===ae.x9.\u67e5\u770b&&Ai()}catch(e){oe(ae.vN),re.current=ae.vN,console.log(e)}},xi=async()=>{try{const e=await(0,D.T$s)();e&&(H.current=e.id,e.data?(oe(null===e||void 0===e?void 0:e.data),re.current=null===e||void 0===e?void 0:e.data):(oe(ae.vN),re.current=ae.vN),bi())}catch(e){oe(ae.vN),re.current=ae.vN,console.log(e)}},bi=()=>{if(C){var e;const i=null===(e=(0,B.l4)(null===C||void 0===C?void 0:C.widget_id))||void 0===e?void 0:e.data;if(i){switch(i.type){case ae.YL.COMBINE_BOX:Si({currentTarget:{id:i.task_id}});break;case ae.YL.START_OR_END:Ti({currentTarget:{id:i.task_id}});break;default:Ci({currentTarget:{id:i.task_id}},i.name)}gi(i.task_id)}}},gi=e=>{e&&setTimeout((()=>{const i=document.getElementById(e);i&&i.scrollIntoView({behavior:"instant",block:"start"})}),100)},yi=()=>{t?(oe(t||[]),re.current=t||[],He(o||[])):(oe(ae.vN),re.current=ae.vN)},Ai=()=>{yi()},wi=e=>{var i;return null===e||void 0===e||null===(i=e.children)||void 0===i?void 0:i.map((e=>{const i=(null===e||void 0===e?void 0:e.props)||e;return i.children&&i.children.length>0?{...i,children:wi(i)}:i}))},ki=()=>{ve.current=null,Be(null),ce(null)},Ci=async(e,i)=>{const t=e.currentTarget.id;C&&(0,B.ZY)({widget_id:C.widget_id,data:{task_id:t,name:i,type:ae.YL.SUB_TASK}}),Be(t),ce(null);const a=await Ze(t);Ye((null===a||void 0===a?void 0:a.guideType)||ae.U9.STANDARD),ce({...a,nameShow:i}),ji(t)},ji=e=>{fe.current?be.some((i=>i===e))?ye((i=>i.filter((i=>i!==e)))):ye((i=>[...new Set([...i,e,Ee])].filter((e=>!(null!==e&&void 0!==e&&e.includes(ae.QE.\u7ed3\u675f.id))&&!(null!==e&&void 0!==e&&e.includes(ae.QE.\u5f00\u59cb.id)))))):ye([])},Si=(e,t)=>{const a=e.currentTarget.id;C&&(0,B.ZY)({widget_id:C.widget_id,data:{task_id:a,type:ae.YL.COMBINE_BOX}}),Be(a.replace((0,ke.ZW)(i),""));const l=Ue(a.replace((0,ke.ZW)(i),""),re.current);null!==l&&void 0!==l&&l.params?ce(l):ce(null),ji(a)},Ti=(e,t)=>{const a=e.currentTarget.id;C&&(0,B.ZY)({widget_id:C.widget_id,data:{task_id:a,type:ae.YL.START_OR_END}}),Be(a.replace((0,ke.ZW)(i),""));const l=Ue(a.replace((0,ke.ZW)(i),""),re.current);null!==l&&void 0!==l&&l.params?ce(l):ce({...l,params:ae.XP})},Ii=async()=>{if(!le){const i=he.current,{current:t}=Ae;let a,l=[i];[ae.QE.\u5e76\u884c.idPrefix,ae.QE.\u5faa\u73af.idPrefix,ae.QE.\u6761\u4ef6.idPrefix].includes(t.idPrefix)&&(l=(0,E.eA)(wi(t),"id")),null!==e&&void 0!==e&&e.id||(a=await ti(l)),ei(l);const d=(0,E.iK)(ne,i);if((null===se||void 0===se?void 0:se.id)===(null===he||void 0===he?void 0:he.current)&&ce(null),d){await Ri({data:d,type:"del",value:a})&&hi(d)}ye((e=>e.filter((e=>e!==i))))}},Ni=()=>{if(!le){var e;const i=null===(e=Ae.current)||void 0===e?void 0:e.desc,t=(0,E.iK)(re.current,i,"desc");p.A.confirm({title:`${Q("\u662f\u5426\u5220\u9664\u5f53\u524d\u8fdb\u7a0b\u4e2d\u6240\u6709\u76f8\u540c\u7684\u5b50\u4efb\u52a1")}?`,icon:(0,k.jsx)(g.A,{}),okText:Q("\u786e\u8ba4"),cancelText:Q("\u53d6\u6d88"),onOk:async()=>{ii(i);await Ri({data:t})&&hi(t)}})}},Ei=(e,i)=>i.some((i=>e.startsWith(i))),Di=async()=>{if(!le&&be&&be.length>0){const i=re.current,t=(0,E.iK)(i,be,"id"),a=be.filter((e=>Ei(e,ni))),l=[...new Set([...((0,E.eA)((0,E.Rm)(i,"id",a)),"id"),...be.filter((e=>!Ei(e,ni)))])];let d;null!==e&&void 0!==e&&e.id||(d=await ti(l)),p.A.confirm({title:`${Q(`\u662f\u5426\u5220\u9664${be.length}\u4e2a\u5b50\u4efb\u52a1`)}?`,icon:(0,k.jsx)(g.A,{}),okText:Q("\u786e\u8ba4"),cancelText:Q("\u53d6\u6d88"),onOk:async()=>{ei(l);await Ri({data:t,ids:l,type:"del",value:d})&&hi(t),ye([])}})}},Ri=async e=>{let{data:i,type:t,value:a}=e;return new Promise(((e,l)=>{if(me.current){const o=(0,ze.T)(i);He(o);try{if(A===ae.x9.\u9ed8\u8ba4){const t=(0,D.aVf)({id:H.current,data:i,scheduler_context:o});return void(t&&e(t))}if(A===ae.x9.\u52a8\u4f5c&&d)return d({data:i,type:t,value:a,scheduler_context:o}),void e(!0);e(!0)}catch(n){console.log(n),l(n)}}else P.open({type:"warning",content:Q("\u5b58\u5728\u672a\u5b8c\u5584\u7684\u5b50\u4efb\u52a1")}),me.current=!0}))},_i=e=>e.map((e=>{var i;const{type:t}=e;var a;if(t===ae.ts.\u5b50\u63a7\u4ef6)return{id:xe(null===e||void 0===e?void 0:e.idPrefix),type:null===e||void 0===e?void 0:e.type,idPrefix:null===e||void 0===e?void 0:e.idPrefix,name:null===e||void 0===e?void 0:e.name,hideInBar:null===e||void 0===e?void 0:e.hideInBar,imgUrl:null===e||void 0===e?void 0:e.imgUrl,children:_i(null===e||void 0===e||null===(a=e.children)||void 0===a?void 0:a.map((e=>e.props)))};const[l]=t.split(":");return l===ae.ts.\u57fa\u672c\u63a7\u4ef6?{id:xe(null===e||void 0===e?void 0:e.idPrefix),type:null===e||void 0===e?void 0:e.type,idPrefix:null===e||void 0===e?void 0:e.idPrefix,imgUrl:null===e||void 0===e?void 0:e.imgUrl,name:`\u81ea\u5b9a\u4e49${null===e||void 0===e?void 0:e.name}`,nameShow:null===e||void 0===e?void 0:e.nameShow,desc:null===e||void 0===e?void 0:e.desc,isEnable:null===e||void 0===e?void 0:e.isEnable,isCorrect:null===e||void 0===e?void 0:e.isCorrect,groups:null===e||void 0===e?void 0:e.groups}:{type:null===e||void 0===e?void 0:e.type,idPrefix:null===e||void 0===e?void 0:e.idPrefix,name:`\u81ea\u5b9a\u4e49${null===e||void 0===e?void 0:e.name}`,imgUrl:null===e||void 0===e?void 0:e.imgUrl,params:null===e||void 0===e?void 0:e.params,children:_i(null===e||void 0===e||null===(i=e.children)||void 0===i?void 0:i.map((e=>e.props)))}})),Ui=()=>{Se([...je,_i([Ae.current])[0]])},Li=(e,i,t)=>`${e}-0-${i}-${t}`,Bi=function(e){var i;let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"control",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return null===e||void 0===e||null===(i=e.filter((e=>!!di||1===(null===a||void 0===a?void 0:a[null===e||void 0===e?void 0:e.id]))))||void 0===i?void 0:i.map(((e,i)=>({title:(0,k.jsx)("div",{className:"item",draggable:!0,onDragStart:()=>{return i=e,void(ve.current=i);var i},onDragEnd:ki,children:(0,k.jsx)("div",{className:"name",children:Q(e.name)})},e.name),key:Li(t,i,e.name),icon:(0,k.jsx)("img",{className:"img-icon",src:e.imgUrl||R.Lt,alt:"","aria-hidden":!0})})))},Fi=async()=>{try{let i=[];try{const e=await(0,D.hlh)();e&&(i=null===e||void 0===e?void 0:e.list)}catch(e){console.log(e)}const t={};i.forEach((e=>{t[e.id]=e.is_visible})),Object.values(ae.QE).forEach((e=>{t[e.id]=1}));const a=await(0,D.PwR)({is_visible:null}),{controlLibrary:l=[],dataLibrary:d=[],actionLibrary:n=[],userExperienceLibrary:o=[],deviceExperienceLibrary:r=[],eventDetectionLibrary:s=[],flowLogicLibrary:c=[],globalLibrary:u=[],highFrequencyLibrary:v=[]}=a,p=[{title:Q("\u6d41\u7a0b\u903b\u8f91\u5e93"),key:"0-1",children:Bi([...Object.values(ae.QE).filter((e=>!e.hideInBar)),...c],"flow",t)},{title:Q("\u63a7\u5236\u547d\u4ee4\u5e93"),key:"0-2",children:Bi(l,"control",t)},{title:Q("\u6570\u636e\u91c7\u96c6\u5e93"),key:"0-3",children:Bi(d,"data",t)},{title:Q("\u8bd5\u9a8c\u52a8\u4f5c\u5e93"),key:"0-4",children:Bi(n,"action",t)},{title:Q("\u7528\u6237\u4ea4\u4e92\u5e93"),key:"0-5",children:Bi(o,"user",t)},{title:Q("\u8815\u53d8\u4e13\u7528\u5e93"),key:"0-6",children:Bi(r,"device",t)},{title:Q("\u4e8b\u4ef6\u68c0\u6d4b\u5e93"),key:"0-7",children:Bi(s,"event",t)},{title:Q("\u5168\u5c40\u4e13\u7528\u5e93"),key:"0-8",children:Bi(u,"event",t)},{title:Q("\u9ad8\u9891\u4e13\u7528\u5e93"),key:"0-9",children:Bi(v,"event",t)}];Me(p)}catch(i){console.log(i)}},qi=()=>{We(he.current),j&&j(he.current)},Pi=()=>{li.current.open()},Mi=()=>{A!==ae.x9.\u52a8\u4f5c?We(`${(0,E.n1)()}-${he.current}`):We(`${(0,E.n1)()}-${s}-${he.current}`)},Qi=()=>A===ae.x9.\u52a8\u4f5c?(0,k.jsx)(N.A,{children:(0,k.jsx)(J,{ref:we,children:(0,k.jsxs)("div",{className:"list",children:[(0,k.jsx)("div",{className:"item "+(le?"disabled":""),onClick:Ii,children:Q("\u5220\u9664")}),(0,k.jsx)("div",{className:"item "+(le?"disabled":""),onClick:Mi,children:Q("copyId")}),(0,k.jsx)("div",{className:"item "+(le?"disabled":""),onClick:qi,children:Q("copy\u5b50\u4efb\u52a1Id")})]})})}):A===ae.x9.\u67e5\u770b?(0,k.jsx)(N.A,{children:(0,k.jsx)(J,{ref:we,children:(0,k.jsx)("div",{className:"list",children:(0,k.jsx)("div",{className:"item "+(le?"disabled":""),onClick:qi,children:Q("copy\u5b50\u4efb\u52a1Id")})})})}):(0,k.jsx)(te,{domId:null===e||void 0===e?void 0:e.id,processType:A,layoutConfig:y,handleRemoveClick:Ii,handleCopyClick:qi,handleBatchRemoveClick:Di,handleBatchSameRemoveClick:Ni,handleSaveAs:Ui,handleOpenSelectActiveDialog:Pi,handleMainCopyClick:Mi});return(0,k.jsxs)(V,{children:[Ie&&(0,k.jsx)(q.A,{text:Q("\u4e0b\u8f7d\u4e2d...")}),M,(0,k.jsxs)(T.A,{sizes:oi()?[100]:[15,55,30],onDragEnd:()=>pi(!1),children:[!oi()&&(0,k.jsx)(h.A,{className:"leftBar",size:"small",onMouseUp:()=>O(null),title:(0,k.jsx)("div",{className:"card-title",children:Q("\u63a7\u4ef6\u5217\u8868")}),extra:di?(0,k.jsx)(I.A,{onClick:()=>de({type:_.ZX}),size:"small",children:Q("\u63a7\u4ef6\u5e93")}):(0,k.jsx)(k.Fragment,{}),children:(0,k.jsx)(Y,{children:(0,k.jsx)(m.A,{showIcon:!0,defaultExpandAll:!0,treeData:Pe})})}),(0,k.jsx)("div",{className:"process",children:(0,k.jsx)(h.A,{size:"small",onMouseUp:()=>O(null),children:(0,k.jsxs)(K,{id:(0,ke.XR)(i),children:[(0,k.jsx)("div",{className:"execute-process",style:{height:Qe},children:(0,k.jsx)(W,{id:(0,ke.HW)(i),onDrop:async e=>{e.preventDefault();const t=ue.current;let a=r()(ve.current),l=r()(pe.current),d=null;if(a){if(!("groups"in a)){var n,o,c,u;if(null!==(n=a)&&void 0!==n&&n.groups_specialty)a=(0,ke.U1)(null===(c=a)||void 0===c?void 0:c.groups_specialty,ae.U9.SPECIALTY,a);if(null!==(o=a)&&void 0!==o&&o.groups_standard)a=(0,ke.U1)(null===(u=a)||void 0===u?void 0:u.groups_standard,ae.U9.STANDARD,a)}d={...ge(a),action_id:s}}if(t){var v,p,h,m;t.classList.remove("allow");const e=t.dataset[`${i}SourceId`],a=t.dataset[`${i}TargetId`],n=t.dataset[`${i}SubprocessId`];let o=re.current;if(l){if(e===l.id||a===l.id)return;o=(0,E.iK)(o,l.id);for(const e in l)if(e.includes("Func")||e.includes("select")||"children"===e){const i=wi(l);delete l[e],"children"===e&&i&&(l.children=i)}}else{var f,x,b,g,y;Xe(d),l={id:d.id,description:d.description,type:d.type,name:d.name,imgUrl:d.imgUrl,isCorrect:d.isCorrect,desc:d.desc,children:null===(f=d)||void 0===f?void 0:f.children,idPrefix:null===(x=d)||void 0===x?void 0:x.idPrefix,isFold:null!==(b=null===(g=d)||void 0===g?void 0:g.isFold)&&void 0!==b&&b,"task-type":null===(y=d)||void 0===y?void 0:y.idPrefix,params:d.params,status:"ready",nameShow:d.nameShow}}const r=De({datas:o,addedData:l,sourceId:e,targetId:a,subprocessId:n});if(await Ri({data:r,type:"add",value:null!==pe&&void 0!==pe&&pe.current?[]:[{id:d.id,type:d.idPrefix,parent_id:null!==(v=null===(p=d)||void 0===p?void 0:p.parent_id)&&void 0!==v?v:"",action_id:null!==(h=null===(m=d)||void 0===m?void 0:m.action_id)&&void 0!==h?h:"",ui_params:d,params:(0,ze.T)([d])[0].params}]})&&hi(r),Ee&&0===Ee.indexOf("parallels")){const e=`${(0,ke.ZW)(i)}${Ee}`;Si({currentTarget:{id:e}})}}ve.current=null,pe.current=null},onDragEnter:e=>{var t;const{target:a}=e,l=a.closest(`.${(0,ke.Rz)(i)}`);ue.current&&(ue.current.classList.remove("allow"),ue.current=null),l&&null!==(t=l.classList)&&void 0!==t&&t.contains((0,ke.$v)(i))&&(ue.current=l,l.classList.add("allow"))},onDragOver:e=>{le||oi()||e.preventDefault()},children:(0,k.jsx)("div",{className:"layout",children:Re({propsId:null===e||void 0===e?void 0:e.id,dataArr:ne||[],selectId:Ee,dragStartFunc:(e,i,t)=>{pe.current=t},dragEndFunc:()=>{pe.current=null},prefix:i,ctrlDelData:be,clickBasicFunc:Ci,clickCombineTitleFunc:Si,startOrEndFunc:Ti,clickFoldFunc:async(e,i,t)=>{const a=Le({datas:ne,targetId:i,targetKey:"isFold",targetVal:!t.isFold});await Ri({data:a})&&(ce({...se,isFold:!t.isFold}),oe(a),re.current=a),pi()},clickContextMenuFunc:(e,i,t)=>{he.current=i,Ae.current=t;let{clientX:a,clientY:l}=e;we.current.style.display="block";const d=window.innerWidth,n=window.innerHeight,o=we.current.offsetWidth,r=we.current.offsetHeight;return a=d-a>o?a:a-o,l=n-l>r?l:l-r,we.current.style.top=`${l}px`,we.current.style.left=`${a}px`,document.onclick=()=>{we.current&&(we.current.style.display="none",document.onclick=null)},!1}})})})}),(0,k.jsx)(ie,{children:(0,k.jsxs)("div",{className:"fixed-content",children:[(0,k.jsx)("img",{src:R.eV,alt:"",onClick:async()=>{try{Ne(!0),ui();const e=document.getElementById((0,ke.HW)(i)),t=(0,B.sk)(),a=e.offsetWidth/t,l=e.offsetHeight/t,d=268435456;let o=1;a*l>d&&(o=Math.sqrt(d/(a*l)));const r=await(e=>new Promise(((i,t)=>{const a=document.querySelectorAll(`.${(0,ke.Rz)(e)}`);for(let o=0;o<a.length;o+=1){const t=a[o],r=t.childNodes;var l,d,n;["combine-title-pre-parallels","combine-title-pre-if-else"].some((i=>t.attributes[`data-${e}-source-id`].nodeValue.includes(i)))&&"true"===(null===(l=r[0])||void 0===l||null===(d=l.attributes)||void 0===d||null===(n=d.reverse)||void 0===n?void 0:n.nodeValue)&&Te({containerId:(0,ke.HW)(e),sourceDomId:t.attributes[`data-${e}-source-id`].nodeValue,targetDomId:t.attributes[`data-${e}-target-id`].nodeValue,data:t,prefix:e,name:(0,ke.UM)(e)}),i(document.getElementsByName((0,ke.UM)(e)))}})))(i),s=await(e=>new Promise((async(i,t)=>{const a=document.querySelectorAll(`.${(0,ke.Rz)(e)}`),l=[];for(let d=0;d<a.length;d+=1){const i=a[d],t=i.outerHTML.trim(),n=document.createElement("canvas"),o=n.getContext("2d");n.id=d,n.width=2*document.getElementById((0,ke.HW)(e)).offsetWidth,n.height=i.getBoundingClientRect().height,i.style.position&&(n.style.position=i.style.position,n.style.left=i.style.left,n.style.top=i.style.top,n.style.marginLeft=i.style.marginLeft);const r=await Ce.Wn.from(o,t);r.start(),i.parentNode.appendChild(n),l.push(n),r.stop()}i(l)})))(i);Fe.current.push(...r,...s),r&&s&&setTimeout((async()=>{const i={scale:window.devicePixelRatio,imageTimeout:0,allowTaint:!0,useCORS:!0,width:e.scrollWidth,height:e.scrollHeight},t=await n()(e,i);t&&t.toBlob((e=>{Ne(!1);const i=document.createElement("a");i.href=URL.createObjectURL(e),i.download=decodeURI("\u6d41\u7a0b\u56fe"),document.body.appendChild(i),i.click(),document.body.removeChild(i),Fe.current.forEach((e=>{e.remove()})),Fe.current=[],URL.revokeObjectURL(e),ui(Number((0,B.sk)()))}),"image/jpeg",.92)}),500)}catch(e){console.log(e),Ne(!1)}}}),(0,k.jsx)(f.A,{}),(0,k.jsx)("img",{src:R.bn,alt:"",onClick:()=>{vi(!0)}}),(0,k.jsx)(f.A,{}),(0,k.jsx)("img",{src:R.aY,alt:"",onClick:()=>{vi(!1)}})]})})]})})}),!oi()&&(0,k.jsx)(h.A,{className:"process-right-card",style:{overflow:"hidden",display:"flex",flexDirection:"column"},size:"small",title:(0,k.jsx)("div",{className:"card-title",children:Q("\u53c2\u6570\u8bbe\u7f6e")}),onMouseUp:()=>O(null),extra:(null===se||void 0===se?void 0:se.type)===ae.jW.BASIC&&(0,k.jsx)(x.A,{children:(0,k.jsx)(b.A,{color:"blue",onClick:()=>{var e;const i=null===(e=qe.current)||void 0===e?void 0:e.onType();i&&Ye(i)},children:(0,k.jsx)("div",{className:"title-right",children:Q(ae.GL[Ve])})})}),children:(0,k.jsx)(Z,{children:(0,k.jsx)($e,{ref:qe,editData:se,unitList:$,action_id:s,prefix:i,scheduler:Ke,handleConfirmChange:async i=>{let{data:t,isDb:a=!0,isRender:l=!0,isLine:d=!1,branchDataDel:n,updateEditData:o=!1}=i;const{id:r}=t;let c;if(a&&Je({...t,action_id:s}),o&&ce(t),n){const i=(0,E.eA)(n.children,"id");null!==e&&void 0!==e&&e.id||(c=await ti(i)),ei(i)}if(l){const e=Le({datas:ne,targetId:r,newData:t});await Ri({data:e,value:c})&&(ce(t),oe(e),re.current=e,d&&pi(!0))}}})})})]}),(0,k.jsx)(Qi,{}),(0,k.jsx)(S,{ref:li,handleSelectedAction:async e=>{await(0,D.ay$)({action_id:e})&&ri()}})]})}},16014:(e,i,t)=>{t.d(i,{T:()=>o});t(56543);var a=t(754),l=t(74712);const d=e=>{var i,t,l,d,n;return null!==(i=null===a.A||void 0===a.A||null===(t=a.A.getState())||void 0===t||null===(l=t.global)||void 0===l||null===(d=l.unitList)||void 0===d||null===(n=d.find((i=>i.id===e)))||void 0===n?void 0:n.code)&&void 0!==i?i:""},n=(e,i)=>{var t,l,d,n,o,r,s;return null!==(t=null===a.A||void 0===a.A||null===(l=a.A.getState())||void 0===l||null===(d=l.global)||void 0===d||null===(n=d.unitList)||void 0===n||null===(o=n.find((i=>i.id===e)))||void 0===o||null===(r=o.units)||void 0===r||null===(s=r.find((e=>e.id===i)))||void 0===s?void 0:s.code)&&void 0!==t?t:""},o=e=>{const{inputVariableMap:i,inputVariableCodeList:t}=a.A.getState().inputVariable,r=t.map((e=>i.get(e)));return e.map((e=>{const{id:i,type:t,idPrefix:a,name:s,params:c,children:u,groups:v,nameShow:p,code:h}=e,[m]=t.split(":"),f={id:i,name:s,"task-type":a,params:{},status:"ready",nameShow:p};if(m===l.ts.\u57fa\u672c\u63a7\u4ef6){var x;if([l.QE.\u7ed3\u675f.idPrefix,l.QE.\u5f00\u59cb.idPrefix].includes(a))return{...f,params:{script:null===c||void 0===c||null===(x=c.find((e=>"expr"===e.key)))||void 0===x?void 0:x.value}};let i={};return null===v||void 0===v||v.forEach((t=>{if(t.type===l.sp.\u8fdb\u5ea6\u8868){var a,o,s;let T={};if(null!==t&&void 0!==t&&t.notCustoms&&t.notCustoms.length>0){console.log(t);const{notCustoms:e}=t,i=e.map((e=>e)).filter(Boolean),a=i.filter((e=>e.dialog_type===l.YM.SIGNAL)),o=i.filter((e=>e.dialog_type===l.YM.VARIABLE)),s=i.filter((e=>e.dialog_type===l.YM.RESULT)),h=i.filter((e=>e.dialog_type===l.YM.VARIABLE_LIST));if((null===h||void 0===h?void 0:h.length)>0){var c,u,v;const e=null===h||void 0===h||null===(c=h.map((e=>null===e||void 0===e?void 0:e.variable_list)))||void 0===c||null===(u=c.flat())||void 0===u?void 0:u.filter((e=>null!==e&&void 0!==e&&e)),[i]=h,t=null===r||void 0===r?void 0:r.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.constant_id)));T={...T,variableListParams:{isConstant:null!==(v=null===i||void 0===i?void 0:i.is_constant)&&void 0!==v&&v,variableCode:null===t||void 0===t?void 0:t.code,data:0===(null===e||void 0===e?void 0:e.length)?[]:e.map((e=>{var i,t,a,l,o,r,s,c,u,v,p,h;let m={};return null===e||void 0===e||null===(i=e.related_variables)||void 0===i||i.map((e=>e.variables)).flat().forEach((e=>{var i,t;const{value:a,unit:l="",unitType:o="",value_type:r="",isConstant:s=0,variable_code:c="",daq_code:u,hwKey:v="",buffer_code:p,daqRate:h,type:f}=null===e||void 0===e?void 0:e.default_val;m={...m,[e.code]:{value:0===s?a:c,variable:e.code,mode:null===e||void 0===e||null===(i=e.number_tab)||void 0===i||null===(t=i.channel)||void 0===t?void 0:t.channel,type:1===s?"\u53d8\u91cf":"\u5e38\u91cf",unit:l,unitCode:n(o,l),unitType:o,unitTypeCode:d(o),valueType:r,isCheck:!(null===e||void 0===e||!e.is_feature),varType:null===e||void 0===e?void 0:e.variable_type,daq:u,buffer:p,hwKey:v,axisType:f,daqRate:h}}})),{value:null===e||void 0===e?void 0:e.default_val.value,variable:null===e||void 0===e?void 0:e.code,mode:null===e||void 0===e||null===(t=e.number_tab)||void 0===t||null===(a=t.channel)||void 0===a?void 0:a.channel,type:1===(null===e||void 0===e||null===(l=e.default_val)||void 0===l?void 0:l.isConstant)?"\u53d8\u91cf":"\u5e38\u91cf",func:null===e||void 0===e?void 0:e.default_val.func,unit:null===(o=e.default_val)||void 0===o?void 0:o.unit,unitCode:n(null===(r=e.default_val)||void 0===r?void 0:r.unitType,null===(s=e.default_val)||void 0===s?void 0:s.unit),unitType:null!==(c=null===(u=e.default_val)||void 0===u?void 0:u.unitType)&&void 0!==c?c:"",unitTypeCode:d(null===(v=e.default_val)||void 0===v?void 0:v.unitType),valueType:null!==(p=null===(h=e.default_val)||void 0===h?void 0:h.value_type)&&void 0!==p?p:"",isCheck:!1,variables:m,varType:null===e||void 0===e?void 0:e.variable_type,daq:null===e||void 0===e?void 0:e.daq_code,buffer:null===e||void 0===e?void 0:e.buffer_code,hwKey:null===e||void 0===e?void 0:e.hwKey,axisType:null===e||void 0===e?void 0:e.type,daqRate:null===e||void 0===e?void 0:e.daqRate}}))}}}if((null===s||void 0===s?void 0:s.length)>0){var p,m,f;const e=null===s||void 0===s||null===(p=s.map((e=>e.results)))||void 0===p||null===(m=p.flat())||void 0===m?void 0:m.filter((e=>null!==e&&void 0!==e&&e)),[i]=s,t=null===r||void 0===r?void 0:r.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.constant_id)));T={...T,resultParams:{isConstant:null!==(f=null===i||void 0===i?void 0:i.is_constant)&&void 0!==f&&f,variableCode:null===t||void 0===t?void 0:t.code,data:0===(null===e||void 0===e?void 0:e.length)?[]:null===e||void 0===e?void 0:e.map((e=>{var i;let t={};return null===e||void 0===e||null===(i=e.related_variables)||void 0===i||i.map((e=>e.variables)).flat().forEach((e=>{var i,a;const{value:l,unit:d="",isConstant:n=0,variable_code:o="",daq_code:r,hwKey:s="",buffer_code:c,daqRate:u,type:v}=null===e||void 0===e?void 0:e.default_val;t={...t,[e.code]:{value:0===n?l:o,variable:e.code,mode:null===e||void 0===e||null===(i=e.number_tab)||void 0===i||null===(a=i.channel)||void 0===a?void 0:a.channel,type:1===n?"\u53d8\u91cf":"\u5e38\u91cf",unit:d,isCheck:!(null===e||void 0===e||!e.is_feature),varType:null===e||void 0===e?void 0:e.variable_type,daq:r,buffer:c,hwKey:s,axisType:v,daqRate:u}}})),{value:null===e||void 0===e?void 0:e.result_variable_id,variable:null===e||void 0===e?void 0:e.code,mode:null===e||void 0===e?void 0:e.result_variable_id,type:"\u5e38\u91cf",unit:"",isCheck:!1,variables:t,varType:null===e||void 0===e?void 0:e.variable_type,daq:null===e||void 0===e?void 0:e.daq_code,buffer:null===e||void 0===e?void 0:e.buffer_code,hwKey:null===e||void 0===e?void 0:e.hwKey,axisType:null===e||void 0===e?void 0:e.type,daqRate:null===e||void 0===e?void 0:e.daqRate}}))}}}if((null===a||void 0===a?void 0:a.length)>0){var x,b,g;const e=null===a||void 0===a||null===(x=a.map((e=>null===e||void 0===e?void 0:e.signals)))||void 0===x||null===(b=x.flat())||void 0===b?void 0:b.filter((e=>null!==e&&void 0!==e&&e)),[i]=a,t=null===r||void 0===r?void 0:r.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.constant_id)));T={...T,signalParams:{isConstant:null!==(g=null===i||void 0===i?void 0:i.is_constant)&&void 0!==g&&g,variableCode:null===t||void 0===t?void 0:t.code,data:0===(null===e||void 0===e?void 0:e.length)?[]:e.map((e=>{var i;let t={};return null===e||void 0===e||null===(i=e.related_variables)||void 0===i||i.map((e=>e.variables)).flat().forEach((e=>{var i,a;const{value:l,unit:d="",isConstant:n=0,variable_code:o="",daq_code:r,hwKey:s="",buffer_code:c,daqRate:u,type:v}=null===e||void 0===e?void 0:e.default_val;t={...t,[e.code]:{value:0===n?l:o,variable:e.code,mode:null===e||void 0===e||null===(i=e.number_tab)||void 0===i||null===(a=i.channel)||void 0===a?void 0:a.channel,type:1===n?"\u53d8\u91cf":"\u5e38\u91cf",unit:d,isCheck:!(null===e||void 0===e||!e.is_feature),varType:null===e||void 0===e?void 0:e.variable_type,daq:r,buffer:c,hwKey:s,axisType:v,daqRate:u}}})),{value:null===e||void 0===e?void 0:e.signal_variable_id,variable:null===e||void 0===e?void 0:e.code,mode:e.signal_variable_id,type:"\u5e38\u91cf",unit:null===e||void 0===e?void 0:e.unit_id,isCheck:!1,variables:t,varType:null===e||void 0===e?void 0:e.variable_type,daq:null===e||void 0===e?void 0:e.daq_code,buffer:null===e||void 0===e?void 0:e.buffer_code,hwKey:null===e||void 0===e?void 0:e.hwKey,axisType:null===e||void 0===e?void 0:e.type,daqRate:null===e||void 0===e?void 0:e.daqRate}}))}}}if((null===o||void 0===o?void 0:o.length)>0){var y,A,w;const e=null===o||void 0===o||null===(y=o.map((e=>null===e||void 0===e?void 0:e.variables)))||void 0===y||null===(A=y.flat())||void 0===A?void 0:A.filter((e=>null!==e&&void 0!==e&&e)),[i]=o,t=null===r||void 0===r?void 0:r.find((e=>(null===e||void 0===e?void 0:e.id)===(null===i||void 0===i?void 0:i.constant_id)));T={...T,variableParams:{isConstant:null!==(w=null===i||void 0===i?void 0:i.is_constant)&&void 0!==w&&w,variableCode:null===t||void 0===t?void 0:t.code,data:0===(null===e||void 0===e?void 0:e.length)?[]:null===e||void 0===e?void 0:e.map((e=>{var i,t,a;let l={};null===e||void 0===e||null===(i=e.related_variables)||void 0===i||i.map((e=>e.variables)).flat().forEach((e=>{var i,t;const{value:a,unit:o="",unitType:r="",value_type:s="",isConstant:c=0,variable_code:u="",daq_code:v,hwKey:p="",daqRate:h,type:m,buffer_code:f}=null===e||void 0===e?void 0:e.default_val;l={...l,[e.code]:{value:0===c?a:u,variable:e.code,mode:null===e||void 0===e||null===(i=e.number_tab)||void 0===i||null===(t=i.channel)||void 0===t?void 0:t.channel,type:1===c?"\u53d8\u91cf":"\u5e38\u91cf",unit:o,unitCode:n(r,o),unitType:r,unitTypeCode:d(r),valueType:s,isCheck:!(null===e||void 0===e||!e.is_feature),varType:null===e||void 0===e?void 0:e.variable_type,daq:v,buffer:f,hwKey:p,axisType:m,daqRate:h}}}));const{value:o,unit:r="",unitType:s="",value_type:c="",isConstant:u=0,variable_code:v="",daq_code:p,hwKey:h="",buffer_code:m,daqRate:f,type:x}=null===e||void 0===e?void 0:e.default_val;return{value:0===u?o:v,variable:null===e||void 0===e?void 0:e.code,mode:null===e||void 0===e||null===(t=e.number_tab)||void 0===t||null===(a=t.channel)||void 0===a?void 0:a.channel,type:1===u?"\u53d8\u91cf":"\u5e38\u91cf",unit:r,unitCode:n(s,r),unitType:s,unitTypeCode:d(s),valueType:c,isCheck:!(null===e||void 0===e||!e.is_feature),variables:l,varType:null===e||void 0===e?void 0:e.variable_type,daq:p,buffer:m,hwKey:h,axisType:x,daqRate:f}}))}}}}if(null!==t&&void 0!==t&&t.customs&&(null===t||void 0===t||null===(a=t.customs)||void 0===a?void 0:a.length)>0){var k,C,j,S;const e=null===t||void 0===t||null===(k=t.customs)||void 0===k?void 0:k.map((e=>e)),i=null===e||void 0===e||null===(C=e.map((e=>null===e||void 0===e?void 0:e.customs)))||void 0===C||null===(j=C.flat())||void 0===j?void 0:j.filter((e=>null!==e&&void 0!==e&&e)),[a]=e,l=null===r||void 0===r?void 0:r.find((e=>(null===e||void 0===e?void 0:e.id)===(null===a||void 0===a?void 0:a.constant_id)));T={...T,customParams:{isConstant:null!==(S=null===a||void 0===a?void 0:a.is_constant)&&void 0!==S&&S,variableCode:null===l||void 0===l?void 0:l.code,data:i.map((e=>{let i={};return e.related_variables.map((e=>e.variables)).flat().forEach((e=>{var t,a;const{value:l,unit:d="",isConstant:n=0,variable_code:o="",daq_code:r,hwKey:s="hwKey",buffer_code:c,daqRate:u,type:v}=null===e||void 0===e?void 0:e.default_val;i={...i,[e.code]:{value:0===n?l:o,variable:e.code,mode:null===e||void 0===e||null===(t=e.number_tab)||void 0===t||null===(a=t.channel)||void 0===a?void 0:a.channel,type:1===n?"\u53d8\u91cf":"\u5e38\u91cf",unit:d,isCheck:!(null===e||void 0===e||!e.is_feature),varType:null===e||void 0===e?void 0:e.variable_type,daq:r,buffer:c,hwKey:s,axisType:v,daqRate:u}}})),{value:e.code||e.name,variable:e.name,mode:e.name,type:"\u5e38\u91cf",unit:e.name,isCheck:!1,variables:i,varType:"custom",daq:"",buffer:"",param:i,hwKey:null,axisType:"",daqRate:""}}))}}}null!==t&&void 0!==t&&t.dialogCodes&&(null===t||void 0===t||null===(o=t.dialogCodes)||void 0===o?void 0:o.length)>0&&(null===t||void 0===t||t.dialogCodes.forEach((e=>{let i={};e.variables.forEach((e=>{var t,a;const{value:l,unit:o="",unitType:r="",value_type:s="",isConstant:c=0,variable_code:u="",daq_code:v,hwKey:p,daqRate:h,type:m,buffer_code:f}=null===e||void 0===e?void 0:e.default_val;i={...i,[e.code]:{value:0===c?l:u,variable:e.code,mode:null===e||void 0===e||null===(t=e.number_tab)||void 0===t||null===(a=t.channel)||void 0===a?void 0:a.channel,type:1===c?"\u53d8\u91cf":"\u5e38\u91cf",unit:o,unitCode:n(r,o),unitType:r,unitTypeCode:d(r),valueType:s,isCheck:!(null===e||void 0===e||!e.is_feature),varType:null===e||void 0===e?void 0:e.variable_type,daq:v,buffer:f,hwKey:p,axisType:m,daqRate:h}}})),T={...T,[e.code]:i}}))),null===(s=t.variables)||void 0===s||s.filter((e=>"\u53ef\u89c1\u6027"!==e.name)).map((e=>{var i,t;const{value:a,unit:l="",unitType:o="",value_type:r="",isConstant:s=0,variable_code:c="",daq_code:u,hwKey:v,buffer_code:p,daqRate:h,type:m}=null===e||void 0===e?void 0:e.default_val;return T={...T,[e.code]:{value:0===s?a:c,variable:e.code,mode:null===e||void 0===e||null===(i=e.number_tab)||void 0===i||null===(t=i.channel)||void 0===t?void 0:t.channel,type:1===s?"\u53d8\u91cf":"\u5e38\u91cf",unit:l,unitCode:n(o,l),unitType:o,unitTypeCode:d(o),valueType:r,isCheck:!(null===e||void 0===e||!e.is_feature),varType:null===e||void 0===e?void 0:e.variable_type,daq:u,buffer:p,hwKey:v,axisType:m,daqRate:h}},T})),i={...i,daq_code:h,[l.sp.\u8fdb\u5ea6\u8868]:T,edition:e.guideType}}})),{...f,params:i}}if(m===l.ts.\u6761\u4ef6\u63a7\u4ef6){if(a===l.QE.\u6761\u4ef6.idPrefix){var b;const e=o(u.filter((e=>"true"===e.name))),i=o(u.filter((e=>"false"===e.name)));return{...f,expr:null===(b=c.find((e=>"expr"===e.key)))||void 0===b?void 0:b.value,"true-tasks":e[0],"false-tasks":i[0]}}if(a===l.QE.\u5e76\u884c.idPrefix){const e=o(u);return{...f,"branch-idxs":u.map(((e,i)=>e.important?i:null)).filter((e=>null!==e)),branches:e}}var g,y;if(a===l.QE.\u5faa\u73af\u7ec8\u6b62.idPrefix)return{...f,"break-loop-id":null===(g=c.find((e=>"loop-id"===e.key)))||void 0===g?void 0:g.value};if(a===l.QE.\u91cd\u65b0\u5f00\u59cb.idPrefix)return{...f,"break-loop-id":null===(y=c.find((e=>"loop-id"===e.key)))||void 0===y?void 0:y.value};if(a===l.QE.\u5faa\u73af.idPrefix){var A,w,k,C;const e=null===(A=o(u))||void 0===A?void 0:A.flat();return{...f,expr:null===(w=c.find((e=>"expr"===e.key)))||void 0===w?void 0:w.value,"loop-mode":null===(k=c.find((e=>"type"===e.key)))||void 0===k?void 0:k.value,times:null===(C=c.find((e=>"times"===e.key)))||void 0===C?void 0:C.value,"loop-tasks":e}}}return o(u)}))}},26790:(e,i,t)=>{t.d(i,{A:()=>d});t(65043);var a=t(45591),l=t(70579);const d=e=>{let{isActive:i}=e;return(0,l.jsx)(a.A,{rotate:i?180:0,style:{color:"rgba(0,0,0,0.25)"}})}},52136:(e,i,t)=>{t.d(i,{LF:()=>r,rZ:()=>s,xI:()=>o});var a=t(81143),l=t(18650),d=t(68374);const n=a.Ay.div`

`,o=a.Ay.div`
   
`,r=(0,a.Ay)(n)`
    width: 100%;
    overflow-y: auto;
    height: 100%;
    .head {
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        padding: 10px;
    }
    .require {
        color: red;
        margin-right: 3px
    }
    .title {
        padding: 10px;
    }
    .submit {
        padding:  0 10px;
    }
    .else-card-content {
        display: flex;
        justify-content: space-between;
            
    }
    >.general {
        width: 100%;
        display: flex;
        flex-direction: column;
        height: 100%;

        .card-content {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            .input-width {
                width: ${(0,d.D0)("180px")};
            }
        }


        .ant-collapse {
            width: 100%;
        }

        .ant-collapse-header {
            background: #FFFFFF;
            box-shadow: 0 2px 5px 0 rgb(0 22 121 / 12%);
            border-radius: 6px;
        }

        .ant-collapse-header {
            border-radius: 6px !important;
            overflow: hidden;
            border: 1px solid ${d.o$.borderGray};
        }

        .ant-collapse-content {
            .ant-collapse-content-box {
                padding: 12px 4px;
            }
        }
    }
    >.form {
        width: 100%;
        height: 90%;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        margin-bottom: 1%;

        .row {
            padding:  0 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 6px;
            flex-wrap: wrap;


            >.label {
                min-width: 120px;
                &::after {
                    content: ':'
                }
            }

            >.value {
                display: flex; 
                align-items: center;
                >.kong {
                    width: 20px;
                    height: 20px; 
                    margin-right: 10px;
                    visibility:hidden;
                }
                >.changliang {
                    width: 20px;
                    height: 20px;
                    background-image: url(${l.fd});
                    background-size: 20px 20px;
                    margin-right: 10px;
                }

                >.bianliang {
                    width: 20px;
                    height: 20px;
                    background-image: url(${l.Mo});
                    background-size: 20px 20px;
                    margin-right: 10px;
                }
                .input-value {
                    display: flex;

                    input {
                        width: 80px;
                    }

                    .ant-select {
                        width: 95px;
                        margin-left: 5px;
                    }
                }

                input {
                    width: 180px;
                    height: 24px;
                }

                .ant-select {
                    width: 180px;
                    height: 27px;
                }
                
                textarea {
                    width: 180px;
                }
            }

            input {
                height: 24px;
            }

            .ant-select-selector {
                height: 24px;

                .ant-select-selection-item {
                    line-height: 24px;
                }
            }
        }

        .ant-collapse {
            width: 100%;
        }

        .ant-collapse-header {
            background: #FFFFFF;
            box-shadow: 0 2px 5px 0 rgb(0 22 121 / 12%);
            border-radius: 6px;
        }

        .ant-collapse-header {
            border-radius: 6px !important;
            border: 1px solid ${d.o$.borderGray};
        }

        .ant-collapse-content {
            .ant-collapse-content-box {
                padding: 12px 4px;
            }
        }
    }
`,s=(0,a.Ay)(n)`
    >.form {
        
        >.row {
            padding:  0 10px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-around;
            margin: 16px 0;

            >.label {
                &::after {
                    content: ':'
                }
            }
        }
    }
    >.row-name {
        padding:  10px 10px;
        display: flex;
        >.label {
            margin-right: 1.9vw;
                &::after {
                    content: ':'
                }
            }
    }

`;(0,a.Ay)(n)`
    height: 50vh;
    display: flex;
    justify-content: space-around;

    .container {
        flex: 1;
        margin-right: 12px;

        .layout {
            padding: ${(0,d.D0)("20px")};
            height: 50vh;
        }
    }

    .operate {
        width: 8vw;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
`},60584:(e,i,t)=>{t.d(i,{A:()=>o});t(65043);var a=t(67208),l=t(16090),d=t(74712),n=t(16014);const o=()=>{const{initActionData:e}=(0,l.A)(),i=e=>{(0,a.Lr0)({subtasks:e})};let t=[];const o=(e,i)=>e.map((e=>{const a={...e};return"groups"in a?(t.push({...a,action_id:i}),{id:a.id,type:a.type,name:a.name,imgUrl:a.imgUrl,isCorrect:a.isCorrect,desc:a.desc,children:null===a||void 0===a?void 0:a.children,idPrefix:null===a||void 0===a?void 0:a.idPrefix,params:a.params,"task-type":null===a||void 0===a?void 0:a.idPrefix,status:"ready",nameShow:a.nameShow}):(null!==a&&void 0!==a&&a.children&&a.children.length>0&&(a.children=o(a.children)),a)})),r=e=>e.map((e=>{let{child:i,...t}=e;const a={...t};return null===a||void 0===a||delete a.child,null!==a&&void 0!==a&&a.children&&(null===a||void 0===a?void 0:a.children.length)>0&&(a.children=r(a.children)),a}));return{saveSubTaskDb:e=>{const{type:i,action_id:t}=e;var l;i===d.ts.\u57fa\u672c\u63a7\u4ef6&&(0,a.FZh)({id:e.id,type:e.idPrefix,parent_id:null!==(l=null===e||void 0===e?void 0:e.parent_id)&&void 0!==l?l:"",action_id:t,ui_params:e,params:(0,n.T)([e])[0].params})},putSubTaskDb:e=>{const{type:i}=e;var t,l;i===d.ts.\u57fa\u672c\u63a7\u4ef6&&(0,a.h_k)({id:e.id,type:e.idPrefix,parent_id:null!==(t=null===e||void 0===e?void 0:e.parent_id)&&void 0!==t?t:"",action_id:null!==(l=null===e||void 0===e?void 0:e.action_id)&&void 0!==l?l:"",ui_params:e,params:(0,n.T)([e])[0].params})},getSubTaskParam:async e=>{var i;const{ui_params:t}=null!==(i=await(0,a.YEY)({id:e}))&&void 0!==i?i:{ui_params:""};return t},getBatchSubTaskList:async e=>await(0,a.Yj)({ids:e}),delSubTaskDb:e=>{(0,a.hOI)({id:e})},batchDelSubTaskDb:e=>{(0,a.h7S)({ids:e})},batchDelSameSubTaskDb:e=>{(0,a.JMV)({same:e})},compatibilityFlowChart:async()=>{const{data:e,id:l}=await(0,a.T$s)();t=[];const d=o(r(e.flat()));i(t.map((e=>{var i,t;return{id:e.id,type:e.idPrefix,parent_id:null!==(i=null===e||void 0===e?void 0:e.parent_id)&&void 0!==i?i:"",action_id:null!==(t=null===e||void 0===e?void 0:e.action_id)&&void 0!==t?t:"",ui_params:e,params:(0,n.T)([e])[0].params}}))),(0,a.aVf)({id:l,data:d,scheduler_context:(0,n.T)(d)})},batchAddSubTaskDb:i,compatibilityActionFlowChart:async()=>{const l=await(0,a.rFE)();t=[],l.forEach((e=>{const i=o(r(e.flow_chart.flat()),e.action_id);(0,a.dzs)({...e,flow_chart:i,flow_chart_scheduler:{scheduler_context:(0,n.T)(i)}})})),i(t.map((e=>{var i,t;return{id:e.id,type:e.idPrefix,parent_id:null!==(i=null===e||void 0===e?void 0:e.parent_id)&&void 0!==i?i:"",action_id:null!==(t=null===e||void 0===e?void 0:e.action_id)&&void 0!==t?t:"",ui_params:e,params:(0,n.T)([e])[0].params}}))),e()}}}},65508:(e,i,t)=>{t.d(i,{A:()=>b});var a=t(65043),l=t(74117),d=t(97326),n=t(83720),o=t(25055),r=t(14607),s=t(74712),c=t(33981),u=t(52136),v=t(92275),p=t(26790),h=t(70579);const{Panel:m}=d.A,{TextArea:f}=n.A,x=(e,i)=>{const{editData:t,action_id:f,unitList:x,handleConfirmChange:b,scheduler:g,prefix:y,subTaskRenderKey:A}=e,{t:w}=(0,l.Bd)(),[k,C]=(0,a.useState)((null===t||void 0===t?void 0:t.guideType)||s.U9.STANDARD),[j,S]=(0,a.useState)([]),[T,I]=(0,a.useState)(),[N,E]=(0,a.useState)(),[D,R]=(0,a.useState)(),{copy:_}=(0,c.A)();(0,a.useEffect)((()=>{C(null===t||void 0===t?void 0:t.guideType)}),[null===t||void 0===t?void 0:t.guideType]),(0,a.useEffect)((()=>{I(t.nameShow),E(t.desc),R(t.code)}),[t]),(0,a.useEffect)((()=>{S({...t,groups:k===s.U9.STANDARD?t.groups_standard:t.groups_specialty})}),[k,null===t||void 0===t?void 0:t.guideType,null===t||void 0===t?void 0:t.id]);const U=()=>{let e=k;return t.is_specialty&&(C(k===s.U9.STANDARD?s.U9.SPECIALTY:s.U9.STANDARD),e=k===s.U9.STANDARD?s.U9.SPECIALTY:s.U9.STANDARD),b({data:(0,v.U1)(e===s.U9.STANDARD?t.groups_standard:t.groups_specialty,e,t),isRender:!1}),e},L=e=>{try{b({data:(0,v.U1)(e,k,t),isRender:!1})}catch(i){if("LoopInterrupt"!==i.message)throw i}},B=()=>{b({data:{...t,nameShow:T,desc:N,code:D},isDb:!1})};return(0,a.useImperativeHandle)(i,(()=>({handleSubmit:L,onType:U}))),(0,h.jsx)(u.LF,{children:(0,h.jsxs)("div",{className:"general",children:[!A&&(0,h.jsx)(d.A,{bordered:!1,expandIcon:e=>{let{isActive:i}=e;return(0,h.jsx)(p.A,{isActive:i})},expandIconPosition:"end",children:(0,h.jsxs)(m,{header:(0,h.jsx)("div",{className:"card-title",children:w("\u901a\u7528")}),children:[(0,h.jsx)(o.A.Item,{label:w("\u5b50\u4efb\u52a1id"),children:(0,h.jsx)("div",{className:"card-content",onClick:e=>_(null===t||void 0===t?void 0:t.id),children:null===t||void 0===t?void 0:t.id})}),(0,h.jsx)(o.A.Item,{label:w("\u663e\u793a\u540d\u79f0"),children:(0,h.jsx)("div",{className:"card-content",children:(0,h.jsx)(n.A,{value:T,size:"small",onChange:e=>{I(e.target.value)},className:"input-width",onBlur:B})})}),"daq"===t.desc&&(0,h.jsx)(o.A.Item,{label:w("\u6807\u8bc6\u7b26"),children:(0,h.jsx)("div",{className:"card-content",children:(0,h.jsx)(n.A,{value:D,size:"small",disabled:!0,onChange:e=>{R(e.target.value)},className:"input-width",onBlur:B})})})]},"0")}),(0,h.jsx)(r.A,{unitList:x,paramsData:j,scheduler:g,callback:e=>{try{b({data:(0,v.U1)(e,k,t),isRender:!1,updateEditData:!0})}catch(i){if("LoopInterrupt"!==i.message)throw i}},action_id:f,prefix:y,subTaskRenderKey:A})]})})},b=(0,a.forwardRef)(x)},74712:(e,i,t)=>{t.d(i,{GL:()=>x,NT:()=>u,QE:()=>r,U9:()=>f,XP:()=>n,YL:()=>l,YM:()=>p,dk:()=>m,jW:()=>o,sp:()=>d,ts:()=>c,vN:()=>s,vU:()=>h,x9:()=>b,ze:()=>v});var a=t(18650);const l={COMBINE_BOX:"combineBox",START_OR_END:"startOrEnd",SUB_TASK:"subTask"},d={"\u8fdb\u5ea6\u8868":"schedule","\u89e6\u53d1\u5668":"flipFlop","\u4f20\u611f\u5668":"sensor","\u53d8\u91cf\u5217\u8868":"variate","\u4f20\u611f\u5668\u6781\u9650":"sensorMax","\u53d8\u91cf\u6781\u9650":"variateMax","\u6570\u636e\u91c7\u96c6":"dataCollect"},n=[{key:"expr",value:"return true;"}],o={BASIC:"basic"},r={"\u5f00\u59cb":{type:"basic:SubTaskTimer",idPrefix:"start",name:"\u5f00\u59cb",id:"start-node",hideInBar:!0,isCorrect:!0,imgUrl:"fa fa-arrow-circle-down",params:n},"\u7ed3\u675f":{type:"basic:end",idPrefix:"end",name:"\u7ed3\u675f",hideInBar:!0,isCorrect:!0,id:"end-node",imgUrl:"fa fa-circle-o",params:n},"\u6761\u4ef6":{type:"combine:if-else",idPrefix:"if-else",name:"\u6761\u4ef6",imgUrl:a.yh,id:"if-else",isFold:!0,params:[{key:"expr",value:"return true;"}]},"\u5faa\u73af":{type:"combine:loop",idPrefix:"loop",name:"\u5faa\u73af",imgUrl:a.cu,id:"loop",isFold:!0,params:[{key:"type",options:[{value:"SCRIPT",label:"\u811a\u672c"},{value:"NUM",label:"\u5faa\u73af\u6b21\u6570"}],value:"SCRIPT"},{key:"expr",value:"return true;"},{key:"times",value:1}]},"\u5faa\u73af\u7ec8\u6b62":{type:"combine:break",idPrefix:"break",id:"break",name:"\u5faa\u73af\u7ec8\u6b62",imgUrl:a.cu,params:[{key:"loop-id",value:""}]},"\u91cd\u65b0\u5f00\u59cb":{type:"combine:restart",idPrefix:"restart",id:"restart",name:"\u91cd\u65b0\u5f00\u59cb",imgUrl:a.cu,params:[]},"\u5e76\u884c":{type:"combine:parallels",idPrefix:"parallels",id:"parallels",name:"\u5e76\u884c",isFold:!0,imgUrl:a._u},"\u5b50\u8fdb\u7a0b":{type:"subprocess",idPrefix:"subprocess",id:"subprocess",name:"\u5b50\u8fdb\u7a0b",hideInBar:!0,imgUrl:"fa fa-inbox"}},s=[{...r.\u5f00\u59cb,id:"start-node"},{...r.\u7ed3\u675f,id:"end-node"}],c={"\u57fa\u672c\u63a7\u4ef6":"basic","\u6761\u4ef6\u63a7\u4ef6":"combine","\u5b50\u63a7\u4ef6":"subprocess"},u="flow",v={NOT_CUSTOM:"not_custom",CUSTOM:"custom"},p={VARIABLE:"variable",VARIABLE_LIST:"variable_list",SIGNAL:"signal",RESULT:"result",RELATED:"related"},h=[{value:p.SIGNAL,label:"\u4f20\u611f\u5668\u7a97\u53e3"},{value:p.VARIABLE,label:"\u8f93\u5165\u53d8\u91cf\u7a97\u53e3"},{value:p.RESULT,label:"\u7ed3\u679c\u53d8\u91cf\u7a97\u53e3"},{value:p.VARIABLE_LIST,label:"\u8f93\u5165\u53d8\u91cf\u5217\u8868"}],m={DIALOG:"dialog",CONTROL:"control"},f=(m.DIALOG,m.CONTROL,{STANDARD:"standard",SPECIALTY:"specialty"}),x={[f.STANDARD]:"\u6807\u51c6",[f.SPECIALTY]:"\u4e13\u4e1a"},b={"\u9ed8\u8ba4":"default","\u52a8\u4f5c":"action","\u67e5\u770b":"query"}},92275:(e,i,t)=>{t.d(i,{$v:()=>o,HW:()=>l,Rz:()=>n,U1:()=>v,UM:()=>u,XR:()=>d,Ys:()=>c,Z:()=>s,ZW:()=>r});var a=t(74712);const l=function(){return`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.NT}-container`},d=function(){return`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.NT}-div-container`},n=function(){return`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.NT}-connector`},o=function(){return`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.NT}-allow-add`},r=function(){return`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.NT}-combine-title-pre-`},s=function(){return`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.NT}-subprocess-title-pre-`},c=function(){return`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.NT}-subprocess-end-pre-`},u=function(){return`${arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.NT}-download-name`},v=(e,i,t)=>{var l,d;const n=e.filter((e=>!("parent_id"in e))),o=n.filter((e=>e.type===a.dk.CONTROL)),r=n.filter((e=>e.type===a.dk.DIALOG)),s=r.filter((e=>e.code)),c=r.filter((e=>!e.code)),u=o.filter((e=>e.control_type===a.ze.CUSTOM)),v=o.filter((e=>e.control_type===a.ze.NOT_CUSTOM)),p=null===r||void 0===r?void 0:r.flatMap((e=>null===e||void 0===e?void 0:e.variables.map((i=>({...i,dialog_id:e.id})))));return{...t,isCorrect:!0,groups_standard:null===t||void 0===t||null===(l=t.groups_standard)||void 0===l?void 0:l.map((e=>{return e.type===a.dk.DIALOG?{...e,variables:e.variables.map((i=>{var t;return null!==(t=p.find((t=>e.id===t.dialog_id&&t.id===i.id)))&&void 0!==t?t:i}))}:(null===e||void 0===e?void 0:e.control_type)===a.ze.NOT_CUSTOM?null!==(i=v.find((i=>i.id===e.id)))&&void 0!==i?i:e:(null===e||void 0===e?void 0:e.control_type)===a.ze.CUSTOM&&null!==(t=u.find((i=>i.id===e.id)))&&void 0!==t?t:e;var i,t})),groups_specialty:null===t||void 0===t||null===(d=t.groups_specialty)||void 0===d?void 0:d.map((e=>{return e.type===a.dk.DIALOG?{...e,variables:e.variables.map((i=>{var t;return null!==(t=p.find((t=>e.id===t.dialog_id&&t.id===i.id)))&&void 0!==t?t:i}))}:(null===e||void 0===e?void 0:e.control_type)===a.ze.NOT_CUSTOM?null!==(i=v.find((i=>i.id===e.id)))&&void 0!==i?i:e:(null===e||void 0===e?void 0:e.control_type)===a.ze.CUSTOM&&null!==(t=u.find((i=>i.id===e.id)))&&void 0!==t?t:e;var i,t})),guideType:i,type:a.ts.\u57fa\u672c\u63a7\u4ef6,groups:[{id:crypto.randomUUID(),permission:1,title:"\u8fdb\u5ea6\u8868",type:"schedule",notCustoms:v,customs:u,dialogCodes:s,variables:c.map((e=>e.variables)).flat()}]}}}}]);
//# sourceMappingURL=1434.67d3ff80.chunk.js.map