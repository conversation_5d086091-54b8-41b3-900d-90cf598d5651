"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[1634],{11634:(t,e,r)=>{r.d(e,{_m:()=>x});var n=r(58168),o=r(9417),i=r(77387),l=r(92906),s=r(65043),a="object"===typeof performance&&"function"===typeof performance.now?function(){return performance.now()}:function(){return Date.now()};function c(t){cancelAnimationFrame(t.id)}function u(t,e){var r=a();var n={id:requestAnimationFrame((function o(){a()-r>=e?t.call(null):n.id=requestAnimationFrame(o)}))};return n}var f=-1;function d(t){if(void 0===t&&(t=!1),-1===f||t){var e=document.createElement("div"),r=e.style;r.width="50px",r.height="50px",r.overflow="scroll",document.body.appendChild(e),f=e.offsetWidth-e.clientWidth,document.body.removeChild(e)}return f}var h=null;function p(t){if(void 0===t&&(t=!1),null===h||t){var e=document.createElement("div"),r=e.style;r.width="50px",r.height="50px",r.overflow="scroll",r.direction="rtl";var n=document.createElement("div"),o=n.style;return o.width="100px",o.height="100px",e.appendChild(n),document.body.appendChild(e),e.scrollLeft>0?h="positive-descending":(e.scrollLeft=1,h=0===e.scrollLeft?"negative":"positive-ascending"),document.body.removeChild(e),h}return h}var m=function(t,e){return t};function v(t){var e,r=t.getItemOffset,a=t.getEstimatedTotalSize,f=t.getItemSize,h=t.getOffsetForIndexAndAlignment,v=t.getStartIndexForOffset,S=t.getStopIndexForStartIndex,I=t.initInstanceProps,_=t.shouldResetStyleCacheOnItemSizeChange,y=t.validateProps;return e=function(t){function e(e){var n;return(n=t.call(this,e)||this)._instanceProps=I(n.props,(0,o.A)(n)),n._outerRef=void 0,n._resetIsScrollingTimeoutId=null,n.state={instance:(0,o.A)(n),isScrolling:!1,scrollDirection:"forward",scrollOffset:"number"===typeof n.props.initialScrollOffset?n.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},n._callOnItemsRendered=void 0,n._callOnItemsRendered=(0,l.default)((function(t,e,r,o){return n.props.onItemsRendered({overscanStartIndex:t,overscanStopIndex:e,visibleStartIndex:r,visibleStopIndex:o})})),n._callOnScroll=void 0,n._callOnScroll=(0,l.default)((function(t,e,r){return n.props.onScroll({scrollDirection:t,scrollOffset:e,scrollUpdateWasRequested:r})})),n._getItemStyle=void 0,n._getItemStyle=function(t){var e,o=n.props,i=o.direction,l=o.itemSize,s=o.layout,a=n._getItemStyleCache(_&&l,_&&s,_&&i);if(a.hasOwnProperty(t))e=a[t];else{var c=r(n.props,t,n._instanceProps),u=f(n.props,t,n._instanceProps),d="horizontal"===i||"horizontal"===s,h="rtl"===i,p=d?c:0;a[t]=e={position:"absolute",left:h?void 0:p,right:h?p:void 0,top:d?0:c,height:d?"100%":u,width:d?u:"100%"}}return e},n._getItemStyleCache=void 0,n._getItemStyleCache=(0,l.default)((function(t,e,r){return{}})),n._onScrollHorizontal=function(t){var e=t.currentTarget,r=e.clientWidth,o=e.scrollLeft,i=e.scrollWidth;n.setState((function(t){if(t.scrollOffset===o)return null;var e=n.props.direction,l=o;if("rtl"===e)switch(p()){case"negative":l=-o;break;case"positive-descending":l=i-r-o}return l=Math.max(0,Math.min(l,i-r)),{isScrolling:!0,scrollDirection:t.scrollOffset<o?"forward":"backward",scrollOffset:l,scrollUpdateWasRequested:!1}}),n._resetIsScrollingDebounced)},n._onScrollVertical=function(t){var e=t.currentTarget,r=e.clientHeight,o=e.scrollHeight,i=e.scrollTop;n.setState((function(t){if(t.scrollOffset===i)return null;var e=Math.max(0,Math.min(i,o-r));return{isScrolling:!0,scrollDirection:t.scrollOffset<e?"forward":"backward",scrollOffset:e,scrollUpdateWasRequested:!1}}),n._resetIsScrollingDebounced)},n._outerRefSetter=function(t){var e=n.props.outerRef;n._outerRef=t,"function"===typeof e?e(t):null!=e&&"object"===typeof e&&e.hasOwnProperty("current")&&(e.current=t)},n._resetIsScrollingDebounced=function(){null!==n._resetIsScrollingTimeoutId&&c(n._resetIsScrollingTimeoutId),n._resetIsScrollingTimeoutId=u(n._resetIsScrolling,150)},n._resetIsScrolling=function(){n._resetIsScrollingTimeoutId=null,n.setState({isScrolling:!1},(function(){n._getItemStyleCache(-1,null)}))},n}(0,i.A)(e,t),e.getDerivedStateFromProps=function(t,e){return g(t,e),y(t),null};var x=e.prototype;return x.scrollTo=function(t){t=Math.max(0,t),this.setState((function(e){return e.scrollOffset===t?null:{scrollDirection:e.scrollOffset<t?"forward":"backward",scrollOffset:t,scrollUpdateWasRequested:!0}}),this._resetIsScrollingDebounced)},x.scrollToItem=function(t,e){void 0===e&&(e="auto");var r=this.props,n=r.itemCount,o=r.layout,i=this.state.scrollOffset;t=Math.max(0,Math.min(t,n-1));var l=0;if(this._outerRef){var s=this._outerRef;l="vertical"===o?s.scrollWidth>s.clientWidth?d():0:s.scrollHeight>s.clientHeight?d():0}this.scrollTo(h(this.props,t,e,i,this._instanceProps,l))},x.componentDidMount=function(){var t=this.props,e=t.direction,r=t.initialScrollOffset,n=t.layout;if("number"===typeof r&&null!=this._outerRef){var o=this._outerRef;"horizontal"===e||"horizontal"===n?o.scrollLeft=r:o.scrollTop=r}this._callPropsCallbacks()},x.componentDidUpdate=function(){var t=this.props,e=t.direction,r=t.layout,n=this.state,o=n.scrollOffset;if(n.scrollUpdateWasRequested&&null!=this._outerRef){var i=this._outerRef;if("horizontal"===e||"horizontal"===r)if("rtl"===e)switch(p()){case"negative":i.scrollLeft=-o;break;case"positive-ascending":i.scrollLeft=o;break;default:var l=i.clientWidth,s=i.scrollWidth;i.scrollLeft=s-l-o}else i.scrollLeft=o;else i.scrollTop=o}this._callPropsCallbacks()},x.componentWillUnmount=function(){null!==this._resetIsScrollingTimeoutId&&c(this._resetIsScrollingTimeoutId)},x.render=function(){var t=this.props,e=t.children,r=t.className,o=t.direction,i=t.height,l=t.innerRef,c=t.innerElementType,u=t.innerTagName,f=t.itemCount,d=t.itemData,h=t.itemKey,p=void 0===h?m:h,v=t.layout,g=t.outerElementType,S=t.outerTagName,I=t.style,_=t.useIsScrolling,y=t.width,x=this.state.isScrolling,M="horizontal"===o||"horizontal"===v,z=M?this._onScrollHorizontal:this._onScrollVertical,O=this._getRangeToRender(),w=O[0],R=O[1],b=[];if(f>0)for(var C=w;C<=R;C++)b.push((0,s.createElement)(e,{data:d,key:p(C,d),index:C,isScrolling:_?x:void 0,style:this._getItemStyle(C)}));var T=a(this.props,this._instanceProps);return(0,s.createElement)(g||S||"div",{className:r,onScroll:z,ref:this._outerRefSetter,style:(0,n.A)({position:"relative",height:i,width:y,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:o},I)},(0,s.createElement)(c||u||"div",{children:b,ref:l,style:{height:M?"100%":T,pointerEvents:x?"none":void 0,width:M?T:"100%"}}))},x._callPropsCallbacks=function(){if("function"===typeof this.props.onItemsRendered&&this.props.itemCount>0){var t=this._getRangeToRender(),e=t[0],r=t[1],n=t[2],o=t[3];this._callOnItemsRendered(e,r,n,o)}if("function"===typeof this.props.onScroll){var i=this.state,l=i.scrollDirection,s=i.scrollOffset,a=i.scrollUpdateWasRequested;this._callOnScroll(l,s,a)}},x._getRangeToRender=function(){var t=this.props,e=t.itemCount,r=t.overscanCount,n=this.state,o=n.isScrolling,i=n.scrollDirection,l=n.scrollOffset;if(0===e)return[0,0,0,0];var s=v(this.props,l,this._instanceProps),a=S(this.props,s,l,this._instanceProps),c=o&&"backward"!==i?1:Math.max(1,r),u=o&&"forward"!==i?1:Math.max(1,r);return[Math.max(0,s-c),Math.max(0,Math.min(e-1,a+u)),s,a]},e}(s.PureComponent),e.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},e}var g=function(t,e){t.children,t.direction,t.height,t.layout,t.innerTagName,t.outerTagName,t.width,e.instance},S=function(t,e,r){var n=t.itemSize,o=r.itemMetadataMap,i=r.lastMeasuredIndex;if(e>i){var l=0;if(i>=0){var s=o[i];l=s.offset+s.size}for(var a=i+1;a<=e;a++){var c=n(a);o[a]={offset:l,size:c},l+=c}r.lastMeasuredIndex=e}return o[e]},I=function(t,e,r,n,o){for(;n<=r;){var i=n+Math.floor((r-n)/2),l=S(t,i,e).offset;if(l===o)return i;l<o?n=i+1:l>o&&(r=i-1)}return n>0?n-1:0},_=function(t,e,r,n){for(var o=t.itemCount,i=1;r<o&&S(t,r,e).offset<n;)r+=i,i*=2;return I(t,e,Math.min(r,o-1),Math.floor(r/2),n)},y=function(t,e){var r=t.itemCount,n=e.itemMetadataMap,o=e.estimatedItemSize,i=e.lastMeasuredIndex,l=0;if(i>=r&&(i=r-1),i>=0){var s=n[i];l=s.offset+s.size}return l+(r-i-1)*o},x=v({getItemOffset:function(t,e,r){return S(t,e,r).offset},getItemSize:function(t,e,r){return r.itemMetadataMap[e].size},getEstimatedTotalSize:y,getOffsetForIndexAndAlignment:function(t,e,r,n,o,i){var l=t.direction,s=t.height,a=t.layout,c=t.width,u="horizontal"===l||"horizontal"===a?c:s,f=S(t,e,o),d=y(t,o),h=Math.max(0,Math.min(d-u,f.offset)),p=Math.max(0,f.offset-u+f.size+i);switch("smart"===r&&(r=n>=p-u&&n<=h+u?"auto":"center"),r){case"start":return h;case"end":return p;case"center":return Math.round(p+(h-p)/2);default:return n>=p&&n<=h?n:n<p?p:h}},getStartIndexForOffset:function(t,e,r){return function(t,e,r){var n=e.itemMetadataMap,o=e.lastMeasuredIndex;return(o>0?n[o].offset:0)>=r?I(t,e,o,0,r):_(t,e,Math.max(0,o),r)}(t,r,e)},getStopIndexForStartIndex:function(t,e,r,n){for(var o=t.direction,i=t.height,l=t.itemCount,s=t.layout,a=t.width,c="horizontal"===o||"horizontal"===s?a:i,u=S(t,e,n),f=r+c,d=u.offset+u.size,h=e;h<l-1&&d<f;)h++,d+=S(t,h,n).size;return h},initInstanceProps:function(t,e){var r={itemMetadataMap:{},estimatedItemSize:t.estimatedItemSize||50,lastMeasuredIndex:-1};return e.resetAfterIndex=function(t,n){void 0===n&&(n=!0),r.lastMeasuredIndex=Math.min(r.lastMeasuredIndex,t-1),e._getItemStyleCache(-1),n&&e.forceUpdate()},r},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(t){t.itemSize}})},92906:(t,e,r)=>{r.r(e),r.d(e,{default:()=>i});var n=Number.isNaN||function(t){return"number"===typeof t&&t!==t};function o(t,e){if(t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(o=t[r],i=e[r],!(o===i||n(o)&&n(i)))return!1;var o,i;return!0}const i=function(t,e){var r;void 0===e&&(e=o);var n,i=[],l=!1;return function(){for(var o=[],s=0;s<arguments.length;s++)o[s]=arguments[s];return l&&r===this&&e(o,i)||(n=t.apply(this,o),l=!0,r=this,i=o),n}}}}]);
//# sourceMappingURL=1634.26f53512.chunk.js.map