"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[1750],{1750:(e,a,l)=>{l.r(a),l.d(a,{default:()=>A});var n=l(65043),t=l(25055),i=l(8918),r=l(83720),d=l(12624),s=l(19853),o=l.n(s),c=l(81143),u=l(74117),p=l(68358),v=l(63189),b=l(13830),m=l(63379),h=l(70579);const{useForm:x,Item:j}=t.A,y=c.Ay.div`
    .bind-input-variable{
        display: block;padding: 0;
        .bind-value-span{
            margin-bottom: 8px;
        }
    }
`,A=e=>{let{open:a,onClose:l,config:s,setConfig:c,inputVariableType:A}=e;const{t:f}=(0,u.Bd)(),[g]=x();(0,n.useEffect)((()=>{o()(s,g.getFieldsValue())||g.setFieldsValue(s)}),[s]);return(0,h.jsx)(p.A,{open:a,onClose:l,children:(0,h.jsx)(y,{children:(0,h.jsx)(t.A,{form:g,onValuesChange:(e,a)=>{var l;let n=a;null!==e&&void 0!==e&&null!==(l=e.variable)&&void 0!==l&&l.value&&(n={...n,attr:{...n.attr,label:e.variable.value.variable_name}}),c(n)},children:(0,h.jsx)(i.A,{defaultActiveKey:"attr",items:[{key:"attr",label:f("\u5c5e\u6027"),forceRender:!0,children:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(j,{label:f("\u7ec4\u4ef6\u5bbd\u5ea6"),name:["attr","compWidth"],children:(0,h.jsx)(v.A,{style:{width:"100%"},addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,h.jsx)(j,{label:f("\u7ec4\u4ef6\u9ad8\u5ea6"),name:["attr","compHeight"],children:(0,h.jsx)(v.A,{style:{width:"100%"},addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),A!==b.p.Label&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(j,{label:f("\u6807\u7b7e\u6587\u672c"),name:["attr","label"],children:(0,h.jsx)(r.A,{})}),(0,h.jsx)(j,{label:f("\u6807\u7b7e\u659c\u4f53"),name:["attr","labelItalic"],valuePropName:"checked",children:(0,h.jsx)(d.A,{})}),(0,h.jsx)(j,{label:f("\u6807\u7b7e\u52a0\u7c97"),name:["attr","labelBold"],valuePropName:"checked",children:(0,h.jsx)(d.A,{})})]}),(0,h.jsx)(j,{label:f("\u5185\u5bb9\u659c\u4f53"),name:["attr","contentItalic"],valuePropName:"checked",children:(0,h.jsx)(d.A,{})}),(0,h.jsx)(j,{label:f("\u5185\u5bb9\u52a0\u7c97"),name:["attr","contentBold"],valuePropName:"checked",children:(0,h.jsx)(d.A,{})})]})},{key:"variable",label:f("\u53d8\u91cf"),forceRender:!0,children:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(j,{label:f("\u503c"),name:["variable","value"],labelCol:{style:{width:"46px"}},children:(0,h.jsx)(b.A,{inputVariableType:A})}),(0,h.jsx)(j,{label:f("\u53ef\u89c1\u6027"),name:["variable","visible"],labelCol:{style:{width:"46px"}},children:(0,h.jsx)(b.A,{inputVariableType:b.p[f("\u5e03\u5c14\u578b")]})}),A!==b.p.Label&&(0,h.jsx)(j,{label:f("\u53ef\u7f16\u8f91"),name:["variable","disabled"],labelCol:{style:{width:"46px"}},children:(0,h.jsx)(b.A,{inputVariableType:b.p[f("\u5e03\u5c14\u578b")]})})]})},...A!==b.p.Label?[{key:"event",label:f("\u4e8b\u4ef6"),forceRender:!0,children:(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(j,{label:f("\u5185\u5bb9\u53d8\u5316\u65f6\u89e6\u53d1"),name:["event","change"],children:(0,h.jsx)(m.A,{})})})}]:[]]})})})})}},13830:(e,a,l)=>{l.d(a,{A:()=>x,p:()=>v.ps});var n=l(65043),t=l(16569),i=l(6051),r=l(95206),d=l(81143),s=l(80077),o=l(74117),c=l(88359),u=l(51554),p=l(78178),v=l(56543),b=l(754),m=l(70579);const h=d.Ay.div`
    .bind-input-variable{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;

        .label{
            margin-right: 10px;
        }
        .bind-value-span{
            word-break: break-all;
        }
        .bind-fun-div{
            white-space: nowrap;
        }
    }
`,x=e=>{let{id:a,value:l,onChange:d,inputVariableType:x,checkFn:j,isSetProgrammableParameters:y=!1}=e;const A=(0,s.wA)(),{t:f}=(0,o.Bd)(),g=(0,n.useRef)(),[C,k]=(0,n.useState)(!1),[w,V]=(0,n.useState)(),[_,S]=(0,n.useState)("add");(0,n.useEffect)((()=>{l&&T(l)}),[l]);const T=e=>{if((null===e||void 0===e?void 0:e.variable_type)!==x)return void d();(0,b.B)("inputVariable","inputVariableMap").has(e.code)||d()},B=e=>{const a=j&&j(e);if(a)return void t.Ay.error(a);const{id:l,code:n,variable_name:i,variable_type:r,name:s}=e;d({id:l,code:n,variable_name:null!==i&&void 0!==i?i:s,variable_type:r,restrict:{variableType:v.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x}})};return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(h,{children:(0,m.jsxs)("div",{className:"bind-input-variable",children:[(0,m.jsxs)("div",{className:"bind-value-span",children:[f("\u7ed1\u5b9a\u53d8\u91cf"),":",null===l||void 0===l?void 0:l.variable_name]}),(0,m.jsx)("div",{className:"bind-fun-div",children:(0,m.jsxs)(i.A,{children:[(0,m.jsx)(r.Ay,{onClick:()=>{g.current.open({variableType:v.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x})},children:"\u9009\u62e9"}),l?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(r.Ay,{onClick:()=>{V(null===l||void 0===l?void 0:l.id),S("edit"),k(!0)},children:f("\u7f16\u8f91")}),(0,m.jsx)(r.Ay,{onClick:()=>d(),children:f("\u89e3\u7ed1")})]}):(0,m.jsx)(r.Ay,{onClick:()=>{S("add"),k(!0)},children:f("\u65b0\u5efa")})]})})]})}),(0,m.jsx)(u.A,{ref:g,isSetProgrammableParameters:y,handleSelectedVariable:B}),C&&(0,m.jsx)(p.A,{isSetProgrammableParameters:y,variableType:x,modalIndex:0,editId:w,mode:_,open:C,onOk:async e=>{const a=await A((0,c.w)()),l=null===a||void 0===a?void 0:a.find((a=>a.code===e.code));l&&B(l),k(!1)},onCancel:()=>{k(!1)}})]})}},51554:(e,a,l)=>{l.d(a,{A:()=>j});var n=l(65043),t=l(80077),i=l(16569),r=l(83720),d=l(79806),s=l(74117),o=l(93950),c=l.n(o),u=l(56543),p=l(75440),v=l(29977),b=l(6051),m=l(70579);const h=e=>{let{handleSelected:a,t:l}=e;return[{title:l?l("\u540d\u79f0"):"\u540d\u79f0",dataIndex:"variable_name",key:"variable_name"},{title:l?l("\u6807\u8bc6\u7b26"):"\u6807\u8bc6\u7b26",dataIndex:"code",key:"code"},{title:l?l("\u64cd\u4f5c"):"\u64cd\u4f5c",dataIndex:"code",key:"code",render:(e,l)=>(0,m.jsx)(b.A,{size:"middle",children:(0,m.jsx)("a",{onClick:()=>a(l),children:"\u9009\u62e9"})})}]},x=(e,a)=>{let{handleSelectedVariable:l=e=>console.log(e),isSetProgrammableParameters:o=!1}=e;const b=(0,v.A)(),x=(0,t.d4)((e=>e.template.resultData)),[j,y]=(0,n.useState)(!1),[A,f]=(0,n.useState)(),[g,C]=(0,n.useState)([]),[k,w]=(0,n.useState)([]),{t:V}=(0,s.Bd)(),_=(0,n.useMemo)((()=>b.map((e=>({...e,variable_name:null===e||void 0===e?void 0:e.name})))),[b]),S=(0,n.useMemo)((()=>x.map((e=>({...e,id:e.code})))),[x]);(0,n.useEffect)((()=>{j&&T()}),[j]);const T=()=>{if(A)switch(null===A||void 0===A?void 0:A.variableType){case u.oY.\u8f93\u5165\u53d8\u91cf:{const e=[..._.filter((e=>!(null!==A&&void 0!==A&&A.inputVarType)||e.variable_type===(null===A||void 0===A?void 0:A.inputVarType)))];w(e),C(e);break}case u.oY.\u4fe1\u53f7\u53d8\u91cf:case u.oY.\u7ed3\u679c\u53d8\u91cf:w(S),C(S);break;default:console.log("\u672a\u5904\u7406\u7684\u53d8\u91cf\u7c7b\u578b",null===A||void 0===A?void 0:A.variableType)}};(0,n.useImperativeHandle)(a,(()=>({open:e=>{f(e),y(!0)}})));const B=c()((async e=>{if(e){const a=g.filter((a=>{const l=a.variable_name.toLowerCase(),n=a.code.toLowerCase(),t=e.toLowerCase();return l.includes(t)||n.includes(t)}));w(a)}else w(g)}),200);return(0,m.jsxs)(p.A,{open:j,onCancel:()=>{y(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:[(0,m.jsx)(r.A,{allowClear:!0,onChange:e=>B(e.target.value),placeholder:V("\u540d\u79f0/\u5185\u90e8\u540d"),style:{width:"300px",marginBottom:"10px"}}),(0,m.jsx)(d.A,{rowKey:"code",columns:h({handleSelected:e=>{var a;!o||"Array"===(null===e||void 0===e?void 0:e.variable_type)&&"programmableParameters"===(null===e||void 0===e||null===(a=e.custom_array_tab)||void 0===a?void 0:a.useType)?(l(e,A),y(!1)):i.Ay.error("\u8bf7\u9009\u62e9\u81ea\u5b9a\u4e49\u6570\u7ec4\u7528\u9014\u4e3a\u7a0b\u63a7\u53c2\u6570\u7684\u53d8\u91cf")}}),dataSource:k})]})},j=(0,n.forwardRef)(x)},68358:(e,a,l)=>{l.d(a,{A:()=>v});var n=l(65043),t=l(48677),i=l(80077),r=l(14463),d=l(25055),s=l(36282),o=l(96603),c=l(14524),u=l(70579);const p=e=>{let{setting:a,onChange:l}=e;const[t]=d.A.useForm();(0,n.useEffect)((()=>{t.setFieldsValue({...a})}),[a]);return(0,u.jsx)(s.A,{content:(0,u.jsxs)(d.A,{form:t,name:"basic",labelCol:{style:{width:35}},onValuesChange:(e,a)=>{l(a)},children:[(0,u.jsx)(d.A.Item,{label:"\u4f4d\u7f6e",name:"placement",children:(0,u.jsxs)(o.Ay.Group,{size:"small",children:[(0,u.jsx)(o.Ay.Button,{value:"top",children:"\u4e0a"}),(0,u.jsx)(o.Ay.Button,{value:"right",children:"\u53f3"}),(0,u.jsx)(o.Ay.Button,{value:"bottom",children:"\u4e0b"}),(0,u.jsx)(o.Ay.Button,{value:"left",children:"\u5de6"})]})}),(0,u.jsx)(d.A.Item,{label:"\u5c3a\u5bf8",name:"size",children:(0,u.jsxs)(o.Ay.Group,{size:"small",children:[(0,u.jsx)(o.Ay.Button,{value:"default",children:"\u6b63\u5e38"}),(0,u.jsx)(o.Ay.Button,{value:"large",children:"\u5927"})]})})]}),title:"",trigger:"click",placement:"leftTop",children:(0,u.jsx)(c.A,{})})},v=e=>{let{children:a,open:l,onClose:n}=e;const d=(0,i.wA)(),{drawSetting:s}=(0,i.d4)((e=>e.split));return(0,u.jsx)(u.Fragment,{children:l&&(0,u.jsx)(t.A,{open:l,size:null===s||void 0===s?void 0:s.size,placement:null===s||void 0===s?void 0:s.placement,onClose:n,extra:(0,u.jsx)(p,{setting:s,onChange:e=>{d({type:r.cd,param:e})}}),children:a})})}}}]);
//# sourceMappingURL=1750.53366859.chunk.js.map