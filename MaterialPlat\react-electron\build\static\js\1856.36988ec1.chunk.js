"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[1856],{31856:(e,l,i)=>{i.r(l),i.d(l,{default:()=>c});var a=i(65043),d=i(80077),n=i(14607),t=i(41445),o=i(97292),r=i(29977);var s=i(55518),v=i(70579);const u=e=>{let{variable:l,disabled:i,onChange:s}=e;const{control_tab:u,default_val:c}=l,b=(0,r.A)(),p=(0,d.d4)((e=>e.global.unitList)),[h,_]=(0,a.useState)();(0,a.useEffect)((()=>{if(null!==c&&void 0!==c&&c.data){var e,l,i;(a=null===h||void 0===h||null===(e=h.data[0])||void 0===e||null===(l=e.related_variables[0])||void 0===l||null===(i=l.variables)||void 0===i?void 0:i.map((e=>null===e||void 0===e?void 0:e.id)),d=null===u||void 0===u?void 0:u.related_variables,(null===a||void 0===a?void 0:a.length)===(null===d||void 0===d?void 0:d.length)&&(null===a||void 0===a?void 0:a.every(((e,l)=>e===d[l]))))&&h||(null!==c&&void 0!==c&&c.data&&c.data.length>0?_({...c,groups:null===c||void 0===c?void 0:c.data}):_(f()))}var a,d}),[u.dialog_type,u.type,u.control_name,u.default_name,u.code,u.related_variables]);const f=()=>{var e,l,i,a;return{desc:(null===u||void 0===u?void 0:u.dialog_type)===t.YM.SIGNAL&&u.is_daq?"input":"daq",groups:[{id:crypto.randomUUID(),title:(null===u||void 0===u?void 0:u.control_name)||(null===(e=t.vU.find((e=>e.value===u.dialog_type)))||void 0===e?void 0:e.label),control_type:null===u||void 0===u?void 0:u.type,type:"control",dialog_type:null===u||void 0===u?void 0:u.dialog_type,default_name:null===u||void 0===u?void 0:u.default_name,code:null===u||void 0===u?void 0:u.code,signals:[],results:[],variables:[],customs:[],related_variables:(null===u||void 0===u||null===(l=u.related_variables)||void 0===l?void 0:l.length)>0?[{id:crypto.randomUUID(),title:"\u5173\u8054\u53d8\u91cf",control_type:null===u||void 0===u?void 0:u.type,type:"control",variables:null===b||void 0===b||null===(i=b.filter((e=>{var l;return null===u||void 0===u||null===(l=u.related_variables)||void 0===l?void 0:l.includes(e.id)})))||void 0===i||null===(a=i.sort(((e,l)=>{var i,a;return(null===u||void 0===u||null===(i=u.related_variables)||void 0===i?void 0:i.indexOf(e.id))-(null===u||void 0===u||null===(a=u.related_variables)||void 0===a?void 0:a.indexOf(l.id))})))||void 0===a?void 0:a.map((e=>{var l;return{...e,code:null===e||void 0===e||null===(l=e.code)||void 0===l?void 0:l.replace(o.O.INPUT,o.O.CONTROL_INPUT)}}))}]:[]}]}};return(0,v.jsx)(n.A,{unitList:p,disabled:i,paramsData:h,callback:e=>{s&&(console.log(e),s(((e,l)=>{const i=e.filter((e=>!("parent_id"in e))),a=i.filter((e=>e.type===t.dk.CONTROL)),d=i.filter((e=>e.type===t.dk.DIALOG)),n=d.filter((e=>e.code)),o=d.filter((e=>!e.code)),r=a.filter((e=>e.control_type===t.ze.CUSTOM)),s=a.filter((e=>e.control_type===t.ze.NOT_CUSTOM));return{...l,data:i,groups:[{id:crypto.randomUUID(),permission:1,title:"\u8fdb\u5ea6\u8868",type:"schedule",notCustoms:s,customs:r,dialogCodes:n,variables:o.map((e=>e.variables)).flat()}]}})(e,h)))}})},c=e=>{let{variable:l,disabled:i,onChange:a}=e;return(0,v.jsx)(s.A,{variable:l,disabled:i,onChange:a,render:e=>{let{innerDisabled:i}=e;return(0,v.jsx)(u,{variable:l,disabled:i,onChange:e=>{a({...l,default_val:e})}})}})}},55518:(e,l,i)=>{i.d(l,{A:()=>N});var a=i(65043),d=i(74117),n=i(56543),t=i(81143),o=i(68374),r=i(18650);const s=0,v="left";var u=i(70579);const c=t.Ay.div`
    width: ${(0,o.D0)("20px")};
    height: ${(0,o.D0)("20px")};
    background-size: ${(0,o.D0)("20px")} ${(0,o.D0)("20px")};
    background-image: url(${e=>{let{isConstant:l}=e;return l?r.fd:r.Mo}});
`,b=e=>{let{variable:l,onChange:i,disabled:a}=e;const{default_val:d,is_fx:n}=l;return!n||a?(0,u.jsx)(u.Fragment,{}):(0,u.jsx)(c,{isConstant:d.isConstant===s,onClick:()=>{i({...l,default_val:{...d,isConstant:0===(null===d||void 0===d?void 0:d.isConstant)?1:0}})}})};var p=i(95206),h=i(34154),_=i(67208),f=i(16090),g=i(36497),y=i(29977);const m=e=>{var l;let{disabled:i,variable:d,handleChange:n}=e;const t=(0,y.A)(),o=(0,a.useMemo)((()=>(null===t||void 0===t?void 0:t.filter((e=>e.variable_type===d.variable_type&&e.id!==d.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[t,d]);return(0,u.jsx)(g.A,{showSearch:!0,optionFilterProp:"labelName",disabled:i,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===d||void 0===d||null===(l=d.default_val)||void 0===l?void 0:l.variable_id,options:o,onChange:(e,l)=>n(l)})},x=e=>{let{disabled:l,content:i,buttonType:d,actionId:t,script:o}=e;const[r,s]=(0,a.useState)(!1),{startAction:v}=(0,f.A)(),c=()=>{d!==h.NR.\u52a8\u4f5c?d!==h.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{s(!0),await(0,_.O5k)({script:o,result_type:n.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{s(!1)}})():(async()=>{try{t&&(s(!0),await v({action_id:t}))}catch(e){console.log("err when handleSubmitAction",e)}finally{s(!1)}})()};return(0,u.jsx)(p.Ay,{loading:r,disabled:l,className:"button-width",onClick:()=>c(),children:i})},C=t.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:l}=e;return l?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,o.D0)("80px")};
        pointer-events: auto;
    }
`,w=e=>{let{disabled:l,variable:i,render:a,onChange:d,buttonShow:n}=e;const{button_variable_tab:t,default_val:o}=i;return(0,u.jsx)(C,{isLeft:(null===t||void 0===t?void 0:t.position)===v,children:1===o.isConstant?(0,u.jsx)(m,{disabled:l,variable:i,handleChange:e=>{d({...i,default_val:{...o,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,u.jsxs)(u.Fragment,{children:[n&&(null===t||void 0===t?void 0:t.isEnable)&&(0,u.jsx)(x,{...t,disabled:l}),a()]})})};var j=i(12624),S=i(32513);const k=e=>{let{variable:l,disabled:i=!1,onChange:a,usableShowType:d="checkbox"}=e;return null!==l&&void 0!==l&&l.is_enable?"switch"===d?(0,u.jsx)(j.A,{disabled:i,checked:null===l||void 0===l?void 0:l.is_feature,onChange:e=>{a({...l,is_feature:e})}}):(0,u.jsx)(S.A,{disabled:i,checked:null===l||void 0===l?void 0:l.is_feature,onChange:e=>{a({...l,is_feature:e.target.checked})}}):(0,u.jsx)(u.Fragment,{})},A=t.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,N=e=>{let{variable:l,disabled:i=!1,onChange:a,render:t,usableShow:o=!0,buttonShow:r=!0,fxShow:s=!0,nameShow:v=!0,usableShowType:c}=e;const{t:p}=(0,d.Bd)(),h=i||(l.variable_type===n.ps.\u5e03\u5c14\u578b?(null===l||void 0===l?void 0:l.is_enable)&&(null===l||void 0===l?void 0:l.is_feature):(null===l||void 0===l?void 0:l.is_enable)&&!(null!==l&&void 0!==l&&l.is_feature));return(0,u.jsxs)(A,{children:[(o||v)&&(0,u.jsx)("div",{className:"input-render-left",children:(0,u.jsxs)("div",{children:[o&&(0,u.jsx)(k,{variable:l,disabled:i,onChange:a,usableShowType:c}),v&&(0,u.jsx)("div",{className:"variable_name",children:p(l.name)})]})}),(0,u.jsxs)("div",{className:"input-render-right",children:[s&&(0,u.jsx)(b,{variable:l,onChange:a,disabled:h}),(0,u.jsx)(w,{disabled:h,variable:l,onChange:a,buttonShow:r,render:()=>t({innerDisabled:h})})]})]})}}}]);
//# sourceMappingURL=1856.36988ec1.chunk.js.map