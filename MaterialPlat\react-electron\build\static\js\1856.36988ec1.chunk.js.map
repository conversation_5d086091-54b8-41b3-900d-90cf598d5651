{"version": 3, "file": "static/js/1856.36988ec1.chunk.js", "mappings": "mQAWA,MAAMA,EAAgBC,IAEf,IAFgB,SACnBC,EAAQ,SAAEC,EAAQ,SAAEC,GACvBH,EACG,MAAM,YAAEI,EAAW,YAAEC,GAAgBJ,EAC/BK,GAAoBC,EAAAA,EAAAA,KACpBC,GAAWC,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,OAAOH,YAC5CI,EAAOC,IAAYC,EAAAA,EAAAA,aAE1BC,EAAAA,EAAAA,YAAU,KACN,GAAe,OAAXV,QAAW,IAAXA,GAAAA,EAAaW,KAAM,CAAC,IAADC,EAAAC,EAAAC,GAmBHC,EAlBqB,OAALR,QAAK,IAALA,GAAc,QAATK,EAALL,EAAOI,KAAK,UAAE,IAAAC,GAAsB,QAAtBC,EAAdD,EAAgBI,kBAAkB,UAAE,IAAAH,GAAW,QAAXC,EAApCD,EAAsCI,iBAAS,IAAAH,OAA1C,EAALA,EAAiDI,KAAIC,GAAM,OAADA,QAAC,IAADA,OAAC,EAADA,EAAGC,KAkBvEC,EAlBuF,OAAXtB,QAAW,IAAXA,OAAW,EAAXA,EAAaiB,mBAoB3G,OAAJD,QAAI,IAAJA,OAAI,EAAJA,EAAMO,WAAe,OAAJD,QAAI,IAAJA,OAAI,EAAJA,EAAMC,UACd,OAAJP,QAAI,IAAJA,OAAI,EAAJA,EAAMQ,OAAM,CAACC,EAASC,IAAUD,IAAYH,EAAKI,QApBpClB,IACC,OAAXP,QAAW,IAAXA,GAAAA,EAAaW,MAAQX,EAAYW,KAAKW,OAAS,EAC/Cd,EAAS,IAAKR,EAAa0B,OAAmB,OAAX1B,QAAW,IAAXA,OAAW,EAAXA,EAAaW,OAEhDH,EAASmB,KAGrB,CAUJ,IAAwBZ,EAAMM,CAV1B,GACD,CAECtB,EAAY6B,YACZ7B,EAAY8B,KACZ9B,EAAY+B,aACZ/B,EAAYgC,aACZhC,EAAYiC,KACZjC,EAAYiB,oBAShB,MASMW,EAAOA,KAAO,IAADM,EAAAC,EAAAC,EAAAC,EACf,MAAO,CACHC,MAVW,OAAXtC,QAAW,IAAXA,OAAW,EAAXA,EAAa6B,eAAgBU,EAAAA,GAAYC,QAAUxC,EAAYyC,OACxD,QAEJ,MAQHd,OAAQ,CAAC,CACLN,GAAIqB,OAAOC,aACXC,OAAkB,OAAX5C,QAAW,IAAXA,OAAW,EAAXA,EAAa+B,gBAA2E,QAA/DG,EAAIW,EAAAA,GAAaC,MAAKC,GAAKA,EAAEvC,QAAUR,EAAY6B,qBAAY,IAAAK,OAAA,EAA3DA,EAA6Dc,OACjGC,aAAyB,OAAXjD,QAAW,IAAXA,OAAW,EAAXA,EAAa8B,KAC3BA,KAAM,UACND,YAAwB,OAAX7B,QAAW,IAAXA,OAAW,EAAXA,EAAa6B,YAC1BG,aAAyB,OAAXhC,QAAW,IAAXA,OAAW,EAAXA,EAAagC,aAC3BC,KAAiB,OAAXjC,QAAW,IAAXA,OAAW,EAAXA,EAAaiC,KACnBiB,QAAS,GACTC,QAAS,GACTjC,UAAW,GACXkC,QAAS,GACTnC,mBAA8B,OAAXjB,QAAW,IAAXA,GAA8B,QAAnBmC,EAAXnC,EAAaiB,yBAAiB,IAAAkB,OAAnB,EAAXA,EAAgCZ,QAAS,EAAI,CAAC,CAC7DF,GAAIqB,OAAOC,aACXC,MAAO,2BACPK,aAAyB,OAAXjD,QAAW,IAAXA,OAAW,EAAXA,EAAa8B,KAC3BA,KAAM,UACNZ,UAA4B,OAAjBhB,QAAiB,IAAjBA,GACsD,QADrCkC,EAAjBlC,EACLmD,QAAON,IAAC,IAAAO,EAAA,OAAe,OAAXtD,QAAW,IAAXA,GAA8B,QAAnBsD,EAAXtD,EAAaiB,yBAAiB,IAAAqC,OAAnB,EAAXA,EAAgCC,SAASR,EAAE1B,GAAG,WAAC,IAAAe,GACkD,QADlDC,EADtDD,EAELoB,MAAK,CAACC,EAAGC,KAAC,IAAAC,EAAAC,EAAA,OAAgB,OAAX5D,QAAW,IAAXA,GAA8B,QAAnB2D,EAAX3D,EAAaiB,yBAAiB,IAAA0C,OAAnB,EAAXA,EAAgCE,QAAQJ,EAAEpC,MAAiB,OAAXrB,QAAW,IAAXA,GAA8B,QAAnB4D,EAAX5D,EAAaiB,yBAAiB,IAAA2C,OAAnB,EAAXA,EAAgCC,QAAQH,EAAErC,IAAI,eAAAgB,OAFvF,EAAjBA,EAGLlB,KAAIC,IAAC,IAAA0C,EAAA,MAAK,IAAK1C,EAAGa,KAAO,OAADb,QAAC,IAADA,GAAO,QAAN0C,EAAD1C,EAAGa,YAAI,IAAA6B,OAAN,EAADA,EAASC,QAAQC,EAAAA,EAAWC,MAAOD,EAAAA,EAAWE,eAAgB,MAC3F,KAEZ,EAUL,OACIC,EAAAA,EAAAA,KAACC,EAAAA,EAAY,CACThE,SAAUA,EACVN,SAzCGA,EA0CHuE,WAAY7D,EACZ8D,SAZYC,IACZxE,IACAyE,QAAQC,IAAIF,GACZxE,ECrFiB2E,EAACC,EAAa/D,KACvC,MAAMgE,EAAiBD,EAAYtB,QAAON,KAAO,cAAeA,KAC1D8B,EAAcD,EAAevB,QAAON,GAAKA,EAAEjB,OAASgD,EAAAA,GAAiBC,UACrEC,EAAaJ,EAAevB,QAAON,GAAKA,EAAEjB,OAASgD,EAAAA,GAAiBG,SACpEC,EAAcF,EAAW3B,QAAON,GAAKA,EAAEd,OACvCkD,EAAiBH,EAAW3B,QAAON,IAAMA,EAAEd,OAC3CmD,EAAgBP,EAAYxB,QAAON,GAAKA,EAAEE,eAAiBoC,EAAAA,GAAYC,SACvEC,EAAmBV,EAAYxB,QAAON,GAAKA,EAAEE,eAAiBoC,EAAAA,GAAYG,aAEhF,MAAO,IACA5E,EACHA,KAAMgE,EACNjD,OAAQ,CAAC,CACLN,GAAIqB,OAAOC,aACX8C,WAAY,EACZ7C,MAAO,qBACPd,KAAM,WACN4D,WAAYH,EACZnC,QAASgC,EACTF,cACAhE,UAAWiE,EAAehE,KAAIC,GAAKA,EAAEF,YAAWyE,SAEvD,ED+DgBjB,CAAcH,EAAO/D,IAClC,GASE,EA0BV,EAtBgBoF,IAAuC,IAAtC,SAAE/F,EAAQ,SAAEC,EAAQ,SAAEC,GAAU6F,EAC7C,OACIzB,EAAAA,EAAAA,KAAC0B,EAAAA,EAAY,CACThG,SAAUA,EACVC,SAAUA,EACVC,SAAUA,EACV+F,OAAQC,IAAA,IAAC,cAAEC,GAAeD,EAAA,OACtB5B,EAAAA,EAAAA,KAACxE,EAAa,CACVE,SAAUA,EACVC,SAAUkG,EACVjG,SAAWkG,IACPlG,EAAS,IACFF,EACHI,YAAagG,GACf,GAER,GAER,C,yGEjHH,MAAMC,EACL,EAUKC,EAEH,O,eCLH,MAAMC,EAAKC,EAAAA,GAAOC,GAAG;cACfC,EAAAA,EAAAA,IAAI;eACHA,EAAAA,EAAAA,IAAI;wBACKA,EAAAA,EAAAA,IAAI,YAAWA,EAAAA,EAAAA,IAAI;4BACd3G,IAAA,IAAC,WAAE4G,GAAY5G,EAAA,OAAM4G,EAAaC,EAAAA,GAAUC,EAAAA,EAAM;EA0C9E,EA/Bed,IAER,IAFS,SACZ/F,EAAQ,SAAEE,EAAQ,SAAED,GACvB8F,EACG,MAAM,YAAE3F,EAAW,MAAE0G,GAAU9G,EAgB/B,OAAK8G,GAAS7G,GACHqE,EAAAA,EAAAA,KAAAyC,EAAAA,SAAA,KAIPzC,EAAAA,EAAAA,KAACiC,EAAE,CACCI,WAAYvG,EAAYuG,aAAeN,EACvCW,QAASA,KAjBb9G,EAAS,IACFF,EACHI,YAAa,IACNA,EACHuG,WAAwC,KAAjB,OAAXvG,QAAW,IAAXA,OAAW,EAAXA,EAAauG,YAAmB,EAAI,IAapB,GAClC,E,sEClDV,MAmCA,EAnCiB5G,IAA2C,IAADkH,EAAA,IAAzC,SAAEhH,EAAQ,SAAED,EAAQ,aAAEkH,GAAcnH,EAClD,MAAMM,GAAoBC,EAAAA,EAAAA,KAMpB6G,GAAkBC,EAAAA,EAAAA,UAAQ,KAEE,OAAjB/G,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmBmD,QAAOjC,GAAKA,EAAE8F,gBAAkBrH,EAASqH,eAAiB9F,EAAEC,KAAOxB,EAASwB,MAEhGF,KAAKgG,IACN,IACAA,EACHC,UAAW,GAAGD,EAAKE,QAAQF,EAAKlF,aAGzC,CAAC/B,EAAmBL,IAEvB,OACIsE,EAAAA,EAAAA,KAACmD,EAAAA,EAAM,CACHC,YAAU,EACVC,iBAAiB,YACjB1H,SAAUA,EACV2H,WAAY,CAAEzE,MAAO,YAAaxC,MAAO,MACzCkH,UAAU,cACVlH,MAAe,OAARX,QAAQ,IAARA,GAAqB,QAAbiH,EAARjH,EAAUI,mBAAW,IAAA6G,OAAb,EAARA,EAAuBa,YAC9BC,QACIZ,EAEJjH,SAAUA,CAAC8H,EAAIC,IAAWf,EAAae,IACzC,ECdJC,EAASnI,IAMR,IANS,SACZE,EAAQ,QACRkI,EAAO,WACPC,EAAU,SACVC,EAAQ,OACRC,GACHvI,EACG,MAAOwI,EAASC,IAAc3H,EAAAA,EAAAA,WAAS,IACjC,YAAE4H,IAAgBC,EAAAA,EAAAA,KAwClBC,EAAgBA,KACdP,IAAeQ,EAAAA,GAAqBC,aAKpCT,IAAeQ,EAAAA,GAAqBE,aAKxCnE,QAAQC,IAAI,0DA5BYmE,WACxB,IACIP,GAAW,SACLQ,EAAAA,EAAAA,KAAa,CACfV,SACAW,YAAaC,EAAAA,GAAYC,MAEjC,CAAE,MAAOC,GACLzE,QAAQC,IAAI,+BAAgCwE,EAChD,CAAC,QACGZ,GAAW,EACf,GAaIa,GA1CmBN,WACvB,IACQV,IACAG,GAAW,SACLC,EAAY,CACda,UAAWjB,IAGvB,CAAE,MAAOe,GACLzE,QAAQC,IAAI,8BAA+BwE,EAC/C,CAAC,QACGZ,GAAW,EACf,GAyBIe,EASoB,EAG5B,OACIjF,EAAAA,EAAAA,KAACkF,EAAAA,GAAU,CACPjB,QAASA,EACTtI,SAAUA,EACV4H,UAAU,eACVb,QAASA,IAAM2B,IAAgBc,SAE9BtB,GACQ,EAIfuB,EAAYlD,EAAAA,GAAOC,GAAG;;sBAENV,IAAA,IAAC,OAAE4D,GAAQ5D,EAAA,OAAM4D,EAAS,MAAQ,aAAa;;;;;kBAKpDjD,EAAAA,EAAAA,IAAI;;;EAqErB,EAvDqBR,IAEd,IAFe,SAClBjG,EAAQ,SAAED,EAAQ,OAAEiG,EAAM,SAAE/F,EAAQ,WAAE0J,GACzC1D,EACG,MAAM,oBAAE2D,EAAmB,YAAEzJ,GAAgBJ,EAiB7C,OACIsE,EAAAA,EAAAA,KAACoF,EACG,CACAC,QAA2B,OAAnBE,QAAmB,IAAnBA,OAAmB,EAAnBA,EAAqBC,YAAaxD,EAAqBmD,SAIhC,IAA3BrJ,EAAYuG,YAEJrC,EAAAA,EAAAA,KAACyF,EAAQ,CACL9J,SAAUA,EACVD,SAAUA,EACVkH,aAvBFd,IAClBlG,EAAS,IACFF,EACHI,YAAa,IACNA,EACH0H,YAAc,OAAD1B,QAAC,IAADA,OAAC,EAADA,EAAG5E,GAChBwI,cAAgB,OAAD5D,QAAC,IAADA,OAAC,EAADA,EAAGhE,OAExB,KAkBc6H,EAAAA,EAAAA,MAAAlD,EAAAA,SAAA,CAAA0C,SAAA,CAEQG,IAAiC,OAAnBC,QAAmB,IAAnBA,OAAmB,EAAnBA,EAAqBK,YAC/B5F,EAAAA,EAAAA,KAAC4D,EAAM,IACC2B,EACJ5J,SAAUA,IAMlBgG,QAKZ,E,0BC7JpB,MAqCA,EArCoBlG,IAEb,IAFc,SACjBC,EAAQ,SAAEC,GAAW,EAAK,SAAEC,EAAQ,eAAEiK,EAAiB,YAC1DpK,EACG,OAAa,OAARC,QAAQ,IAARA,GAAAA,EAAUoK,UAKQ,WAAnBD,GAEI7F,EAAAA,EAAAA,KAAC+F,EAAAA,EAAM,CACHpK,SAAUA,EACVqK,QAAiB,OAARtK,QAAQ,IAARA,OAAQ,EAARA,EAAUuK,WACnBrK,SAAUsK,IACNtK,EAAS,IACFF,EACHuK,WAAYC,GACd,KAOdlG,EAAAA,EAAAA,KAACmG,EAAAA,EAAQ,CACLxK,SAAUA,EACVqK,QAAiB,OAARtK,QAAQ,IAARA,OAAQ,EAARA,EAAUuK,WACnBrK,SAAUwK,IACNxK,EAAS,IACFF,EACHuK,WAAYG,EAAEC,OAAOL,SACvB,KA3BHhG,EAAAA,EAAAA,KAAAyC,EAAAA,SAAA,GA6BL,ECrCG2C,EAAYlD,EAAAA,GAAOC,GAAG;;;;;;;;;;;;;;;;;;;EC2FnC,EA3EqB1G,IAId,IAJe,SAClBC,EAAQ,SAAEC,GAAW,EAAK,SAAEC,EAAQ,OAAE+F,EAAM,WAC5C2E,GAAa,EAAI,WAAEhB,GAAa,EAAI,OAAEiB,GAAS,EAAI,SAAEC,GAAW,EAAI,eACpEX,GACHpK,EACG,MAAM,EAAEgL,IAAMC,EAAAA,EAAAA,MAOR7E,EAAgBlG,IAElBD,EAASqH,gBAAkB4D,EAAAA,GAAoBC,oBACjC,OAARlL,QAAQ,IAARA,OAAQ,EAARA,EAAUoK,aAAqB,OAARpK,QAAQ,IAARA,OAAQ,EAARA,EAAUuK,aACzB,OAARvK,QAAQ,IAARA,OAAQ,EAARA,EAAUoK,cAAsB,OAARpK,QAAQ,IAARA,GAAAA,EAAUuK,aAG5C,OACIN,EAAAA,EAAAA,MAACP,EAAS,CAAAD,SAAA,EAEDmB,GAAcE,KACXxG,EAAAA,EAAAA,KAAA,OAAKuD,UAAU,oBAAmB4B,UAC9BQ,EAAAA,EAAAA,MAAA,OAAAR,SAAA,CAGQmB,IACItG,EAAAA,EAAAA,KAAC6G,EAAW,CACRnL,SAAUA,EACVC,SAAUA,EACVC,SAAUA,EACViK,eAAgBA,IAOxBW,IACIxG,EAAAA,EAAAA,KAAA,OAAKuD,UAAU,gBAAe4B,SAAEsB,EAAE/K,EAASwH,cAQnEyC,EAAAA,EAAAA,MAAA,OAAKpC,UAAU,qBAAoB4B,SAAA,CAG3BoB,IACIvG,EAAAA,EAAAA,KAAC8G,EAAM,CACHpL,SAAUA,EACVE,SAAUA,EACVD,SAAUkG,KAMtB7B,EAAAA,EAAAA,KAAC+G,EAAY,CACTpL,SAAUkG,EACVnG,SAAUA,EACVE,SAAUA,EACV0J,WAAYA,EACZ3D,OAAQA,IAAMA,EAAO,CAAEE,yBAKvB,C", "sources": ["module/variableInput/render/typeRender/Control/index.js", "module/variableInput/render/typeRender/Control/utils.js", "module/variableInput/render/constants.js", "module/variableInput/render/commonRender/fxIcon.js", "module/variableInput/render/commonRender/fxSelect.js", "module/variableInput/render/commonRender/buttonRender.js", "module/variableInput/render/commonRender/usableCheck.js", "module/variableInput/render/commonRender/style.js", "module/variableInput/render/commonRender/index.js"], "names": ["FeatureRender", "_ref", "variable", "disabled", "onChange", "control_tab", "default_val", "inputVariableList", "useInputVariableList", "unitList", "useSelector", "state", "global", "value", "setValue", "useState", "useEffect", "data", "_value$data$", "_value$data$$related_", "_value$data$$related_2", "arr1", "related_variables", "variables", "map", "i", "id", "arr2", "length", "every", "element", "index", "groups", "init", "dialog_type", "type", "control_name", "default_name", "code", "_DIALOG_TYPES$find", "_control_tab$related_", "_inputVariableList$fi", "_inputVariableList$fi2", "desc", "DIALOG_TYPE", "SIGNAL", "is_daq", "crypto", "randomUUID", "title", "DIALOG_TYPES", "find", "f", "label", "control_type", "signals", "results", "customs", "filter", "_control_tab$related_2", "includes", "sort", "a", "b", "_control_tab$related_3", "_control_tab$related_4", "indexOf", "_i$code", "replace", "codePrefix", "INPUT", "CONTROL_INPUT", "_jsx", "RenderParams", "paramsData", "callback", "param", "console", "log", "handleDefault", "preferences", "fullPreference", "controlData", "GUIDE_TABLE_TYPE", "CONTROL", "dialogData", "DIALOG", "dialogCodes", "dialogNotCodes", "controlCustom", "CUSTOM_TYPE", "CUSTOM", "controlNotCustom", "NOT_CUSTOM", "permission", "notCustoms", "flat", "_ref2", "CommonRender", "render", "_ref3", "innerDisabled", "v", "FORMDATA_TYPE", "BUTTON_TAB_TYPE", "Fx", "styled", "div", "rem", "isConstant", "iconFx1", "iconFx", "is_fx", "_Fragment", "onClick", "_variable$default_val", "handleChange", "fxSelectOptions", "useMemo", "variable_type", "item", "labelName", "name", "Select", "showSearch", "optionFilterProp", "fieldNames", "className", "variable_id", "options", "__", "option", "<PERSON><PERSON>", "content", "buttonType", "actionId", "script", "loading", "setLoading", "startAction", "useAction", "handleOnClick", "TAB_BUTTON_TYPE_TYPE", "动作", "脚本", "async", "submitScript", "result_type", "SCRIPT_TYPE", "BOOL", "err", "handlesSubmitScript", "action_id", "handleSubmitAction", "AntdButton", "children", "Container", "isLeft", "buttonShow", "button_variable_tab", "position", "FxSelect", "variable_code", "_jsxs", "isEnable", "usableShowType", "is_enable", "Switch", "checked", "is_feature", "newVal", "Checkbox", "e", "target", "usableShow", "fxShow", "nameShow", "t", "useTranslation", "INPUT_VARIABLE_TYPE", "布尔型", "UsableCheck", "FxIcon", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}