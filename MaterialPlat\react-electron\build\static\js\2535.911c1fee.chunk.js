"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[2535],{72535:(t,e,r)=>{r.d(e,{A:()=>T});var i=r(65043),s=r(65173),n=r.n(s),o=function(t,e){return Number(t.slice(0,-1*e.length))},a=function(t){return t.endsWith("px")?{value:t,type:"px",numeric:o(t,"px")}:t.endsWith("fr")?{value:t,type:"fr",numeric:o(t,"fr")}:t.endsWith("%")?{value:t,type:"%",numeric:o(t,"%")}:"auto"===t?{value:t,type:"auto"}:null},h=function(t){return t.split(" ").map(a)},u=function(t,e,r){return e.concat(r).map((function(e){return e.style[t]})).filter((function(t){return void 0!==t&&""!==t}))},c=function(t){for(var e=0;e<t.length;e++)if(t[e].numeric>0)return e;return null},l=function(){return!1},p=function(t,e,r){t.style[e]=r},d=function(t,e,r){var i=t[e];return void 0!==i?i:r};function g(t){var e;return(e=[]).concat.apply(e,Array.from(t.ownerDocument.styleSheets).map((function(t){var e=[];try{e=Array.from(t.cssRules||[])}catch(r){}return e}))).filter((function(e){var r=!1;try{r=t.matches(e.selectorText)}catch(i){}return r}))}var m=function(t,e,r){this.direction=t,this.element=e.element,this.track=e.track,"column"===t?(this.gridTemplateProp="grid-template-columns",this.gridGapProp="grid-column-gap",this.cursor=d(r,"columnCursor",d(r,"cursor","col-resize")),this.snapOffset=d(r,"columnSnapOffset",d(r,"snapOffset",30)),this.dragInterval=d(r,"columnDragInterval",d(r,"dragInterval",1)),this.clientAxis="clientX",this.optionStyle=d(r,"gridTemplateColumns")):"row"===t&&(this.gridTemplateProp="grid-template-rows",this.gridGapProp="grid-row-gap",this.cursor=d(r,"rowCursor",d(r,"cursor","row-resize")),this.snapOffset=d(r,"rowSnapOffset",d(r,"snapOffset",30)),this.dragInterval=d(r,"rowDragInterval",d(r,"dragInterval",1)),this.clientAxis="clientY",this.optionStyle=d(r,"gridTemplateRows")),this.onDragStart=d(r,"onDragStart",l),this.onDragEnd=d(r,"onDragEnd",l),this.onDrag=d(r,"onDrag",l),this.writeStyle=d(r,"writeStyle",p),this.startDragging=this.startDragging.bind(this),this.stopDragging=this.stopDragging.bind(this),this.drag=this.drag.bind(this),this.minSizeStart=e.minSizeStart,this.minSizeEnd=e.minSizeEnd,e.element&&(this.element.addEventListener("mousedown",this.startDragging),this.element.addEventListener("touchstart",this.startDragging))};m.prototype.getDimensions=function(){var t=this.grid.getBoundingClientRect(),e=t.width,r=t.height,i=t.top,s=t.bottom,n=t.left,o=t.right;"column"===this.direction?(this.start=i,this.end=s,this.size=r):"row"===this.direction&&(this.start=n,this.end=o,this.size=e)},m.prototype.getSizeAtTrack=function(t,e){return function(t,e,r,i){void 0===r&&(r=0),void 0===i&&(i=!1);var s=i?t+1:t;return e.slice(0,s).reduce((function(t,e){return t+e.numeric}),0)+(r?t*r:0)}(t,this.computedPixels,this.computedGapPixels,e)},m.prototype.getSizeOfTrack=function(t){return this.computedPixels[t].numeric},m.prototype.getRawTracks=function(){var t=u(this.gridTemplateProp,[this.grid],g(this.grid));if(!t.length){if(this.optionStyle)return this.optionStyle;throw Error("Unable to determine grid template tracks from styles.")}return t[0]},m.prototype.getGap=function(){var t=u(this.gridGapProp,[this.grid],g(this.grid));return t.length?t[0]:null},m.prototype.getRawComputedTracks=function(){return window.getComputedStyle(this.grid)[this.gridTemplateProp]},m.prototype.getRawComputedGap=function(){return window.getComputedStyle(this.grid)[this.gridGapProp]},m.prototype.setTracks=function(t){this.tracks=t.split(" "),this.trackValues=h(t)},m.prototype.setComputedTracks=function(t){this.computedTracks=t.split(" "),this.computedPixels=h(t)},m.prototype.setGap=function(t){this.gap=t},m.prototype.setComputedGap=function(t){var e,r;this.computedGap=t,this.computedGapPixels=(e="px",((r=this.computedGap).endsWith(e)?Number(r.slice(0,-1*e.length)):null)||0)},m.prototype.getMousePosition=function(t){return"touches"in t?t.touches[0][this.clientAxis]:t[this.clientAxis]},m.prototype.startDragging=function(t){if(!("button"in t)||0===t.button){t.preventDefault(),this.element?this.grid=this.element.parentNode:this.grid=t.target.parentNode,this.getDimensions(),this.setTracks(this.getRawTracks()),this.setComputedTracks(this.getRawComputedTracks()),this.setGap(this.getGap()),this.setComputedGap(this.getRawComputedGap());var e=this.trackValues.filter((function(t){return"%"===t.type})),r=this.trackValues.filter((function(t){return"fr"===t.type}));if(this.totalFrs=r.length,this.totalFrs){var i=c(r);null!==i&&(this.frToPixels=this.computedPixels[i].numeric/r[i].numeric)}if(e.length){var s=c(e);null!==s&&(this.percentageToPixels=this.computedPixels[s].numeric/e[s].numeric)}var n=this.getSizeAtTrack(this.track,!1)+this.start;if(this.dragStartOffset=this.getMousePosition(t)-n,this.aTrack=this.track-1,!(this.track<this.tracks.length-1))throw Error("Invalid track index: "+this.track+". Track must be between two other tracks and only "+this.tracks.length+" tracks were found.");this.bTrack=this.track+1,this.aTrackStart=this.getSizeAtTrack(this.aTrack,!1)+this.start,this.bTrackEnd=this.getSizeAtTrack(this.bTrack,!0)+this.start,this.dragging=!0,window.addEventListener("mouseup",this.stopDragging),window.addEventListener("touchend",this.stopDragging),window.addEventListener("touchcancel",this.stopDragging),window.addEventListener("mousemove",this.drag),window.addEventListener("touchmove",this.drag),this.grid.addEventListener("selectstart",l),this.grid.addEventListener("dragstart",l),this.grid.style.userSelect="none",this.grid.style.webkitUserSelect="none",this.grid.style.MozUserSelect="none",this.grid.style.pointerEvents="none",this.grid.style.cursor=this.cursor,window.document.body.style.cursor=this.cursor,this.onDragStart(this.direction,this.track)}},m.prototype.stopDragging=function(){this.dragging=!1,this.cleanup(),this.onDragEnd(this.direction,this.track),this.needsDestroy&&(this.element&&(this.element.removeEventListener("mousedown",this.startDragging),this.element.removeEventListener("touchstart",this.startDragging)),this.destroyCb(),this.needsDestroy=!1,this.destroyCb=null)},m.prototype.drag=function(t){var e=this.getMousePosition(t),r=this.getSizeOfTrack(this.track),i=this.aTrackStart+this.minSizeStart+this.dragStartOffset+this.computedGapPixels,s=this.bTrackEnd-this.minSizeEnd-this.computedGapPixels-(r-this.dragStartOffset);e<i+this.snapOffset&&(e=i),e>s-this.snapOffset&&(e=s),e<i?e=i:e>s&&(e=s);var n=e-this.aTrackStart-this.dragStartOffset-this.computedGapPixels,o=this.bTrackEnd-e+this.dragStartOffset-r-this.computedGapPixels;if(this.dragInterval>1){var a=Math.round(n/this.dragInterval)*this.dragInterval;o-=a-n,n=a}if(n<this.minSizeStart&&(n=this.minSizeStart),o<this.minSizeEnd&&(o=this.minSizeEnd),"px"===this.trackValues[this.aTrack].type)this.tracks[this.aTrack]=n+"px";else if("fr"===this.trackValues[this.aTrack].type)if(1===this.totalFrs)this.tracks[this.aTrack]="1fr";else{var h=n/this.frToPixels;this.tracks[this.aTrack]=h+"fr"}else if("%"===this.trackValues[this.aTrack].type){var u=n/this.percentageToPixels;this.tracks[this.aTrack]=u+"%"}if("px"===this.trackValues[this.bTrack].type)this.tracks[this.bTrack]=o+"px";else if("fr"===this.trackValues[this.bTrack].type)if(1===this.totalFrs)this.tracks[this.bTrack]="1fr";else{var c=o/this.frToPixels;this.tracks[this.bTrack]=c+"fr"}else if("%"===this.trackValues[this.bTrack].type){var l=o/this.percentageToPixels;this.tracks[this.bTrack]=l+"%"}var p=this.tracks.join(" ");this.writeStyle(this.grid,this.gridTemplateProp,p),this.onDrag(this.direction,this.track,p)},m.prototype.cleanup=function(){window.removeEventListener("mouseup",this.stopDragging),window.removeEventListener("touchend",this.stopDragging),window.removeEventListener("touchcancel",this.stopDragging),window.removeEventListener("mousemove",this.drag),window.removeEventListener("touchmove",this.drag),this.grid&&(this.grid.removeEventListener("selectstart",l),this.grid.removeEventListener("dragstart",l),this.grid.style.userSelect="",this.grid.style.webkitUserSelect="",this.grid.style.MozUserSelect="",this.grid.style.pointerEvents="",this.grid.style.cursor=""),window.document.body.style.cursor=""},m.prototype.destroy=function(t,e){void 0===t&&(t=!0),t||!1===this.dragging?(this.cleanup(),this.element&&(this.element.removeEventListener("mousedown",this.startDragging),this.element.removeEventListener("touchstart",this.startDragging)),e&&e()):(this.needsDestroy=!0,e&&(this.destroyCb=e))};var f=function(t,e,r){return e in t?t[e]:r},w=function(t,e){return function(r){if(r.track<1)throw Error("Invalid track index: "+r.track+". Track must be between two other tracks.");var i="column"===t?e.columnMinSizes||{}:e.rowMinSizes||{},s="column"===t?"columnMinSize":"rowMinSize";return new m(t,Object.assign({},{minSizeStart:f(i,r.track-1,d(e,s,d(e,"minSize",0))),minSizeEnd:f(i,r.track+1,d(e,s,d(e,"minSize",0)))},r),e)}},v=function(t){var e=this;this.columnGutters={},this.rowGutters={},this.options=Object.assign({},{columnGutters:t.columnGutters||[],rowGutters:t.rowGutters||[],columnMinSizes:t.columnMinSizes||{},rowMinSizes:t.rowMinSizes||{}},t),this.options.columnGutters.forEach((function(t){e.columnGutters[t.track]=w("column",e.options)(t)})),this.options.rowGutters.forEach((function(t){e.rowGutters[t.track]=w("row",e.options)(t)}))};v.prototype.addColumnGutter=function(t,e){this.columnGutters[e]&&this.columnGutters[e].destroy(),this.columnGutters[e]=w("column",this.options)({element:t,track:e})},v.prototype.addRowGutter=function(t,e){this.rowGutters[e]&&this.rowGutters[e].destroy(),this.rowGutters[e]=w("row",this.options)({element:t,track:e})},v.prototype.removeColumnGutter=function(t,e){var r=this;void 0===e&&(e=!0),this.columnGutters[t]&&this.columnGutters[t].destroy(e,(function(){delete r.columnGutters[t]}))},v.prototype.removeRowGutter=function(t,e){var r=this;void 0===e&&(e=!0),this.rowGutters[t]&&this.rowGutters[t].destroy(e,(function(){delete r.rowGutters[t]}))},v.prototype.handleDragStart=function(t,e,r){"column"===e?(this.columnGutters[r]&&this.columnGutters[r].destroy(),this.columnGutters[r]=w("column",this.options)({track:r}),this.columnGutters[r].startDragging(t)):"row"===e&&(this.rowGutters[r]&&this.rowGutters[r].destroy(),this.rowGutters[r]=w("row",this.options)({track:r}),this.rowGutters[r].startDragging(t))},v.prototype.destroy=function(t){var e=this;void 0===t&&(t=!0),Object.keys(this.columnGutters).forEach((function(r){return e.columnGutters[r].destroy(t,(function(){delete e.columnGutters[r]}))})),Object.keys(this.rowGutters).forEach((function(r){return e.rowGutters[r].destroy(t,(function(){delete e.rowGutters[r]}))}))};const S=function(t){return new v(t)};function y(t,e){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&-1===e.indexOf(i)&&(r[i]=t[i]);return r}var k=function(t){function e(e){t.call(this,e),this.columnGutters={},this.rowGutters={},this.state={gridTemplateColumns:e.gridTemplateColumns?e.gridTemplateColumns:null,gridTemplateRows:e.gridTemplateRows?e.gridTemplateRows:null},this.getGridProps=this.getGridProps.bind(this),this.getGutterProps=this.getGutterProps.bind(this),this.handleDragStart=this.handleDragStart.bind(this),this.writeStyle=this.writeStyle.bind(this),this.onDrag=this.onDrag.bind(this)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.componentDidMount=function(){var t=this.props;t.children;var e=y(t,["children"]);e.writeStyle=this.writeStyle,e.onDrag=this.onDrag,this.split=S(e)},e.prototype.componentDidUpdate=function(t){var e=this,r=this.props,i=r.columnMinSizes,s=r.rowMinSizes;r.columnMaxSizes;var n=r.rowMaxSizes;r.children;var o=y(r,["columnMinSizes","rowMinSizes","columnMaxSizes","rowMaxSizes","children"]),a=t.columnMinSizes,h=t.rowMinSizes;t.columnMaxSizes;var u=t.rowMaxSizes,c=["minSize","maxSize","columnMinSize","rowMinSize","columnMaxSize","rowMaxSize","columnMinSizes","rowMinSizes","columnMaxSizes","rowMaxSizes","snapOffset","columnSnapOffset","rowSnapOffset","dragInterval","columnDragInterval","rowDragInterval","cursor","columnCursor","rowCursor"].map((function(r){return e.props[r]!==t[r]})).reduce((function(t,e){return t||e}),!1);i!==a&&(c=!0),s!==h&&(c=!0),n!==u&&(c=!0),c&&(o.columnMinSizes=i,o.rowMinSizes=s,this.split.destroy(!1),this.split=S(o))},e.prototype.componentWillUnmount=function(){this.split.destroy(),delete this.split},e.getDerivedStateFromProps=function(t,e){var r={},i=!1;return t.gridTemplateColumns&&t.gridTemplateColumns!==e.gridTemplateColumns&&(r.gridTemplateColumns=t.gridTemplateColumns,i=!0),t.gridTemplateRows&&t.gridTemplateRows!==e.prevGridTemplateRows&&(r.gridTemplateRows=t.gridTemplateRows,i=!0),i?r:null},e.prototype.onDrag=function(t,e,r){var i=this.props.onDrag;i&&i(t,e,r)},e.prototype.getGridProps=function(){var t=this.state,e=t.gridTemplateColumns,r=t.gridTemplateRows,i={};return e&&(i.gridTemplateColumns=e),r&&(i.gridTemplateRows=r),{style:i}},e.prototype.getGutterProps=function(t,e){return{onMouseDown:this.handleDragStart(t,e),onTouchStart:this.handleDragStart(t,e)}},e.prototype.handleDragStart=function(t,e){var r=this;return function(i){r.split.handleDragStart(i,t,e)}},e.prototype.writeStyle=function(t,e,r){var i={};"grid-template-columns"===e?i.gridTemplateColumns=r:"grid-template-rows"===e&&(i.gridTemplateRows=r),this.setState(i)},e.prototype.render=function(){var t=this.props,e=t.component,r=t.render,s=t.children,n={getGridProps:this.getGridProps,getGutterProps:this.getGutterProps};return e?i.createElement(e,n):r?r(n):s?"function"===typeof s?s(n):0!==i.Children.count(s)?i.Children.only(s):null:null},e}(i.Component);k.propTypes={component:n().element,render:n().func,children:n().element,gridTemplateColumns:n().string,gridTemplateRows:n().string,columnMinSizes:n().objectOf(n().number),rowMinSizes:n().objectOf(n().number),columnMaxSizes:n().objectOf(n().number),rowMaxSizes:n().objectOf(n().number),onDrag:n().func},k.defaultProps={component:void 0,render:void 0,children:void 0,gridTemplateColumns:void 0,gridTemplateRows:void 0,columnMinSizes:void 0,rowMinSizes:void 0,columnMaxSizes:void 0,rowMaxSizes:void 0,onDrag:void 0};const T=k}}]);
//# sourceMappingURL=2535.911c1fee.chunk.js.map