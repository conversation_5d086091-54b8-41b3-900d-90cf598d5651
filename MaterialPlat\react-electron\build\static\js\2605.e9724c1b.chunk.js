"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[2605],{2032:(e,t,n)=>{n.d(t,{A:()=>r});n(65043);var a=n(80077),o=n(63612),i=n(67208);const r=()=>{const e=(0,a.wA)();return{initHardwareList:async()=>{try{const t=await(0,i.Hos)();t&&e({type:o.UI,param:t})}catch(t){console.log(t)}}}}},13523:(e,t,n)=>{n.d(t,{$5:()=>o,fO:()=>i});var a=n(63880);const o={"\u786c\u4ef6\u914d\u7f6e\u9996\u9875":"-1","\u5355\u7ad9\u9996\u9875":"-2","\u591a\u7ad9\u9996\u9875":"-3","\u4ece\u673a\u5355\u7ad9\u9996\u9875":"-5","\u4ece\u673a\u591a\u7ad9\u9996\u9875":"-6"},i=(o.\u786c\u4ef6\u914d\u7f6e\u9996\u9875,a.Bm.\u5e03\u5c402,a.Y8.\u786c\u4ef6\u7ba1\u7406\u5668,a.Y8.\u7ad9\u7ba1\u7406\u5668,o.\u5355\u7ad9\u9996\u9875,a.Bm.\u5e03\u5c403,a.Y8.\u9879\u76ee\u7ba1\u7406\u5668,a.Y8.\u6a21\u677f\u7ba1\u7406\u5668,a.Y8.\u6570\u636e\u5206\u6790,o.\u591a\u7ad9\u9996\u9875,a.Bm.\u5e03\u5c404,a.Y8.\u5168\u5c40\u76d1\u63a7,a.Y8.\u6570\u636e\u5206\u6790,a.Y8.\u9879\u76ee\u7ba1\u7406\u5668,a.Y8.\u6a21\u677f\u7ba1\u7406\u5668,o.\u4ece\u673a\u5355\u7ad9\u9996\u9875,a.Bm.\u5e03\u5c402,a.Y8.\u9879\u76ee\u7ba1\u7406\u5668,a.Y8.\u6570\u636e\u5206\u6790,o.\u4ece\u673a\u591a\u7ad9\u9996\u9875,a.Bm.\u5e03\u5c403,a.Y8.\u5168\u5c40\u76d1\u63a7,a.Y8.\u6570\u636e\u5206\u6790,a.Y8.\u9879\u76ee\u7ba1\u7406\u5668,[{station_or_home_layout_config_id:1,station_or_home_id:"-1",layout_config_json:JSON.stringify({showSiderbar:!1,showStatuBar:!0,layoutConfig:{layoutType:2,modulesConfig:{1:{name:"\u786c\u4ef6\u7ba1\u7406\u5668",target:"hardwareManager",iconId:"1c3290cc-d1df-4486-9d1f-6705cef68599"},2:{name:"\u7ad9\u7ba1\u7406\u5668",target:"hostManager",iconId:"4ecd3cc3-9a29-4871-9b12-1e01c6cb8e2e"}},mainTitle:"\u4e2d\u673a\u8bd5\u9a8c\u8f6f\u4ef6\u5e73\u53f0",subTitle:""}}),created_time:"2025-06-21 19:57:45",updated_time:"2025-06-21 19:57:46",created_user_id:null,updated_user_id:null,delete_flag:0},{station_or_home_layout_config_id:2,station_or_home_id:"-2",layout_config_json:JSON.stringify({showSiderbar:!0,showStatuBar:!0,layoutConfig:{layoutType:3,modulesConfig:{1:{name:"\u9879\u76ee\u7ba1\u7406\u5668",target:"projectManager",iconId:"1c3290cc-d1df-4486-9d1f-6705cef68599"},2:{name:"\u6a21\u677f\u7ba1\u7406\u5668",target:"templateManager",iconId:"4ecd3cc3-9a29-4871-9b12-1e01c6cb8e2e"},3:{name:"\u6570\u636e\u5206\u6790",target:"dataAnalysis",iconId:"4ecd3cc3-9a29-4871-9b12-1e01c6cb8e2e"}},mainTitle:"\u4e2d\u673a\u8bd5\u9a8c\u8f6f\u4ef6\u5e73\u53f0",subTitle:""}}),created_time:"2025-06-21 19:57:45",updated_time:"2025-06-21 19:57:46",created_user_id:null,updated_user_id:null,delete_flag:0},{station_or_home_layout_config_id:3,station_or_home_id:"-3",layout_config_json:JSON.stringify({showSiderbar:!0,showStatuBar:!0,layoutConfig:{layoutType:2,modulesConfig:{1:{name:"\u591a\u673a\u76d1\u63a7",target:"globalMonitoring"},2:{name:"\u6570\u636e\u5206\u6790",target:"dataAnalysis"}},mainTitle:"\u4e2d\u673a\u8bd5\u9a8c\u8f6f\u4ef6\u5e73\u53f0",subTitle:""}}),created_time:"2025-06-21 19:57:45",updated_time:"2025-06-21 19:57:46",created_user_id:null,updated_user_id:null,delete_flag:0},{station_or_home_layout_config_id:4,station_or_home_id:"globalMonitoring-2",layout_config_json:JSON.stringify({showSiderbar:!1,showStatuBar:!0,layoutConfig:{layoutType:2,modulesConfig:{1:{name:"\u9879\u76ee\u7ba1\u7406\u5668",target:"projectManager",iconId:"1c3290cc-d1df-4486-9d1f-6705cef68599"},2:{name:"\u6a21\u677f\u7ba1\u7406\u5668",target:"templateManager",iconId:"4ecd3cc3-9a29-4871-9b12-1e01c6cb8e2e"}},mainTitle:"\u4e2d\u673a\u8bd5\u9a8c\u8f6f\u4ef6\u5e73\u53f0",subTitle:""}}),created_time:"2025-06-21 19:57:45",updated_time:"2025-06-21 19:57:46",created_user_id:null,updated_user_id:null,delete_flag:0},{station_or_home_layout_config_id:5,station_or_home_id:"globalMonitoring-3",layout_config_json:JSON.stringify({showSiderbar:!1,showStatuBar:!0,layoutConfig:{layoutType:3,modulesConfig:{1:{name:"\u9879\u76ee\u7ba1\u7406\u5668",target:"projectManager",iconId:"1c3290cc-d1df-4486-9d1f-6705cef68599"},2:{name:"\u6a21\u677f\u7ba1\u7406\u5668",target:"templateManager",iconId:"4ecd3cc3-9a29-4871-9b12-1e01c6cb8e2e"},3:{name:"\u6570\u636e\u5206\u6790",target:"dataAnalysis",iconId:"4ecd3cc3-9a29-4871-9b12-1e01c6cb8e2e"}},mainTitle:"\u4e2d\u673a\u8bd5\u9a8c\u8f6f\u4ef6\u5e73\u53f0",subTitle:""}}),created_time:"2025-06-21 19:57:45",updated_time:"2025-06-21 19:57:46",created_user_id:null,updated_user_id:null,delete_flag:0}])},32605:(e,t,n)=>{n.r(t),n.d(t,{default:()=>V});var a=n(65043),o=n(16569),i=n(32513),r=n(74117),l=n(86178),c=n.n(l),s=n(91688),d=n(80077),u=n(34458),g=n(42225),p=n(88557),m=n(63612),h=n(67208);const _=()=>{const e=(0,s.W6)(),t=(0,d.wA)(),{afterLogin:n}=(0,p.A)(),{getHardWareAxios:a}=(0,g.A)(),o=async()=>{const e=await(0,h.Zd2)();e&&(0,u.X4)(null===e||void 0===e?void 0:e.data)};return{doLogin:async(i,r)=>{try{const l=await(0,h.pHe)({account:i.account,password:i.password});if(l){{const e=await a(l),t=JSON.parse(null===e||void 0===e?void 0:e.data),{code:n,data:o}=t;if(0!==n)return`\u52a0\u5bc6\u72d7\u9519\u8bef-${o}`}return(0,u.iA)(l),t({type:m.BI,param:i.account}),t({type:m.pF,param:1===(null===l||void 0===l?void 0:l.role_id)}),t({type:m.Qy,param:1===(null===l||void 0===l?void 0:l.role_id)?"":"hidden-dom"}),o(),await n(r),e.push("/"),"\u767b\u9646\u6210\u529f"}return 0}catch(l){throw console.log(l),l}}}};var y=n(18650),f=n(33154),x=n(44409),v=n(68130),b=n(95620),w=n(21256),j=n(81077),$=n(61668),A=n(33013),D=n(65913),C=n(2032),S=n(45303),N=n(57692),k=(n(20790),n(4178));const M="active",L="exception",T="success";var B=n(81143),F=n(68374);const Y=B.Ay.div`
      display: flex;
      width: 100vw;
      height: 100vh;
      background-color: #FFFFFF; 
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1001;
      .progress-layout {
        display: flex;
        flex-direction: column;
        width: 500px;
        align-items: flex-start;
        justify-content: center;
        .title {
            margin-bottom: 24px;
        }
        .error {
            color: red;
        }
      }
`;var H=n(70579);const I=e=>{let{onSuccess:t}=e;const{updateWindowSize:n,updateTitle:o,windowMaximize:i}=(0,k.A)(),{t:l}=(0,r.Bd)(),[c,s]=(0,a.useState)(0),[d,u]=(0,a.useState)(M),[g,p]=(0,a.useState)(""),m=[{name:"Java Server",func:h.od7},{name:"Task Server",func:h.psN},{name:"Hardware Server",func:h._E8}];(0,a.useEffect)((()=>{o(l("\u6b63\u5728\u542f\u52a8")),_()}),[]);const _=async()=>{let e=0;const a=Math.ceil(100/m.length),r=a/30;for(const t of m){let n=0,o=!1;for(;n<30;)try{p(`${t.name} \u670d\u52a1\u542f\u52a8\u4e2d\uff0c\u8bf7\u7a0d\u540e...`),await t.func(),o=!0;break}catch(l){if(n+=1,console.warn(`${t.name} \u542f\u52a8\u5931\u8d25\uff0c\u91cd\u8bd5 ${n}/30`),s((e=>Math.ceil(Math.min(e+r,100)))),n>=30)return u(L),void p(`${t.name} \u670d\u52a1\u542f\u52a8\u9519\u8bef\uff0c\u8bf7\u8054\u7cfb\u7ba1\u7406\u5458`);await new Promise((e=>setTimeout(e,2e3)))}o&&(e+=1,s(Math.ceil(e*a)))}u(T),p("\u670d\u52a1\u5df2\u542f\u52a8"),t&&setTimeout((()=>{o(),n(),i(),t(T)}),1e3)};return(0,H.jsx)(Y,{children:(0,H.jsxs)("div",{className:"progress-layout",children:[(0,H.jsx)("div",{className:"title "+(d===L?"error":""),children:g}),(0,H.jsx)(N.A,{percent:c,status:d})]})})};var P=n(84),O=n(98171),z=n(15701),E=n(91465),J=n(96603),U=n(47419),R=n(11645),K=n(75440);const W=B.Ay.div`
    padding: 8px;
    .bg-box{
        width: 100%;
        height: 0;
        padding-bottom: 100%;
        border: 1px solid #dbdbdb;
        background: #fff;
        cursor: pointer;
        position: relative;
        .content-box{
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    .radio-box{
        text-align: center;
        margin-top: 8px;
    }
`,G=e=>{let{open:t,onOk:n,onCancel:o}=e;const{t:i}=(0,r.Bd)(),[l,c]=(0,a.useState)(p.K["\u4e3b\u673a\u5de5\u4f5c\u6a21\u5f0f"]),s=[{value:p.K["\u4e3b\u673a\u5de5\u4f5c\u6a21\u5f0f"],label:"\u8bd5\u9a8c\u6a21\u5f0f"},{value:p.K["\u786c\u4ef6\u914d\u7f6e\u6a21\u5f0f"],label:"\u786c\u4ef6\u914d\u7f6e\u6a21\u5f0f"}];return(0,H.jsx)(K.A,{open:t,title:i("\u5de5\u4f5c\u6a21\u5f0f\u914d\u7f6e"),contentHeight:"65vh",onCancel:o,onOk:async()=>{n(l)},okText:i("\u786e\u8ba4\u5e76\u542f\u52a8"),children:(0,H.jsx)(J.Ay.Group,{style:{width:"100%"},value:l,onChange:e=>{c(e.target.value)},children:(0,H.jsx)(U.A,{children:s.map((e=>(0,H.jsx)(R.A,{style:{justifyContent:"center"},span:6,children:(0,H.jsxs)(W,{children:[(0,H.jsx)("div",{className:"bg-box",onClick:()=>c(e.value),children:(0,H.jsx)("div",{className:"content-box",children:e.label})}),(0,H.jsx)("div",{className:"radio-box",children:(0,H.jsx)(J.Ay,{value:e.value,children:e.label})})]})},e.value)))})})})},Q=B.Ay.div`
    .container {
        width: 100vw;
        height: 100vh;
        display: flex;
        .left-background {
            width: 70%;
            background-image: url(${y.CN});
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }
        .right-login {
            width: 30%;
            padding: ${(0,F.D0)("90px")} ${(0,F.D0)("100px")} ${(0,F.D0)("230px")} ${(0,F.D0)("148px")};
            background-color: #fff;
            .login-container {
                height: 100%;
                width: 100%;
                /* background-color: #eee; */
                .app_logo {
                    width: ${(0,F.D0)("117px")};
                    height: ${(0,F.D0)("32px")};
                    background-size: contain;
                    background-image: url(${y.yF});
                    margin-bottom: ${(0,F.D0)("64px")};
                }
                .login_title {
                    width: ${(0,F.D0)("62px")};
                    height: ${(0,F.D0)("44px")};
                    font-size: ${(0,F.D0)("31px")};
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 550;
                    color: #333333;
                    line-height: ${(0,F.D0)("44px")};
                    margin-bottom: ${(0,F.D0)("12px")};
                }
                .login_tip {
                    width: ${(0,F.D0)("160px")};
                    height: ${(0,F.D0)("22px")};
                    font-size: ${(0,F.D0)("16px")};
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #666666;
                    line-height: ${(0,F.D0)("22px")};
                    margin-bottom: ${(0,F.D0)("40px")};
                }
                .login_input_container {
                    position: relative;
                    .login_input {
                        width: ${(0,F.D0)("297px")};
                        height: ${(0,F.D0)("40px")};
                        background: rgba(255,255,255,0.7);
                        border-radius: ${(0,F.D0)("4px")};
                        border: ${(0,F.D0)("1px")} solid #5A78FF;
                        margin-bottom: ${(0,F.D0)("14px")};
                        padding-left: ${(0,F.D0)("12px")};
                        font-size: ${(0,F.D0)("14px")};
                        color: gery;
                        outline: none;
                    }
                    .login_input_icon {
                        width: ${(0,F.D0)("15px")};
                        height: ${(0,F.D0)("7px")};
                        position: absolute;
                        top: ${(0,F.D0)("18px")};
                        right: ${(0,F.D0)("40px")};
                    }
                }
                .login_btn_container {
                    .login_btn {
                        cursor: pointer;
                        width: ${(0,F.D0)("297px")};
                        height: ${(0,F.D0)("42px")};
                        background: #5A78FF;
                        border-radius: ${(0,F.D0)("4px")};
                        border: none;
                        font-size: ${(0,F.D0)("14px")};
                        color: #fff;
                        margin: ${(0,F.D0)("40px")} 0 ${(0,F.D0)("24px")} 0;
                    }
                    .login_btn:hover {
                        background: #5a78f1;
                    }
                }
                .helper {
                    text-align: center;
                    width: ${(0,F.D0)("290px")};
                    height: ${(0,F.D0)("17px")};
                    font-size: ${(0,F.D0)("12px")};
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #676767;
                    line-height: ${(0,F.D0)("17px")};
                }
            }
        }
    }
`,q={account:"admin",password:"admin",softwareConfig:!1},V=()=>{const e=(0,d.wA)(),{initModuleData:t}=(0,j.A)(),{doLogin:n}=_(),{initSystemConfig:l}=(0,p.A)(),{initUnitsData:s}=(0,f.A)(),{initHardwareList:u}=(0,C.A)(),{initAudioData:g}=(0,$.A)(),{initStationInfo:N}=(0,v.A)(),{openDialog:M}=(0,P.A)(),{initStandByConfig:L}=(0,O.A)(),{initGlobalProjectID:T}=(0,z.A)(),{initProjectList:B}=(0,A.A)(),{initPair:F,initPublisher:Y,initUiPair:J}=(0,x.A)(),U=(0,d.d4)((e=>e.global.loading)),R=(0,d.d4)((e=>e.global.loadingName)),K=(0,d.d4)((e=>e.global.isServerSuccess)),{initWidget:W}=(0,w.A)(),[V,X]=o.Ay.useMessage(),[Z,ee]=(0,a.useState)({...q}),[te,ne]=(0,a.useState)(!1),{t:ae}=(0,r.Bd)(),[oe,ie]=(0,a.useState)(!1),{updateTitle:re}=(0,k.A)();(0,a.useEffect)((()=>{re(),ee({...q,softwareConfig:!1})}),[]);const le=e=>{ee({...Z,[e.target.name]:e.target.value})},ce=async e=>{if(e===p.K["\u786c\u4ef6\u914d\u7f6e\u6a21\u5f0f"])return;const t=await(0,h.wHv)();if(t){if(0===t.need_today_inspect_count&&0===t.need_recent_inspect_count)return;M({type:S.hT})}},se=async()=>{try{if(!Z||null===Z||void 0===Z||!Z.account||null===Z||void 0===Z||!Z.password)return void V.open({type:"error",content:ae(`\u672a\u586b\u5199${null!==Z&&void 0!==Z&&Z.account?"":"\u8d26\u53f7"}${null!==Z&&void 0!==Z&&Z.password?"":"\u5bc6\u7801"}`)});null!==Z&&void 0!==Z&&Z.softwareConfig?ie(!0):de()}catch(e){console.error(e)}},de=async a=>{const o=e((0,E.J_)("\u767b\u5f55\u4e2d"));let i=null;try{var r,d,p;if(i=await n(Z,a),"\u767b\u9646\u6210\u529f"===i)null===(d=window)||void 0===d||null===(p=d.logger)||void 0===p||p.info("\u6b22\u8fce, \u767b\u5f55"),e({type:m.q7,param:c()(await(0,h.r9J)()).format("YYYY-MM-DD HH:mm:ss")}),F(),J(),await Promise.all([l(),ce(a),s(),Y(),W(),t(),g(),u(),N(),L(),T(),B()]);else null!==(r=i)&&void 0!==r&&r.includes("\u52a0\u5bc6\u72d7\u9519\u8bef")?V.open({type:"error",content:ae(i.split("-")[1])}):V.open({type:"error",content:ae("\u8d26\u53f7\u6216\u5bc6\u7801\u9519\u8bef")});e((0,E.ge)(o))}catch(_){e((0,E.ge)(o))}},ue=()=>{ie(!1)};return(0,H.jsxs)(H.Fragment,{children:[(0,H.jsx)(b.T8,{}),!K&&(0,H.jsx)(I,{onSuccess:()=>{e({type:m.Nb,param:!0})}}),(0,H.jsxs)(Q,{children:[X,U&&(0,H.jsx)(D.A,{text:R}),(0,H.jsxs)("div",{className:"container",children:[(0,H.jsx)("div",{className:"left-background"}),(0,H.jsx)("div",{className:"right-login",children:(0,H.jsxs)("div",{className:"login-container",children:[(0,H.jsx)("div",{className:"app_logo"}),(0,H.jsx)("div",{className:"login_title",children:ae("\u767b\u5f55")}),(0,H.jsx)("div",{className:"login_tip",children:ae("\u6b22\u8fce\u767b\u5f55\u4e2d\u673a\u8bd5\u9a8c\u7cfb\u7edf")}),(0,H.jsx)("div",{className:"login_input_container",children:(0,H.jsx)("input",{id:"username",onChange:le,value:(null===Z||void 0===Z?void 0:Z.account)||"",name:"account",className:"login_input",placeholder:ae("\u8bf7\u8f93\u5165\u8d26\u53f7")})}),(0,H.jsxs)("div",{className:"login_input_container",children:[(0,H.jsx)("input",{id:"password",onChange:le,onKeyDown:e=>{"Enter"===e.key&&se()},value:(null===Z||void 0===Z?void 0:Z.password)||"",type:te?"text":"password",name:"password",className:"login_input",placeholder:ae("\u8bf7\u8f93\u5165\u5bc6\u7801")}),(0,H.jsx)("img",{onClick:()=>{ne(!te)},className:"login_input_icon",src:te?y.H9:y.l0,alt:"see/nosee"})]}),(0,H.jsx)("div",{className:"login_input_container",children:(0,H.jsx)(i.A,{onChange:e=>{ee({...Z,softwareConfig:e.target.checked})},checked:(null===Z||void 0===Z?void 0:Z.softwareConfig)||!1,children:ae("\u662f\u5426\u8fdb\u884c\u8f6f\u4ef6\u914d\u7f6e")})}),(0,H.jsx)("div",{className:"login_btn_container",children:(0,H.jsx)("button",{className:"login_btn",id:"login-btn",onClick:se,children:(0,H.jsx)("span",{children:ae("\u767b\u5f55")})})}),(0,H.jsx)("div",{className:"helper",children:`${ae("\u5982\u6709\u7591\u95ee\uff0c\u8bf7\u8054\u7cfb\u7ba1\u7406\u5458")}`})]})})]})]}),oe?(0,H.jsx)(G,{open:oe,onOk:e=>{de(e),ue()},onCancel:ue}):null]})}},51422:(e,t,n)=>{n.d(t,{A:()=>r});var a=n(80077),o=n(63612),i=n(67208);const r=()=>{const e=(0,a.wA)(),t=(0,a.d4)((e=>e.global.layoutList)),n=async()=>{try{const t=await(0,i.vNh)();t&&e({type:o.VB,param:t})}catch(t){console.log("err",t)}};return{layoutList:t,initLayoutList:n,updateHomeLayoutConfig:async e=>{let{id:a,newLayoutConfig:o}=e;try{const e=null===t||void 0===t?void 0:t.find((e=>e.station_or_home_layout_config_id===a)),r={...e,layout_config_json:JSON.stringify({...JSON.parse(null===e||void 0===e?void 0:e.layout_config_json),layoutConfig:o})};await(0,i.iPP)(r),await n()}catch(r){console.log("err",r)}}}}},61668:(e,t,n)=>{n.d(t,{A:()=>r});n(65043);var a=n(80077),o=n(63612),i=n(67208);const r=()=>{const e=(0,a.wA)();return{initAudioData:async()=>{try{const t=await(0,i.lrG)();if(t){const n=t.reverse();e({type:o.FY,param:n})}}catch(t){console.log(t)}},playAudio:async e=>{let{audio_id:t,onAudioEnd:n,isLoopPlay:a=!1}=e;try{const e=await(0,i.$i0)({audio_id:t});if(e){const o=new File([e],"test.mp3",{type:"audio/mpeg"}),i=document.createElement("audio"),r=URL.createObjectURL(o);i.src=r,i.id=t,i.load(),i.play(),document.body.appendChild(i),i.addEventListener("ended",(()=>{a?i.play():i.remove(),n&&n(!0,t),URL.revokeObjectURL(r)}),!1)}}catch(o){console.error("Error playing audio:",o)}},removeAudio:async(e,t)=>{try{const n=document.getElementById(e);n&&(n.remove(),t&&t(!0,e),URL.revokeObjectURL(n.src))}catch(n){console.error("Error removing audio:",n)}}}}},63880:(e,t,n)=>{n.d(t,{Bm:()=>a,Lg:()=>o,Y8:()=>i});const a={"\u5e03\u5c401":1,"\u5e03\u5c402":2,"\u5e03\u5c403":3,"\u5e03\u5c404":4,"\u5e03\u5c405":5,"\u5e03\u5c406":6},o={[a.\u5e03\u5c401]:1,[a.\u5e03\u5c402]:2,[a.\u5e03\u5c403]:3,[a.\u5e03\u5c404]:4,[a.\u5e03\u5c405]:5,[a.\u5e03\u5c406]:6},i={"\u6a21\u677f\u7ba1\u7406\u5668":"templateManager","\u9879\u76ee\u7ba1\u7406\u5668":"projectManager","\u5168\u5c40\u76d1\u63a7":"globalMonitoring","\u786c\u4ef6\u7ba1\u7406\u5668":"hardwareManager","\u7ad9\u7ba1\u7406\u5668":"hostManager","\u6570\u636e\u5206\u6790":"dataAnalysis"};a.\u5e03\u5c403,i.\u9879\u76ee\u7ba1\u7406\u5668,i.\u6a21\u677f\u7ba1\u7406\u5668,i.\u5168\u5c40\u76d1\u63a7},88557:(e,t,n)=>{n.d(t,{A:()=>s,K:()=>c});var a=n(80077),o=n(67208),i=n(63612),r=n(13523),l=n(51422);const c={"\u786c\u4ef6\u914d\u7f6e\u6a21\u5f0f":0,"\u4e3b\u673a\u5de5\u4f5c\u6a21\u5f0f":1,"\u4ece\u673a\u5de5\u4f5c\u6a21\u5f0f":2},s=()=>{const e=(0,a.wA)(),{initLayoutList:t}=(0,l.A)(),n=async()=>{try{const t=await(0,o.IDE)();t.project_directory=t.project_directory.replace(/\\/g,"/"),t&&e({type:i.B6,param:t})}catch(t){console.error(t)}};return{afterLogin:async n=>{await t();const a=await(0,o.BxM)();n!==c.\u786c\u4ef6\u914d\u7f6e\u6a21\u5f0f&&0!==a.length?1!==a.length?e({type:i.H$,param:r.$5.\u591a\u7ad9\u9996\u9875}):e({type:i.H$,param:r.$5.\u5355\u7ad9\u9996\u9875}):e({type:i.H$,param:r.$5.\u786c\u4ef6\u914d\u7f6e\u9996\u9875})},initSystemConfig:n,updateSystemConfig:async e=>{try{await(0,o.BHK)(e),n()}catch(t){console.error(t)}}}}},98171:(e,t,n)=>{n.d(t,{A:()=>c,M:()=>l});var a=n(80077),o=n(67208),i=n(63612);let r=!0;const l={"\u4e3b\u673a":"master","\u4ece\u673a":"slave1:1","\u5907\u7528\u673a":"slave1:n"},c=()=>{const e=(0,a.d4)((e=>e.global.standbyConfig)),t=(0,a.wA)(),n=async()=>{const e=await(0,o.ceQ)();return e&&("master"===e.mode&&r?((0,o.lpx)(),r=!1):r=!0,t({type:i.tT,param:e})),e};return{standbyConfig:e,initStandByConfig:n,updateStandByConfig:async e=>{await(0,o.roU)(e)&&n()}}}}}]);
//# sourceMappingURL=2605.e9724c1b.chunk.js.map