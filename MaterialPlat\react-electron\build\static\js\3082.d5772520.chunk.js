"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3082],{93082:(e,t,i)=>{i.r(t),i.d(t,{default:()=>_});var n=i(65043),a=i(74117),d=i(80077),o=i(80231),s=i(67208),c=i(21256),l=i(68971),r=(i(4554),i(95911)),u=i(36950),g=i(3945),f=i(56543),p=i(73225),h=i(81143),y=i(68374);const m=h.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .disabled {
        cursor: no-drop;
    }
    .unique-content {
        padding: 2px;
    }

`,x=h.Ay.div`
    height: 100%;
    background: ${y.o$.white};

    >.tips {
        padding: 30px;
        text-align: center;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    >.btns {
        display: flex;
        justify-content: center;
        padding-top: 30px;
    }
`;var C=i(70579);const v=e=>{let{domId:t,layoutConfig:i,handleConfigPidSetting:n}=e;const{t:d}=(0,a.Bd)();return(0,C.jsx)(m,{children:(0,C.jsx)(o.A,{domId:t,layoutConfig:i,children:(0,C.jsx)("div",{className:"unique-content",onClick:n,children:d("\u914d\u7f6epid\u8bbe\u7f6e")})})})},_=e=>{let{id:t,item:i,layoutConfig:o}=e;const{t:h}=(0,a.Bd)(),y=(0,d.d4)((e=>e.template.widgetData)),{getConfigPidList:m,addOrUpdateConfig:_}=(0,l.A)(),[j,w]=(0,n.useState)(),{editWidget:b}=(0,c.A)(),[k,S]=(0,n.useState)(!1),[A,N]=(0,n.useState)(null),z=(0,n.useRef)(null),O=(0,n.useMemo)((()=>(0,u.Rm)(y,"widget_id",null===i||void 0===i?void 0:i.widget_id)),[i,y]);(0,n.useEffect)((()=>{I()}),[O]);const I=async()=>{try{var e;const t=await(0,s.nAy)({key:"list",operation:"listInit"}),i=null===(e=await m())||void 0===e?void 0:e.find((e=>e.config_id===O.data_source));if(i){const{config_id:e,config_value:n}=i;z.current=e;const a=JSON.parse(n);if(N(a),a.FormKey in t){const e=JSON.parse(t[a.FormKey]);w(e)}else w()}}catch(t){console.error(t)}},J=async(e,t)=>{let i=(0,g.Ed)(t||j,e,j);A&&(i={...i,...A});const n=await(0,s.Azz)(i);null!==n&&void 0!==n&&n.data&&w(JSON.parse(n.data))},q=async e=>{O&&await b({...O,data_source:e})};return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsxs)(x,{children:[j?(0,C.jsx)(r.A,{config:j,onResize:w,onBtnChange:J,onSelectChange:(e,t)=>{J(t,e)}}):(0,C.jsx)("div",{className:"tips",children:h("\u672a\u914d\u7f6e\u6b63\u786e\u7684pid\u8bbe\u7f6e")}),(0,C.jsx)(p.A,{open:k,formData:A,onCancel:()=>S(!1),submitCallback:async e=>{const t={config_id:z.current,config_key:f.mN.pid\u8bbe\u7f6e.key,config_value:JSON.stringify(e)},i=await _(t);await q(i),S(!1),I()}})]}),(0,C.jsx)(v,{domId:t,handleConfigPidSetting:()=>{S(!0)},layoutConfig:o})]})}}}]);
//# sourceMappingURL=3082.d5772520.chunk.js.map