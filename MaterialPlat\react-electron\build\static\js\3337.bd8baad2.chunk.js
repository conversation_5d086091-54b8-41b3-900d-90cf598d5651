"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3337],{43337:(e,t,l)=>{l.r(t),l.d(t,{default:()=>X});var n,a=l(65043),o=l(80077),s=l(74117),i=l(11238),r=l(21256),d=l(36950),c=l(80231),u=l(86178),v=l.n(u),h=l(25055),f=l(47419),m=l(11645),p=l(97914),x=l(32513),g=l(6051),y=l(95206),b=l(75440);const j={"\u4e3b\u673a":"hostName","\u91cd\u8981\u6027":"significance","\u7c7b\u578b":"type","\u7b49\u7ea7":"grade","\u9879\u76ee\u540d\u79f0":"projectName","\u8bd5\u6837\u540d\u79f0":"sampleName","\u5185\u5bb9":"content","\u64cd\u4f5c\u65f6\u95f4":"operationTime"},A={hostName:120,significance:80,type:90,grade:80,projectName:180,sampleName:90,content:500,operationTime:130},w={rowNumber:5,dataList:null===(n=Object.keys(j))||void 0===n?void 0:n.filter((e=>"\u4e3b\u673a"!==e)).map((e=>({code:j[e]}))),isUseGlobal:!1};var C=l(10202),k=l(70579);const S=e=>{let{value:t=[],onChange:l,dataSource:n,noDeleteDatas:o,rowKey:i="code",transferSelectIndex:r,transferSelectIndexChange:d}=e;const{t:c}=(0,s.Bd)(),[u,v]=(0,a.useState)();return(0,k.jsx)(k.Fragment,{children:(0,k.jsx)(C.A,{noDeleteDatas:o,listStyle:{width:"100%",height:"45vh"},rowKey:i,isMove:!0,oneWay:!0,dataSource:n||[],targetKeys:Array.isArray(t)&&t.length>0?null===t||void 0===t?void 0:t.map((e=>e[i])):[],onChange:e=>{const t=e.map((e=>({[i]:e})));null===l||void 0===l||l(t)},onChangeDelWay:e=>{null===l||void 0===l||l(t.filter((t=>t[i]!==e[i])))},onChangeMove:e=>{const n=e.map((e=>t.find((t=>t[i]===e))));null===l||void 0===l||l(n),d(e.findIndex((e=>e===u[i])))},onChangeWay:e=>{e?(v(e),d(t.findIndex((t=>t[i]===e[i])))):(v(null),d(null))},render:e=>null!==e&&void 0!==e&&e.code?`${c(null===e||void 0===e?void 0:e.label)}(${null===e||void 0===e?void 0:e.code})`:c(null===e||void 0===e?void 0:e.label),wayRender:e=>null!==e&&void 0!==e&&e.code?`${c(null===e||void 0===e?void 0:e.label)}(${null===e||void 0===e?void 0:e.code})`:c(null===e||void 0===e?void 0:e.label)})})};var N=l(81143);const R=N.Ay.div`
    background: #fff;
    padding: 5px 10px 10px;
    
    .transfer-content{
        .ant-transfer {
            flex: 1;
            overflow: hidden;
            .ant-transfer-list{
                overflow: hidden;
            }
        }
    }

    .footer-div{
        margin-top: 20px;
        text-align: right;
    }
`,{Item:I,useForm:D}=h.A,H=e=>{let{open:t,setOpen:l,config:n,updateConfig:o}=e;const{t:i}=(0,s.Bd)(),[r]=D(),[c,u]=(0,a.useState)(null),v=()=>{l(!1)};(0,a.useEffect)((()=>{(0,d.Im)(n)||r.setFieldsValue({...n})}),[n]);const A=h.A.useWatch("isUseGlobal",r);return(0,a.useEffect)((()=>{const e=r.getFieldValue("dataList");if(A){const t={code:j["\u4e3b\u673a"]};e.every((e=>e.code!==j["\u4e3b\u673a"]))&&r.setFieldValue("dataList",[t,...e])}else r.setFieldValue("dataList",e.filter((e=>e.code!==j["\u4e3b\u673a"])))}),[A]),(0,k.jsx)(b.A,{open:t,title:i("\u7f16\u8f91\u63a7\u4ef6"),maskClosable:!1,width:"600px",footer:null,onCancel:v,children:(0,k.jsxs)(R,{children:[(0,k.jsxs)(h.A,{form:r,labelCol:{style:{width:"70px"}},children:[(0,k.jsxs)(f.A,{gutter:24,children:[(0,k.jsx)(m.A,{span:12,children:(0,k.jsx)(I,{labelCol:{style:{width:"auto"}},label:i("\u663e\u793a\u884c\u6570"),name:"rowNumber",children:(0,k.jsx)(p.A,{min:0,style:{width:"80%"}})})}),(0,k.jsx)(m.A,{span:12,children:(0,k.jsx)(I,{labelCol:{style:{width:"auto"}},label:"",valuePropName:"checked",name:"isUseGlobal",children:(0,k.jsx)(x.A,{children:i("\u5e94\u7528\u4e8e\u5168\u5c40\u76d1\u63a7")})})})]}),(0,k.jsx)(I,{style:{overflow:"hidden"},label:"",name:"dataList",children:(0,k.jsx)(S,{noDeleteDatas:[j["\u4e3b\u673a"]],dataSource:Object.keys(j).filter((e=>"\u4e3b\u673a"!==e)).map((e=>({label:e,code:j[e],disabled:!A&&j[e]===j["\u4e3b\u673a"]}))),transferSelectIndex:c,transferSelectIndexChange:e=>{u(e)}})})]}),(0,k.jsx)("div",{className:"footer-div",children:(0,k.jsxs)(g.A,{children:[(0,k.jsx)(y.Ay,{onClick:v,children:i("\u53d6\u6d88")}),(0,k.jsx)(y.Ay,{type:"primary",onClick:async()=>{const e=await r.validateFields();e?(o(e),v()):(o(void 0),v())},children:i("\u786e\u8ba4")})]})})]})})};var _=l(60446),O=l.n(_),T=l(19372),M=l(36497),$=l(83720),F=l(93950),L=l.n(F),Y=l(9339),E=l(67208),V=l(34458),z=l(44409),W=l(56543),B=l(90912);const G=N.Ay.div`
    width: 100%;
    height: 100%;
    padding: 5px;
    display: flex;
    flex-direction: column;

    .search-box {
        width: 100%;
        display: flex;
        .form-container{
            flex: 1;
            .ant-form-item{
                display: inline-block;
                margin-right: 12px;
            }
        }
        .action{
        }
    }
    .table-container{
        flex: 1;
        overflow: hidden;
    }
`,{RangePicker:U}=T.A,P=[O()().startOf("day"),O()().endOf("day")],J=Object.entries(j).reduce(((e,t)=>{let[l,n]=t;return e[n]=l,e}),{}),K=(e,t)=>{let{config:l}=e;const n=(0,o.d4)((e=>e.global.userIsAdmin)),{useSubscriber:i}=(0,z.A)(),{t:r}=(0,s.Bd)(),d=(0,a.useRef)(),c=(0,a.useRef)(),[u,v]=(0,a.useState)(0),[f,m]=(0,a.useState)(),[p,x]=(0,a.useState)([]),b=(0,a.useMemo)((()=>n?p:p.filter((e=>"Error"===(null===e||void 0===e?void 0:e.grade)))),[n,p]),[j]=h.A.useForm(),C=(0,a.useRef)(!0),S=(0,a.useCallback)((()=>{if(c.current){const e=c.current.offsetHeight-30-45;v(e>0?e:0)}}),[]);(0,a.useEffect)((()=>{S();const e=new ResizeObserver(L()(S,100));return c.current&&e.observe(c.current),()=>{c.current&&e.unobserve(c.current),e.disconnect()}}),[S]);const N=e=>((null===l||void 0===l?void 0:l.dataList)||[]).map((t=>({title:J[t.code],dataIndex:t.code,key:t.code,width:A[t.code],ellipsis:!0,render:t=>e(t)})))||[],R=async()=>{const e=await j.getFieldsValue(),t=(null===e||void 0===e?void 0:e.HostName)||"",[l,n]=(null===e||void 0===e?void 0:e.dateRange)||[],a=l?`${null===l||void 0===l?void 0:l.format("YYYY-MM-DD HH:ss:mm")}`:"",o=n?`${null===n||void 0===n?void 0:n.format("YYYY-MM-DD HH:ss:mm")}`:"",s=(0,V.HN)();try{const l=await(0,E.eVJ)({ClassName:`project_${s}`,HostName:t||void 0,StartTime:a||void 0,EndTime:o||void 0,types:(null===e||void 0===e?void 0:e.types)||void 0,grades:(null===e||void 0===e?void 0:e.grades)||void 0}),n=l.reverse();l&&x(n||[])}catch(i){console.error(i)}};(0,a.useEffect)((()=>{(async()=>{if(null!==l&&void 0!==l&&l.isUseGlobal){const e=[{value:"%%",label:"\u5168\u90e8"},{value:"",label:"\u5168\u5c40\u76d1\u63a7\u9879\u76ee"}],t=await(0,E.BxM)();await j.setFieldsValue({HostName:"%%"}),Array.isArray(t)&&m([...e,...[...t].map((e=>({value:e.stationName,label:e.stationName})))])}R()})()}),[l]);(0,a.useEffect)((()=>((async()=>{d.current=await i(W.t6.LOG_CONTROL_DATA);for await(const[e,t]of d.current){const e=JSON.parse(t);C.current&&x((t=>[...t,...e]))}})(),()=>{var e,t;null===(e=d.current)||void 0===e||null===(t=e.close)||void 0===t||t.call(e)})),[]),(0,a.useImperativeHandle)(t,(()=>({getAllDatas:()=>p})));const[I,D]=(0,a.useState)(!1),[H,_]=(0,a.useState)(""),O=async e=>{_(e),D(!0)};return(0,k.jsxs)(G,{children:[(0,k.jsxs)("div",{className:"search-box",children:[(0,k.jsxs)(h.A,{form:j,className:"form-container",layout:"horizontal",initialValues:{dateRange:P},children:[(null===l||void 0===l?void 0:l.isUseGlobal)&&(0,k.jsx)(h.A.Item,{name:"HostName",label:r("\u4e3b\u673a"),children:(0,k.jsx)(M.A,{options:null===f||void 0===f?void 0:f.map((e=>({...e,label:r(e.label)}))),style:{width:"150px"},allowClear:!1})}),(0,k.jsx)(h.A.Item,{name:"dateRange",label:r("\u65f6\u95f4\u8303\u56f4"),children:(0,k.jsx)(U,{allowClear:!0,showTime:!0})}),(0,k.jsx)(h.A.Item,{name:"types",label:r("\u7c7b\u578b"),children:(0,k.jsx)($.A,{allowClear:!0,placeholder:r("\u591a\u6761\u8bf7\u4f7f\u7528\u9017\u53f7\u5206\u9694")})}),(0,k.jsx)(h.A.Item,{name:"grades",label:r("\u7b49\u7ea7"),children:(0,k.jsx)($.A,{allowClear:!0,placeholder:r("\u591a\u6761\u8bf7\u4f7f\u7528\u9017\u53f7\u5206\u9694")})})]}),(0,k.jsx)("div",{className:"action",children:(0,k.jsxs)(g.A,{children:[(0,k.jsx)(y.Ay,{type:"primary",className:"get-button",onClick:async()=>{C.current=!1,R()},children:r("\u67e5\u8be2")}),(0,k.jsx)(y.Ay,{className:"reset-button",onClick:()=>{C.current=!0;const e={dateRange:P,types:void 0,grades:void 0};var t;null!==l&&void 0!==l&&l.isUseGlobal&&(e.HostName=null===f||void 0===f||null===(t=f[0])||void 0===t?void 0:t.value);j.setFieldsValue(e),R()},children:r("\u91cd\u7f6e")}),(0,k.jsx)(y.Ay,{className:"reset-button",onClick:()=>{x([])},children:r("\u6e05\u5c4f")}),n&&(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(y.Ay,{onClick:()=>O("task"),children:r("TaskServer Log")}),(0,k.jsx)(y.Ay,{onClick:()=>O("hw"),children:r("HardwareConnector Log")})]})]})})]}),(0,k.jsx)("div",{className:"table-container",ref:c,children:N(r).length>0?(0,k.jsx)(Y.A,{bordered:!0,columns:N(r),size:"small",dataSource:b,scroll:{y:u},pagination:{pageSize:(null===l||void 0===l?void 0:l.rowNumber)||w.rowNumber,showQuickJumper:!0,hideOnSinglePage:!0,showSizeChanger:!1}}):null}),I&&(0,k.jsx)(B.A,{open:I,type:H,onCancel:()=>{_(""),D(!1)}})]})},q=(0,a.forwardRef)(K),Q=N.Ay.div`
    width: 100%;
    height: 100%;
    background: #fff;
    
`,X=e=>{let{item:t,id:l,layoutConfig:n}=e;const{t:u}=(0,s.Bd)(),h=(0,o.d4)((e=>e.template.widgetData)),{editWidget:f}=(0,r.A)(),m=(0,a.useRef)(null),[p,x]=(0,a.useState)(!1),g=(0,a.useMemo)((()=>(0,d.Rm)(h,"widget_id",null===t||void 0===t?void 0:t.widget_id)),[t,h]),y=(0,a.useMemo)((()=>{var e;return null!==(e=null===g||void 0===g?void 0:g.data_source)&&void 0!==e?e:w}),[g]);return(0,k.jsxs)(Q,{children:[(0,k.jsx)(q,{config:y,red:m}),p&&(0,k.jsx)(H,{open:p,setOpen:x,config:y,updateConfig:e=>{f({...g,data_source:e})}}),(0,k.jsxs)(c.A,{domId:l,layoutConfig:n,handelEditClick:!0,children:[(0,k.jsx)("div",{className:"unique-content",onClick:()=>x(!0),children:u("\u7f16\u8f91\u63a7\u4ef6\u5c5e\u6027")}),(0,k.jsx)("div",{className:"unique-content",onClick:()=>{var e;const t=y.dataList||[],l=(n=j,t.map((e=>{const{code:t}=e;return Object.keys(n).find((e=>n[e]===t))||null})).filter((e=>null!==e)));var n;const a=(null===m||void 0===m||null===(e=m.current)||void 0===e?void 0:e.getAllDatas)||[],o=[l];a.forEach((e=>{const l=t.map((t=>null!==e&&void 0!==e&&e[null===t||void 0===t?void 0:t.code]?String(null===e||void 0===e?void 0:e[null===t||void 0===t?void 0:t.code]):""));o.push(l)}));const s=i.Wp.aoa_to_sheet(o),r=i.Wp.book_new();i.Wp.book_append_sheet(r,s,"Sheet1"),i._h(r,`\u65e5\u5fd7\u63a7\u4ef6${v()().format("YYYYMMDDHHmmss")}.xlsx`)},children:u("\u5bfc\u51faxlsx")})]})]})}},90912:(e,t,l)=>{l.d(t,{A:()=>m});var n=l(65043),a=l(16569),o=l(95206),s=l(8918),i=l(11634),r=l(74117),d=l(34458),c=l(75440),u=l(4178);const v=l(81143).Ay.div`
    background: rgba(255,255,255,0.9);
    padding: 10px;
    user-select: text;
    height: 65vh;
    .search-layout {
        display: flex;
        justify-content: end;
        margin-bottom: 10px;
    }
    
`;var h=l(70579);const f=(0,n.forwardRef)(((e,t)=>{let{path:l,logRef:o,isArr:s}=e;const[d,c]=(0,n.useState)([]),{t:v}=(0,r.Bd)(),{getReadLog:f}=(0,u.A)();(0,n.useEffect)((()=>{l&&m(l)}),[l]);const m=async e=>{try{const t=await f({path:e});c(t)}catch(t){a.Ay.error(`${v("\u83b7\u53d6\u65e5\u5fd7\u5931\u8d25")}${t}`)}};(0,n.useImperativeHandle)(t,(()=>({reset:()=>{m(l)}})));return(0,h.jsx)(i._m,{height:(()=>{var e,t,l;const n=null!==(e=(null!==(t=null===o||void 0===o||null===(l=o.current)||void 0===l?void 0:l.offsetHeight)&&void 0!==t?t:400)-50)&&void 0!==e?e:400;return s?n-80:n})(),itemCount:null===d||void 0===d?void 0:d.length,itemSize:e=>18*Math.ceil((null===d||void 0===d?void 0:d[e].length)/80),width:"100%",children:e=>{let{index:t,style:l}=e;return(0,h.jsx)("div",{style:l,children:null===d||void 0===d?void 0:d[t]})}})})),m=e=>{let{open:t,type:l,onCancel:i}=e;const{t:m}=(0,r.Bd)(),p=(0,n.useRef)(),x=(0,n.useRef)(),g=(0,n.useRef)(),{getRead:y}=(0,u.A)(),[b,j]=(0,n.useState)(void 0);(0,n.useEffect)((()=>{(async e=>{const t=function(e){const t=e.lastIndexOf("\\");return-1===t?null:e.slice(0,t)}((0,d.Ti)());if("task"===e){let e=`${t}/TaskServer/bin/Debug/net6.0/TaskServer.log`;e=`${(0,d.Ti)()}/taskServer/TaskServer.log`,j(e)}else{const e=`${(0,d.Ti)()}/taskServer/subTasks.json`,t=await y({path:e});if(t){const{hardwareConnectors:e}=JSON.parse(t),l=e.map((e=>{const t=function(e){const t=e.split("\\");return t.length>1?(t.pop(),t.join("\\")):e}(e.exePath),l=function(e){const t=e.split("\\");return t[t.length-1]}(t);return{key:l,label:l,path:`${t}/HardwareConnector.log`}}));j(l)}else a.Ay.error(m("\u672a\u627e\u5230log"))}})(l)}),[l]);const A=(0,n.useMemo)((()=>Array.isArray(b)?b.map((e=>({key:e.key,label:e.label,children:(0,h.jsx)(f,{isArr:!0,tabsRef:g,path:e.path,logRef:p,ref:x})}))):[]),[b]),w=()=>{var e;x.current&&(null===(e=x.current)||void 0===e||e.reset())};return(0,h.jsx)(c.A,{open:t,title:m("\u65e5\u5fd7"),maskClosable:!1,width:"70vw",onCancel:i,footer:null,children:(0,h.jsxs)(v,{ref:p,children:[(0,h.jsx)("div",{className:"search-layout",children:(0,h.jsx)(o.Ay,{onClick:w,children:m("\u91cd\u65b0\u83b7\u53d6\u65e5\u5fd7")})}),Array.isArray(b)?(0,h.jsx)(s.A,{items:A,destroyInactiveTabPane:!0}):(0,h.jsx)(f,{path:b,ref:x,onReset:w,logRef:p})]})})}}}]);
//# sourceMappingURL=3337.bd8baad2.chunk.js.map