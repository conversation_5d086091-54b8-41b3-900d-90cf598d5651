"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3590],{42999:(e,l,t)=>{t.d(l,{A:()=>h});var a=t(65043),n=t(37097),r=t(6051),s=t(36282),o=t(18650),i=t(81143),c=t(68374);const d=i.Ay.div`
    .color-layout {
        display: flex;
        align-items: center;
    }
 
    .background-layout {
        min-width: ${(0,c.D0)("25px")} !important;
        min-height: ${(0,c.D0)("25px")} !important;
        background-color: #000;
        border-radius: 2px;
    }
    .background-img {
        width: ${(0,c.D0)("23px")};
        height: ${(0,c.D0)("23px")};
        display: flex;
        align-items:center ;
        justify-content: center;
        cursor: pointer;
    }
    .allowed {
        cursor: not-allowed;
    }

`;var x=t(70579);const h=e=>{let{onChange:l,value:t,disabled:i=!1}=e;const[c,h]=(0,a.useState)(t);(0,a.useEffect)((()=>{h(t||"#000")}),[t]);const u=(0,x.jsx)(x.Fragment,{children:(0,x.jsx)(n.Xq,{color:c,showMoreColor:!1,onChangeComplete:e=>{const{rgb:t}=e,a=`rgba(${t.r},${t.g},${t.b},${t.a})`;h(a),l&&l(a)}})});return(0,x.jsx)(d,{children:(0,x.jsx)("div",{className:"color-layout",children:(0,x.jsxs)(r.A,{children:[(0,x.jsx)("div",{className:"background-layout",style:{backgroundColor:c}}),!i&&(0,x.jsx)(s.A,{overlayClassName:"popover-sketch-picker",content:u,title:"",trigger:"click",placement:"bottom",destroyOnHidden:!0,arrow:!1,children:(0,x.jsx)("img",{className:"background-img "+(i?"allowed":""),src:o.Dp,alt:""})})]})})})}},53590:(e,l,t)=>{t.r(l),t.d(l,{default:()=>B});var a=t(65043),n=t(25055),r=t(8918),s=t(83720),o=t(47419),i=t(11645),c=t(12624),d=t(36497),x=t(32513),h=t(19853),u=t.n(h),j=t(74117),m=t(68358),b=t(63189),p=t(63379),A=t(42999),g=t(97588),v=t(80286);const y=t(81143).Ay.div`
    .atom-button-attr{
        max-width: 300px;
        .title{
            margin-top: 12px;
            margin-bottom: 12px;
            font-size: 14px;
            padding-left: 21px;
        }
    }
    .ant-space-item{
        .background-layout{
            border: 1px solid rgba(0,0,0,.2);
        }
    }
`;var f=t(70579);const C=Object.keys(v.C3).map((e=>({label:e,value:v.C3[e]}))),k=Object.keys(v.zZ).map((e=>({label:e,value:v.zZ[e]}))),{useForm:w,Item:z}=n.A,B=e=>{let{open:l,onClose:t,config:h,setConfig:v}=e;const{t:B}=(0,j.Bd)(),[F]=w(),N="100px";(0,a.useEffect)((()=>{u()(h,F.getFieldsValue())||F.setFieldsValue(h)}),[h]);const[V,$]=(0,a.useState)(!1),I=e=>{$(!1)};return(0,f.jsx)(m.A,{open:l,onClose:t,style:{width:"500px"},children:(0,f.jsx)(y,{children:(0,f.jsx)(n.A,{form:F,labelCol:{style:{width:N}},onValuesChange:(e,l)=>{var t;let a=l;null!==e&&void 0!==e&&null!==(t=e.variable)&&void 0!==t&&t.value&&(a={...a,attr:{...a.attr,label:e.variable.value.variable_name}}),v(a)},children:(0,f.jsx)(r.A,{defaultActiveKey:"attr",items:[{key:"attr",label:B("\u5c5e\u6027"),forceRender:!0,children:(0,f.jsx)(f.Fragment,{children:(0,f.jsxs)("div",{className:"atom-button-attr",children:[(0,f.jsx)(z,{label:B("\u6309\u94ae\u5185\u5bb9"),name:["attr","text"],children:(0,f.jsx)(s.A,{placeholder:"\u8bf7\u8f93\u5165"})}),(0,f.jsx)(z,{label:B("\u8f93\u5165\u63d0\u793a"),name:["attr","placeholder"],children:(0,f.jsx)(s.A,{placeholder:"\u8bf7\u8f93\u5165"})}),(0,f.jsx)(z,{label:B("\u7ec4\u4ef6\u5bbd\u5ea6"),name:["attr","compWidth"],children:(0,f.jsx)(b.A,{style:{width:"100%"},addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,f.jsx)(z,{label:B("\u7ec4\u4ef6\u9ad8\u5ea6"),name:["attr","compHeight"],children:(0,f.jsx)(b.A,{style:{width:"100%"},addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,f.jsxs)(o.A,{children:[(0,f.jsx)(i.A,{children:(0,f.jsx)(z,{label:B("\u662f\u5426\u7981\u7528"),name:["attr","disabled"],valuePropName:"checked",children:(0,f.jsx)(c.A,{})})}),(0,f.jsx)(i.A,{children:(0,f.jsx)(z,{label:B("\u5173\u95ed\u5f39\u7a97"),name:["attr","closeDialog"],valuePropName:"checked",children:(0,f.jsx)(c.A,{})})})]}),(0,f.jsx)("div",{className:"title",children:B("\u6309\u94ae\u5c5e\u6027")}),(0,f.jsx)(z,{label:B("\u6309\u94ae\u6837\u5f0f"),name:["attr","buttonStyleType"],children:(0,f.jsx)(d.A,{options:C})}),(0,f.jsxs)(o.A,{children:[(0,f.jsx)(i.A,{flex:N}),(0,f.jsx)(i.A,{flex:"auto",children:(0,f.jsx)(z,{labelCol:{style:{width:"auto"}},label:"",valuePropName:"checked",name:["attr","buttonUsedBorderRadius"],children:(0,f.jsx)(x.A,{children:B("\u4f7f\u7528\u5706\u89d2")})})})]}),(0,f.jsx)(z,{label:B("\u6309\u94ae\u5c3a\u5bf8"),name:["attr","buttonSize"],children:(0,f.jsx)(d.A,{options:k})}),(0,f.jsx)(z,{label:B("\u6309\u94ae\u80cc\u666f\u8272"),name:["attr","buttonBackgroundColor"],children:(0,f.jsx)(A.A,{})}),(0,f.jsx)(z,{label:B("\u6309\u94ae\u6587\u5b57\u989c\u8272"),name:["attr","buttonTextColor"],children:(0,f.jsx)(A.A,{})}),(0,f.jsxs)(o.A,{children:[(0,f.jsx)(i.A,{flex:N,style:{textAlign:"right"},children:(0,f.jsx)(z,{labelCol:{style:{width:"auto"}},label:"",valuePropName:"checked",name:["attr","buttonBorderShow"],children:(0,f.jsxs)(x.A,{children:[B("\u6309\u94ae\u8fb9\u6846")," ",":"]})})}),(0,f.jsx)(i.A,{flex:"auto",children:(0,f.jsx)(z,{labelCol:{style:{width:"auto"}},label:"",name:["attr","buttonBorderColor"],children:(0,f.jsx)(A.A,{})})})]}),(0,f.jsxs)(o.A,{children:[(0,f.jsx)(i.A,{flex:N,style:{textAlign:"right"},children:(0,f.jsx)(z,{labelCol:{style:{width:"auto"}},label:"",valuePropName:"checked",name:["attr","buttonIconShow"],children:(0,f.jsxs)(x.A,{children:[B("\u56fe\u6807")," ",":"]})})}),(0,f.jsx)(i.A,{flex:"auto",children:(0,f.jsx)(z,{labelCol:{style:{width:"auto"}},label:"",name:["attr","buttonIcon"],children:(0,f.jsx)(g.A,{src:F.getFieldValue(["attr","buttonIcon"]),btnCLick:e=>{$(!0)},btnTitle:B("\u9009\u62e9\u56fe\u7247"),open:V,onCancel:I,onChange:e=>{F.setFieldValue(["attr","buttonIcon"],e),I()},modalTitle:B("\u9009\u62e9\u56fe\u7247")})})})]})]})})},{key:"event",label:B("\u4e8b\u4ef6"),forceRender:!0,children:(0,f.jsx)(f.Fragment,{children:(0,f.jsx)(z,{label:B("\u6309\u94ae\u70b9\u51fb\u65f6\u89e6\u53d1"),name:["event","click"],children:(0,f.jsx)(p.A,{})})})}]})})})})}},68358:(e,l,t)=>{t.d(l,{A:()=>u});var a=t(65043),n=t(48677),r=t(80077),s=t(14463),o=t(25055),i=t(36282),c=t(96603),d=t(14524),x=t(70579);const h=e=>{let{setting:l,onChange:t}=e;const[n]=o.A.useForm();(0,a.useEffect)((()=>{n.setFieldsValue({...l})}),[l]);return(0,x.jsx)(i.A,{content:(0,x.jsxs)(o.A,{form:n,name:"basic",labelCol:{style:{width:35}},onValuesChange:(e,l)=>{t(l)},children:[(0,x.jsx)(o.A.Item,{label:"\u4f4d\u7f6e",name:"placement",children:(0,x.jsxs)(c.Ay.Group,{size:"small",children:[(0,x.jsx)(c.Ay.Button,{value:"top",children:"\u4e0a"}),(0,x.jsx)(c.Ay.Button,{value:"right",children:"\u53f3"}),(0,x.jsx)(c.Ay.Button,{value:"bottom",children:"\u4e0b"}),(0,x.jsx)(c.Ay.Button,{value:"left",children:"\u5de6"})]})}),(0,x.jsx)(o.A.Item,{label:"\u5c3a\u5bf8",name:"size",children:(0,x.jsxs)(c.Ay.Group,{size:"small",children:[(0,x.jsx)(c.Ay.Button,{value:"default",children:"\u6b63\u5e38"}),(0,x.jsx)(c.Ay.Button,{value:"large",children:"\u5927"})]})})]}),title:"",trigger:"click",placement:"leftTop",children:(0,x.jsx)(d.A,{})})},u=e=>{let{children:l,open:t,onClose:a}=e;const o=(0,r.wA)(),{drawSetting:i}=(0,r.d4)((e=>e.split));return(0,x.jsx)(x.Fragment,{children:t&&(0,x.jsx)(n.A,{open:t,size:null===i||void 0===i?void 0:i.size,placement:null===i||void 0===i?void 0:i.placement,onClose:a,extra:(0,x.jsx)(h,{setting:i,onChange:e=>{o({type:s.cd,param:e})}}),children:l})})}}}]);
//# sourceMappingURL=3590.b4aa5ee3.chunk.js.map