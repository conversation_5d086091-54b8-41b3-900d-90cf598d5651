"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3966],{16204:(e,t,r)=>{r.d(t,{A:()=>h});r(65043);var n=r(75440),c=r(25055),o=r(83720),a=r(6051),i=r(74117),s=r(4554),l=r(40940),u=(r(34458),r(81143));r(68374);const d=u.Ay.div`
    display: flex;
    justify-content: space-between;
    height: 10vh;
    .upload-modal-left {
        width: 70%;
        min-width: 0; /* 允许子元素收缩 */
    }
    .upload-modal-right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 9vw;
        flex-shrink: 0; /* 防止按钮区域被压缩 */
    }
    .path-layout {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        
        > div:first-child {
            flex-shrink: 0; /* 标签文字不压缩 */
            margin-right: 8px;
        }
        
        .ant-upload-wrapper {
            flex: 1;
            min-width: 0; /* 允许上传组件收缩 */
            
            .ant-upload-list {
                .ant-upload-list-item {
                    .ant-upload-list-item-name {
                        max-width: 200px; /* 限制文件名最大宽度 */
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }
`;var f=r(70579);const h=e=>{let{open:t,onCancel:r,onOk:u,title:h="\u5bfc\u51fa\u9879\u76ee",pathName:p="\u5bfc\u51fa\u8def\u5f84",defaultPath:m="",defaultFileName:v=""}=e;const{t:g}=(0,i.Bd)(),[b]=c.A.useForm();return(0,f.jsx)(n.A,{open:t,title:g(h),width:600,onCancel:r,footer:null,children:(0,f.jsxs)(d,{children:[(0,f.jsx)("div",{className:"upload-modal-left",children:(0,f.jsxs)(c.A,{form:b,initialValues:{path:m,fileName:v},children:[(0,f.jsx)(c.A.Item,{label:g(p),name:"path",rules:[{required:!0,message:g("\u8bf7\u9009\u62e9\u8def\u5f84")}],children:(0,f.jsx)(l.A,{})}),(0,f.jsx)(c.A.Item,{label:g("\u6587\u4ef6\u540d\u79f0"),name:"fileName",rules:[{required:!0,message:g("\u8bf7\u8f93\u5165\u6587\u4ef6\u540d\u79f0")},{whitespace:!0,message:g("\u6587\u4ef6\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a")}],children:(0,f.jsx)(o.A,{placeholder:g("\u8bf7\u8f93\u5165\u5bfc\u51fa\u6587\u4ef6\u540d\u79f0"),style:{width:"100%"}})})]})}),(0,f.jsx)("div",{className:"upload-modal-right",children:(0,f.jsxs)(a.A,{direction:"vertical",children:[(0,f.jsx)(s.A,{block:!0,onClick:async()=>{try{const e=await b.validateFields();u(e.path,e.fileName)}catch(e){console.log("\u8868\u5355\u9a8c\u8bc1\u5931\u8d25:",e)}},children:g("\u786e\u8ba4")}),(0,f.jsx)(s.A,{block:!0,onClick:r,children:g("\u53d6\u6d88")})]})})]})})}},46959:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(58168),c=r(65043);const o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"};var a=r(22172),i=function(e,t){return c.createElement(a.A,(0,n.A)({},e,{ref:t,icon:o}))};const s=c.forwardRef(i)},63804:(e,t,r)=>{r.d(t,{d:()=>l,A:()=>u});var n=r(65043),c=r(67208),o=r(36950),a=r(19853),i=r.n(a);function s(){let{threshold:e=0,rootMargin:t="0px",enableIntersectionObserver:r=!0,enablePageVisibility:c=!0,enableMutationObserver:o=!0,onVisibilityChange:a=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const i=(0,n.useRef)(null),s=(0,n.useRef)({isIntersecting:!1,isPageVisible:!0,displayStyle:"block",position:{top:0,left:0}}),l=(0,n.useRef)(!1),u=(0,n.useCallback)((()=>{const e=s.current,t=e.isIntersecting&&e.isPageVisible&&"none"!==e.displayStyle;t!==l.current&&(l.current=t,a&&a(t))}),[a]);return(0,n.useEffect)((()=>{if(!i.current)return()=>{};const n=[];if(r){const r=new IntersectionObserver((e=>{e.forEach((e=>{const t=s.current.isIntersecting,r=e.isIntersecting;t!==r&&(s.current={...s.current,isIntersecting:r},u(),r?console.log("\ud83d\udd0d \u5143\u7d20\u8fdb\u5165\u89c6\u53e3"):console.log("\ud83d\udc7b \u5143\u7d20\u79bb\u5f00\u89c6\u53e3"))}))}),{threshold:e,rootMargin:t});r.observe(i.current),n.push((()=>r.disconnect()))}if(c){const e=()=>{const e=!document.hidden;s.current={...s.current,isPageVisible:e},u(),e?console.log("\ud83d\udc41\ufe0f \u9875\u9762\u53d8\u4e3a\u53ef\u89c1"):console.log("\ud83d\ude48 \u9875\u9762\u53d8\u4e3a\u4e0d\u53ef\u89c1")};document.addEventListener("visibilitychange",e),n.push((()=>document.removeEventListener("visibilitychange",e)))}if(o){const e=new MutationObserver((e=>{e.forEach((e=>{if("attributes"===e.type&&"style"===e.attributeName){const e=window.getComputedStyle(i.current).display;s.current={...s.current,displayStyle:e},u(),"none"===e?console.log("\ud83d\udeab \u5143\u7d20\u88ab\u9690\u85cf (display: none)"):console.log("\u2705 \u5143\u7d20\u663e\u793a\u6837\u5f0f\u6062\u590d")}}))}));e.observe(i.current,{attributes:!0,attributeFilter:["style"]}),n.push((()=>e.disconnect()))}return u(),()=>{n.forEach((e=>e()))}}),[e,t,r,c,o,u]),{targetRef:i}}const l={daqbuffer:"daqbuffer","\u4e8c\u7ef4\u6570\u7ec4":"doubleArray","\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408":"doubleArraySet"},u=(e,t)=>{let{controlCompId:r,dataSourceType:a,dataSourceCode:l,dataCodes:u,timer:d=-1,number:f=-1,testStatus:h=1,daqCurveSelectedSampleCodes:p}=e;const m=(0,n.useRef)(!1),v=(0,n.useRef)(!1),g=(0,n.useRef)(null),b=(0,n.useRef)(!1),y=(0,n.useRef)(!1),w=(0,n.useRef)();(0,n.useRef)(t).current=t,(0,n.useEffect)((()=>{if(!l||!r||!a||!u||0===u.length)return;const e={templateName:(0,o.n1)(),controlCompId:r,dataSourceType:a,dataSourceCode:l,dataCodes:u,timer:d,number:f,testStatus:h,daqCurveSelectedSampleCodes:null!==p&&void 0!==p?p:[]};i()(e,w.current)||(null===t||void 0===t||t(),w.current=e,b.current?m.current?(0,c.pj8)({...w.current}):(0,c.i_N)({...w.current}).then((()=>{m.current=!0,v.current=!0})):m.current&&(y.current=!0))}),[r,a,l,u,d,f,h,p]);const{targetRef:x}=s({onVisibilityChange:(0,n.useCallback)((async e=>{var t,r,n;if(b.current=e,e&&w.current){if(!m.current)return await(0,c.i_N)({...w.current}),m.current=!0,void(v.current=!0);if(y.current)return(0,c.pj8)({...w.current}),void(y.current=!1)}(g.current&&clearTimeout(g.current),e&&!v.current&&null!==(t=w.current)&&void 0!==t&&t.controlCompId)&&(await(0,c.pkF)(null===(n=w.current)||void 0===n?void 0:n.controlCompId),v.current=!0);!e&&v.current&&null!==(r=w.current)&&void 0!==r&&r.controlCompId&&(g.current=setTimeout((async()=>{await(0,c.UVQ)(w.current.controlCompId),v.current=!1}),3e3))}),[])});return(0,n.useEffect)((()=>()=>{g.current&&clearTimeout(g.current),m.current&&(0,c.UWZ)(w.current.controlCompId)}),[]),{targetRef:x}}},69312:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(58168),c=r(65043);const o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"};var a=r(22172),i=function(e,t){return c.createElement(a.A,(0,n.A)({},e,{ref:t,icon:o}))};const s=c.forwardRef(i)},84617:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(65043),c=r(60383),o=r(80077),a=r(44409),i=r(36950),s=r(91465);const l=e=>{let{controlCompId:t,onMessage:r}=e;const l=(0,o.wA)(),{useSubscriber:u}=(0,a.A)(),d=(0,n.useRef)(),f=(0,n.useRef)(r),h=(0,n.useRef)();(0,n.useEffect)((()=>{f.current=r}),[r]),(0,n.useEffect)((()=>(p(),()=>{var e,t;null===(e=d.current)||void 0===e||null===(t=e.close)||void 0===t||t.call(e)})),[t]);const p=async()=>{const e=`${(0,i.n1)()}-ControlCompUIData-${t}-UIData`;d.current=await u(e);for await(const[t,o]of d.current){let e;try{e=c.D(o)}catch(r){try{e=JSON.parse(o)}catch(n){console.error("GridLayout\u6570\u636e\u89e3\u6790\u5931\u8d25",n)}}2===e.mode?h.current=l((0,s.J_)("\u5927\u6570\u636e\u91cf\u52a0\u8f7d\u4e2d...")):3===e.mode?l((0,s.ge)(h.current)):f.current(e)}}}}}]);
//# sourceMappingURL=3966.04252ba1.chunk.js.map