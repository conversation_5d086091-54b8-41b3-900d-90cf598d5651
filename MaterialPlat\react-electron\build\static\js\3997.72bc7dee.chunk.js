"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3997],{22854:(e,n,t)=>{t.r(n),t.d(n,{default:()=>y});var a=t(65043),o=t(80231),r=t(93950),c=t.n(r),i=t(80077),d=t(12097),l=t(58168);const s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M820 436h-40c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8zm32-104H732V120c0-4.4-3.6-8-8-8H300c-4.4 0-8 3.6-8 8v212H172c-44.2 0-80 35.8-80 80v328c0 17.7 14.3 32 32 32h168v132c0 4.4 3.6 8 8 8h424c4.4 0 8-3.6 8-8V772h168c17.7 0 32-14.3 32-32V412c0-44.2-35.8-80-80-80zM360 180h304v152H360V180zm304 664H360V568h304v276zm200-140H732V500H292v204H160V412c0-6.6 5.4-12 12-12h680c6.6 0 12 5.4 12 12v292z"}}]},name:"printer",theme:"outlined"};var f=t(22172),u=function(e,n){return a.createElement(f.A,(0,l.A)({},e,{ref:n,icon:s}))};const p=a.forwardRef(u);t(92676);var v=t(81143);t(68374);const h=v.Ay.div`
    display: flex;
    justify-content:center;
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-color: #fff;
    padding: 10px;    

   
    .pdf-layout {

        display: flex;
    }
    .func-layout {
        padding: 10px;
        display: flex;
        align-items: flex-start;
        ${e=>e.isOpenExperiment&&"\n            pointer-events: none;\n        "}
    }
`,x=v.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }

`;var m=t(70579);const g=e=>{let{domId:n,layoutConfig:t}=e;return(0,m.jsx)(x,{children:(0,m.jsx)(o.A,{domId:n,layoutConfig:t})})},w=(e,n)=>{var t;let{id:o,data:r,layoutConfig:l,onPrintCallback:s}=e;const f=(0,a.useRef)(),u=(0,i.d4)((e=>e.subTask.openExperiment)),v=(0,i.d4)((e=>e.template.exportList)),{pdf_config:x,export_name:w}=null!==(t=null===v||void 0===v?void 0:v.find((e=>"pdf"===(null===e||void 0===e?void 0:e.export_type)&&1===(null===e||void 0===e?void 0:e.default_flag))))&&void 0!==t?t:{pdf_config:[],export_name:""},y=(0,a.useCallback)(c()((e=>b(e)),1500),[]),b=()=>{f.current&&f.current.downloadPdf()};return(0,a.useImperativeHandle)(n,(()=>({downloadPdf:b}))),(0,m.jsxs)(h,{isOpenExperiment:u,children:[(0,m.jsx)("div",{className:"func-layout",children:(0,m.jsx)(p,{onClick:y})}),(0,m.jsx)(d.A,{parentId:o,ref:f,layoutData:x,config:{export_name:w},onPrintCallback:s}),o&&(0,m.jsx)(g,{domId:o,layoutConfig:l})]})},y=(0,a.forwardRef)(w)},46959:(e,n,t)=>{t.d(n,{A:()=>d});var a=t(58168),o=t(65043);const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"};var c=t(22172),i=function(e,n){return o.createElement(c.A,(0,a.A)({},e,{ref:n,icon:r}))};const d=o.forwardRef(i)},69312:(e,n,t)=>{t.d(n,{A:()=>d});var a=t(58168),o=t(65043);const r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"};var c=t(22172),i=function(e,n){return o.createElement(c.A,(0,a.A)({},e,{ref:n,icon:r}))};const d=o.forwardRef(i)}}]);
//# sourceMappingURL=3997.72bc7dee.chunk.js.map