"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4376],{94376:(e,i,n)=>{n.d(i,{A:()=>se});var s=n(65043),r=n(80077),d=n(56434),l=n.n(d),t=n(74117),a=n(67299),c=n(8237),o=n(56543),u=n(81143),x=n(68374);u.Ay.div`
    background: ${x.o$.splitBack};
    border-radius: 8px;
    padding: 10px;
    height: 100%;
    width: 100%;
   
`,u.Ay.div`
    background: #FFFFFF;
    /* border-radius: 8px; */
    /* border: 1px solid #D7E2FF; */
    display: flex;
    align-items: center;
    justify-content:center ;
    height: 100%;
    width: 100%;
    overflow: hidden;
`;const j=u.Ay.div`
    height: 100%;
    width: 100%;
    overflow: hidden;
`;u.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }

`;var h=n(70579);const m=(0,s.lazy)((()=>Promise.all([n.e(2535),n.e(910)]).then(n.bind(n,90910)))),g=(0,s.lazy)((()=>Promise.all([n.e(2535),n.e(9882)]).then(n.bind(n,49882)))),p=(0,s.lazy)((()=>n.e(2629).then(n.bind(n,82629)))),f=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(3164)]).then(n.bind(n,3164)))),y=(0,s.lazy)((()=>Promise.all([n.e(1043),n.e(1937)]).then(n.bind(n,77252)))),E=(0,s.lazy)((()=>n.e(113).then(n.bind(n,20113)))),b=(0,s.lazy)((()=>Promise.all([n.e(3481),n.e(4154)]).then(n.bind(n,43481)))),C=(0,s.lazy)((()=>Promise.resolve().then(n.bind(n,12069)))),T=(0,s.lazy)((()=>n.e(742).then(n.bind(n,10742)))),_=(0,s.lazy)((()=>n.e(6988).then(n.bind(n,64607)))),S=(0,s.lazy)((()=>Promise.all([n.e(9478),n.e(4839)]).then(n.bind(n,54839)))),A=(0,s.lazy)((()=>Promise.all([n.e(9478),n.e(7813)]).then(n.bind(n,17813)))),X=(0,s.lazy)((()=>Promise.all([n.e(5575),n.e(6099)]).then(n.bind(n,88312)))),F=(0,s.lazy)((()=>n.e(3509).then(n.bind(n,3509)))),P=(0,s.lazy)((()=>n.e(7794).then(n.bind(n,7794)))),k=(0,s.lazy)((()=>Promise.all([n.e(9372),n.e(4917),n.e(3966)]).then(n.bind(n,84917)))),v=(0,s.lazy)((()=>Promise.all([n.e(5558),n.e(8098)]).then(n.bind(n,68098)))),R=(0,s.lazy)((()=>Promise.all([n.e(9372),n.e(1238),n.e(1634),n.e(3337)]).then(n.bind(n,43337)))),z=(0,s.lazy)((()=>n.e(5689).then(n.bind(n,78070)))),I=(0,s.lazy)((()=>n.e(5503).then(n.bind(n,45503)))),M=(0,s.lazy)((()=>Promise.all([n.e(2535),n.e(53),n.e(3082)]).then(n.bind(n,93082)))),N=(0,s.lazy)((()=>Promise.all([n.e(5239),n.e(3821),n.e(7326),n.e(8185),n.e(4607),n.e(1434)]).then(n.bind(n,11434)))),O=(0,s.lazy)((()=>Promise.resolve().then(n.bind(n,30092)))),L=(0,s.lazy)((()=>n.e(9950).then(n.bind(n,69950)))),D=(0,s.lazy)((()=>Promise.all([n.e(1238),n.e(5347)]).then(n.bind(n,75347)))),U=(0,s.lazy)((()=>Promise.all([n.e(9478),n.e(5239),n.e(9372),n.e(8114),n.e(1234),n.e(4917),n.e(113),n.e(3481),n.e(2097),n.e(3997)]).then(n.bind(n,22854)))),w=(0,s.lazy)((()=>Promise.all([n.e(5239),n.e(3821),n.e(7326),n.e(8185),n.e(4607),n.e(1434),n.e(3714)]).then(n.bind(n,83714)))),B=(0,s.lazy)((()=>Promise.all([n.e(9478),n.e(8114),n.e(1234),n.e(4068)]).then(n.bind(n,64068)))),G=(0,s.lazy)((()=>Promise.all([n.e(9478),n.e(8114),n.e(1234),n.e(6143)]).then(n.bind(n,56143)))),V=(0,s.lazy)((()=>n.e(3056).then(n.bind(n,3056)))),Y=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(1600)]).then(n.bind(n,41600)))),H=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(9965)]).then(n.bind(n,79965)))),$=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(6224)]).then(n.bind(n,56224)))),K=(0,s.lazy)((()=>n.e(7397).then(n.bind(n,57397)))),W=(0,s.lazy)((()=>n.e(3694).then(n.bind(n,33694)))),q=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(4976)]).then(n.bind(n,24976)))),Z=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(8070)]).then(n.bind(n,98070)))),J=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(6660)]).then(n.bind(n,26660)))),Q=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(1533)]).then(n.bind(n,71533)))),ee=(0,s.lazy)((()=>Promise.all([n.e(5960),n.e(4807)]).then(n.bind(n,34807))));let ie="";const ne=(e,i)=>{let{config:n={},isEdit:d,onResize:u,isContextMenu:x=!0}=e;const{t:ne}=(0,t.Bd)(),se=(0,s.useRef)(),re=(0,s.useRef)(),[de,le]=(0,s.useState)(),{subCurrentDomId:te}=(0,a.A)(),ae=(0,r.d4)((e=>e.split.currentDomId)),ce=!(null!==n&&void 0!==n&&n.is_lock);(0,s.useEffect)((()=>{const e=l()(n);le(e),se.current=e}),[n]),(0,s.useEffect)((()=>{if(!ae){const e=document.getElementById(ie);e&&(e.style.border="1px solid #D7E2FF")}}),[ae]);const oe=e=>d?`edit-${e}`:e,ue=()=>ie,xe=e=>{let{id:i}=e;if(!d)return;const n=document.getElementById(ie);n&&(n.style.border="1px solid #D7E2FF");const s=oe(i);document.getElementById(s).style.border="2px solid #87CEEB",te(s),ie=s},je=e=>{var i,r,l;let t=null===e||void 0===e?void 0:e.type;const a=null===e||void 0===e||null===(i=e.type)||void 0===i?void 0:i.split("-");if(a&&0!==(null===a||void 0===a?void 0:a.length)&&(t=a[0]),null!==e&&void 0!==e&&null!==(r=e.type)&&void 0!==r&&r.startsWith("dialog-")&&(t=c.rX.DIALOG),d)return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(L,{layoutConfig:n,item:e,isContextMenu:x,id:oe(e.id),onClick:()=>xe(e)})});switch(t){case c.rX.ATOM_INPUT:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(V,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n,widget_type:t,inputVariableType:o.ps.\u6587\u672c,atomTypeName:"\u6587\u672c\u8f93\u5165\u6846"},e.id)});case c.rX.ATOM_SELECT:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(V,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n,inputVariableType:o.ps.\u9009\u62e9,atomTypeName:"\u9009\u62e9\u5668"},e.id)});case c.rX.ATOM_INPUT_NUMBER:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(V,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n,inputVariableType:o.ps.\u6570\u5b57\u578b,atomTypeName:"\u6570\u5b57\u8f93\u5165\u6846"},e.id)});case c.rX.ATOM_CHECKBOX_SINGLE:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(V,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n,inputVariableType:o.ps.\u5e03\u5c14\u578b,atomTypeName:"\u52fe\u9009\u6846"},e.id)});case c.rX.ATOM_RENDER_PARAMS:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(V,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n,inputVariableType:o.ps.Control,atomTypeName:"\u52a0\u51cf\u63a7\u4ef6"},e.id)});case c.rX.ATOM_DATETIME:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(V,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n,inputVariableType:o.ps.\u65f6\u95f4\u65e5\u671f,atomTypeName:"\u65f6\u95f4\u65e5\u671f\u63a7\u4ef6"},e.id)});case c.rX.ATOM_LABEL:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(V,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n,inputVariableType:o.ps.Label,atomTypeName:"Label"},e.id)});case c.rX.ATOM_RESULT_LABEL:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(Y,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n},e.id)});case c.rX.RESULT_ARRAY_LABEL:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(H,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n},e.id)});case c.rX.RESULT_TABLE:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)($,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n},e.id)});case c.rX.ATOM_BUTTON:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(W,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n},e.id)});case c.rX.CREEP_SAMPLE_PARAMS:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(q,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n},e.id)});case c.rX.CREEP_TEMP_RANGE:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(Z,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n},e.id)});case c.rX.CUSTOM_WAVEFORM:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(J,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n},e.id)});case c.rX["\u7a0b\u63a7\u53c2\u6570"]:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(Q,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n},e.id)});case c.rX.ATOM_TABLE_2_DATA_GATHER:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(ee,{id:oe(e.id),item:e,isEdit:d,layoutConfig:n,widget_type:t},e.id)});case c.rX.PID\u9762\u677f:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(M,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.\u4e8c\u7ef4\u6570\u7ec4\u8868\u683c:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(k,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.\u8fdb\u5ea6\u6761:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(v,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.\u6570\u5b57IO_output:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(z,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.\u7279\u6b8a\u8868\u5934:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(S,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.\u8815\u53d8\u5206\u5c4f\u8868\u5934:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(A,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.\u6570\u5b57IO_input:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(I,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.BLOCK:return(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(K,{id:oe(e.id),layoutConfig:n,item:e,isEdit:d,children:(null===e||void 0===e||null===(l=e.children)||void 0===l?void 0:l.length)>0&&e.children.map((e=>me(e).view))},e.id)});case c.rX.SAMPLE:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(f,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.SAMPLE_TABLE:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(E,{id:oe(e.id),item:e,layoutConfig:n,title:ne(c.h0[c.rX.SAMPLE_TABLE])},e.id)})});case c.rX.SUB_TASK_PARAM:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(w,{id:`${oe(e.id)}`,item:e,layoutConfig:n},e.id)})});case c.rX.SAMPLE_STATISTIC_TABLE:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(b,{id:oe(e.id),isEdit:d,item:e,layoutConfig:n,title:ne(c.h0[c.rX.SAMPLE_STATISTIC_TABLE])},e.id)})});case c.rX.LIGHTNING_LINE_CHART:return(0,h.jsx)(j,{children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(G,{id:`${oe(e.id)}`,item:e,layoutConfig:n},e.id)})},e.id);case c.rX.ARRAY_CURVE:return(0,h.jsx)(j,{children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(B,{id:`${oe(e.id)}`,item:e,layoutConfig:n},e.id)})},e.id);case c.rX.INSTRUCTION_INPUT:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(p,{id:oe(e.id),layoutConfig:n},e.id)})});case c.rX.SHORTCUT:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(C,{id:oe(e.id),item:e,layoutConfig:n})})});case c.rX.HEADER:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(_,{id:oe(e.id),item:e,layoutConfig:n})})});case c.rX.FOOTER:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(T,{id:oe(e.id),layoutConfig:n,item:e})})});case c.rX.PROCESS:return(0,h.jsx)(j,{id:oe(e.id),"data-name":"process",children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(N,{item:e,id:oe(e.id),layoutConfig:n})})});case c.rX.DYNAMIC_FORM:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(F,{id:oe(e.id),item:e,layoutConfig:n})})});case c.rX.ATOM_TABS:case c.rX.TAB_FIXED:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(X,{id:oe(e.id),item:e,layoutConfig:n})})});case c.rX.VIDEO:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(y,{id:oe(e.id),item:e,layoutConfig:n})})});case c.rX.TEST_REPORT:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(U,{id:oe(e.id),item:e,layoutConfig:n})})});case c.rX.DIALOG:{var u;const i=null===(u=JSON.parse(null===e||void 0===e?void 0:e.data_source))||void 0===u?void 0:u.dialog_id;return(0,h.jsx)(j,{id:oe(e.id),style:{overflow:"auto"},children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(O,{id:oe(e.id),dialogId:i,layoutConfig:n,showImg:!0})})})}case c.rX.LOG:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(R,{item:e,id:oe(e.id),isEdit:d,layoutConfig:n},e.id)})});case c.rX.RESTULEREPORT:return(0,h.jsx)(j,{id:oe(e.id),onClick:()=>xe(e),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(D,{id:oe(e.id),layoutConfig:n})})},e.id);case c.rX.EMPTY:case c.rX.CONTENT_SPLIT_EMPTY:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(P,{id:oe(e.id),layoutConfig:n})})});case c.rX.CREEP_CURVE:case c.rX.CREEP_SIGNAL_OFFSET_TABLE:case c.rX.CREEP_TEST_DATA_TABLE:case c.rX.DYNAMIC_UP_LIMIT:case c.rX.DYNAMIC_LOW_LIMIT:case c.rX.DYNAMIC_FUNC_GENERATOR:case c.rX.\u5168\u5c40_\u5feb\u6377\u65b9\u5f0f:case c.rX.\u5168\u5c40_\u5206\u5c4f\u76d1\u63a7:case c.rX.\u5168\u5c40_\u65e5\u5fd7:case c.rX.\u5168\u5c40_\u6570\u636e\u76d1\u63a7\u8868\u683c:case c.rX.DYNAMIC_SAMPLE:case c.rX.GROUP_SAMPLE:case c.rX.DYNAMIC_CURVE_FITTING:case c.rX.GAOZHOU_TABLE:case c.rX.DYNAMIC_CURVE5:case c.rX.DYNAMIC_CURVE1:case c.rX.DYNAMIC_CURVE2:case c.rX.DYNAMIC_CURVE3:case c.rX.DYNAMIC_CURVE6:case c.rX.ATOM_CHECKBOX_GROUP:case c.rX.GAOZHOU_CURVE:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(P,{id:oe(e.id),layoutConfig:n,content:`\u63a7\u4ef6\u7c7b\u578b\u5df2\u5f03\u7528\uff1a${t}`})})});default:return(0,h.jsx)(j,{id:oe(e.id),children:(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(P,{id:oe(e.id),layoutConfig:n})})})}},he=(e,i,n)=>e.map((e=>{const s=e;return s.id===i&&(s.sizes=n),s.children&&s.children.length>0&&(s.children=he(s.children,i,n)),{...s,view:null}})),me=e=>{const i=e;if((null===i||void 0===i?void 0:i.type)===c.rX.BLOCK)return i&&(i.view=je(i)),i;if(null===i||void 0===i||!i.children||null===i||void 0===i||!i.children.length)return i&&(i.view=je(i)),i;const n=()=>{var e;const n={...se.current,view:null,children:he(null===(e=se.current)||void 0===e?void 0:e.children,null===i||void 0===i?void 0:i.id,re.current)};se.current=n,u(l()(n))},r=(e,n,s)=>{re.current=s,i.sizes=s},d=e=>({gridColumn:e}),t=e=>({gridRow:e}),a=(e,n)=>{let{className:s,style:r,gutterProps:d}=n;const l="column"===e?"repeating-linear-gradient(#000, #000 10px, #fff 0px, #fff 15px)":"repeating-linear-gradient(to right, #000, #000 10px, #fff 0px, #fff 15px)";return i.type===c.rX.CONTENT_SPLIT_EMPTY?(0,h.jsx)("div",{className:s,style:{...r,background:l,pointerEvents:ce?"auto":"none"},...ce?d:{}}):(0,h.jsx)("div",{className:s,style:{...r,pointerEvents:ce?"auto":"none"},...ce?d:{}})};return i.direction===c.oM.HOR?i.view=(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(m,{sizes:i.sizes,id:i.id,onDragEnd:ce?n:()=>{},onDrag:ce?r:()=>{},disabled:!ce,render:e=>{let{getGridProps:n,getGutterProps:r}=e;return(0,h.jsx)("div",{className:"grid-content "+(i.children.length>1?" grid-col":""),...n(),children:i.children.map(((e,i)=>(0,h.jsxs)(s.Fragment,{children:[me(e).view,i%2===0&&a("column",{className:"gutter-col",style:d(i+2),gutterProps:r("column",i+1),index:i})]},e.layout_id)))})}})}):i.view=(0,h.jsx)(s.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:(0,h.jsx)(g,{minSize:i.type===c.rX.SHORTCUT?50:25,sizes:i.sizes,id:i.id,onDragEnd:ce?n:()=>{},onDrag:ce?r:()=>{},disabled:!ce,render:e=>{let{getGridProps:n,getGutterProps:r}=e;return(0,h.jsx)("div",{className:"grid-content "+(i.children.length>1?"grid-row":""),...n(),children:i.children.map(((e,i)=>(0,h.jsxs)(s.Fragment,{children:[me(e).view,i%2===0&&a("row",{className:"gutter-row",style:t(i+2),gutterProps:r("row",i+1),index:i})]},e.layout_id)))})}})}),i};(0,s.useImperativeHandle)(i,(()=>({getSelectedId:ue})));const ge=me(de);return null===ge||void 0===ge?void 0:ge.view},se=(0,s.forwardRef)(ne)}}]);
//# sourceMappingURL=4376.1a27320b.chunk.js.map