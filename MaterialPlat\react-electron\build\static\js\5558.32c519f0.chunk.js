"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[5558],{25558:(e,t,n)=>{n.d(t,{A:()=>ie});var r=n(65043),a=n(98139),o=n.n(a),l=n(89379),i=n(64467),u=n(60436),c=n(82284),s=n(5544),d=n(32375),v=n(28678),f=n(2231),g=n(97907),m=n(58168),h=n(80045),b=n(97950);function p(e,t,n){return(e-t)/(n-t)}function C(e,t,n,r){var a=p(t,n,r),o={};switch(e){case"rtl":o.right="".concat(100*a,"%"),o.transform="translateX(50%)";break;case"btt":o.bottom="".concat(100*a,"%"),o.transform="translateY(50%)";break;case"ttb":o.top="".concat(100*a,"%"),o.transform="translateY(-50%)";break;default:o.left="".concat(100*a,"%"),o.transform="translateX(-50%)"}return o}function k(e,t){return Array.isArray(e)?e[t]:e}var A=n(25001);const y=r.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}});var x=r.createContext({}),E=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],S=r.forwardRef((function(e,t){var n,a=e.prefixCls,u=e.value,c=e.valueIndex,s=e.onStartMove,d=e.onDelete,v=e.style,f=e.render,g=e.dragging,b=e.draggingDelete,p=e.onOffsetChange,x=e.onChangeComplete,S=e.onFocus,$=e.onMouseEnter,M=(0,h.A)(e,E),w=r.useContext(y),O=w.min,B=w.max,D=w.direction,P=w.disabled,F=w.keyboard,j=w.range,H=w.tabIndex,R=w.ariaLabelForHandle,z=w.ariaLabelledByForHandle,N=w.ariaRequired,I=w.ariaValueTextFormatterForHandle,L=w.styles,q=w.classNames,T="".concat(a,"-handle"),W=function(e){P||s(e,c)},V=C(D,u,O,B),X={};null!==c&&(X={tabIndex:P?null:k(H,c),role:"slider","aria-valuemin":O,"aria-valuemax":B,"aria-valuenow":u,"aria-disabled":P,"aria-label":k(R,c),"aria-labelledby":k(z,c),"aria-required":k(N,c),"aria-valuetext":null===(n=k(I,c))||void 0===n?void 0:n(u),"aria-orientation":"ltr"===D||"rtl"===D?"horizontal":"vertical",onMouseDown:W,onTouchStart:W,onFocus:function(e){null===S||void 0===S||S(e,c)},onMouseEnter:function(e){$(e,c)},onKeyDown:function(e){if(!P&&F){var t=null;switch(e.which||e.keyCode){case A.A.LEFT:t="ltr"===D||"btt"===D?-1:1;break;case A.A.RIGHT:t="ltr"===D||"btt"===D?1:-1;break;case A.A.UP:t="ttb"!==D?1:-1;break;case A.A.DOWN:t="ttb"!==D?-1:1;break;case A.A.HOME:t="min";break;case A.A.END:t="max";break;case A.A.PAGE_UP:t=2;break;case A.A.PAGE_DOWN:t=-2;break;case A.A.BACKSPACE:case A.A.DELETE:d(c)}null!==t&&(e.preventDefault(),p(t,c))}},onKeyUp:function(e){switch(e.which||e.keyCode){case A.A.LEFT:case A.A.RIGHT:case A.A.UP:case A.A.DOWN:case A.A.HOME:case A.A.END:case A.A.PAGE_UP:case A.A.PAGE_DOWN:null===x||void 0===x||x()}}});var Y=r.createElement("div",(0,m.A)({ref:t,className:o()(T,(0,i.A)((0,i.A)((0,i.A)({},"".concat(T,"-").concat(c+1),null!==c&&j),"".concat(T,"-dragging"),g),"".concat(T,"-dragging-delete"),b),q.handle),style:(0,l.A)((0,l.A)((0,l.A)({},V),v),L.handle)},X,M));return f&&(Y=f(Y,{index:c,prefixCls:a,value:u,dragging:g,draggingDelete:b})),Y}));const $=S;var M=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],w=r.forwardRef((function(e,t){var n=e.prefixCls,a=e.style,o=e.onStartMove,i=e.onOffsetChange,u=e.values,c=e.handleRender,d=e.activeHandleRender,v=e.draggingIndex,f=e.draggingDelete,g=e.onFocus,p=(0,h.A)(e,M),C=r.useRef({}),A=r.useState(!1),y=(0,s.A)(A,2),x=y[0],E=y[1],S=r.useState(-1),w=(0,s.A)(S,2),O=w[0],B=w[1],D=function(e){B(e),E(!0)};r.useImperativeHandle(t,(function(){return{focus:function(e){var t;null===(t=C.current[e])||void 0===t||t.focus()},hideHelp:function(){(0,b.flushSync)((function(){E(!1)}))}}}));var P=(0,l.A)({prefixCls:n,onStartMove:o,onOffsetChange:i,render:c,onFocus:function(e,t){D(t),null===g||void 0===g||g(e)},onMouseEnter:function(e,t){D(t)}},p);return r.createElement(r.Fragment,null,u.map((function(e,t){var n=v===t;return r.createElement($,(0,m.A)({ref:function(e){e?C.current[t]=e:delete C.current[t]},dragging:n,draggingDelete:n&&f,style:k(a,t),key:t,value:e,valueIndex:t},P))})),d&&x&&r.createElement($,(0,m.A)({key:"a11y"},P,{value:u[O],valueIndex:null,dragging:-1!==v,draggingDelete:f,render:d,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))}));const O=w;const B=function(e){var t=e.prefixCls,n=e.style,a=e.children,u=e.value,c=e.onClick,s=r.useContext(y),d=s.min,v=s.max,f=s.direction,g=s.includedStart,m=s.includedEnd,h=s.included,b="".concat(t,"-text"),p=C(f,u,d,v);return r.createElement("span",{className:o()(b,(0,i.A)({},"".concat(b,"-active"),h&&g<=u&&u<=m)),style:(0,l.A)((0,l.A)({},p),n),onMouseDown:function(e){e.stopPropagation()},onClick:function(){c(u)}},a)};const D=function(e){var t=e.prefixCls,n=e.marks,a=e.onClick,o="".concat(t,"-mark");return n.length?r.createElement("div",{className:o},n.map((function(e){var t=e.value,n=e.style,l=e.label;return r.createElement(B,{key:t,prefixCls:o,style:n,value:t,onClick:a},l)}))):null};const P=function(e){var t=e.prefixCls,n=e.value,a=e.style,u=e.activeStyle,c=r.useContext(y),s=c.min,d=c.max,v=c.direction,f=c.included,g=c.includedStart,m=c.includedEnd,h="".concat(t,"-dot"),b=f&&g<=n&&n<=m,p=(0,l.A)((0,l.A)({},C(v,n,s,d)),"function"===typeof a?a(n):a);return b&&(p=(0,l.A)((0,l.A)({},p),"function"===typeof u?u(n):u)),r.createElement("span",{className:o()(h,(0,i.A)({},"".concat(h,"-active"),b)),style:p})};const F=function(e){var t=e.prefixCls,n=e.marks,a=e.dots,o=e.style,l=e.activeStyle,i=r.useContext(y),u=i.min,c=i.max,s=i.step,d=r.useMemo((function(){var e=new Set;if(n.forEach((function(t){e.add(t.value)})),a&&null!==s)for(var t=u;t<=c;)e.add(t),t+=s;return Array.from(e)}),[u,c,s,a,n]);return r.createElement("div",{className:"".concat(t,"-step")},d.map((function(e){return r.createElement(P,{prefixCls:t,key:e,value:e,style:o,activeStyle:l})})))};const j=function(e){var t=e.prefixCls,n=e.style,a=e.start,u=e.end,c=e.index,s=e.onStartMove,d=e.replaceCls,v=r.useContext(y),f=v.direction,g=v.min,m=v.max,h=v.disabled,b=v.range,C=v.classNames,k="".concat(t,"-track"),A=p(a,g,m),x=p(u,g,m),E=function(e){!h&&s&&s(e,-1)},S={};switch(f){case"rtl":S.right="".concat(100*A,"%"),S.width="".concat(100*x-100*A,"%");break;case"btt":S.bottom="".concat(100*A,"%"),S.height="".concat(100*x-100*A,"%");break;case"ttb":S.top="".concat(100*A,"%"),S.height="".concat(100*x-100*A,"%");break;default:S.left="".concat(100*A,"%"),S.width="".concat(100*x-100*A,"%")}var $=d||o()(k,(0,i.A)((0,i.A)({},"".concat(k,"-").concat(c+1),null!==c&&b),"".concat(t,"-track-draggable"),s),C.track);return r.createElement("div",{className:$,style:(0,l.A)((0,l.A)({},S),n),onMouseDown:E,onTouchStart:E})};const H=function(e){var t=e.prefixCls,n=e.style,a=e.values,i=e.startPoint,u=e.onStartMove,c=r.useContext(y),s=c.included,d=c.range,v=c.min,f=c.styles,g=c.classNames,m=r.useMemo((function(){if(!d){if(0===a.length)return[];var e=null!==i&&void 0!==i?i:v,t=a[0];return[{start:Math.min(e,t),end:Math.max(e,t)}]}for(var n=[],r=0;r<a.length-1;r+=1)n.push({start:a[r],end:a[r+1]});return n}),[a,d,i,v]);if(!s)return null;var h=null!==m&&void 0!==m&&m.length&&(g.tracks||f.tracks)?r.createElement(j,{index:null,prefixCls:t,start:m[0].start,end:m[m.length-1].end,replaceCls:o()(g.tracks,"".concat(t,"-tracks")),style:f.tracks}):null;return r.createElement(r.Fragment,null,h,m.map((function(e,a){var o=e.start,i=e.end;return r.createElement(j,{index:a,prefixCls:t,style:(0,l.A)((0,l.A)({},k(n,a)),f.track),start:o,end:i,key:a,onStartMove:u})})))};var R=n(52664);function z(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}const N=function(e,t,n,a,o,l,i,c,v,f,g){var m=r.useState(null),h=(0,s.A)(m,2),b=h[0],p=h[1],C=r.useState(-1),k=(0,s.A)(C,2),A=k[0],y=k[1],E=r.useState(!1),S=(0,s.A)(E,2),$=S[0],M=S[1],w=r.useState(n),O=(0,s.A)(w,2),B=O[0],D=O[1],P=r.useState(n),F=(0,s.A)(P,2),j=F[0],H=F[1],N=r.useRef(null),I=r.useRef(null),L=r.useRef(null),q=r.useContext(x),T=q.onDragStart,W=q.onDragChange;(0,R.A)((function(){-1===A&&D(n)}),[n,A]),r.useEffect((function(){return function(){document.removeEventListener("mousemove",N.current),document.removeEventListener("mouseup",I.current),L.current&&(L.current.removeEventListener("touchmove",N.current),L.current.removeEventListener("touchend",I.current))}}),[]);var V=function(e,t,n){void 0!==t&&p(t),D(e);var r=e;n&&(r=e.filter((function(e,t){return t!==A}))),i(r),W&&W({rawValues:e,deleteIndex:n?A:-1,draggingIndex:A,draggingValue:t})},X=(0,d.A)((function(e,t,n){if(-1===e){var r=j[0],i=j[j.length-1],c=a-r,s=o-i,d=t*(o-a);d=Math.max(d,c),d=Math.min(d,s);var f=l(r+d);d=f-r;var g=j.map((function(e){return e+d}));V(g)}else{var m=(o-a)*t,h=(0,u.A)(B);h[e]=j[e];var b=v(h,m,e,"dist");V(b.values,b.value,n)}})),Y=r.useMemo((function(){var e=(0,u.A)(n).sort((function(e,t){return e-t})),t=(0,u.A)(B).sort((function(e,t){return e-t})),r={};t.forEach((function(e){r[e]=(r[e]||0)+1})),e.forEach((function(e){r[e]=(r[e]||0)-1}));var a=f?1:0;return Object.values(r).reduce((function(e,t){return e+Math.abs(t)}),0)<=a?B:n}),[n,B,f]);return[A,b,$,Y,function(r,a,o){r.stopPropagation();var l=o||n,i=l[a];y(a),p(i),H(l),D(l),M(!1);var u=z(r),s=u.pageX,d=u.pageY,v=!1;T&&T({rawValues:l,draggingIndex:a,draggingValue:i});var m=function(n){n.preventDefault();var r,o,l=z(n),i=l.pageX,u=l.pageY,c=i-s,m=u-d,h=e.current.getBoundingClientRect(),b=h.width,p=h.height;switch(t){case"btt":r=-m/p,o=c;break;case"ttb":r=m/p,o=c;break;case"rtl":r=-c/b,o=m;break;default:r=c/b,o=m}v=!!f&&(Math.abs(o)>130&&g<B.length),M(v),X(a,r,v)},h=function e(t){t.preventDefault(),document.removeEventListener("mouseup",e),document.removeEventListener("mousemove",m),L.current&&(L.current.removeEventListener("touchmove",N.current),L.current.removeEventListener("touchend",I.current)),N.current=null,I.current=null,L.current=null,c(v),y(-1),M(!1)};document.addEventListener("mouseup",h),document.addEventListener("mousemove",m),r.currentTarget.addEventListener("touchend",h),r.currentTarget.addEventListener("touchmove",m),N.current=m,I.current=h,L.current=r.currentTarget}]};var I=r.forwardRef((function(e,t){var n=e.prefixCls,a=void 0===n?"rc-slider":n,m=e.className,h=e.style,b=e.classNames,p=e.styles,C=e.id,k=e.disabled,A=void 0!==k&&k,x=e.keyboard,E=void 0===x||x,S=e.autoFocus,$=e.onFocus,M=e.onBlur,w=e.min,B=void 0===w?0:w,P=e.max,j=void 0===P?100:P,R=e.step,z=void 0===R?1:R,I=e.value,L=e.defaultValue,q=e.range,T=e.count,W=e.onChange,V=e.onBeforeChange,X=e.onAfterChange,Y=e.onChangeComplete,G=e.allowCross,_=void 0===G||G,U=e.pushable,K=void 0!==U&&U,J=e.reverse,Q=e.vertical,Z=e.included,ee=void 0===Z||Z,te=e.startPoint,ne=e.trackStyle,re=e.handleStyle,ae=e.railStyle,oe=e.dotStyle,le=e.activeDotStyle,ie=e.marks,ue=e.dots,ce=e.handleRender,se=e.activeHandleRender,de=e.track,ve=e.tabIndex,fe=void 0===ve?0:ve,ge=e.ariaLabelForHandle,me=e.ariaLabelledByForHandle,he=e.ariaRequired,be=e.ariaValueTextFormatterForHandle,pe=r.useRef(null),Ce=r.useRef(null),ke=r.useMemo((function(){return Q?J?"ttb":"btt":J?"rtl":"ltr"}),[J,Q]),Ae=function(e){return(0,r.useMemo)((function(){if(!0===e||!e)return[!!e,!1,!1,0];var t=e.editable,n=e.draggableTrack;return[!0,t,!t&&n,e.minCount||0,e.maxCount]}),[e])}(q),ye=(0,s.A)(Ae,5),xe=ye[0],Ee=ye[1],Se=ye[2],$e=ye[3],Me=ye[4],we=r.useMemo((function(){return isFinite(B)?B:0}),[B]),Oe=r.useMemo((function(){return isFinite(j)?j:100}),[j]),Be=r.useMemo((function(){return null!==z&&z<=0?1:z}),[z]),De=r.useMemo((function(){return"boolean"===typeof K?!!K&&Be:K>=0&&K}),[K,Be]),Pe=r.useMemo((function(){return Object.keys(ie||{}).map((function(e){var t=ie[e],n={value:Number(e)};return t&&"object"===(0,c.A)(t)&&!r.isValidElement(t)&&("label"in t||"style"in t)?(n.style=t.style,n.label=t.label):n.label=t,n})).filter((function(e){var t=e.label;return t||"number"===typeof t})).sort((function(e,t){return e.value-t.value}))}),[ie]),Fe=function(e,t,n,a,o,l){var i=r.useCallback((function(n){return Math.max(e,Math.min(t,n))}),[e,t]),c=r.useCallback((function(r){if(null!==n){var a=e+Math.round((i(r)-e)/n)*n,o=function(e){return(String(e).split(".")[1]||"").length},l=Math.max(o(n),o(t),o(e)),u=Number(a.toFixed(l));return e<=u&&u<=t?u:null}return null}),[n,e,t,i]),s=r.useCallback((function(r){var o=i(r),l=a.map((function(e){return e.value}));null!==n&&l.push(c(r)),l.push(e,t);var u=l[0],s=t-e;return l.forEach((function(e){var t=Math.abs(o-e);t<=s&&(u=e,s=t)})),u}),[e,t,a,n,i,c]),d=function r(o,l,i){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit";if("number"===typeof l){var d,v=o[i],f=v+l,g=[];a.forEach((function(e){g.push(e.value)})),g.push(e,t),g.push(c(v));var m=l>0?1:-1;"unit"===s?g.push(c(v+m*n)):g.push(c(f)),g=g.filter((function(e){return null!==e})).filter((function(e){return l<0?e<=v:e>=v})),"unit"===s&&(g=g.filter((function(e){return e!==v})));var h="unit"===s?v:f;d=g[0];var b=Math.abs(d-h);if(g.forEach((function(e){var t=Math.abs(e-h);t<b&&(d=e,b=t)})),void 0===d)return l<0?e:t;if("dist"===s)return d;if(Math.abs(l)>1){var p=(0,u.A)(o);return p[i]=d,r(p,l-m,i,s)}return d}return"min"===l?e:"max"===l?t:void 0},v=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e[n],o=d(e,t,n,r);return{value:o,changed:o!==a}},f=function(e){return null===l&&0===e||"number"===typeof l&&e<l};return[s,function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e.map(s),i=a[n],u=d(a,t,n,r);if(a[n]=u,!1===o){var c=l||0;n>0&&a[n-1]!==i&&(a[n]=Math.max(a[n],a[n-1]+c)),n<a.length-1&&a[n+1]!==i&&(a[n]=Math.min(a[n],a[n+1]-c))}else if("number"===typeof l||null===l){for(var g=n+1;g<a.length;g+=1)for(var m=!0;f(a[g]-a[g-1])&&m;){var h=v(a,1,g);a[g]=h.value,m=h.changed}for(var b=n;b>0;b-=1)for(var p=!0;f(a[b]-a[b-1])&&p;){var C=v(a,-1,b-1);a[b-1]=C.value,p=C.changed}for(var k=a.length-1;k>0;k-=1)for(var A=!0;f(a[k]-a[k-1])&&A;){var y=v(a,-1,k-1);a[k-1]=y.value,A=y.changed}for(var x=0;x<a.length-1;x+=1)for(var E=!0;f(a[x+1]-a[x])&&E;){var S=v(a,1,x+1);a[x+1]=S.value,E=S.changed}}return{value:a[n],values:a}}]}(we,Oe,Be,Pe,_,De),je=(0,s.A)(Fe,2),He=je[0],Re=je[1],ze=(0,v.A)(L,{value:I}),Ne=(0,s.A)(ze,2),Ie=Ne[0],Le=Ne[1],qe=r.useMemo((function(){var e=null===Ie||void 0===Ie?[]:Array.isArray(Ie)?Ie:[Ie],t=(0,s.A)(e,1)[0],n=null===Ie?[]:[void 0===t?we:t];if(xe){if(n=(0,u.A)(e),T||void 0===Ie){var r=T>=0?T+1:2;for(n=n.slice(0,r);n.length<r;){var a;n.push(null!==(a=n[n.length-1])&&void 0!==a?a:we)}}n.sort((function(e,t){return e-t}))}return n.forEach((function(e,t){n[t]=He(e)})),n}),[Ie,xe,we,T,He]),Te=function(e){return xe?e:e[0]},We=(0,d.A)((function(e){var t=(0,u.A)(e).sort((function(e,t){return e-t}));W&&!(0,f.A)(t,qe,!0)&&W(Te(t)),Le(t)})),Ve=(0,d.A)((function(e){e&&pe.current.hideHelp();var t=Te(qe);null===X||void 0===X||X(t),(0,g.Ay)(!X,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null===Y||void 0===Y||Y(t)})),Xe=N(Ce,ke,qe,we,Oe,He,We,Ve,Re,Ee,$e),Ye=(0,s.A)(Xe,5),Ge=Ye[0],_e=Ye[1],Ue=Ye[2],Ke=Ye[3],Je=Ye[4],Qe=function(e,t){if(!A){var n=(0,u.A)(qe),r=0,a=0,o=Oe-we;qe.forEach((function(t,n){var l=Math.abs(e-t);l<=o&&(o=l,r=n),t<e&&(a=n)}));var l=r;Ee&&0!==o&&(!Me||qe.length<Me)?(n.splice(a+1,0,e),l=a+1):n[r]=e,xe&&!qe.length&&void 0===T&&n.push(e);var i,c,s=Te(n);if(null===V||void 0===V||V(s),We(n),t)null===(i=document.activeElement)||void 0===i||null===(c=i.blur)||void 0===c||c.call(i),pe.current.focus(l),Je(t,l,n);else null===X||void 0===X||X(s),(0,g.Ay)(!X,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null===Y||void 0===Y||Y(s)}},Ze=r.useState(null),et=(0,s.A)(Ze,2),tt=et[0],nt=et[1];r.useEffect((function(){if(null!==tt){var e=qe.indexOf(tt);e>=0&&pe.current.focus(e)}nt(null)}),[tt]);var rt=r.useMemo((function(){return(!Se||null!==Be)&&Se}),[Se,Be]),at=(0,d.A)((function(e,t){Je(e,t),null===V||void 0===V||V(Te(qe))})),ot=-1!==Ge;r.useEffect((function(){if(!ot){var e=qe.lastIndexOf(_e);pe.current.focus(e)}}),[ot]);var lt=r.useMemo((function(){return(0,u.A)(Ke).sort((function(e,t){return e-t}))}),[Ke]),it=r.useMemo((function(){return xe?[lt[0],lt[lt.length-1]]:[we,lt[0]]}),[lt,xe,we]),ut=(0,s.A)(it,2),ct=ut[0],st=ut[1];r.useImperativeHandle(t,(function(){return{focus:function(){pe.current.focus(0)},blur:function(){var e,t=document.activeElement;null!==(e=Ce.current)&&void 0!==e&&e.contains(t)&&(null===t||void 0===t||t.blur())}}})),r.useEffect((function(){S&&pe.current.focus(0)}),[]);var dt=r.useMemo((function(){return{min:we,max:Oe,direction:ke,disabled:A,keyboard:E,step:Be,included:ee,includedStart:ct,includedEnd:st,range:xe,tabIndex:fe,ariaLabelForHandle:ge,ariaLabelledByForHandle:me,ariaRequired:he,ariaValueTextFormatterForHandle:be,styles:p||{},classNames:b||{}}}),[we,Oe,ke,A,E,Be,ee,ct,st,xe,fe,ge,me,he,be,p,b]);return r.createElement(y.Provider,{value:dt},r.createElement("div",{ref:Ce,className:o()(a,m,(0,i.A)((0,i.A)((0,i.A)((0,i.A)({},"".concat(a,"-disabled"),A),"".concat(a,"-vertical"),Q),"".concat(a,"-horizontal"),!Q),"".concat(a,"-with-marks"),Pe.length)),style:h,onMouseDown:function(e){e.preventDefault();var t,n=Ce.current.getBoundingClientRect(),r=n.width,a=n.height,o=n.left,l=n.top,i=n.bottom,u=n.right,c=e.clientX,s=e.clientY;switch(ke){case"btt":t=(i-s)/a;break;case"ttb":t=(s-l)/a;break;case"rtl":t=(u-c)/r;break;default:t=(c-o)/r}Qe(He(we+t*(Oe-we)),e)},id:C},r.createElement("div",{className:o()("".concat(a,"-rail"),null===b||void 0===b?void 0:b.rail),style:(0,l.A)((0,l.A)({},ae),null===p||void 0===p?void 0:p.rail)}),!1!==de&&r.createElement(H,{prefixCls:a,style:ne,values:qe,startPoint:te,onStartMove:rt?at:void 0}),r.createElement(F,{prefixCls:a,marks:Pe,dots:ue,style:oe,activeStyle:le}),r.createElement(O,{ref:pe,prefixCls:a,style:re,values:Ke,draggingIndex:Ge,draggingDelete:Ue,onStartMove:at,onOffsetChange:function(e,t){if(!A){var n=Re(qe,e,t);null===V||void 0===V||V(Te(qe)),We(n.values),nt(n.value)}},onFocus:$,onBlur:M,handleRender:ce,activeHandleRender:se,onChangeComplete:Ve,onDelete:Ee?function(e){if(!(A||!Ee||qe.length<=$e)){var t=(0,u.A)(qe);t.splice(e,1),null===V||void 0===V||V(Te(t)),We(t);var n=Math.max(0,e-1);pe.current.hideHelp(),pe.current.focus(n)}}:void 0}),r.createElement(D,{prefixCls:a,marks:Pe,onClick:Qe})))}));const L=I;var q=n(45818),T=n(78440);const W=(0,r.createContext)({});var V=n(13758),X=n(96651);const Y=r.forwardRef(((e,t)=>{const{open:n,draggingDelete:a,value:o}=e,l=(0,r.useRef)(null),i=n&&!a,u=(0,r.useRef)(null);function c(){q.A.cancel(u.current),u.current=null}return r.useEffect((()=>(i?u.current=(0,q.A)((()=>{var e;null===(e=l.current)||void 0===e||e.forceAlign(),u.current=null})):c(),c)),[i,e.title,o]),r.createElement(X.A,Object.assign({ref:(0,V.K4)(l,t)},e,{open:i}))}));var G=n(38525),_=n(61857),U=n(94414),K=n(78855),J=n(78446);const Q=e=>{const{componentCls:t,antCls:n,controlSize:r,dotSize:a,marginFull:o,marginPart:l,colorFillContentHover:i,handleColorDisabled:u,calc:c,handleSize:s,handleSizeHover:d,handleActiveColor:v,handleActiveOutlineColor:f,handleLineWidth:g,handleLineWidthHover:m,motionDurationMid:h}=e;return{[t]:Object.assign(Object.assign({},(0,U.dF)(e)),{position:"relative",height:r,margin:`${(0,G.zA)(l)} ${(0,G.zA)(o)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${(0,G.zA)(o)} ${(0,G.zA)(l)}`},[`${t}-rail`]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:`background-color ${h}`},[`${t}-track,${t}-tracks`]:{position:"absolute",transition:`background-color ${h}`},[`${t}-track`]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},[`${t}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${t}-rail`]:{backgroundColor:e.railHoverBg},[`${t}-track`]:{backgroundColor:e.trackHoverBg},[`${t}-dot`]:{borderColor:i},[`${t}-handle::after`]:{boxShadow:`0 0 0 ${(0,G.zA)(g)} ${e.colorPrimaryBorderHover}`},[`${t}-dot-active`]:{borderColor:e.dotActiveBorderColor}},[`${t}-handle`]:{position:"absolute",width:s,height:s,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:c(g).mul(-1).equal(),insetBlockStart:c(g).mul(-1).equal(),width:c(s).add(c(g).mul(2)).equal(),height:c(s).add(c(g).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:s,height:s,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${(0,G.zA)(g)} ${e.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`\n            inset-inline-start ${h},\n            inset-block-start ${h},\n            width ${h},\n            height ${h},\n            box-shadow ${h},\n            outline ${h}\n          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:c(d).sub(s).div(2).add(m).mul(-1).equal(),insetBlockStart:c(d).sub(s).div(2).add(m).mul(-1).equal(),width:c(d).add(c(m).mul(2)).equal(),height:c(d).add(c(m).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${(0,G.zA)(m)} ${v}`,outline:`6px solid ${f}`,width:d,height:d,insetInlineStart:e.calc(s).sub(d).div(2).equal(),insetBlockStart:e.calc(s).sub(d).div(2).equal()}}},[`&-lock ${t}-handle`]:{"&::before, &::after":{transition:"none"}},[`${t}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${t}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${t}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${t}-dot`]:{position:"absolute",width:a,height:a,backgroundColor:e.colorBgElevated,border:`${(0,G.zA)(g)} solid ${e.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-rail`]:{backgroundColor:`${e.railBg} !important`},[`${t}-track`]:{backgroundColor:`${e.trackBgDisabled} !important`},[`\n          ${t}-dot\n        `]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${t}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:s,height:s,boxShadow:`0 0 0 ${(0,G.zA)(g)} ${u}`,insetInlineStart:0,insetBlockStart:0},[`\n          ${t}-mark-text,\n          ${t}-dot\n        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${n}-tooltip-inner`]:{minWidth:"unset"}})}},Z=(e,t)=>{const{componentCls:n,railSize:r,handleSize:a,dotSize:o,marginFull:l,calc:i}=e,u=t?"paddingBlock":"paddingInline",c=t?"width":"height",s=t?"height":"width",d=t?"insetBlockStart":"insetInlineStart",v=t?"top":"insetInlineStart",f=i(r).mul(3).sub(a).div(2).equal(),g=i(a).sub(r).div(2).equal(),m=t?{borderWidth:`${(0,G.zA)(g)} 0`,transform:`translateY(${(0,G.zA)(i(g).mul(-1).equal())})`}:{borderWidth:`0 ${(0,G.zA)(g)}`,transform:`translateX(${(0,G.zA)(e.calc(g).mul(-1).equal())})`};return{[u]:r,[s]:i(r).mul(3).equal(),[`${n}-rail`]:{[c]:"100%",[s]:r},[`${n}-track,${n}-tracks`]:{[s]:r},[`${n}-track-draggable`]:Object.assign({},m),[`${n}-handle`]:{[d]:f},[`${n}-mark`]:{insetInlineStart:0,top:0,[v]:i(r).mul(3).add(t?0:l).equal(),[c]:"100%"},[`${n}-step`]:{insetInlineStart:0,top:0,[v]:r,[c]:"100%",[s]:r},[`${n}-dot`]:{position:"absolute",[d]:i(r).sub(o).div(2).equal()}}},ee=e=>{const{componentCls:t,marginPartWithMark:n}=e;return{[`${t}-horizontal`]:Object.assign(Object.assign({},Z(e,!0)),{[`&${t}-with-marks`]:{marginBottom:n}})}},te=e=>{const{componentCls:t}=e;return{[`${t}-vertical`]:Object.assign(Object.assign({},Z(e,!1)),{height:"100%"})}},ne=(0,K.OF)("Slider",(e=>{const t=(0,J.oX)(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[Q(t),ee(t),te(t)]}),(e=>{const t=e.controlHeightLG/4,n=e.controlHeightSM/2,r=e.lineWidth+1,a=e.lineWidth+1.5,o=e.colorPrimary,l=new _.Y(o).setA(.2).toRgbString();return{controlSize:t,railSize:4,handleSize:t,handleSizeHover:n,dotSize:8,handleLineWidth:r,handleLineWidthHover:a,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:o,handleActiveOutlineColor:l,handleColorDisabled:new _.Y(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}}));function re(){const[e,t]=r.useState(!1),n=r.useRef(null),a=()=>{q.A.cancel(n.current)};return r.useEffect((()=>a),[]),[e,e=>{a(),e?t(e):n.current=(0,q.A)((()=>{t(e)}))}]}var ae=n(35296),oe=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};const le=r.forwardRef(((e,t)=>{const{prefixCls:n,range:a,className:l,rootClassName:i,style:u,disabled:c,tooltipPrefixCls:s,tipFormatter:d,tooltipVisible:v,getTooltipPopupContainer:f,tooltipPlacement:g,tooltip:m={},onChangeComplete:h,classNames:b,styles:p}=e,C=oe(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:k}=e,{getPrefixCls:A,direction:y,className:x,style:E,classNames:S,styles:$,getPopupContainer:M}=(0,ae.TP)("slider"),w=r.useContext(T.A),O=null!==c&&void 0!==c?c:w,{handleRender:B,direction:D}=r.useContext(W),P="rtl"===(D||y),[F,j]=re(),[H,R]=re(),z=Object.assign({},m),{open:N,placement:I,getPopupContainer:V,prefixCls:X,formatter:G}=z,_=null!==N&&void 0!==N?N:v,U=(F||H)&&!1!==_,K=function(e,t){return e||null===e?e:t||null===t?t:e=>"number"===typeof e?e.toString():""}(G,d),[J,Q]=re(),Z=(e,t)=>e||(t?P?"left":"right":"top"),ee=A("slider",n),[te,le,ie]=ne(ee),ue=o()(l,x,S.root,null===b||void 0===b?void 0:b.root,i,{[`${ee}-rtl`]:P,[`${ee}-lock`]:J},le,ie);P&&!C.vertical&&(C.reverse=!C.reverse),r.useEffect((()=>{const e=()=>{(0,q.A)((()=>{R(!1)}),1)};return document.addEventListener("mouseup",e),()=>{document.removeEventListener("mouseup",e)}}),[]);const ce=a&&!_,se=B||((e,t)=>{const{index:n}=t,a=e.props;function o(e,t,n){var r,o,l,i;n&&(null===(o=(r=C)[e])||void 0===o||o.call(r,t)),null===(i=(l=a)[e])||void 0===i||i.call(l,t)}const l=Object.assign(Object.assign({},a),{onMouseEnter:e=>{j(!0),o("onMouseEnter",e)},onMouseLeave:e=>{j(!1),o("onMouseLeave",e)},onMouseDown:e=>{R(!0),Q(!0),o("onMouseDown",e)},onFocus:e=>{var t;R(!0),null===(t=C.onFocus)||void 0===t||t.call(C,e),o("onFocus",e,!0)},onBlur:e=>{var t;R(!1),null===(t=C.onBlur)||void 0===t||t.call(C,e),o("onBlur",e,!0)}}),i=r.cloneElement(e,l),u=(!!_||U)&&null!==K;return ce?i:r.createElement(Y,Object.assign({},z,{prefixCls:A("tooltip",null!==X&&void 0!==X?X:s),title:K?K(t.value):"",value:t.value,open:u,placement:Z(null!==I&&void 0!==I?I:g,k),key:n,classNames:{root:`${ee}-tooltip`},getPopupContainer:V||f||M}),i)}),de=ce?(e,t)=>{const n=r.cloneElement(e,{style:Object.assign(Object.assign({},e.props.style),{visibility:"hidden"})});return r.createElement(Y,Object.assign({},z,{prefixCls:A("tooltip",null!==X&&void 0!==X?X:s),title:K?K(t.value):"",open:null!==K&&U,placement:Z(null!==I&&void 0!==I?I:g,k),key:"tooltip",classNames:{root:`${ee}-tooltip`},getPopupContainer:V||f||M,draggingDelete:t.draggingDelete}),n)}:void 0,ve=Object.assign(Object.assign(Object.assign(Object.assign({},$.root),E),null===p||void 0===p?void 0:p.root),u),fe=Object.assign(Object.assign({},$.tracks),null===p||void 0===p?void 0:p.tracks),ge=o()(S.tracks,null===b||void 0===b?void 0:b.tracks);return te(r.createElement(L,Object.assign({},C,{classNames:Object.assign({handle:o()(S.handle,null===b||void 0===b?void 0:b.handle),rail:o()(S.rail,null===b||void 0===b?void 0:b.rail),track:o()(S.track,null===b||void 0===b?void 0:b.track)},ge?{tracks:ge}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},$.handle),null===p||void 0===p?void 0:p.handle),rail:Object.assign(Object.assign({},$.rail),null===p||void 0===p?void 0:p.rail),track:Object.assign(Object.assign({},$.track),null===p||void 0===p?void 0:p.track)},Object.keys(fe).length?{tracks:fe}:{}),step:C.step,range:a,className:ue,style:ve,disabled:O,ref:t,prefixCls:ee,handleRender:se,activeHandleRender:de,onChangeComplete:e=>{null===h||void 0===h||h(e),Q(!1)}})))}));const ie=le}}]);
//# sourceMappingURL=5558.32c519f0.chunk.js.map