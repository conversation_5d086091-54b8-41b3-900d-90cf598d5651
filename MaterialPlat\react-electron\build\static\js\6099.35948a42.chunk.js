"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[6099],{51554:(e,i,t)=>{t.d(i,{A:()=>g});var a=t(65043),n=t(80077),d=t(16569),l=t(83720),o=t(79806),s=t(74117),r=t(93950),c=t.n(r),u=t(56543),h=t(75440),p=t(29977),b=t(6051),x=t(70579);const v=e=>{let{handleSelected:i,t:t}=e;return[{title:t?t("\u540d\u79f0"):"\u540d\u79f0",dataIndex:"variable_name",key:"variable_name"},{title:t?t("\u6807\u8bc6\u7b26"):"\u6807\u8bc6\u7b26",dataIndex:"code",key:"code"},{title:t?t("\u64cd\u4f5c"):"\u64cd\u4f5c",dataIndex:"code",key:"code",render:(e,t)=>(0,x.jsx)(b.A,{size:"middle",children:(0,x.jsx)("a",{onClick:()=>i(t),children:"\u9009\u62e9"})})}]},m=(e,i)=>{let{handleSelectedVariable:t=e=>console.log(e),isSetProgrammableParameters:r=!1}=e;const b=(0,p.A)(),m=(0,n.d4)((e=>e.template.resultData)),[g,y]=(0,a.useState)(!1),[f,j]=(0,a.useState)(),[w,_]=(0,a.useState)([]),[A,C]=(0,a.useState)([]),{t:D}=(0,s.Bd)(),k=(0,a.useMemo)((()=>b.map((e=>({...e,variable_name:null===e||void 0===e?void 0:e.name})))),[b]),I=(0,a.useMemo)((()=>m.map((e=>({...e,id:e.code})))),[m]);(0,a.useEffect)((()=>{g&&N()}),[g]);const N=()=>{if(f)switch(null===f||void 0===f?void 0:f.variableType){case u.oY.\u8f93\u5165\u53d8\u91cf:{const e=[...k.filter((e=>!(null!==f&&void 0!==f&&f.inputVarType)||e.variable_type===(null===f||void 0===f?void 0:f.inputVarType)))];C(e),_(e);break}case u.oY.\u4fe1\u53f7\u53d8\u91cf:case u.oY.\u7ed3\u679c\u53d8\u91cf:C(I),_(I);break;default:console.log("\u672a\u5904\u7406\u7684\u53d8\u91cf\u7c7b\u578b",null===f||void 0===f?void 0:f.variableType)}};(0,a.useImperativeHandle)(i,(()=>({open:e=>{j(e),y(!0)}})));const B=c()((async e=>{if(e){const i=w.filter((i=>{const t=i.variable_name.toLowerCase(),a=i.code.toLowerCase(),n=e.toLowerCase();return t.includes(n)||a.includes(n)}));C(i)}else C(w)}),200);return(0,x.jsxs)(h.A,{open:g,onCancel:()=>{y(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:[(0,x.jsx)(l.A,{allowClear:!0,onChange:e=>B(e.target.value),placeholder:D("\u540d\u79f0/\u5185\u90e8\u540d"),style:{width:"300px",marginBottom:"10px"}}),(0,x.jsx)(o.A,{rowKey:"code",columns:v({handleSelected:e=>{var i;!r||"Array"===(null===e||void 0===e?void 0:e.variable_type)&&"programmableParameters"===(null===e||void 0===e||null===(i=e.custom_array_tab)||void 0===i?void 0:i.useType)?(t(e,f),y(!1)):d.Ay.error("\u8bf7\u9009\u62e9\u81ea\u5b9a\u4e49\u6570\u7ec4\u7528\u9014\u4e3a\u7a0b\u63a7\u53c2\u6570\u7684\u53d8\u91cf")}}),dataSource:A})]})},g=(0,a.forwardRef)(m)},88312:(e,i,t)=>{t.r(i),t.d(i,{default:()=>he});var a=t(65043),n=t(80077),d=t(16569),l=t(67208),o=t(41086),s=t(36950),r=t(34458),c=t(14463),u=t(12847),h=t(21256),p=t(36990),b=t(72295),x=t(74117),v=t(71424),m=t(81143),g=t(68374),y=t(33613);const f=m.Ay.div`
    height: 100%;
    display: flex;

    flex-direction: ${e=>e.tabDirection||"column"};

    
    .tab {
        display: flex;
        align-items: ${e=>e.align||"center"} !important;
        flex-direction: column;
    }

    .content {
        width: 100%;
        height: 100%;
        padding-top: 4px;
        position: relative;

        .block_mask{
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1000;
        }
    }
    .tab-active{
        position: relative;
        left: 0;
        flex: 1;
        overflow: auto;
    }
    .tab-hidden{
        position: absolute;
        left: -9999px;
        top: 0;
        width: 100%;
        height: 100%;
    }
    
    .navbar-content{
        height: 100%;   
    }
`,j=m.Ay.div`
    display: flex;
    align-items: ${e=>e.align||"center"} !important;
    flex-direction: column;


`,w=m.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220, 1);
        padding: 2px
    }
    .disabled {
        cursor: no-drop;
    }
    .unique-content {
        padding: 2px;
    }
`,_=m.Ay.div`
    height: 100%;

    /* 纵向样式 */
    .tab-ver-layout {
        background: linear-gradient(45deg, rgba(77, 155, 255), rgba(140, 195, 255));
        width: auto;
        height: ${g.OZ.tabHeight};
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-radius: ${(0,g.D0)("24px")};
        border: 1px solid #E7EDFF;
        font-size: ${(0,g.D0)("16px")};
        .tab-item { 
            border-radius: ${(0,g.D0)("24px")};
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-around;
            font-weight: 400;
            color: #FFFFFF;
            padding: 0 ${(0,g.D0)("12px")};
            .tab-item-label{
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                line-height: 1;
            }
            
        }
        .tab-item-selected {
            background: rgba(245,247,255,0.84);
            font-weight: bold;
            color: #2D4586;
        }
    }

    /* 横向样式 */
    .tab-hor-layout {
        display: flex;
        flex-direction: column;
        gap: 10px;

        width: 220px;
        height: 100%;

        padding: 30px 10px;
        overflow-y: auto;
        .tab-item { 
            color: #fff;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: start;
            font-weight: 400;
            padding: 0 ${(0,g.D0)("12px")};
            cursor: pointer;
            width: auto !important;
        }
        .tab-item-selected {
            border: 1px solid #fff;
            border-radius: ${(0,g.D0)("24px")};
            color: #2D4586;
            background: rgba(245,247,255,0.84);
            font-weight: bold;
        }
    }
`;var A=t(70579);const C=e=>{let{labelWidth:i=120,item:t,isSelected:a,onChange:n}=e;const{id:d,label:l,visible_bind_code:o}=t,{t:s}=(0,x.Bd)();return(0,v.A)(o,!0)?(0,A.jsx)("div",{onClick:()=>n&&n(d),style:{width:`${i}px`},className:"tab-item "+(a?"tab-item-selected":""),children:(0,A.jsx)("div",{className:"tab-item-label",children:s(l)})}):(0,A.jsx)(A.Fragment,{})},D=e=>{let{labelWidth:i,tabDirection:t,value:a,item:{items:n=[]},onChange:d}=e;return(0,A.jsx)(_,{tabDirection:t,children:(0,A.jsx)("div",{className:t===y.T.\u7eb5\u5411?"tab-ver-layout":"tab-hor-layout",children:n.map((e=>(0,A.jsx)(C,{labelWidth:i,item:e,isSelected:e.id===a,onChange:d},e.id)))})})};var k=t(80231);const I=e=>{let{domId:i,layoutConfig:t,onOpenEdit:a}=e;const{t:d}=(0,x.Bd)(),l=(0,n.d4)((e=>e.global.userIsAdmin));return(0,A.jsx)(w,{children:(0,A.jsx)(k.A,{domId:i,layoutConfig:t,children:l?(0,A.jsx)("div",{className:"unique-content",onClick:async()=>{a()},children:d("\u7f16\u8f91\u6d3b\u9875\u5939")}):null})})},N=(0,a.memo)(I);var B=t(25055),F=t(75440),E=t(54962),S=t(4554),T=t(67299),L=t(8237);const O=m.Ay.div`
    .layout{
        display: flex;
        justify-content: space-between;
        padding-left: ${(0,g.D0)("10px")};
        padding-right: ${(0,g.D0)("10px")};
        height: 60vh;
        .left-layout {
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .control-layout {
                padding: 10px;
                flex: 1;
                overflow: auto;
                .tab-layout {
                    border-radius: 2px;
                    height: 100%;
                    width: 100%;
                    cursor: pointer;
                    overflow-y: auto;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(0,0,0,0.65);
                    line-height: 22px;
                    .tab-name {
                        padding: 2px;
                    }
                    .tab-name-opt { 
                        background: #F5F7FF;
                        border-radius: 4px;
                        font-size: 14px;
                        font-weight: 400;
                        color: #155BEC;
                        line-height: 22px;
                    }
                }
            }  
        }

        .right-layout {
            display: flex;
            flex-direction: column;
            width: 9vw;
            margin-top: 4vh;
            .button{
                margin-top: ${(0,g.D0)("10px")};
            }
        }
    }
`,W=m.Ay.div`
    display: flex;
    justify-content: space-between;
    align-items: start;

    gap: 8px;

    .headLeft {
        flex: 1;
    }
`,$=m.Ay.div`
    display: flex;
    align-items: center;
    gap: 8px;

    .head-func {
        cursor: pointer;
        width: 2.5vw;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: ${(0,g.D0)("15px")};
        font-weight: 400;
        color: rgba(0,0,0,0.45);
        line-height: 14px;
        >img {
            width: ${(0,g.D0)("25px")};
            height: ${(0,g.D0)("25px")};
            margin-bottom: 1px;
        }
        &:hover {
            background: #F5F7FF;
            border-radius: 4px;
            box-shadow: 2px 0px 7px 0px rgba(3,36,71,0.08);
        }
    }
    .disabled {
        cursor: no-drop;
        pointer-events: none;
    }
`;var z=t(47419),V=t(11645),P=t(83720),M=t(97914),H=t(75433),R=t(75859),U=t(56543);const{Item:Y,useForm:q}=B.A,G=m.Ay.div`
    .left-layout{
        padding-top: 0;
    }
    .head-layout{
        flex-wrap: wrap;
        .tab-config-modal-box-form{
            width: 100% !important;
        }
        .head-right{
            flex: 1;
        }
    }
    .value-valve-box{
        white-space: nowrap;
        overflow: hidden;
        margin-right: 8px;
    }
    
`,X=e=>{let{open:i,setOpen:t,editData:n,onOk:d}=e;const{t:l}=(0,x.Bd)(),[o]=q();(0,a.useEffect)((()=>{n&&o.setFieldsValue(n)}),[n]);const s=()=>{t(!1),o.resetFields()};return(0,A.jsx)(F.A,{open:i,title:l("\u914d\u7f6e\u89c6\u56fe"),maskClosable:!1,destroyOnClose:!0,width:"60vw",onCancel:s,footer:null,children:(0,A.jsx)(G,{className:"tab-config-modal-box",children:(0,A.jsx)(H.A,{value:null===n||void 0===n?void 0:n.layout,expandedLeft:(0,A.jsxs)(B.A,{className:"tab-config-modal-box-form",form:o,children:[(0,A.jsxs)(z.A,{children:[(0,A.jsx)(V.A,{span:"12",children:(0,A.jsx)(Y,{labelCol:{span:5},label:l("\u6d3b\u9875\u5939\u540d\u79f0"),name:"label",rules:[{required:!0}],children:(0,A.jsx)(P.A,{size:"small"})})}),(0,A.jsx)(V.A,{span:"12",style:{overflow:"hidden"},children:(0,A.jsx)(Y,{label:l("\u53ef\u89c1\u6027"),name:"visible_bind_code",labelCol:{style:{width:"70px"}},children:(0,A.jsx)(R.A,{inputVariableType:U.ps.\u5e03\u5c14\u578b})})})]}),(0,A.jsxs)(z.A,{children:[(0,A.jsx)(V.A,{span:"12",children:(0,A.jsx)(Y,{label:l("\u8499\u677f\u900f\u660e\u5ea6"),name:"mask_opacity",initialValue:.3,labelCol:{span:5},children:(0,A.jsx)(M.A,{style:{width:"100%"},size:"small",max:.99,min:.01})})}),(0,A.jsx)(V.A,{span:"12",style:{overflow:"hidden"},children:(0,A.jsx)(Y,{label:l("\u53ef\u7f16\u8f91"),name:"disabled_bind_code",labelCol:{style:{width:"70px"}},children:(0,A.jsx)(R.A,{inputVariableType:U.ps.\u5e03\u5c14\u578b})})})]})]}),onOk:async e=>{try{const i=await o.validateFields();d({...n,...i,layout:e}),t(!1),o.resetFields()}catch(i){console.log("err",i)}},onCancel:s})})})};var J=t(18650);const K=e=>{let{handleBtn:i}=e;const{t:t}=(0,x.Bd)();return(0,A.jsxs)($,{children:[(0,A.jsxs)("div",{className:"head-func",onClick:()=>i(L.y8.UP),children:[(0,A.jsx)("img",{src:J.ne,alt:""}),(0,A.jsx)("div",{children:t("\u4e0a\u79fb")})]}),(0,A.jsxs)("div",{className:"head-func",onClick:()=>i(L.y8.DOWN),children:[(0,A.jsx)("img",{src:J.gW,alt:""}),(0,A.jsx)("div",{children:t("\u4e0b\u79fb")})]}),(0,A.jsxs)("div",{className:"head-func",onClick:()=>i(L.y8.ADD),children:[(0,A.jsx)("img",{src:J.X7,alt:""}),(0,A.jsx)("div",{children:t("\u65b0\u5efa")})]}),(0,A.jsxs)("div",{className:"head-func",onClick:()=>i(L.y8.DEL),children:[(0,A.jsx)("img",{src:J.Dk,alt:""}),(0,A.jsx)("div",{children:t("\u5220\u9664")})]}),(0,A.jsxs)("div",{className:"head-func",onClick:()=>i(L.y8.EDIT),children:[(0,A.jsx)("img",{src:J.YG,alt:""}),(0,A.jsx)("div",{children:t("\u7f16\u8f91")})]})]})},Z=e=>{let{expandedLeft:i,handleBtn:t}=e;return(0,A.jsxs)(W,{children:[(0,A.jsx)("div",{className:"headLeft",children:i}),(0,A.jsx)(K,{handleBtn:t})]})};var Q=t(96603);const{Item:ee}=B.A,ie=e=>{let{form:i}=e;const t=B.A.useWatch("tabDirection",i),{t:a}=(0,x.Bd)();return(0,A.jsx)(B.A,{form:i,labelCol:{span:8},wrapperCol:{span:16},children:(0,A.jsxs)(V.A,{span:24,children:[(0,A.jsxs)(z.A,{children:[(0,A.jsx)(V.A,{span:12,children:(0,A.jsx)(ee,{label:a("\u5e03\u5c40"),name:"tabDirection",children:(0,A.jsx)(Q.Ay.Group,{children:Object.entries(y.T).map(((e,i)=>{let[t,n]=e;return(0,A.jsx)(Q.Ay.Button,{value:n,children:a(t)},i)}))})})}),(0,A.jsx)(V.A,{span:12,children:(0,A.jsx)(ee,{label:a("tab\u4f4d\u7f6e"),name:"tabDisplay",children:(0,A.jsx)(Q.Ay.Group,{disabled:t===y.T["\u6a2a\u5411"],children:Object.entries(y.P).map(((e,i)=>{let[t,n]=e;return(0,A.jsx)(Q.Ay.Button,{value:n,children:a(t)},i)}))})})})]}),(0,A.jsxs)(z.A,{children:[(0,A.jsx)(V.A,{span:24,children:(0,A.jsx)(ee,{label:a("tab\u4e0b\u6807"),name:"tabIndexBindCode",labelCol:{span:4},children:(0,A.jsx)(R.A,{inputVariableType:U.ps.\u6570\u5b57\u578b})})}),(0,A.jsx)(V.A,{span:12,children:(0,A.jsx)(ee,{label:a("\u6807\u9898\u5bbd\u5ea6"),name:"labelWidth",labelCol:{style:{width:"80px"}},initialValue:120,children:(0,A.jsx)(M.A,{style:{width:"100%"},addonAfter:"px"})})})]})]})})},te=m.Ay.div`
    flex: 1;
    overflow: hidden;
    >div{
        height: 100%;
    }
`,ae=e=>{let{open:i,setOpen:t,binderIds:n,tabDirection:c,tabIndexBindCode:u,tabDisplay:h,labelWidth:p,pageId:b,onOk:v}=e;const{t:m}=(0,x.Bd)(),[g]=B.A.useForm(),[y,f]=(0,a.useState)([]),[j,w]=(0,a.useState)(),{subCurrentDomId:_}=(0,T.A)(),[C,D]=(0,a.useState)(!1),[k,I]=(0,a.useState)(),N=(0,a.useMemo)((()=>y.find((e=>e.id===j))),[j,y]);(0,a.useEffect)((()=>{i&&n&&W(n)}),[n,i]),(0,a.useEffect)((()=>{i&&(null===g||void 0===g||g.setFieldsValue({tabDirection:c,tabIndexBindCode:u,tabDisplay:h,labelWidth:p}))}),[i,h,p,c]);const W=async e=>{const i=await(0,l.PXE)({binder_ids:e});if(i){const e=null===i||void 0===i?void 0:i.map((e=>(0,o.CL)(e))).sort(((e,i)=>e.order_num-i.order_num));var t;if(f(e),!j&&0!==(null===e||void 0===e?void 0:e.length))$(null===(t=e[0])||void 0===t?void 0:t.id)}},$=e=>{w(e)},z=async e=>{switch(e){case L.y8.CONFIRM:V();break;case L.y8.CANCEL:t(!1);break;case L.y8.ADD:P();break;case L.y8.EDIT:M();break;case L.y8.DEL:H();break;case L.y8.UP:R();break;case L.y8.DOWN:U()}},V=async()=>{const e=await(null===g||void 0===g?void 0:g.validateFields());await v({newBinderList:y,...e}),t(!1)},P=()=>{var e;const i=(null===y||void 0===y?void 0:y.length)+1,t=y.at(-1).order_num+1,a={key:crypto.randomUUID(),label:"",id:crypto.randomUUID(),page_id:b,children:"",delete_flag:L.PA.NO_DEL,order_num:t>i?t:i,layout:(0,L.Fw)((0,s.vx)(3),b),user_id:null===(e=(0,r.ug)())||void 0===e?void 0:e.id};I(a),D(!0)},M=()=>{I(N),D(!0)},H=()=>{if(1===y.length)return void d.Ay.error("\u9700\u8981\u4fdd\u7559\u4e00\u9875");const e=y.filter((e=>e.id!==j));f(e),w(e[0].id)},R=()=>{const e=y.findIndex((e=>e.id===j));0!==e&&f(y.map(((i,t)=>(t===e-1&&(i.order_num+=1),t===e&&(i.order_num-=1),i))).sort(((e,i)=>e.order_num-i.order_num)))},U=()=>{const e=y.findIndex((e=>e.id===j));e!==y.length-1&&f(y.map(((i,t)=>(t===e+1&&(i.order_num-=1),t===e&&(i.order_num+=1),i))).sort(((e,i)=>e.order_num-i.order_num)))};return(0,A.jsxs)(F.A,{open:i,title:m("\u7f16\u8f91tab"),maskClosable:!1,width:"60vw",onCancel:()=>z(L.y8.CANCEL),footer:null,children:[(0,A.jsx)(O,{children:(0,A.jsxs)("div",{className:"layout",children:[(0,A.jsxs)("div",{className:"left-layout",children:[(0,A.jsx)(Z,{expandedLeft:(0,A.jsx)(ie,{form:g}),handleBtn:z}),(0,A.jsx)(te,{children:(0,A.jsx)(E.A,{title:m("\u5e03\u5c40"),children:(0,A.jsx)("div",{className:"control-layout",children:(0,A.jsx)("div",{className:"tab-layout",children:null===y||void 0===y?void 0:y.map((e=>e.delete_flag===L.PA.NO_DEL?(0,A.jsx)("div",{className:[j===e.id?"tab-name tab-name-opt":"tab-name"],onClick:()=>$(e.id),children:m(e.label)},e.key):""))})})})})]}),(0,A.jsxs)("div",{className:"right-layout",children:[(0,A.jsx)(S.A,{block:!0,className:"button",onClick:()=>z(L.y8.CONFIRM),children:m("\u786e\u8ba4")}),(0,A.jsx)(S.A,{block:!0,className:"button",onClick:()=>z(L.y8.CANCEL),children:m("\u53d6\u6d88")})]})]})}),C?(0,A.jsx)(X,{open:C,setOpen:e=>{D(e),_(null)},editData:k,onOk:e=>{y.some((i=>i.id===e.id))?f(y.map((i=>i.id===e.id?e:i))):f([...y,e]),_(null)}}):null]})};var ne=t(754),de=t(63612),le=t(94376);const oe=e=>{let{tab:{id:i,layout:t,disabled_bind_code:a,mask_opacity:n},onInit:d}=e;const s=(0,v.A)(a,!0),r=async e=>{try{await(0,l._Yu)((0,o.gT)(e,i))&&d()}catch(t){throw console.error(t),t}};return(0,A.jsxs)("div",{className:"content",children:[!s&&(0,A.jsx)("div",{className:"block_mask",style:{background:`rgba(0,0,0,${null!==n&&void 0!==n?n:.3})`}}),t&&(0,A.jsx)(le.A,{config:t,onResize:e=>{r(e)}})]})},se=(0,a.memo)(oe);window.setCacheMode=e=>{ne.A.dispatch({type:de.Vo,param:e})};const re=(0,a.memo)((e=>{let{visible:i,render:t}=e;const n=(0,a.useRef)(!1);return n.current?t():i?(n.current=!0,t()):(0,A.jsx)(A.Fragment,{})})),ce=e=>{let{tabList:i,activeTabId:t,onInit:a}=e;switch((0,n.d4)((e=>e.global.tabFixedCacheMode))){case 1:return null===i||void 0===i?void 0:i.map((e=>(0,A.jsx)(re,{visible:(null===e||void 0===e?void 0:e.id)===t,render:()=>(0,A.jsx)("div",{className:(null===e||void 0===e?void 0:e.id)===t?"content tab-active":"content tab-hidden",children:(0,A.jsx)(se,{tab:e,onInit:a})},null===e||void 0===e?void 0:e.id)},null===e||void 0===e?void 0:e.id)));case 2:return null===i||void 0===i?void 0:i.map((e=>(0,A.jsx)("div",{className:(null===e||void 0===e?void 0:e.id)===t?"content tab-active":"content tab-hidden",children:(0,A.jsx)(se,{tab:e,onInit:a})},null===e||void 0===e?void 0:e.id)));default:{const e=null===i||void 0===i?void 0:i.find((e=>e.id===t));return e?(0,A.jsx)("div",{className:(null===e||void 0===e?void 0:e.id)===t?"content tab-active":"content tab-hidden",children:(0,A.jsx)(se,{tab:e,onInit:a})},null===e||void 0===e?void 0:e.id):(0,A.jsx)(A.Fragment,{})}}},ue=(0,a.memo)(ce),he=e=>{var i,t;let{item:x,id:v,layoutConfig:m}=e;const g=(0,n.wA)(),{changedBinderId:w,binderIdMapTab:_}=(0,n.d4)((e=>e.split)),C=(0,n.d4)((e=>e.template.widgetData)),{updateInputVariableValue:k}=(0,b.A)(),{initPageData:I}=(0,u.A)(),{editWidget:B}=(0,h.A)(),[F,E]=(0,a.useState)([]),[S,T]=(0,a.useState)(null===(i=(0,r.l4)(x.widget_id))||void 0===i||null===(t=i.data)||void 0===t?void 0:t.tag_id),[L,O]=(0,a.useState)(!1),[W,$]=(0,a.useState)([]),[z,V]=(0,a.useState)(y.T.\u7eb5\u5411),[P,M]=(0,a.useState)(y.P.\u4e2d),[H,R]=(0,a.useState)(120),[U,Y]=(0,a.useState)(),q=(0,a.useRef)();(0,p.A)({code:U,callback:e=>{F.length<=0||(Number.isInteger(e)?F.length>e?T(F[e].id):d.Ay.error("\u6d3b\u9875\u5939\u4e0b\u6807\u53d8\u91cf\u6ca1\u6709\u5bf9\u5e94tab"):d.Ay.error("\u6d3b\u9875\u5939\u4e0b\u6807\u53d8\u91cf\u503c\u4e0d\u4e3a\u6574\u6570"))}});const G=(0,a.useMemo)((()=>(0,s.Rm)(C,"widget_id",null===x||void 0===x?void 0:x.widget_id)),[x,C]);(0,a.useEffect)((()=>{Y(),X()}),[JSON.stringify(null===G||void 0===G?void 0:G.data_source)]),(0,a.useEffect)((()=>{0!==W.length&&(0,r.HN)()&&K(W)}),[W,(0,r.HN)(),L]),(0,a.useEffect)((()=>{0===(null===W||void 0===W?void 0:W.length)||S&&!W.every((e=>e!==S))||J(W[0])}),[W,S]),(0,a.useEffect)((()=>{w&&W.some((e=>e===w))&&(console.log("changedBinderId","\u6211\u66f4\u65b0\u4e86",w,W),K(W),g({type:c.EH,param:null}),null!==m&&void 0!==m&&m.binder_id||(console.log("\u66f4\u65b0\u9875\u9762"),I()))}),[w]);const X=async()=>{let e=JSON.parse(null===x||void 0===x?void 0:x.data_source);var i,t;!G||null!==G&&void 0!==G&&G.data_source?(e=null===G||void 0===G?void 0:G.data_source,e&&(e.binder_ids&&$(e.binder_ids),e.display&&M(e.display),e.labelWidth&&R(e.labelWidth),e.direction&&V(e.direction),null!==(i=e)&&void 0!==i&&i.tabIndexBindCode&&Y(null===(t=e)||void 0===t?void 0:t.tabIndexBindCode))):await B({...G,data_source:e})},J=(0,a.useCallback)((e=>{e&&(T(e),(0,r.ZY)({widget_id:x.widget_id,data:{tag_id:e}}),U&&k({code:U,value:F.findIndex((i=>i.id===e))}))}),[x.widget_id,U,k,F]),K=(0,a.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:W;!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(null!==e&&void 0!==e&&e.widget_id){const t={..._,[null===e||void 0===e?void 0:e.widget_id]:{binder_id:e.binder_id,children:e.children,data_source:e.data_source,direction:e.direction,id:e.id,layout_id:e.layout_id,name:e.name,page_id:e.page_id,parent_id:e.parent_id,sizes:e.sizes,type:e.type,widget_data_source:e.widget_data_source,widget_id:e.widget_id,binderIds:i}};g({type:c.dE,param:{...t}})}}(x,e);const i=await(0,l.PXE)({binder_ids:e});if(i){const e=null===i||void 0===i?void 0:i.map((e=>(0,o.CL)(e))).sort(((e,i)=>e.order_num-i.order_num));E(e)}}),[W,J]);(0,a.useEffect)((()=>{q.current=K}),[K]);const Z=async e=>{let{newBinderIds:i,newTabDisplay:t,newTabDirection:a,newTabIndexBindCode:n,newLabelWidth:d}=e;await B({...G,data_source:{binder_ids:i,display:t,direction:a,tabIndexBindCode:n,labelWidth:d}}),g({type:c.EH,param:x.id}),null!==m&&void 0!==m&&m.binder_id&&(console.log("changedBinderId","\u4f60\u8be5\u66f4\u65b0\u4e86",null===m||void 0===m?void 0:m.binder_id),g({type:c.EH,param:m.binder_id}))},Q=(0,a.useCallback)((()=>{O(!0)}),[]);return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsxs)(f,{tabDirection:z,children:[(0,A.jsx)(j,{align:P,children:(0,A.jsx)(D,{labelWidth:H,tabDirection:z,value:S,item:{items:F},onChange:J})}),(0,A.jsx)(ue,{tabList:F,activeTabId:S,onInit:(0,a.useCallback)((()=>{var e;return null===(e=q.current)||void 0===e?void 0:e.call(q)}),[])})]}),(0,A.jsx)(N,{domId:v,layoutConfig:m,onOpenEdit:Q}),L&&(0,A.jsx)(ae,{open:L,setOpen:O,binderIds:W,tabDirection:z,tabIndexBindCode:U,tabDisplay:P,labelWidth:H,pageId:x.page_id,onOk:async e=>{let{newBinderList:i,tabDisplay:t,tabDirection:a,tabIndexBindCode:n,labelWidth:d}=e;try{await(0,l.Kv3)({binders:i.map((e=>(0,o.wS)(e,e.id)))})&&Z({newBinderIds:i.map((e=>e.id)),newTabDisplay:t,newTabDirection:a,newTabIndexBindCode:n,newLabelWidth:d})}catch(s){throw console.error(s),s}return!1}})]})}}}]);
//# sourceMappingURL=6099.35948a42.chunk.js.map