{"version": 3, "file": "static/js/6143.4a2a105b.chunk.js", "mappings": "6SAaA,MA+FA,EA/FuBA,IAGhB,IAHiB,GACpBC,EAAE,KAAEC,EAAI,aAAEC,EAAY,aACtBC,GAAe,GAClBJ,EAEG,MAAMK,EAAiC,OAAJH,QAAI,IAAJA,GAAAA,EAAMI,mBAAqBC,KAAKC,MAAU,OAAJN,QAAI,IAAJA,OAAI,EAAJA,EAAMI,oBAAsB,KAC/FG,GAAaC,EAAAA,EAAAA,KAAYC,IAAK,IAAAC,EAAA,OAAqB,QAArBA,EAAID,EAAME,mBAAW,IAAAD,OAAA,EAAjBA,EAAmBE,eAAeC,MAAKC,GAAKA,EAAEf,KAAOI,GAA2B,IAGlHY,GAAaP,EAAAA,EAAAA,KAAYC,GAASA,EAAMO,SAASD,aACjDE,GAASC,EAAAA,EAAAA,UAAQ,KAAMC,EAAAA,EAAAA,IAASJ,EAAY,YAAiB,OAAJf,QAAI,IAAJA,OAAI,EAAJA,EAAMoB,YAAY,CAACpB,EAAMe,KAClF,WAAEM,IAAeC,EAAAA,EAAAA,MAEhBC,EAAQC,IAAaC,EAAAA,EAAAA,aAE5BC,EAAAA,EAAAA,YAAU,KAAO,IAADC,EAEZ,GAAU,OAANV,QAAM,IAANA,GAAAA,EAAQW,aAAqB,OAANX,QAAM,IAANA,GAAmB,QAAbU,EAANV,EAAQW,mBAAW,IAAAD,GAAnBA,EAAqBE,WAEvCC,IAAQP,EAAc,OAANN,QAAM,IAANA,OAAM,EAANA,EAAQW,cACzBJ,EAAgB,OAANP,QAAM,IAANA,OAAM,EAANA,EAAQW,iBAH1B,CAUA,GAAIrB,EAAY,CACZ,MAAMwB,EC/BaC,KAAe,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EACzC,IAAKL,EACD,MAAM,IAAIM,MAAM,yBAIpB,MAAMC,EAAuBC,GACJ,mBAAVA,EAA4BA,EACzB,IAAVA,GAAyB,MAAVA,GACL,IAAVA,GAAyB,MAAVA,GACZC,QAAQD,GAIbE,EAAqBC,IACP,CACZC,OAAQ,SACRC,IAAK,MACLC,IAAK,MACL,aAAc,aACd,aAAc,cAEHH,IAAY,OAIzBI,EAAgBC,IACF,CACZC,MAAO,QACPC,OAAQ,SACRC,OAAQ,UAEGH,IAAa,SA6C1BI,EAAgBA,CAACC,EAAaC,EAAUC,EAAWC,KACrD,MAAMC,GAASC,EAAAA,EAAAA,GAAgB,CAC3BF,eAGJ,GAAgB,OAAXH,QAAW,IAAXA,IAAAA,EAAc,IAAe,OAARC,QAAQ,IAARA,IAAAA,EAAW,GACjC,OAAOG,EAGX,MAAME,EAAQN,EAAY,GAEpBO,EAAM,CAAC,EAyBb,OAvBAC,OAAOC,KAAKL,GAAQM,SAASC,IAAS,IAADC,EAAAC,EACjCN,EAAII,GAAO,CACPG,KAAMV,EAAOO,GAAKG,KAClBC,MAAO,CACH,CACIC,OAAQ9B,EAAoBoB,EAAMW,WAClCC,SAAUxB,EAAaY,EAAMa,YAC7BC,cAAed,EAAMe,gBAAkB,EACvCC,OAAQpC,EAAoBoB,EAAMiB,WAClCC,SAAUlB,EAAMmB,YAAc,IAC9BC,SAAUxC,EAAoBoB,EAAMqB,WACpCC,MAAOtB,EAAMuB,YAAc,UAC3BC,KAAM7B,EAAS,GACf8B,QAAS7C,EAAoBP,EAAUqD,mBACvCC,OAAwB,QAAjBrB,EAAAjC,EAAUuD,eAAO,IAAAtB,OAAA,EAAjBA,EAAmBlE,KAAM,GAChCA,GAAI,8BAAUiE,KAAOV,EAAS,KAC9Ba,KAAM,8BAAgB,OAANV,QAAM,IAANA,GAAa,QAAPS,EAANT,EAASO,UAAI,IAAAE,OAAP,EAANA,EAAeC,QAAQnC,EAAUwD,SAAW,YAC5DjC,cAGX,IAGEK,CAAG,EAGRJ,EAAmC,iBAAtBxB,EAAUyD,QAAmBC,EAAAA,GAAYC,yBAAOD,EAAAA,GAAYE,yBAEzErC,EAlFoBsC,KAAgB,IAADC,EAAAC,EACrC,OAAe,OAAVF,QAAU,IAAVA,GAAqB,QAAXC,EAAVD,EAAYG,iBAAS,IAAAF,GAAK,QAALC,EAArBD,EAAwB,UAAE,IAAAC,GAA1BA,EAA4BE,MAE1BJ,EAAWG,UAAU,GAAGC,MAAMC,KAAIC,IAAG,CACxCpG,IAAIqG,EAAAA,EAAAA,KACJnB,MAAOkB,EAAIlB,OAAS,UACpBoB,iBAAkBF,EAAIpG,GACtBuG,OAAQ/D,EAAoB4D,EAAII,WAChCC,MAAOjE,EAAoB4D,EAAIM,UAC/BC,QAASnE,EAAoB4D,EAAIQ,YACjCC,WAAYT,EAAIU,aAAe,GAC/BxC,OAAQ9B,EAAoB4D,EAAIW,WAChCC,SAAUxE,EAAoB4D,EAAIa,aAClCC,MAAO1E,EAAoB4D,EAAIe,UAC/BC,OAAQ5E,EAAoB4D,EAAIiB,eAbW,EAc5C,EAmEWC,CAAiBrF,EAAUsF,aACvCC,EA/Dc,QADMC,EAgESxF,EAAUyF,oBA/DzB,IAAXD,GAAAA,EAAaE,WAEXF,EAAYE,WAAWxB,KAAIyB,IAAK,CACnC5H,GAAI4H,EAAMC,SACV3C,MAAO0C,EAAM1C,OAAS,OACtBqB,OAAQ/D,EAAoBoF,EAAMpB,WAClCC,MAAOjE,EAAoBoF,EAAMlB,UACjCoB,MAAOF,EAAMxD,MAAQ,GACrB2D,WAAW,EACXpB,QAASnE,EAAoBoF,EAAMhB,YACnCC,WAAYe,EAAMd,aAAe,GACjCxC,QAAQ,EACR0C,SAAUxE,EAAoBoF,EAAMX,aACpCC,MAAO1E,EAAoBoF,EAAMT,UACjCC,OAAQ5E,EAAoBoF,EAAMP,WAClCW,WAAY,EACZC,QAASL,EAAM1B,OAAS,OAhBS,GADfuB,MAiE1B,MAAM/D,EAASL,EAAcpB,EAAUiG,aAAcjG,EAAUkG,UAAW3E,EAAWC,GAC/E2E,EAAU/E,EAAcpB,EAAUiG,aAAcjG,EAAUoG,WAAY7E,EAAWC,GAuHvF,MApHkB,CACd6E,KAAM,CACF/B,OAAQ/D,EAAoBP,EAAUsG,0BACtCnE,KAAMnC,EAAUuG,YAAc,eAC9B/E,aACAgF,gBAAiBxG,EAAUyG,aAAezG,EAAU0G,YAAc,GAClEC,WAAY,IACZC,eAAgB,IAEpB/G,WAAY,CACRgH,MAAO,CACHC,UAAU,EACVC,MAAO,EACP5E,KAAM,6BACN6E,QAAShH,EAAUiH,WAAa,OAChCC,OAAwB,QAAjBjH,EAAAD,EAAUmH,eAAO,IAAAlH,OAAA,EAAjBA,EAAmBlC,KAAM,GAChCqJ,QAASpH,EAAUkG,WAAa,GAChCzE,UAEJ4F,OAAQ,CACJP,UAA8B,QAApB5G,EAAAF,EAAUoG,kBAAU,IAAAlG,OAAA,EAApBA,EAAsBoH,QAAS,EACzCP,MAAO,EACP5E,KAAM,6BACN6E,QAAShH,EAAUiH,WAAa,OAChCC,OAAwB,QAAjB/G,EAAAH,EAAUmH,eAAO,IAAAhH,OAAA,EAAjBA,EAAmBpC,KAAM,GAChCqJ,QAASpH,EAAUoG,YAAc,GACjC3E,OAAQ0E,IAGhBoB,MAAO,CACHpF,KAAMnC,EAAUwH,SAAW,UAC3BC,MAAuB,QAAjBrH,EAAAJ,EAAUmH,eAAO,IAAA/G,OAAA,EAAjBA,EAAmBrC,KAAM,GAC/B2J,eAAgBhH,EAAkBV,EAAU2H,cAC5CC,SAAU5H,EAAU6H,aAAe,EACnCC,QAAS9H,EAAU+H,YAAc,GACjCC,UAAWhI,EAAUiI,cAAgB,GACrCC,MAAO3H,EAAoBP,EAAUmI,OACrCC,KAAMrH,EAAaf,EAAUqI,mBAC7BC,UAAWtI,EAAUuI,aAAe,EACpCtF,MAAOjD,EAAUwI,SAAW,UAC5BC,OAAQlI,EAAoBP,EAAU0I,aACtCC,SAAU5H,EAAaf,EAAUqI,mBACjCO,cAAe5I,EAAU6I,uBAAyB,EAClDC,UAAW9I,EAAU+I,mBAAqB,UAC1CC,WAAYzI,EAAoBP,EAAUiJ,aAC1CC,aAAcnI,EAAaf,EAAUmJ,mBACrCC,kBAAmBpJ,EAAUqJ,uBAAyB,EACtDC,cAAetJ,EAAUuJ,mBAAqB,WAElD1C,MAAO,CACH1E,KAAMnC,EAAUwD,SAAW,UAC3BkE,eAAgBhH,EAAkBV,EAAUwJ,cAC5C5B,SAAU5H,EAAUyJ,aAAe,EACnC3B,QAAS9H,EAAU0J,YAAc,GACjC1B,UAAWhI,EAAU2J,cAAgB,GACrCzB,MAAO3H,EAAoBP,EAAU4J,OACrCxB,KAAM,QACNE,UAAWtI,EAAU6J,aAAe,EACpC5G,MAAOjD,EAAU8J,SAAW,UAC5BrB,OAAQlI,EAAoBP,EAAU+J,aACtCpB,SAAU5H,EAAaf,EAAUgK,mBACjCpB,cAAe5I,EAAUiK,uBAAyB,EAClDnB,UAAW9I,EAAUkK,mBAAqB,UAC1ClB,WAAYzI,EAAoBP,EAAUmK,aAC1CjB,aAAcnI,EAAaf,EAAUoK,mBACrChB,kBAAmBpJ,EAAUqK,uBAAyB,EACtDf,cAAetJ,EAAUsK,mBAAqB,WAElDjD,OAAQ,CACJlF,KAAMnC,EAAUuK,UAAY,WAC5B7C,eAAgBhH,EAAkBV,EAAUwK,eAC5C5C,SAAU5H,EAAUyK,cAAgB,EACpC3C,QAAS9H,EAAU0K,aAAe,GAClC1C,UAAWhI,EAAU2K,eAAiB,GACtCzC,MAAO3H,EAAoBP,EAAU4K,QACrCxC,KAAM,QACNE,UAAWtI,EAAU6K,cAAgB,EACrC5H,MAAOjD,EAAU8K,UAAY,UAC7BrC,OAAQlI,EAAoBP,EAAU+K,cACtCpC,SAAU5H,EAAaf,EAAUgL,oBACjCpC,cAAe5I,EAAUiL,wBAA0B,EACnDnC,UAAW9I,EAAUkL,oBAAsB,UAC3ClC,WAAYzI,EAAoBP,EAAUmL,cAC1CjC,aAAcnI,EAAaf,EAAUoL,oBACrChC,kBAAmBpJ,EAAUqL,wBAA0B,EACvD/B,cAAetJ,EAAUsL,oBAAsB,WAEnDC,UAAW,GACXC,OAAQ,CACJC,KAAMlL,EAAoBP,EAAU0L,cAExCC,SAAU,CACNF,KAAMlL,EAAoBP,EAAU4L,gBAExCC,SAAU,CACNJ,KAAMlL,EAAoBP,EAAU8L,gBACpCC,KAAMxG,GAEVyG,OAAQ,GACRC,WAAY,CACRC,aAAc3L,EAAoBP,EAAUmM,wBAC5CC,UAAWpM,EAAUqM,8BAAgC,GACrDC,QAAiB,OAATtM,QAAS,IAATA,GAAmC,QAA1BK,EAATL,EAAWuM,gCAAwB,IAAAlM,OAA1B,EAATA,EAAqC6D,KAAIpF,IAAC,IAAA0N,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACnD/O,IAAIqG,EAAAA,EAAAA,KACJ2I,MAAe,QAAVP,EAAG,OAAD1N,QAAC,IAADA,OAAC,EAADA,EAAGiO,aAAK,IAAAP,EAAAA,EAAI,GACnBhM,MAAQ,OAAD1B,QAAC,IAADA,OAAC,EAADA,EAAG0B,MACV4G,QAAU,OAADtI,QAAC,IAADA,GAAAA,EAAGkO,EAAI,CAAClO,EAAEkO,GAAK,GACxB1J,MAAQ,OAADxE,QAAC,IAADA,GAAU,QAAT2N,EAAD3N,EAAGyE,eAAO,IAAAkJ,OAAT,EAADA,EAAY1O,GACnBkP,MAAiB,QAAZP,EAAG,OAAD5N,QAAC,IAADA,OAAC,EAADA,EAAG0E,eAAO,IAAAkJ,EAAAA,EAAI,GACrB1F,QAAa,QAAN2F,EAAG,OAAD7N,QAAC,IAADA,OAAC,EAADA,EAAGoO,SAAC,IAAAP,EAAAA,EAAI,GACjBzF,MAAqB,QAAhB0F,EAAG,OAAD9N,QAAC,IAADA,GAAU,QAAT+N,EAAD/N,EAAGqI,eAAO,IAAA0F,OAAT,EAADA,EAAY9O,UAAE,IAAA6O,EAAAA,EAAI,GACzBO,MAAiB,QAAZL,EAAG,OAADhO,QAAC,IAADA,OAAC,EAADA,EAAG0I,eAAO,IAAAsF,EAAAA,EAAI,GACxB,MAAM,IAIC,EDjNUM,CAAsB7O,GACxC8O,QAAQC,IAAI,eAAM/O,EAAYwB,GAE9BwN,EAAaxN,EACjB,CAGAP,EAAUgO,EAAAA,EAXV,CAWwB,GACzB,CAACvO,EAAQV,IAGZ,MAAMgP,EAAgBxN,IAClBP,EAAUO,GAEVV,EAAW,IACJJ,EACHW,YAAaG,GACf,EAIA0N,GAAqBC,EAAAA,EAAAA,UACrBC,GAAazO,EAAAA,EAAAA,UAAQ,KACvB,IAAKK,EACD,OAAO,KAEX,MAAM,WAAEqO,KAAeC,GAAMtO,EAE7B,OAAIO,IAAQ2N,EAAmBK,QAASD,GAC7BJ,EAAmBK,SAG9BL,EAAmBK,QAAUD,EAEtBA,EAAC,GACT,CAACtO,IAEEwO,GAAqBL,EAAAA,EAAAA,UACrBE,GAAa1O,EAAAA,EAAAA,UAAQ,KACvB,IAAKK,EACD,OAAO,KAEX,MAAQqO,WAAYI,GAAMzO,EAE1B,OAAIO,IAAQiO,EAAmBD,QAASE,GAC7BD,EAAmBD,SAG9BC,EAAmBD,QAAUE,EAEtBA,EAAC,GACT,CAACzO,IAEJ,OAEI0O,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CACPnQ,GAAIA,EACJE,aAAcA,EACdsB,OAAQoO,EACRC,WAAYA,EACZL,aAAcA,EACdY,eAAa,EACbjQ,aAAcA,GAChB,C", "sources": ["module/layout/controlComp/lib/CurveDaqBuffer/index.js", "module/layout/controlComp/lib/CurveDaqBuffer/utils/convertOldConfigToNew.js"], "names": ["_ref", "id", "item", "layoutConfig", "isRightClick", "currentSettingIdFromWidget", "widget_data_source", "JSON", "parse", "oldSetting", "useSelector", "state", "_state$staticCurve", "staticCurve", "settingResList", "find", "i", "widgetData", "template", "widget", "useMemo", "findItem", "widget_id", "editWidget", "useWidget", "config", "setConfig", "useState", "useEffect", "_widget$data_source", "data_source", "curveGroup", "isEqual", "newConfig", "oldConfig", "_oldConfig$x_units", "_oldConfig$y2_channel", "_oldConfig$x_units2", "_oldConfig$x_units3", "_oldConfig$coordinate", "Error", "convertBooleanValue", "value", "Boolean", "mapProportionType", "oldType", "extend", "not", "all", "mapLineStyle", "oldStyle", "solid", "dashed", "dotted", "convertCurves", "curveConfig", "yChannel", "pointTags", "sourceType", "curves", "initBufferCurve", "curve", "res", "Object", "keys", "for<PERSON>ach", "key", "_oldConfig$y_units", "_curves$key", "name", "lines", "isLine", "line_open", "lineType", "line_style", "lineThickness", "line_thickness", "isSign", "sign_open", "signType", "sign_style", "signEach", "sign_each", "color", "line_color", "code", "isApply", "apply_point_count", "yUnit", "y_units", "y_label", "display", "SOURCE_TYPE", "单数据源", "多数据源", "lineConfig", "_lineConfig$line_tags", "_lineConfig$line_tags2", "line_tags", "array", "map", "tag", "uuidv4", "resultVariableId", "isName", "name_flag", "isAll", "all_flag", "isChunk", "chunk_flag", "sampleCode", "sample_code", "line_flag", "isSample", "sample_flag", "isVal", "val_flag", "isAbbr", "abbr_flag", "convertPointTags", "line_config", "chunkTags", "blockConfig", "block_config", "block_tags", "block", "block_id", "title", "showTitle", "curveIndex", "results", "curve_config", "y_channel", "curves2", "y2_channel", "base", "curve_nama_setting_enabl", "curve_name", "sourceInputCode", "buffer_code", "input_code", "updateFreq", "crossInputCode", "yAxis", "isEnable", "index", "xSignal", "x_channel", "xUnit", "x_units", "ySignal", "y2Axis", "length", "xAxis", "x_label", "unit", "proportionType", "x_proportion", "lowLimit", "x_low_limit", "upLimit", "x_up_limit", "<PERSON><PERSON><PERSON><PERSON>", "x_last_range", "isLog", "x_log", "type", "x_grid_line_style", "thickness", "x_thickness", "x_color", "isGrid", "x_grid_line", "gridType", "gridThickness", "x_grid_line_thickness", "gridColor", "x_grid_line_color", "isZeroLine", "x_zero_line", "zeroLineType", "x_zero_line_style", "zeroLineThickness", "x_zero_line_thickness", "zeroLineColor", "x_zero_line_color", "y_proportion", "y_low_limit", "y_up_limit", "y_last_range", "y_log", "y_thickness", "y_color", "y_grid_line", "y_grid_line_style", "y_grid_line_thickness", "y_grid_line_color", "y_zero_line", "y_zero_line_style", "y_zero_line_thickness", "y_zero_line_color", "y2_label", "y2_proportion", "y2_low_limit", "y2_up_limit", "y2_last_range", "y2_log", "y2_thickness", "y2_color", "y2_grid_line", "y2_grid_line_style", "y2_grid_line_thickness", "y2_grid_line_color", "y2_zero_line", "y2_zero_line_style", "y2_zero_line_thickness", "y2_zero_line_color", "auxiliary", "legend", "open", "legend_flag", "pointTag", "line_tag_flag", "chunkTag", "block_tag_flag", "list", "marker", "defineAxis", "isDefineAxis", "coordinate_source_flag", "inputCode", "coordinate_source_input_code", "source", "coordinate_source_select", "_i$label", "_i$y_units", "_i$y_label", "_i$x", "_i$x_units$id", "_i$x_units", "_i$x_label", "label", "y", "yName", "x", "xName", "convertOldConfigToNew", "console", "log", "updateConfig", "initialOption", "compConfigCacheRef", "useRef", "compConfig", "compStatus", "c", "current", "compStatusCacheRef", "s", "_jsx", "Comp<PERSON><PERSON>", "isBufferCurve"], "sourceRoot": ""}