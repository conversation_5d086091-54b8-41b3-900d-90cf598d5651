"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[6224],{5411:(e,l,n)=>{n.d(l,{A:()=>r});var a=n(65043),t=n(80077),i=n(36950);const r=e=>{let{codes:l,showUnit:n}=e;const r=(0,t.d4)((e=>e.project.optSample)),d=(0,t.d4)((e=>e.project.resultHistoryData)),o=(0,t.d4)((e=>e.template.resultData)),s=(0,t.d4)((e=>e.global.unitList));return(0,a.useMemo)((()=>l?l.map((e=>{try{var l,a;const v=null===d||void 0===d?void 0:d[null===r||void 0===r?void 0:r.code].find((l=>l.code===e)),{format_type:m,dimension_id:p,unit_id:h,format_info:b}=null!==(l=o.find((l=>l.code===e)))&&void 0!==l?l:{};let x=null!==(a=null===v||void 0===v?void 0:v.value)&&void 0!==a?a:"--";if("number"===typeof x&&(x=(0,i.jq)(m,(0,i.tJ)(x,p,h),(0,i._q)(m,b))),n&&p&&h){var t,c,u;x=`${x} ${null===s||void 0===s||null===(t=s.find((e=>(null===e||void 0===e?void 0:e.id)===p)))||void 0===t||null===(c=t.units)||void 0===c||null===(u=c.find((e=>(null===e||void 0===e?void 0:e.id)===h)))||void 0===u?void 0:u.name}`}return x}catch(v){return console.log("err",v),null}})):[]),[JSON.stringify(l),o,d,r,s])}},13830:(e,l,n)=>{n.d(l,{A:()=>x,p:()=>m.ps});var a=n(65043),t=n(16569),i=n(6051),r=n(95206),d=n(81143),o=n(80077),s=n(74117),c=n(88359),u=n(51554),v=n(78178),m=n(56543),p=n(754),h=n(70579);const b=d.Ay.div`
    .bind-input-variable{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;

        .label{
            margin-right: 10px;
        }
        .bind-value-span{
            word-break: break-all;
        }
        .bind-fun-div{
            white-space: nowrap;
        }
    }
`,x=e=>{let{id:l,value:n,onChange:d,inputVariableType:x,checkFn:y,isSetProgrammableParameters:f=!1}=e;const j=(0,o.wA)(),{t:g}=(0,s.Bd)(),A=(0,a.useRef)(),[C,w]=(0,a.useState)(!1),[_,k]=(0,a.useState)(),[S,I]=(0,a.useState)("add");(0,a.useEffect)((()=>{n&&V(n)}),[n]);const V=e=>{if((null===e||void 0===e?void 0:e.variable_type)!==x)return void d();(0,p.B)("inputVariable","inputVariableMap").has(e.code)||d()},T=e=>{const l=y&&y(e);if(l)return void t.Ay.error(l);const{id:n,code:a,variable_name:i,variable_type:r,name:o}=e;d({id:n,code:a,variable_name:null!==i&&void 0!==i?i:o,variable_type:r,restrict:{variableType:m.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x}})};return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(b,{children:(0,h.jsxs)("div",{className:"bind-input-variable",children:[(0,h.jsxs)("div",{className:"bind-value-span",children:[g("\u7ed1\u5b9a\u53d8\u91cf"),":",null===n||void 0===n?void 0:n.variable_name]}),(0,h.jsx)("div",{className:"bind-fun-div",children:(0,h.jsxs)(i.A,{children:[(0,h.jsx)(r.Ay,{onClick:()=>{A.current.open({variableType:m.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x})},children:"\u9009\u62e9"}),n?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(r.Ay,{onClick:()=>{k(null===n||void 0===n?void 0:n.id),I("edit"),w(!0)},children:g("\u7f16\u8f91")}),(0,h.jsx)(r.Ay,{onClick:()=>d(),children:g("\u89e3\u7ed1")})]}):(0,h.jsx)(r.Ay,{onClick:()=>{I("add"),w(!0)},children:g("\u65b0\u5efa")})]})})]})}),(0,h.jsx)(u.A,{ref:A,isSetProgrammableParameters:f,handleSelectedVariable:T}),C&&(0,h.jsx)(v.A,{isSetProgrammableParameters:f,variableType:x,modalIndex:0,editId:_,mode:S,open:C,onOk:async e=>{const l=await j((0,c.w)()),n=null===l||void 0===l?void 0:l.find((l=>l.code===e.code));n&&T(n),w(!1)},onCancel:()=>{w(!1)}})]})}},51554:(e,l,n)=>{n.d(l,{A:()=>y});var a=n(65043),t=n(80077),i=n(16569),r=n(83720),d=n(79806),o=n(74117),s=n(93950),c=n.n(s),u=n(56543),v=n(75440),m=n(29977),p=n(6051),h=n(70579);const b=e=>{let{handleSelected:l,t:n}=e;return[{title:n?n("\u540d\u79f0"):"\u540d\u79f0",dataIndex:"variable_name",key:"variable_name"},{title:n?n("\u6807\u8bc6\u7b26"):"\u6807\u8bc6\u7b26",dataIndex:"code",key:"code"},{title:n?n("\u64cd\u4f5c"):"\u64cd\u4f5c",dataIndex:"code",key:"code",render:(e,n)=>(0,h.jsx)(p.A,{size:"middle",children:(0,h.jsx)("a",{onClick:()=>l(n),children:"\u9009\u62e9"})})}]},x=(e,l)=>{let{handleSelectedVariable:n=e=>console.log(e),isSetProgrammableParameters:s=!1}=e;const p=(0,m.A)(),x=(0,t.d4)((e=>e.template.resultData)),[y,f]=(0,a.useState)(!1),[j,g]=(0,a.useState)(),[A,C]=(0,a.useState)([]),[w,_]=(0,a.useState)([]),{t:k}=(0,o.Bd)(),S=(0,a.useMemo)((()=>p.map((e=>({...e,variable_name:null===e||void 0===e?void 0:e.name})))),[p]),I=(0,a.useMemo)((()=>x.map((e=>({...e,id:e.code})))),[x]);(0,a.useEffect)((()=>{y&&V()}),[y]);const V=()=>{if(j)switch(null===j||void 0===j?void 0:j.variableType){case u.oY.\u8f93\u5165\u53d8\u91cf:{const e=[...S.filter((e=>!(null!==j&&void 0!==j&&j.inputVarType)||e.variable_type===(null===j||void 0===j?void 0:j.inputVarType)))];_(e),C(e);break}case u.oY.\u4fe1\u53f7\u53d8\u91cf:case u.oY.\u7ed3\u679c\u53d8\u91cf:_(I),C(I);break;default:console.log("\u672a\u5904\u7406\u7684\u53d8\u91cf\u7c7b\u578b",null===j||void 0===j?void 0:j.variableType)}};(0,a.useImperativeHandle)(l,(()=>({open:e=>{g(e),f(!0)}})));const T=c()((async e=>{if(e){const l=A.filter((l=>{const n=l.variable_name.toLowerCase(),a=l.code.toLowerCase(),t=e.toLowerCase();return n.includes(t)||a.includes(t)}));_(l)}else _(A)}),200);return(0,h.jsxs)(v.A,{open:y,onCancel:()=>{f(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:[(0,h.jsx)(r.A,{allowClear:!0,onChange:e=>T(e.target.value),placeholder:k("\u540d\u79f0/\u5185\u90e8\u540d"),style:{width:"300px",marginBottom:"10px"}}),(0,h.jsx)(d.A,{rowKey:"code",columns:b({handleSelected:e=>{var l;!s||"Array"===(null===e||void 0===e?void 0:e.variable_type)&&"programmableParameters"===(null===e||void 0===e||null===(l=e.custom_array_tab)||void 0===l?void 0:l.useType)?(n(e,j),f(!1)):i.Ay.error("\u8bf7\u9009\u62e9\u81ea\u5b9a\u4e49\u6570\u7ec4\u7528\u9014\u4e3a\u7a0b\u63a7\u53c2\u6570\u7684\u53d8\u91cf")}}),dataSource:w})]})},y=(0,a.forwardRef)(x)},56224:(e,l,n)=>{n.r(l),n.d(l,{Container:()=>N,default:()=>L});var a=n(65043),t=n(81143),i=n(19853),r=n.n(i),d=n(80231),o=n(97320),s=n(80077),c=n(79806),u=n(5411),v=n(71424),m=n(70579);const p=t.Ay.div`
    display: flex;

    width: 100%;
    height: 100%;
    background: #ffffff;
    overflow: hidden; 
`,h=[{title:"\u672a\u8bbe\u7f6e",dataIndex:"default",key:"default"}],b=e=>{let{config:{attr:{columnConfig:l}={},variable:{visible:n}={}}={}}=e;const t=(0,s.d4)((e=>e.template.resultData)),i=(0,v.A)(null===n||void 0===n?void 0:n.code,!0),r=(0,u.A)({codes:null===l||void 0===l?void 0:l.map((e=>{let{resultVariableCode:l}=e;return l})),showUnit:!0}),[d,o]=(0,a.useState)([]),[b,x]=(0,a.useState)(h),y=(0,a.useCallback)((async e=>{const{newColumns:l,newData:n}=e.reduce(((e,l,n)=>{let{title:a,resultVariableCode:t}=l;return{newColumns:[...e.newColumns,{title:a,dataIndex:t,key:""}],newData:[{...e.newData[0],[t]:null===r||void 0===r?void 0:r[n]}]}}),{newColumns:[],newData:[]});o(n),x(l)}),[t,r]);return(0,a.useEffect)((()=>{l?y(l):(x(h),o([]))}),[l,y]),(0,m.jsx)(m.Fragment,{children:i&&(0,m.jsx)(p,{children:(0,m.jsx)(c.A,{bordered:!0,dataSource:d,columns:b.length>0?b:h,pagination:!1,style:{width:"100%"}})})})};var x=n(25055),y=n(8918),f=n(74117),j=n(68358),g=n(13830),A=n(63942),C=n(83720),w=n(95206),_=n(51554),k=n(56543);const S=t.Ay.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px;
    .label{
        margin-right: 10px;
    }
`,I=e=>{let{id:l,value:n,onChange:t}=e;const i=(0,s.d4)((e=>e.template.resultData)),r=(0,a.useRef)(),d=(0,a.useMemo)((()=>i.find((e=>e.code===n))),[i,n]);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)(S,{children:[(0,m.jsx)("span",{className:"label",children:null===d||void 0===d?void 0:d.variable_name}),(0,m.jsx)("div",{children:(0,m.jsx)(w.Ay,{onClick:()=>{r.current.open({variableType:k.oY.\u7ed3\u679c\u53d8\u91cf})},children:"\u9009\u62e9"})})]}),(0,m.jsx)(_.A,{ref:r,handleSelectedVariable:e=>{const{result_variable_id:l,code:n,variable_name:a,variable_type:i}=e;t(n)}})]})};var V=n(6051);const T=e=>{let{handleEdit:l,handleDel:n,resultData:a}=e;return[{title:"\u6807\u9898",dataIndex:"title",key:"title"},{title:"\u6570\u636e\u7c7b\u578b",dataIndex:"resultVariableCode",key:"resultVariableCode",render:e=>{var l;return null===a||void 0===a||null===(l=a.find((l=>l.code===e)))||void 0===l?void 0:l.variable_name}},{title:"\u64cd\u4f5c",key:"id",render:(e,a)=>(0,m.jsxs)(V.A,{size:"middle",children:[(0,m.jsx)("a",{onClick:()=>l(a),children:"\u7f16\u8f91"}),(0,m.jsx)("a",{onClick:()=>n(a),children:"\u5220\u9664"})]})}]},{Item:D}=x.A,F=e=>{let{id:l,value:n=[],onChange:t}=e;const i=(0,s.d4)((e=>e.template.resultData)),[r,d]=(0,a.useState)(!1),o=(0,a.useRef)(),u=(0,a.useRef)(),v=()=>{d(!1),u.current=null};return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.A,{columns:T({handleEdit:e=>{d(!0),u.current={...e}},handleDel:e=>{t(n.filter((l=>l.id!==e.id)))},resultData:i}),dataSource:n}),(0,m.jsx)("a",{onClick:()=>{u.current=null,d(!0)},children:"\u6dfb\u52a0\u4e00\u5217"}),r?(0,m.jsx)(A.A,{title:"\u6570\u636e\u5217\u8bbe\u7f6e",open:r,onOk:async()=>{const e=await o.current.validateFields();u.current?t(n.map((l=>l.id===u.current.id?{...u.current,...e}:l))):t([...n,{...e,id:(new Date).getTime()}]),v()},onCancel:v,children:(0,m.jsxs)(x.A,{ref:o,initialValues:r?{...u.current}:{},children:[(0,m.jsx)(D,{name:"title",label:"\u5217\u6807\u9898",rules:[{required:!0}],children:(0,m.jsx)(C.A,{})}),(0,m.jsx)(D,{name:"resultVariableCode",label:"\u7ed3\u679c\u53d8\u91cf",rules:[{required:!0}],children:(0,m.jsx)(I,{})})]})}):null]})},{useForm:B,Item:E}=x.A,P=e=>{let{open:l,onClose:n,config:t,setConfig:i}=e;const{t:d}=(0,f.Bd)(),[o]=B();(0,a.useEffect)((()=>{r()(t,o.getFieldsValue())||o.setFieldsValue(t)}),[t]);return(0,m.jsx)(j.A,{open:l,onClose:n,children:(0,m.jsx)(x.A,{form:o,labelCol:{span:6},wrapperCol:{span:18},onValuesChange:(e,l)=>{var n;let a=l;null!==e&&void 0!==e&&null!==(n=e.variable)&&void 0!==n&&n.value&&(a={...a,attr:{...a.attr,label:e.variable.value.variable_name}}),i(a)},children:(0,m.jsx)(y.A,{defaultActiveKey:"attr",items:[{key:"attr",label:d("\u5c5e\u6027"),forceRender:!0,children:(0,m.jsx)(m.Fragment,{children:(0,m.jsx)(E,{label:d("\u5217\u914d\u7f6e"),name:["attr","columnConfig"],children:(0,m.jsx)(F,{})})})},{key:"variable",label:d("\u53d8\u91cf"),forceRender:!0,children:(0,m.jsx)(m.Fragment,{children:(0,m.jsx)(E,{label:d("\u53ef\u89c1\u6027"),name:["variable","visible"],children:(0,m.jsx)(g.A,{inputVariableType:g.p[d("\u5e03\u5c14\u578b")]})})})}]})})})},z={attr:{columnConfig:void 0},variable:{value:null,visible:null}},N=t.Ay.div`
    width: ${e=>{let{compWidth:l}=e;return null!==l&&void 0!==l?l:"100%"}};
    
    overflow: hidden;
`,L=e=>{var l;let{item:n,id:t,layoutConfig:i}=e;const{updateLayoutItem:s}=(0,o.A)(),[c,u]=(0,a.useState)(!1),[v,p]=(0,a.useState)(z);(0,a.useEffect)((()=>{try{if(null!==n&&void 0!==n&&n.data_source){const{comp_config:e}=JSON.parse(null===n||void 0===n?void 0:n.data_source);r()(e,v)||p(e)}}catch(e){console.log("err",e)}}),[null===n||void 0===n?void 0:n.data_source]);return(0,m.jsxs)(N,{id:t,compWidth:null===v||void 0===v||null===(l=v.attr)||void 0===l?void 0:l.compWidth,children:[(0,m.jsx)(b,{config:v}),(0,m.jsx)(P,{open:c,onClose:()=>{u(!1),s({layout:i,newItem:{...n,data_source:JSON.stringify({comp_config:v})}})},config:v,setConfig:p}),(0,m.jsx)(d.A,{domId:t,layoutConfig:i,children:(0,m.jsx)("div",{className:"unique-content",onClick:()=>u(!0),children:"\u7f16\u8f91\u7ed3\u679c\u53d8\u91cf\u8868\u683c"})})]})}},68358:(e,l,n)=>{n.d(l,{A:()=>m});var a=n(65043),t=n(48677),i=n(80077),r=n(14463),d=n(25055),o=n(36282),s=n(96603),c=n(14524),u=n(70579);const v=e=>{let{setting:l,onChange:n}=e;const[t]=d.A.useForm();(0,a.useEffect)((()=>{t.setFieldsValue({...l})}),[l]);return(0,u.jsx)(o.A,{content:(0,u.jsxs)(d.A,{form:t,name:"basic",labelCol:{style:{width:35}},onValuesChange:(e,l)=>{n(l)},children:[(0,u.jsx)(d.A.Item,{label:"\u4f4d\u7f6e",name:"placement",children:(0,u.jsxs)(s.Ay.Group,{size:"small",children:[(0,u.jsx)(s.Ay.Button,{value:"top",children:"\u4e0a"}),(0,u.jsx)(s.Ay.Button,{value:"right",children:"\u53f3"}),(0,u.jsx)(s.Ay.Button,{value:"bottom",children:"\u4e0b"}),(0,u.jsx)(s.Ay.Button,{value:"left",children:"\u5de6"})]})}),(0,u.jsx)(d.A.Item,{label:"\u5c3a\u5bf8",name:"size",children:(0,u.jsxs)(s.Ay.Group,{size:"small",children:[(0,u.jsx)(s.Ay.Button,{value:"default",children:"\u6b63\u5e38"}),(0,u.jsx)(s.Ay.Button,{value:"large",children:"\u5927"})]})})]}),title:"",trigger:"click",placement:"leftTop",children:(0,u.jsx)(c.A,{})})},m=e=>{let{children:l,open:n,onClose:a}=e;const d=(0,i.wA)(),{drawSetting:o}=(0,i.d4)((e=>e.split));return(0,u.jsx)(u.Fragment,{children:n&&(0,u.jsx)(t.A,{open:n,size:null===o||void 0===o?void 0:o.size,placement:null===o||void 0===o?void 0:o.placement,onClose:a,extra:(0,u.jsx)(v,{setting:o,onChange:e=>{d({type:r.cd,param:e})}}),children:l})})}},97320:(e,l,n)=>{n.d(l,{A:()=>o});n(65043);var a=n(80077),t=n(84856),i=n(67208),r=n(14463),d=n(41086);const o=()=>{const e=(0,a.wA)(),{saveLayout:l}=(0,t.A)(),n=async l=>{let{layout:n,newItem:a}=l;const t={...n,children:o(n.children,a)},[s]=await(0,i.PXE)({binder_ids:[null===n||void 0===n?void 0:n.binder_id]});await(0,i.Kv3)({binders:[{...s,layout:(0,d.gT)(t,null===n||void 0===n?void 0:n.binder_id)}]}),e({type:r.EH,param:s.binder_id})},o=(e,l)=>e.map((e=>e.id===l.id?l:e.children&&e.children.length>0?{...e,children:o(e.children,l)}:e)),s=async e=>{let{layout:n,newItem:a}=e;const t={...n,children:o(n.children,a)};await l(t)};return{updateLayoutItem:async e=>{let{layout:l,newItem:a}=e;null!==l&&void 0!==l&&l.binder_id?(console.log("\u754c\u9762\u5185\u5bb9-tab"),await n({layout:l,newItem:a})):(console.log("\u754c\u9762\u5185\u5bb9-\u4e3b\u7a97\u53e3"),await s({layout:l,newItem:a}))}}}}}]);
//# sourceMappingURL=6224.af423003.chunk.js.map