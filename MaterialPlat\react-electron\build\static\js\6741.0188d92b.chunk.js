"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[6741],{6741:(e,a,i)=>{i.r(a),i.d(a,{default:()=>c});i(65043);var n=i(95206),l=i(16090),t=i(67208),d=i(56543),s=i(55518),o=i(70579);const r=e=>{let{disabled:a,variable:i}=e;const{actionId:s,content:r,function:c,isEnable:b,pic:v,source:u}=null===i||void 0===i?void 0:i.button_tab,{startAction:h}=(0,l.A)();return b?(0,o.jsx)(n.Ay,{disabled:a,onClick:async()=>{try{"action_lib"===u&&s&&await h({action_id:String(s)}),"editor"===u&&c&&await(0,t.HGI)({script:c,result_type:d.Jt.BOOL})}catch(e){console.log(e)}},icon:v&&(0,o.jsx)("img",{src:v,alt:"",style:{height:"100%",marginRight:"3px"}}),children:r}):(0,o.jsx)(o.Fragment,{})},c=e=>{let{variable:a,disabled:i,onChange:n}=e;return(0,o.jsx)(s.A,{variable:a,disabled:i,onChange:n,fxShow:!1,buttonShow:!1,render:e=>{let{innerDisabled:i}=e;return(0,o.jsx)(r,{variable:a,disabled:i})}})}},55518:(e,a,i)=>{i.d(a,{A:()=>N});var n=i(65043),l=i(74117),t=i(56543),d=i(81143),s=i(68374),o=i(18650);const r=0,c="left";var b=i(70579);const v=d.Ay.div`
    width: ${(0,s.D0)("20px")};
    height: ${(0,s.D0)("20px")};
    background-size: ${(0,s.D0)("20px")} ${(0,s.D0)("20px")};
    background-image: url(${e=>{let{isConstant:a}=e;return a?o.fd:o.Mo}});
`,u=e=>{let{variable:a,onChange:i,disabled:n}=e;const{default_val:l,is_fx:t}=a;return!t||n?(0,b.jsx)(b.Fragment,{}):(0,b.jsx)(v,{isConstant:l.isConstant===r,onClick:()=>{i({...a,default_val:{...l,isConstant:0===(null===l||void 0===l?void 0:l.isConstant)?1:0}})}})};var h=i(95206),p=i(34154),x=i(67208),g=i(16090),f=i(36497),_=i(29977);const w=e=>{var a;let{disabled:i,variable:l,handleChange:t}=e;const d=(0,_.A)(),s=(0,n.useMemo)((()=>(null===d||void 0===d?void 0:d.filter((e=>e.variable_type===l.variable_type&&e.id!==l.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[d,l]);return(0,b.jsx)(f.A,{showSearch:!0,optionFilterProp:"labelName",disabled:i,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===l||void 0===l||null===(a=l.default_val)||void 0===a?void 0:a.variable_id,options:s,onChange:(e,a)=>t(a)})},m=e=>{let{disabled:a,content:i,buttonType:l,actionId:d,script:s}=e;const[o,r]=(0,n.useState)(!1),{startAction:c}=(0,g.A)(),v=()=>{l!==p.NR.\u52a8\u4f5c?l!==p.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{r(!0),await(0,x.O5k)({script:s,result_type:t.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{r(!1)}})():(async()=>{try{d&&(r(!0),await c({action_id:d}))}catch(e){console.log("err when handleSubmitAction",e)}finally{r(!1)}})()};return(0,b.jsx)(h.Ay,{loading:o,disabled:a,className:"button-width",onClick:()=>v(),children:i})},y=d.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:a}=e;return a?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,s.D0)("80px")};
        pointer-events: auto;
    }
`,j=e=>{let{disabled:a,variable:i,render:n,onChange:l,buttonShow:t}=e;const{button_variable_tab:d,default_val:s}=i;return(0,b.jsx)(y,{isLeft:(null===d||void 0===d?void 0:d.position)===c,children:1===s.isConstant?(0,b.jsx)(w,{disabled:a,variable:i,handleChange:e=>{l({...i,default_val:{...s,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,b.jsxs)(b.Fragment,{children:[t&&(null===d||void 0===d?void 0:d.isEnable)&&(0,b.jsx)(m,{...d,disabled:a}),n()]})})};var C=i(12624),S=i(32513);const A=e=>{let{variable:a,disabled:i=!1,onChange:n,usableShowType:l="checkbox"}=e;return null!==a&&void 0!==a&&a.is_enable?"switch"===l?(0,b.jsx)(C.A,{disabled:i,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{n({...a,is_feature:e})}}):(0,b.jsx)(S.A,{disabled:i,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{n({...a,is_feature:e.target.checked})}}):(0,b.jsx)(b.Fragment,{})},k=d.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,N=e=>{let{variable:a,disabled:i=!1,onChange:n,render:d,usableShow:s=!0,buttonShow:o=!0,fxShow:r=!0,nameShow:c=!0,usableShowType:v}=e;const{t:h}=(0,l.Bd)(),p=i||(a.variable_type===t.ps.\u5e03\u5c14\u578b?(null===a||void 0===a?void 0:a.is_enable)&&(null===a||void 0===a?void 0:a.is_feature):(null===a||void 0===a?void 0:a.is_enable)&&!(null!==a&&void 0!==a&&a.is_feature));return(0,b.jsxs)(k,{children:[(s||c)&&(0,b.jsx)("div",{className:"input-render-left",children:(0,b.jsxs)("div",{children:[s&&(0,b.jsx)(A,{variable:a,disabled:i,onChange:n,usableShowType:v}),c&&(0,b.jsx)("div",{className:"variable_name",children:h(a.name)})]})}),(0,b.jsxs)("div",{className:"input-render-right",children:[r&&(0,b.jsx)(u,{variable:a,onChange:n,disabled:p}),(0,b.jsx)(j,{disabled:p,variable:a,onChange:n,buttonShow:o,render:()=>d({innerDisabled:p})})]})]})}}}]);
//# sourceMappingURL=6741.0188d92b.chunk.js.map