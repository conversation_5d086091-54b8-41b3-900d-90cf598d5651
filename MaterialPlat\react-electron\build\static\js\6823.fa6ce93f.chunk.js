/*! For license information please see 6823.fa6ce93f.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[6823],{5427:(e,l,a)=>{a.d(l,{A:()=>f});var n=a(65043),t=a(25055),i=a(36497),d=a(8354),o=a(74117),r=a(74125),s=a(13327),u=a(97475),c=a(70579);const{useForm:v,Item:h}=t.A,b=(e,l)=>{let{renderConfig:a,onValueChange:v}=e;const{t:b}=(0,o.Bd)(),f=(0,n.useMemo)((()=>a.map((e=>({label:e.name,value:e.code})))),[a]),p=(0,n.useMemo)((()=>Object.entries(r.Rg).map((e=>{let[l,a]=e;return{label:l,value:a}}))),[r.Rg]);return(0,c.jsxs)(t.A,{ref:l,labelCol:{span:8},wrapperCol:{span:16},initialValues:{WaveControlPattern:r.Rg.\u4f4d\u79fb,WaveControlMode:r.uO.\u659c\u6ce2},requiredMark:!1,onValuesChange:(e,l)=>{v(l)},children:[(0,c.jsx)(h,{label:b("\u63a7\u5236\u65b9\u5f0f"),name:"WaveControlPattern",rules:[{required:!0}],children:(0,c.jsx)(i.A,{options:null===p||void 0===p?void 0:p.map((e=>({...e,label:b(e.label)})))})}),(0,c.jsx)(h,{label:b("\u63a7\u5236\u6a21\u5f0f"),name:"WaveControlMode",rules:[{required:!0}],children:(0,c.jsx)(i.A,{options:null===f||void 0===f?void 0:f.map((e=>({...e,label:b(e.label)})))})}),(0,c.jsx)(h,{shouldUpdate:!0,noStyle:!0,children:e=>{var l;let{getFieldValue:n}=e;const t=n("WaveControlMode"),d=n("WaveControlPattern"),o=null===a||void 0===a||null===(l=a.find((e=>e.code===t)))||void 0===l?void 0:l.params;return null===o||void 0===o?void 0:o.map((e=>{let{name:l,code:a,renderType:t,options:o,targetContorlModeCode:u,dimension:v}=e;const f=u?n(u):d;return(0,c.jsx)(h,{label:b(l),name:a,rules:[{required:!0}],children:t===r.YD.\u9009\u62e9\u5668?(0,c.jsx)(i.A,{options:null===o||void 0===o?void 0:o.map((e=>({...e,label:b(e.label)})))}):(0,c.jsx)(s.A,{dimensionId:null===v||void 0===v?void 0:v[f]})},a)}))}}),(0,c.jsx)(h,{shouldUpdate:!0,noStyle:!0,children:e=>{var l;let{getFieldValue:n}=e;const t=n("WaveControlMode"),i=null===a||void 0===a||null===(l=a.find((e=>e.code===t)))||void 0===l?void 0:l.saveRules;return i?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(d.A,{orientation:"left",plain:!0,style:{borderBlockStart:"0 rgba(5, 5, 5, 0.5)"},children:b("\u5b58\u76d8\u7b56\u7565")}),(0,c.jsx)(u.A,{saveRules:i,getFieldValue:n})]}):(0,c.jsx)(c.Fragment,{})}})]})},f=(0,n.forwardRef)(b)},13327:(e,l,a)=>{a.d(l,{A:()=>c});var n=a(65043),t=a(36497),i=a(97914),d=a(80077),o=a(36950),r=a(70579);const{Option:s}=t.A,u=e=>{let{dimensionId:l,unitId:a,handleUnitChange:i,disabled:o}=e;const u=(0,d.d4)((e=>e.global.unitList)),c=(0,n.useMemo)((()=>{var e;return(null===(e=u.find((e=>e.id===l)))||void 0===e?void 0:e.units)||[]}),[u,l]);return(0,r.jsx)(t.A,{value:a,style:{width:120},disabled:o,onChange:e=>{i(e)},children:c.map((e=>{let{id:l,name:a}=e;return(0,r.jsx)(s,{value:l,children:a},l)}))})},c=e=>{var l;let{value:a,onChange:t,dimensionId:s,unitDisabled:c}=e;const v=(0,d.d4)((e=>e.global.unitList)),[h,b]=(0,n.useState)(),[f,p]=(0,n.useState)(null===(l=v.find((e=>e.id===s)))||void 0===l?void 0:l.default_unit_id),m=(0,n.useMemo)((()=>{var e;return null===(e=v.find((e=>e.id===s)))||void 0===e?void 0:e.default_unit_id}),[s,v]);(0,n.useEffect)((()=>{p(m)}),[m]),(0,n.useEffect)((()=>{void 0!==a&&null!==a&&b((0,o.tJ)(Number(a),s,f,m))}),[a]);const x=e=>{if(h){const l=(0,o.tJ)(Number(a),s,e,m);b(l)}p(e)};return(0,r.jsx)(i.A,{style:{width:"100%"},value:h,addonAfter:!!s&&(0,r.jsx)(u,{dimensionId:s,unitId:f,handleUnitChange:x,disabled:c}),onChange:e=>{b(e),t((0,o.tJ)(Number(e),s,m,f))}})}},55518:(e,l,a)=>{a.d(l,{A:()=>M});var n=a(65043),t=a(74117),i=a(56543),d=a(81143),o=a(68374),r=a(18650);const s=0,u="left";var c=a(70579);const v=d.Ay.div`
    width: ${(0,o.D0)("20px")};
    height: ${(0,o.D0)("20px")};
    background-size: ${(0,o.D0)("20px")} ${(0,o.D0)("20px")};
    background-image: url(${e=>{let{isConstant:l}=e;return l?r.fd:r.Mo}});
`,h=e=>{let{variable:l,onChange:a,disabled:n}=e;const{default_val:t,is_fx:i}=l;return!i||n?(0,c.jsx)(c.Fragment,{}):(0,c.jsx)(v,{isConstant:t.isConstant===s,onClick:()=>{a({...l,default_val:{...t,isConstant:0===(null===t||void 0===t?void 0:t.isConstant)?1:0}})}})};var b=a(95206),f=a(34154),p=a(67208),m=a(16090),x=a(36497),g=a(29977);const C=e=>{var l;let{disabled:a,variable:t,handleChange:i}=e;const d=(0,g.A)(),o=(0,n.useMemo)((()=>(null===d||void 0===d?void 0:d.filter((e=>e.variable_type===t.variable_type&&e.id!==t.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[d,t]);return(0,c.jsx)(x.A,{showSearch:!0,optionFilterProp:"labelName",disabled:a,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===t||void 0===t||null===(l=t.default_val)||void 0===l?void 0:l.variable_id,options:o,onChange:(e,l)=>i(l)})},j=e=>{let{disabled:l,content:a,buttonType:t,actionId:d,script:o}=e;const[r,s]=(0,n.useState)(!1),{startAction:u}=(0,m.A)(),v=()=>{t!==f.NR.\u52a8\u4f5c?t!==f.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{s(!0),await(0,p.O5k)({script:o,result_type:i.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{s(!1)}})():(async()=>{try{d&&(s(!0),await u({action_id:d}))}catch(e){console.log("err when handleSubmitAction",e)}finally{s(!1)}})()};return(0,c.jsx)(b.Ay,{loading:r,disabled:l,className:"button-width",onClick:()=>v(),children:a})},_=d.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:l}=e;return l?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,o.D0)("80px")};
        pointer-events: auto;
    }
`,y=e=>{let{disabled:l,variable:a,render:n,onChange:t,buttonShow:i}=e;const{button_variable_tab:d,default_val:o}=a;return(0,c.jsx)(_,{isLeft:(null===d||void 0===d?void 0:d.position)===u,children:1===o.isConstant?(0,c.jsx)(C,{disabled:l,variable:a,handleChange:e=>{t({...a,default_val:{...o,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,c.jsxs)(c.Fragment,{children:[i&&(null===d||void 0===d?void 0:d.isEnable)&&(0,c.jsx)(j,{...d,disabled:l}),n()]})})};var w=a(12624),A=a(32513);const k=e=>{let{variable:l,disabled:a=!1,onChange:n,usableShowType:t="checkbox"}=e;return null!==l&&void 0!==l&&l.is_enable?"switch"===t?(0,c.jsx)(w.A,{disabled:a,checked:null===l||void 0===l?void 0:l.is_feature,onChange:e=>{n({...l,is_feature:e})}}):(0,c.jsx)(A.A,{disabled:a,checked:null===l||void 0===l?void 0:l.is_feature,onChange:e=>{n({...l,is_feature:e.target.checked})}}):(0,c.jsx)(c.Fragment,{})},S=d.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,M=e=>{let{variable:l,disabled:a=!1,onChange:n,render:d,usableShow:o=!0,buttonShow:r=!0,fxShow:s=!0,nameShow:u=!0,usableShowType:v}=e;const{t:b}=(0,t.Bd)(),f=a||(l.variable_type===i.ps.\u5e03\u5c14\u578b?(null===l||void 0===l?void 0:l.is_enable)&&(null===l||void 0===l?void 0:l.is_feature):(null===l||void 0===l?void 0:l.is_enable)&&!(null!==l&&void 0!==l&&l.is_feature));return(0,c.jsxs)(S,{children:[(o||u)&&(0,c.jsx)("div",{className:"input-render-left",children:(0,c.jsxs)("div",{children:[o&&(0,c.jsx)(k,{variable:l,disabled:a,onChange:n,usableShowType:v}),u&&(0,c.jsx)("div",{className:"variable_name",children:b(l.name)})]})}),(0,c.jsxs)("div",{className:"input-render-right",children:[s&&(0,c.jsx)(h,{variable:l,onChange:n,disabled:f}),(0,c.jsx)(y,{disabled:f,variable:l,onChange:n,buttonShow:r,render:()=>d({innerDisabled:f})})]})]})}},96823:(e,l,a)=>{a.r(l),a.d(l,{default:()=>D});var n=a(65043),t=a(55518),i=a(16569),d=a(89073),o=a(56434),r=a.n(o),s=a(74125),u=a(6051),c=a(75337),v=a(58168);const h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var b=a(22172),f=function(e,l){return n.createElement(b.A,(0,v.A)({},e,{ref:l,icon:h}))};const p=n.forwardRef(f);const m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"};var x=function(e,l){return n.createElement(b.A,(0,v.A)({},e,{ref:l,icon:m}))};const g=n.forwardRef(x);var C=a(70579);const j=e=>{let{handleClick:l}=e;return(0,C.jsxs)(u.A,{children:[(0,C.jsx)(c.A,{onClick:()=>l("add")}),(0,C.jsx)(p,{onClick:()=>l("edit")}),(0,C.jsx)(g,{onClick:()=>l("del")})]})};var _=a(74117),y=a(75440),w=a(5427);const A={add:"\u6dfb\u52a0\u8bd5\u9a8c\u6bb5",edit:"\u7f16\u8f91\u8bd5\u9a8c\u6bb5"},k=(e,l)=>{let{renderConfig:a,cb:t}=e;const{t:i}=(0,_.Bd)(),d=(0,n.useRef)(),[o,r]=(0,n.useState)(),[s,u]=(0,n.useState)();(0,n.useImperativeHandle)(l,(()=>({open:function(){let{type:e,data:l}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};u(e),r(!0),l?d.current.setFieldsValue(l):d.current.resetFields()}})));return(0,C.jsx)(y.A,{open:o,onOk:async()=>{const e=await d.current.validateFields();t(s,e),r(!1)},onCancel:()=>{r(!1)},title:i(s in A?A[s]:"\u672a\u786e\u8ba4mode"),width:"500px",forceRender:!0,style:{top:"30vh"},children:(0,C.jsx)(w.A,{ref:d,renderConfig:a,onValueChange:e=>{console.log(e)}})})},S=(0,n.forwardRef)(k);const M=a(81143).Ay.div`

    .anticon{
        cursor: pointer;
    }

    .ant-card-body{

        .line{
            display: flex;
            gap: 10px;
            margin-bottom: 5px;
            padding-left: 10px;
            cursor: pointer;
        }

        .select {
            background: #767676;
            color: #fff;
        }
    }
`,N=e=>{let{data:l,order:a,selected:t,onClick:i}=e;const d=(0,n.useMemo)((()=>Object.keys(s.uO).find((e=>s.uO[e]===l.waveType))),[l]);return(0,C.jsxs)("p",{className:`line ${t&&"select"}`,onClick:i,children:[(0,C.jsx)("span",{children:a<10?`0${a}`:a}),(0,C.jsx)("span",{children:d})]})},F=e=>{var l,a;let{variable:t,onDefaultValueChange:o}=e;const u=(0,n.useRef)(),[c,v]=(0,n.useState)(),h=(0,n.useMemo)((()=>{var e,l;return null!==t&&void 0!==t&&null!==(e=t.custom_array_tab)&&void 0!==e&&e.customWaveform?null===t||void 0===t||null===(l=t.custom_array_tab)||void 0===l?void 0:l.customWaveform:r()(s.TB)}),[null===t||void 0===t?void 0:t.custom_array_tab]),b=()=>{u.current.open({type:"add"})},f=()=>{var e;null!==c?u.current.open({type:"edit",data:null===t||void 0===t||null===(e=t.default_val)||void 0===e?void 0:e.value[c]}):i.Ay.error("\u8bf7\u5148\u9009\u62e9\u8bd5\u9a8c\u6bb5")},p=(e,l)=>{const{value:a}=t.default_val;"add"===e?o({value:a.concat(l)}):"edit"===e?o({value:a.map(((e,a)=>a!==c?e:l))}):"del"===e&&(o({value:a.filter(((e,l)=>l!==c))}),v())};return(0,C.jsxs)(M,{children:[(0,C.jsx)(d.A,{title:"\u8bd5\u9a8c\u6bb5\u5217\u8868",extra:(0,C.jsx)(j,{handleClick:e=>{switch(e){case"add":b();break;case"edit":f();break;case"del":p(e);break;default:console.log("\u672a\u5904\u7406\u7684\u64cd\u4f5c:",e)}}}),style:{width:250},children:null===t||void 0===t||null===(l=t.default_val)||void 0===l||null===(a=l.value)||void 0===a?void 0:a.map(((e,l)=>(0,C.jsx)(N,{data:e,order:l+1,selected:l===c,onClick:()=>v(l)},l)))}),(0,C.jsx)(S,{ref:u,renderConfig:h,cb:p})]})},R=e=>{var l;let{variable:a,onDefaultValueChange:n}=e;return"customWaveform"===(null===a||void 0===a||null===(l=a.custom_array_tab)||void 0===l?void 0:l.useType)?(0,C.jsx)(F,{variable:a,onDefaultValueChange:n}):(0,C.jsx)(C.Fragment,{children:"\u63a7\u4ef6\u5b9a\u4e49\u7684\u6570\u7ec4\u8bf7\u5f00\u542ffx"})},D=e=>{let{variable:l,disabled:a,onChange:n}=e;return(0,C.jsx)(t.A,{variable:l,disabled:a,onChange:n,buttonShow:!1,render:e=>{let{innerDisabled:a}=e;return(0,C.jsx)(R,{variable:l,onDefaultValueChange:e=>{n({...l,default_val:{...l.default_val,...e}})}})}})}}}]);
//# sourceMappingURL=6823.fa6ce93f.chunk.js.map