"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[7397],{30862:(i,t,e)=>{e.d(t,{P:()=>n});const n={props:{titleText:"",titleBold:!1,titleItalic:!1,isUseScrollbar:!1,titleFontSize:"16px"},style2Container:{flexDirection:"column",justifyContent:"start",alignItems:"",gap:"20px",width:"100%",height:"100%",borderWidth:"0px",borderStyle:"solid",paddingTop:"30px",paddingBottom:"30px",paddingLeft:"30px",paddingRight:"30px",maskOpacity:.3},variable:{value:null,visible:null}}},57397:(i,t,e)=>{e.r(t),e.d(t,{default:()=>x});var n=e(65043),d=e(19853),l=e.n(d),o=e(80231),a=e(71424),r=e(30862),s=e(81143);const p=s.Ay.div`
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 20px;
    overflow: hidden;
    position: relative;

    .block_mask{
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1000;
    }

    .title_container{
        white-space: nowrap;
    }
        
`,g=s.Ay.div`
    width: 100%;
    height: 100%;
    display: flex;
    padding: 10px;

    .child_container{
        flex: 1;
        min-width: 20px;
        min-height: 20px;
    }
`,c=(s.Ay.div`
    .params_tag{
        width: 100%;
        height: 32px;
        font-size: 17px;

        display: flex;
        align-items: center;

        background: rgba(31,56,88,0.06);
        padding: 0 12px;
        margin-bottom: 12px;
    }

    .params_contant{
        padding: 0 15px;
    }
`,s.Ay.div`
    width: 100%;
    height: 100%;
    background: #F0F0F0;
    border: 1px dashed #A7B1BD;
    padding: 10px;
    color: #A7B1BD;

    display: flex;
    justify-content: center;
    align-items: center;
`);var h=e(70579);const u=i=>{let{config:{props:{titleText:t,titleBold:e=!1,titleItalic:d=!1,isUseScrollbar:l=!1,titleFontSize:o="16px"}={},style2Container:{flexDirection:a,justifyContent:s,alignItems:p,gap:u,borderWidth:y,borderStyle:x,borderColor:v,paddingTop:f=r.P.style2Container.paddingTop,paddingBottom:m=r.P.style2Container.paddingBottom,paddingLeft:b=r.P.style2Container.paddingLeft,paddingRight:C=r.P.style2Container.paddingRight,maskOpacity:j}={}}={},children:k}=i;const w={fontWeight:e?"bold":"initial",fontStyle:d?"italic":"initial",fontSize:o||"16px"};return(0,h.jsxs)(g,{style:{flexDirection:a,justifyContent:s,alignItems:p,gap:u,borderWidth:y,borderStyle:x,borderColor:v,paddingTop:f,paddingBottom:m,paddingLeft:b,paddingRight:C,maskOpacity:j,width:"100%",height:"100%",...l?{display:"block",overflow:"auto"}:{}},children:[t&&(0,h.jsx)("div",{className:"title_container",style:w,children:t}),k&&0!==k.length?n.Children.map(k,(i=>i)):(0,h.jsx)(c,{children:"\u53f3\u51fb\u4e3a\u8be5\u533a\u5757\u6dfb\u52a0\u63a7\u4ef6"})]})},y=(0,n.lazy)((()=>Promise.all([e.e(5960),e.e(9478),e.e(1648),e.e(810)]).then(e.bind(e,20810)))),x=i=>{var t,e,d,s,g,c,x;let{item:v,id:f,layoutConfig:m,children:b}=i;const[C,j]=(0,n.useState)(!1),[k,w]=(0,n.useState)(),_=(0,a.A)(null===k||void 0===k||null===(t=k.variable)||void 0===t||null===(e=t.visible)||void 0===e?void 0:e.code,!0),F=(0,a.A)(null===k||void 0===k||null===(d=k.variable)||void 0===d||null===(s=d.disabled)||void 0===s?void 0:s.code,!0);return(0,n.useEffect)((()=>{try{if(null!==v&&void 0!==v&&v.data_source){const{comp_config:i}=JSON.parse(null===v||void 0===v?void 0:v.data_source);l()(i,k)||("paddingTop"in i.style2Container||(i.style2Container.paddingTop=i.style2Container.padding,i.style2Container.paddingBottom=i.style2Container.padding,i.style2Container.paddingLeft=i.style2Container.padding,i.style2Container.paddingRight=i.style2Container.padding),w(i))}else w(r.P)}catch(i){w(r.P),console.log("err",i)}}),[null===v||void 0===v?void 0:v.data_source]),(0,h.jsx)(h.Fragment,{children:(0,h.jsxs)(p,{id:f,style:{width:null===k||void 0===k||null===(g=k.style2Container)||void 0===g?void 0:g.width,height:null===k||void 0===k||null===(c=k.style2Container)||void 0===c?void 0:c.height},children:[!F&&(0,h.jsx)("div",{className:"block_mask",style:{background:`rgba(0,0,0,${null===k||void 0===k||null===(x=k.style2Container)||void 0===x?void 0:x.maskOpacity})`}}),_&&k&&(0,h.jsx)(u,{config:k,children:b}),(0,h.jsx)(n.Suspense,{fallback:(0,h.jsx)(h.Fragment,{}),children:C&&(0,h.jsx)(y,{open:C,setOpen:j,layoutConfig:m,setConfig:w,item:v})}),F&&(0,h.jsx)(o.A,{domId:f,layoutConfig:m,children:(0,h.jsx)("div",{className:"unique-content",onClick:()=>j(!0),children:"\u7f16\u8f91\u533a\u5757\u5c5e\u6027"})})]})})}}}]);
//# sourceMappingURL=7397.f98e27ac.chunk.js.map