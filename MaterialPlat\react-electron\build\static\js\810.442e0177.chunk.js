"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[810],{20810:(e,l,n)=>{n.r(l),n.d(l,{default:()=>M});var t=n(65043),a=n(25055),i=n(8918),r=n(83720),o=n(12624),d=n(96603),s=n(74117),c=n(68358),u=n(63189),p=n(95206),h=n(81143),m=n(51554),x=n(56543),y=n(70579);const v=h.Ay.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px;
    .label{
        margin-right: 10px;
    }
`,g=e=>{let{id:l,value:n,onChange:a,disabled:i,restrict:r}=e;const o=(0,t.useRef)();(0,t.useEffect)((()=>{n&&(null===r||void 0===r?void 0:r.variableType)===x.oY.\u8f93\u5165\u53d8\u91cf&&d(n)}),[]);const d=e=>{(null===e||void 0===e?void 0:e.variable_type)!==(null===r||void 0===r?void 0:r.inputVarType)&&a()};return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)(v,{children:[(0,y.jsxs)("span",{children:["\u7ed1\u5b9a\u53d8\u91cf:",null===n||void 0===n?void 0:n.variable_name]}),(0,y.jsx)(p.Ay,{size:"small",disabled:i,onClick:()=>{o.current.open(r)},children:"\u9009\u62e9\u53d8\u91cf"}),n&&(0,y.jsx)(p.Ay,{size:"small",onClick:()=>a(),children:"\u53d6\u6d88\u7ed1\u5b9a"})]}),(0,y.jsx)(m.A,{ref:o,handleSelectedVariable:e=>{const{id:l,code:n,variable_name:t,variable_type:i}=e;a({id:l,code:n,variable_name:t,variable_type:i,restrict:r})}})]})};var j=n(97320),b=n(42999),f=n(6051),A=n(26815),C=n(80077),w=n(8237),_=n(23585),k=n(41024);const S=e=>{let{transform:l}=e;return{...l,x:0}};var I=n(1648),B=n(79806);const D=e=>{const{attributes:l,listeners:n,setNodeRef:t,transform:a,transition:i,isDragging:r}=(0,I.gl)({id:e["data-row-key"]}),o={...e.style,transform:k.Ks.Transform.toString(a&&{...a,scaleY:1}),transition:i,cursor:"move",...r?{position:"relative",zIndex:9999}:{}};return(0,y.jsx)("tr",{...e,ref:t,style:o,...l,...n})},T=e=>{let{columns:l,dataSource:n=[],onDataSourceChange:a,...i}=e;const[r,o]=(0,t.useState)([]);(0,t.useEffect)((()=>{o([...n])}),[n]);const d=(0,_.FR)((0,_.MS)(_.AN,{activationConstraint:{distance:1}}));return(0,y.jsx)(_.Mp,{sensors:d,modifiers:[S],onDragEnd:e=>{let{active:l,over:n}=e;if(l.id!==(null===n||void 0===n?void 0:n.id)){const e=r.findIndex((e=>e.key===l.id)),t=r.findIndex((e=>e.key===(null===n||void 0===n?void 0:n.id))),i=(0,I.be)(r,e,t);o(i),a(i)}},children:(0,y.jsx)(I.gB,{items:n.map((e=>e.key)),strategy:I._G,children:(0,y.jsx)(B.A,{components:{body:{row:D}},columns:l,dataSource:r,...i,rowKey:"key"})})})};var F=n(21256);const V=e=>{let{handleControlDel:l,t:n}=e;return[{title:n("\u7c7b\u578b"),dataIndex:"name",key:"name",render:(e,l)=>(0,y.jsx)(y.Fragment,{children:n(e)})},{title:n("\u64cd\u4f5c"),key:"action",render:(e,t)=>(0,y.jsx)(f.A,{size:"middle",children:(0,y.jsx)("a",{onClick:()=>l(t),children:n("\u5220\u9664")})})}]},z=e=>{let{blockData:l,handleSaveLayout:n}=e;const a=(0,C.d4)((e=>e.template.dialogs)),i=(0,C.d4)((e=>e.template.widgetData)),[r,o]=(0,t.useState)([]),d=(0,t.useRef)(),{t:c}=(0,s.Bd)();(0,t.useEffect)((()=>{if(u){const e=(0,F.b)(i),l=u(e.map((e=>e.widget_type===w.rX.DIALOG?{...e,children:a.map((e=>({key:`${w.rX.DIALOG}-${e.dialog_id}`,widget_id:e.dialog_id,title:e.dialog_name,widget_type:w.rX.DIALOG,data_source:JSON.stringify({dialog_id:e.dialog_id})})))}:e)));o(l)}}),[a,i]);const u=e=>null===e||void 0===e?void 0:e.map((e=>({...e,disabled:!1,children:u(null===e||void 0===e?void 0:e.children)}))),h=e=>{const{widget_id:l,title:n,parent_id:t,delete_flag:a,data_source:i,widget_type:r}=d.current;return{id:`${e.id}-${e.children.length+1}-T${(new Date).getTime()}`,name:n,type:r,sizes:"",direction:"",view:null,widget_id:r===w.rX.DIALOG?"15":l,page_id:e.page_id,children:[],data_source:i}};return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{style:{paddingBottom:12},children:[(0,y.jsx)(A.A,{options:r,onChange:e=>{if(!e)return;const l=e.reduce(((e,l,n)=>e.children.find((e=>e.widget_id===l))),{children:r});d.current=l},placeholder:c("\u8bf7\u9009\u62e9"),fieldNames:{label:"title",value:"widget_id",children:"children"},optionRender:e=>(0,y.jsx)(y.Fragment,{children:c(e.title)})}),(0,y.jsx)(p.Ay,{onClick:()=>{try{const e=[...l.children,h(l)];n(e)}catch(e){console.log("err",e)}},children:"+"})]}),(0,y.jsx)(T,{columns:V({handleControlDel:e=>{var t;let{id:a}=e;const i=null===l||void 0===l||null===(t=l.children)||void 0===t?void 0:t.filter((e=>e.id!==a));n(i)},t:c}),dataSource:null===l||void 0===l?void 0:l.children.map((e=>({...e,key:e.id}))),onDataSourceChange:e=>{n(e)},childrenColumnName:"aaa",pagination:!1})]})},N=(0,t.forwardRef)(z);function L(e,l){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(e&&"object"===typeof e){if(n.push(e),e.id===l)return n;if(e.children&&Array.isArray(e.children))for(const t of e.children){const e=L(t,l,[...n]);if(e)return e}}return null}function $(e,l){if(e&&"object"===typeof e){if(e.id===l)return e;if(e.children&&Array.isArray(e.children))for(const n of e.children){const e=$(n,l);if(e)return e}}return null}const R=h.Ay.div`
    display: flex;
    gap: 8px;
    color: #ccc;
    margin-bottom: 8px; 

    .active{
        color: #000;
    }

    .comp-name{
        cursor: pointer;
    }
`,G=e=>{let{layoutConfig:l,item:n,currentItem:a,changeCurrentItem:i}=e;const r=(0,t.useMemo)((()=>L(l,n.id).filter((e=>"block"===e.type))),[l,n.id]);return(0,y.jsx)(R,{children:r.map(((e,l)=>(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("div",{className:a.id===e.id?"active comp-name":"comp-name",onClick:()=>i(e),children:e.name}),l!==r.length-1&&(0,y.jsx)("div",{children:"/"})]})))})};var E=n(30862);const{useForm:O,Item:P}=a.A,M=e=>{let{open:l,setOpen:n,layoutConfig:p,setConfig:h,item:m}=e;const{t:v}=(0,s.Bd)(),{updateLayoutItem:f}=(0,j.A)(),[A]=O(),[C,w]=(0,t.useState)(m.id),_=(0,t.useMemo)((()=>$(p,C)),[C,p]);(0,t.useEffect)((()=>{try{if(null!==_&&void 0!==_&&_.data_source){const{comp_config:e}=JSON.parse(null===_||void 0===_?void 0:_.data_source);"paddingTop"in e.style2Container||(e.style2Container.paddingTop=e.style2Container.padding,e.style2Container.paddingBottom=e.style2Container.padding,e.style2Container.paddingLeft=e.style2Container.padding,e.style2Container.paddingRight=e.style2Container.padding),k(e)}}catch(e){console.log("err",e)}}),[C]);const k=e=>{A.resetFields(),A.setFieldsValue(e)},S=async e=>{try{const l=await A.validateFields(),n=JSON.stringify({comp_config:l});(n!==(null===_||void 0===_?void 0:_.data_source)||e)&&await f({layout:p,newItem:{..._,data_source:n,children:null!==e&&void 0!==e?e:_.children}})}catch(l){console.log("err",l)}},I={width:"200px"},B=a.A.useWatch(["props","isUseScrollbar"],A);return(0,y.jsxs)(c.A,{open:l,onClose:async()=>{await S(),n(!1)},children:[(0,y.jsx)(G,{layoutConfig:p,item:m,currentItem:_,changeCurrentItem:async e=>{await S(),w(e.id)}}),(0,y.jsx)(a.A,{form:A,labelCol:{span:6},wrapperCol:{span:18},initialValues:E.P,onValuesChange:(e,l)=>{var n;if(C!==m.id)return;let t=l;null!==e&&void 0!==e&&null!==(n=e.variable)&&void 0!==n&&n.value&&(t={...t,attr:{...t.attr,label:e.variable.value.variable_name}}),h(t)},children:(0,y.jsx)(i.A,{defaultActiveKey:"props",items:[{key:"props",label:v("\u5c5e\u6027"),forceRender:!0,children:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(P,{label:v("\u6807\u9898\u5185\u5bb9"),name:["props","titleText"],children:(0,y.jsx)(r.A,{style:I})}),(0,y.jsx)(P,{label:v("\u6807\u9898\u52a0\u7c97"),name:["props","titleBold"],valuePropName:"checked",children:(0,y.jsx)(o.A,{})}),(0,y.jsx)(P,{label:v("\u6807\u9898\u659c\u4f53"),name:["props","titleItalic"],valuePropName:"checked",children:(0,y.jsx)(o.A,{})}),(0,y.jsx)(P,{label:v("\u6807\u9898\u5b57\u53f7"),name:["props","titleFontSize"],children:(0,y.jsx)(u.A,{style:I,min:0,addonAfter:"px"})}),(0,y.jsx)(P,{label:v("\u4f7f\u7528\u6eda\u52a8\u6761"),name:["props","isUseScrollbar"],valuePropName:"checked",children:(0,y.jsx)(o.A,{})}),B?"":(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(P,{label:v("\u4e3b\u8f74\u65b9\u5411"),name:["style2Container","flexDirection"],children:(0,y.jsxs)(d.Ay.Group,{size:"small",children:[(0,y.jsx)(d.Ay.Button,{value:"row",children:v("\u6a2a\u5411")}),(0,y.jsx)(d.Ay.Button,{value:"column",children:v("\u7eb5\u5411")})]})}),(0,y.jsx)(P,{label:v("\u4e3b\u8f74\u5bf9\u9f50"),name:["style2Container","justifyContent"],children:(0,y.jsxs)(d.Ay.Group,{size:"small",children:[(0,y.jsx)(d.Ay.Button,{value:"start",children:v("\u8d77\u59cb")}),(0,y.jsx)(d.Ay.Button,{value:"center",children:v("\u5c45\u4e2d")}),(0,y.jsx)(d.Ay.Button,{value:"end",children:v("\u7ed3\u675f")}),(0,y.jsx)(d.Ay.Button,{value:"space-between",children:v("\u4e24\u7aef")}),(0,y.jsx)(d.Ay.Button,{value:"space-around",children:v("\u7b49\u5206")})]})}),(0,y.jsx)(P,{label:v("\u526f\u8f74\u5bf9\u9f50"),name:["style2Container","alignItems"],children:(0,y.jsxs)(d.Ay.Group,{size:"small",children:[(0,y.jsx)(d.Ay.Button,{value:"flex-start",children:v("\u8d77\u59cb")}),(0,y.jsx)(d.Ay.Button,{value:"center",children:v("\u5c45\u4e2d")}),(0,y.jsx)(d.Ay.Button,{value:"flex-end",children:v("\u7ed3\u675f")}),(0,y.jsx)(d.Ay.Button,{value:"",children:v("\u5e73\u94fa")})]})})]}),(0,y.jsx)(P,{label:v("\u63a7\u4ef6\u95f4\u8ddd"),name:["style2Container","gap"],children:(0,y.jsx)(u.A,{style:I,addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,y.jsx)(P,{label:v("\u533a\u5757\u5bbd\u5ea6"),name:["style2Container","width"],children:(0,y.jsx)(u.A,{style:I,addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,y.jsx)(P,{label:v("\u533a\u5757\u9ad8\u5ea6"),name:["style2Container","height"],children:(0,y.jsx)(u.A,{style:I,addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,y.jsx)(P,{label:v("\u8fb9\u6846\u5bbd\u5ea6"),name:["style2Container","borderWidth"],children:(0,y.jsx)(u.A,{style:I,addonAfter:"px"})}),(0,y.jsx)(P,{label:v("\u8fb9\u6846\u7c7b\u578b"),name:["style2Container","borderStyle"],children:(0,y.jsxs)(d.Ay.Group,{size:"small",children:[(0,y.jsx)(d.Ay.Button,{value:"solid",children:v("\u5b9e\u7ebf")}),(0,y.jsx)(d.Ay.Button,{value:"dashed",children:v("\u865a\u7ebf")})]})}),(0,y.jsx)(P,{label:v("\u8fb9\u6846\u989c\u8272"),name:["style2Container","borderColor"],children:(0,y.jsx)(b.A,{})}),(0,y.jsx)(P,{label:v("\u5185\u8fb9\u8ddd-\u4e0a"),name:["style2Container","paddingTop"],children:(0,y.jsx)(u.A,{style:I,min:0,addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,y.jsx)(P,{label:v("\u5185\u8fb9\u8ddd-\u4e0b"),name:["style2Container","paddingBottom"],children:(0,y.jsx)(u.A,{style:I,min:0,addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,y.jsx)(P,{label:v("\u5185\u8fb9\u8ddd-\u5de6"),name:["style2Container","paddingLeft"],children:(0,y.jsx)(u.A,{style:I,min:0,addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,y.jsx)(P,{label:v("\u5185\u8fb9\u8ddd-\u53f3"),name:["style2Container","paddingRight"],children:(0,y.jsx)(u.A,{style:I,min:0,addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,y.jsx)(P,{label:v("\u8499\u677f\u900f\u660e\u5ea6"),name:["style2Container","maskOpacity"],children:(0,y.jsx)(u.A,{style:I,max:1,min:0})})]})},{key:"variable",label:v("\u53d8\u91cf"),forceRender:!0,children:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(P,{label:v("\u53ef\u89c1\u6027"),name:["variable","visible"],children:(0,y.jsx)(g,{restrict:{variableType:x.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x.ps.\u5e03\u5c14\u578b}})}),(0,y.jsx)(P,{label:v("\u53ef\u7f16\u8f91"),name:["variable","disabled"],children:(0,y.jsx)(g,{restrict:{variableType:x.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x.ps.\u5e03\u5c14\u578b}})})]})},{key:"children",label:v("\u63a7\u4ef6"),forceRender:!0,children:(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(N,{blockData:_,handleSaveLayout:S})})}]})})]})}},42999:(e,l,n)=>{n.d(l,{A:()=>p});var t=n(65043),a=n(37097),i=n(6051),r=n(36282),o=n(18650),d=n(81143),s=n(68374);const c=d.Ay.div`
    .color-layout {
        display: flex;
        align-items: center;
    }
 
    .background-layout {
        min-width: ${(0,s.D0)("25px")} !important;
        min-height: ${(0,s.D0)("25px")} !important;
        background-color: #000;
        border-radius: 2px;
    }
    .background-img {
        width: ${(0,s.D0)("23px")};
        height: ${(0,s.D0)("23px")};
        display: flex;
        align-items:center ;
        justify-content: center;
        cursor: pointer;
    }
    .allowed {
        cursor: not-allowed;
    }

`;var u=n(70579);const p=e=>{let{onChange:l,value:n,disabled:d=!1}=e;const[s,p]=(0,t.useState)(n);(0,t.useEffect)((()=>{p(n||"#000")}),[n]);const h=(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(a.Xq,{color:s,showMoreColor:!1,onChangeComplete:e=>{const{rgb:n}=e,t=`rgba(${n.r},${n.g},${n.b},${n.a})`;p(t),l&&l(t)}})});return(0,u.jsx)(c,{children:(0,u.jsx)("div",{className:"color-layout",children:(0,u.jsxs)(i.A,{children:[(0,u.jsx)("div",{className:"background-layout",style:{backgroundColor:s}}),!d&&(0,u.jsx)(r.A,{overlayClassName:"popover-sketch-picker",content:h,title:"",trigger:"click",placement:"bottom",destroyOnHidden:!0,arrow:!1,children:(0,u.jsx)("img",{className:"background-img "+(d?"allowed":""),src:o.Dp,alt:""})})]})})})}},51554:(e,l,n)=>{n.d(l,{A:()=>g});var t=n(65043),a=n(80077),i=n(16569),r=n(83720),o=n(79806),d=n(74117),s=n(93950),c=n.n(s),u=n(56543),p=n(75440),h=n(29977),m=n(6051),x=n(70579);const y=e=>{let{handleSelected:l,t:n}=e;return[{title:n?n("\u540d\u79f0"):"\u540d\u79f0",dataIndex:"variable_name",key:"variable_name"},{title:n?n("\u6807\u8bc6\u7b26"):"\u6807\u8bc6\u7b26",dataIndex:"code",key:"code"},{title:n?n("\u64cd\u4f5c"):"\u64cd\u4f5c",dataIndex:"code",key:"code",render:(e,n)=>(0,x.jsx)(m.A,{size:"middle",children:(0,x.jsx)("a",{onClick:()=>l(n),children:"\u9009\u62e9"})})}]},v=(e,l)=>{let{handleSelectedVariable:n=e=>console.log(e),isSetProgrammableParameters:s=!1}=e;const m=(0,h.A)(),v=(0,a.d4)((e=>e.template.resultData)),[g,j]=(0,t.useState)(!1),[b,f]=(0,t.useState)(),[A,C]=(0,t.useState)([]),[w,_]=(0,t.useState)([]),{t:k}=(0,d.Bd)(),S=(0,t.useMemo)((()=>m.map((e=>({...e,variable_name:null===e||void 0===e?void 0:e.name})))),[m]),I=(0,t.useMemo)((()=>v.map((e=>({...e,id:e.code})))),[v]);(0,t.useEffect)((()=>{g&&B()}),[g]);const B=()=>{if(b)switch(null===b||void 0===b?void 0:b.variableType){case u.oY.\u8f93\u5165\u53d8\u91cf:{const e=[...S.filter((e=>!(null!==b&&void 0!==b&&b.inputVarType)||e.variable_type===(null===b||void 0===b?void 0:b.inputVarType)))];_(e),C(e);break}case u.oY.\u4fe1\u53f7\u53d8\u91cf:case u.oY.\u7ed3\u679c\u53d8\u91cf:_(I),C(I);break;default:console.log("\u672a\u5904\u7406\u7684\u53d8\u91cf\u7c7b\u578b",null===b||void 0===b?void 0:b.variableType)}};(0,t.useImperativeHandle)(l,(()=>({open:e=>{f(e),j(!0)}})));const D=c()((async e=>{if(e){const l=A.filter((l=>{const n=l.variable_name.toLowerCase(),t=l.code.toLowerCase(),a=e.toLowerCase();return n.includes(a)||t.includes(a)}));_(l)}else _(A)}),200);return(0,x.jsxs)(p.A,{open:g,onCancel:()=>{j(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:[(0,x.jsx)(r.A,{allowClear:!0,onChange:e=>D(e.target.value),placeholder:k("\u540d\u79f0/\u5185\u90e8\u540d"),style:{width:"300px",marginBottom:"10px"}}),(0,x.jsx)(o.A,{rowKey:"code",columns:y({handleSelected:e=>{var l;!s||"Array"===(null===e||void 0===e?void 0:e.variable_type)&&"programmableParameters"===(null===e||void 0===e||null===(l=e.custom_array_tab)||void 0===l?void 0:l.useType)?(n(e,b),j(!1)):i.Ay.error("\u8bf7\u9009\u62e9\u81ea\u5b9a\u4e49\u6570\u7ec4\u7528\u9014\u4e3a\u7a0b\u63a7\u53c2\u6570\u7684\u53d8\u91cf")}}),dataSource:w})]})},g=(0,t.forwardRef)(v)},68358:(e,l,n)=>{n.d(l,{A:()=>h});var t=n(65043),a=n(48677),i=n(80077),r=n(14463),o=n(25055),d=n(36282),s=n(96603),c=n(14524),u=n(70579);const p=e=>{let{setting:l,onChange:n}=e;const[a]=o.A.useForm();(0,t.useEffect)((()=>{a.setFieldsValue({...l})}),[l]);return(0,u.jsx)(d.A,{content:(0,u.jsxs)(o.A,{form:a,name:"basic",labelCol:{style:{width:35}},onValuesChange:(e,l)=>{n(l)},children:[(0,u.jsx)(o.A.Item,{label:"\u4f4d\u7f6e",name:"placement",children:(0,u.jsxs)(s.Ay.Group,{size:"small",children:[(0,u.jsx)(s.Ay.Button,{value:"top",children:"\u4e0a"}),(0,u.jsx)(s.Ay.Button,{value:"right",children:"\u53f3"}),(0,u.jsx)(s.Ay.Button,{value:"bottom",children:"\u4e0b"}),(0,u.jsx)(s.Ay.Button,{value:"left",children:"\u5de6"})]})}),(0,u.jsx)(o.A.Item,{label:"\u5c3a\u5bf8",name:"size",children:(0,u.jsxs)(s.Ay.Group,{size:"small",children:[(0,u.jsx)(s.Ay.Button,{value:"default",children:"\u6b63\u5e38"}),(0,u.jsx)(s.Ay.Button,{value:"large",children:"\u5927"})]})})]}),title:"",trigger:"click",placement:"leftTop",children:(0,u.jsx)(c.A,{})})},h=e=>{let{children:l,open:n,onClose:t}=e;const o=(0,i.wA)(),{drawSetting:d}=(0,i.d4)((e=>e.split));return(0,u.jsx)(u.Fragment,{children:n&&(0,u.jsx)(a.A,{open:n,size:null===d||void 0===d?void 0:d.size,placement:null===d||void 0===d?void 0:d.placement,onClose:t,extra:(0,u.jsx)(p,{setting:d,onChange:e=>{o({type:r.cd,param:e})}}),children:l})})}},97320:(e,l,n)=>{n.d(l,{A:()=>d});n(65043);var t=n(80077),a=n(84856),i=n(67208),r=n(14463),o=n(41086);const d=()=>{const e=(0,t.wA)(),{saveLayout:l}=(0,a.A)(),n=async l=>{let{layout:n,newItem:t}=l;const a={...n,children:d(n.children,t)},[s]=await(0,i.PXE)({binder_ids:[null===n||void 0===n?void 0:n.binder_id]});await(0,i.Kv3)({binders:[{...s,layout:(0,o.gT)(a,null===n||void 0===n?void 0:n.binder_id)}]}),e({type:r.EH,param:s.binder_id})},d=(e,l)=>e.map((e=>e.id===l.id?l:e.children&&e.children.length>0?{...e,children:d(e.children,l)}:e)),s=async e=>{let{layout:n,newItem:t}=e;const a={...n,children:d(n.children,t)};await l(a)};return{updateLayoutItem:async e=>{let{layout:l,newItem:t}=e;null!==l&&void 0!==l&&l.binder_id?(console.log("\u754c\u9762\u5185\u5bb9-tab"),await n({layout:l,newItem:t})):(console.log("\u754c\u9762\u5185\u5bb9-\u4e3b\u7a97\u53e3"),await s({layout:l,newItem:t}))}}}}}]);
//# sourceMappingURL=810.442e0177.chunk.js.map