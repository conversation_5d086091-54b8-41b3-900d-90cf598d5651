{"version": 3, "file": "static/js/8722.c31e51f0.chunk.js", "mappings": "mMAMA,MAqBA,EArBoBA,KAChB,MAAMC,GAAWC,EAAAA,EAAAA,MAejB,MAAO,CACHC,iBAfqBC,UACrB,IACI,MAAMC,QAAYC,EAAAA,EAAAA,OACdD,GACAJ,EAAS,CACLM,KAAMC,EAAAA,GACNC,MAAOJ,GAGnB,CAAE,MAAOK,GACLC,QAAQC,IAAIF,EAChB,GAKH,C,4DCrBE,MAAMG,EAAkB,CAC3BC,uCAAQ,KACRC,2BAAM,KACNC,2BAAM,KAENC,uCAAQ,KACRC,uCAAQ,MAoICC,GA/HRN,EAAgBC,qCAIGM,EAAAA,GAAgBC,cAIZC,EAAAA,GAAkBC,+BAKlBD,EAAAA,GAAkBE,yBAQzCX,EAAgBE,yBAIGK,EAAAA,GAAgBK,cAIZH,EAAAA,GAAkBI,+BAKlBJ,EAAAA,GAAkBK,+BAKlBL,EAAAA,GAAkBM,yBAQzCf,EAAgBG,yBAIGI,EAAAA,GAAgBS,cAIZP,EAAAA,GAAkBQ,yBAKlBR,EAAAA,GAAkBM,yBAKlBN,EAAAA,GAAkBI,+BAKlBJ,EAAAA,GAAkBK,+BAQzCd,EAAgBI,qCAIGG,EAAAA,GAAgBC,cAIZC,EAAAA,GAAkBI,+BAKlBJ,EAAAA,GAAkBM,yBAQzCf,EAAgBK,qCAIGE,EAAAA,GAAgBK,cAIZH,EAAAA,GAAkBQ,yBAKlBR,EAAAA,GAAkBM,yBAKlBN,EAAAA,GAAkBI,+BAUhB,CAC1B,CACIK,iCAAkC,EAClCC,mBAAoB,KACpBC,mBAAoBC,KAAKC,UACrB,CACIC,cAAc,EACdC,cAAc,EACdC,aAAc,CACVC,WAAY,EACZC,cAAe,CACX,EAAG,CAAEC,KAAM,iCAASC,OAAQ,kBAAmBC,OAAQ,wCACvD,EAAG,CAAEF,KAAM,2BAAQC,OAAQ,cAAeC,OAAQ,yCAEtDC,UAAW,mDACXC,SAAU,MAItBC,aAAc,sBACdC,aAAc,sBACdC,gBAAiB,KACjBC,gBAAiB,KACjBC,YAAa,GAEjB,CACInB,iCAAkC,EAClCC,mBAAoB,KACpBC,mBAAoBC,KAAKC,UACrB,CACIC,cAAc,EACdC,cAAc,EACdC,aAAc,CACVC,WAAY,EACZC,cAAe,CACX,EAAG,CAAEC,KAAM,iCAASC,OAAQ,iBAAkBC,OAAQ,wCACtD,EAAG,CAAEF,KAAM,iCAASC,OAAQ,kBAAmBC,OAAQ,wCACvD,EAAG,CAAEF,KAAM,2BAAQC,OAAQ,eAAgBC,OAAQ,yCAEvDC,UAAW,mDACXC,SAAU,MAItBC,aAAc,sBACdC,aAAc,sBACdC,gBAAiB,KACjBC,gBAAiB,KACjBC,YAAa,GAEjB,CACInB,iCAAkC,EAClCC,mBAAoB,KACpBC,mBAAoBC,KAAKC,UACrB,CACIC,cAAc,EACdC,cAAc,EACdC,aAAc,CACVC,WAAY,EACZC,cAAe,CACX,EAAG,CAAEC,KAAM,2BAAQC,OAAQ,oBAC3B,EAAG,CAAED,KAAM,2BAAQC,OAAQ,iBAE/BE,UAAW,mDACXC,SAAU,MAItBC,aAAc,sBACdC,aAAc,sBACdC,gBAAiB,KACjBC,gBAAiB,KACjBC,YAAa,GAIjB,CACInB,iCAAkC,EAClCC,mBAAoB,qBACpBC,mBAAoBC,KAAKC,UACrB,CACIC,cAAc,EACdC,cAAc,EACdC,aAAc,CACVC,WAAY,EACZC,cAAe,CACX,EAAG,CAAEC,KAAM,iCAASC,OAAQ,iBAAkBC,OAAQ,wCACtD,EAAG,CAAEF,KAAM,iCAASC,OAAQ,kBAAmBC,OAAQ,yCAE3DC,UAAW,mDACXC,SAAU,MAItBC,aAAc,sBACdC,aAAc,sBACdC,gBAAiB,KACjBC,gBAAiB,KACjBC,YAAa,GAEjB,CACInB,iCAAkC,EAClCC,mBAAoB,qBACpBC,mBAAoBC,KAAKC,UACrB,CACIC,cAAc,EACdC,cAAc,EACdC,aAAc,CACVC,WAAY,EACZC,cAAe,CACX,EAAG,CAAEC,KAAM,iCAASC,OAAQ,iBAAkBC,OAAQ,wCACtD,EAAG,CAAEF,KAAM,iCAASC,OAAQ,kBAAmBC,OAAQ,wCACvD,EAAG,CAAEF,KAAM,2BAAQC,OAAQ,eAAgBC,OAAQ,yCAEvDC,UAAW,mDACXC,SAAU,MAItBC,aAAc,sBACdC,aAAc,sBACdC,gBAAiB,KACjBC,gBAAiB,KACjBC,YAAa,I,wEClQrB,MA2CA,EA3CyBC,KACrB,MAAMlD,GAAWC,EAAAA,EAAAA,MACXkD,GAAaC,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,OAAOH,aAE/CI,EAAiBpD,UACnB,IACI,MAAMC,QAAYoD,EAAAA,EAAAA,OACdpD,GACAJ,EAAS,CAAEM,KAAMmD,EAAAA,GAAwBjD,MAAOJ,GAExD,CAAE,MAAOK,GACLC,QAAQC,IAAI,MAAOF,EACvB,GAwBJ,MAAO,CACH0C,aACAI,iBACAG,uBAxB2BvD,UAAoC,IAA7B,GAAEwD,EAAE,gBAAEC,GAAiBC,EACzD,IACI,MAAMC,EAAmB,OAAVX,QAAU,IAAVA,OAAU,EAAVA,EAAYY,MAAKC,GAAKA,EAAElC,mCAAqC6B,IAEtEM,EAAY,IACXH,EACH9B,mBAAoBC,KAAKC,UAAU,IAE5BD,KAAKiC,MAAY,OAANJ,QAAM,IAANA,OAAM,EAANA,EAAQ9B,oBACtBK,aAAcuB,WAIhBO,EAAAA,EAAAA,KAAqBF,SAErBV,GACV,CAAE,MAAO9C,GACLC,QAAQC,IAAI,MAAOF,EACvB,GAOH,C,oQCnCL,MA+BA,EA/B2B2D,KACvB,MAAM,iBAAEC,IAAqBC,EAAAA,EAAAA,MACvB,cAAEC,IAAkBC,EAAAA,EAAAA,MACpB,WAAEC,IAAeC,EAAAA,EAAAA,MACjB,eAAEC,IAAmBC,EAAAA,EAAAA,MACrB,cAAEC,IAAkBC,EAAAA,EAAAA,MACpB,iBAAE5E,IAAqBH,EAAAA,EAAAA,MACvB,gBAAEgF,IAAoBC,EAAAA,EAAAA,MACtB,kBAAEC,IAAsBC,EAAAA,EAAAA,MACxB,oBAAEC,IAAwBC,EAAAA,EAAAA,KAiBhC,MAAO,CACHC,gBAfoBlF,gBACdmF,QAAQC,IAAI,CACdlB,IACAE,IACAE,IACAE,IACAE,IACA3E,IACA6E,IACAE,IACAE,KACF,EAKL,E,mCCrCE,MAAMK,E,SAAYC,GAAOC,GAAG;;;;;iBCiBnC,MAAM,YAAEC,GAAqD,SAArCC,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,6BAAAA,2BAAAA,6BAAAA,iBAAAA,QAAAA,iBAAAA,gBAAAA,iBAAAA,wBAAAA,oBAAAA,uBAAAA,iBAAAA,uBAAAA,+BAAAA,yBAAAA,8BAAAA,6BAAAA,iBAAAA,uBAAYC,qBAAkCC,OAAOC,QAAQ,YAAc,CAAC,EAqFpG,EAnFkBC,KACd,MAAMhG,GAAWC,EAAAA,EAAAA,OAEX,WAAEgG,IAAeC,EAAAA,EAAAA,KACjBC,GAAW/C,EAAAA,EAAAA,KAAYC,GAASA,EAAM+C,SAASC,YAC/C,UAAEC,EAAS,OAAEC,IAAWC,EAAAA,EAAAA,MACxB,UAAEC,EAAS,cAAEC,IAAkBC,EAAAA,EAAAA,MAC/B,aAAEC,IAAiBC,EAAAA,EAAAA,MACnB,cAAEtC,IAAkBC,EAAAA,EAAAA,MACpB,gBAAEsC,IAAoBC,EAAAA,EAAAA,MACtB,cAAEC,IAAkBC,EAAAA,EAAAA,MAEpB,gBAAE5B,GAAoBjB,KACtB,UAAE8C,IAAcC,EAAAA,EAAAA,MAQtBC,EAAAA,EAAAA,YAAU,KACNC,GAAM,GACP,IAEH,MAAMA,EAAOlH,UACTH,EAAS,CACLM,KAAMgH,EAAAA,GACN9G,OAAO,KAEX+G,EAAAA,EAAAA,IAAajB,SAGPhB,QAAQC,IAAI,CACdqB,IACArC,IACAuC,IACA9G,GAASwH,EAAAA,EAAAA,QAIbnC,IACA6B,UAEMR,IAENM,IAEAS,YAAW,KACkC,SAArC7B,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,6BAAAA,2BAAAA,6BAAAA,iBAAAA,QAAAA,iBAAAA,gBAAAA,iBAAAA,wBAAAA,oBAAAA,uBAAAA,iBAAAA,uBAAAA,+BAAAA,yBAAAA,8BAAAA,6BAAAA,iBAAAA,uBAAYC,sBACZF,EAAY+B,GAAG,8BAA8B,CAACC,EAAOC,KAC5B,IAADC,EAAhB/B,OAAOgC,SACPpH,QAAQC,IAAI,kCAAsB,OAAHiH,QAAG,IAAHA,OAAG,EAAHA,EAAKG,oBAAuB,OAAHH,QAAG,IAAHA,GAAS,QAANC,EAAHD,EAAKI,YAAI,IAAAH,OAAN,EAAHA,EAAWI,QAAY,OAAHL,QAAG,IAAHA,OAAG,EAAHA,EAAKI,MAGrFvB,EAAa,OAAHmB,QAAG,IAAHA,OAAG,EAAHA,EAAKG,SAAa,OAAHH,QAAG,IAAHA,OAAG,EAAHA,EAAKI,KAAK,GAE3C,GACD,IAAK,EAGNE,GAAaC,EAAAA,EAAAA,UAAQ,IAChBhC,EAASpC,MAAKC,GAAKA,EAAEL,KAAO4C,KACpC,CAACJ,EAAUI,IAMd,OACI6B,EAAAA,EAAAA,KAAC5C,EAAS,CAAA6C,SAEFH,IACIE,EAAAA,EAAAA,KAACE,EAAAA,EAAY,CACTC,SATEtE,IAClBgC,EAAWhC,EAAU,EASLuE,OAAkB,OAAVN,QAAU,IAAVA,OAAU,EAAVA,EAAYpE,UAIxB,C,iFC7FpB,MAyEA,EAzEiBgB,KACb,MAAM9E,GAAWC,EAAAA,EAAAA,MAiEjB,MAAO,CACH4E,cAjEkB1E,UAClB,IACI,MAAMC,QAAYqI,EAAAA,EAAAA,OAClB,GAAIrI,EAAK,CACL,MAAMsI,EAAYtI,EAAIuI,UACtB3I,EAAS,CACLM,KAAMsI,EAAAA,GACNpI,MAAOkI,GAEf,CACJ,CAAE,MAAOjI,GACLC,QAAQC,IAAIF,EAChB,GAsDAoI,UAnDc1I,UAIX,IAJkB,SACrB2I,EAAQ,WACRC,EAAU,WACVC,GAAa,GAChBnF,EACG,IACI,MAAMzD,QAAY6I,EAAAA,EAAAA,KAAa,CAAEH,aACjC,GAAI1I,EAAK,CACL,MAAM8I,EAAO,IAAIC,KAAK,CAAC/I,GAAM,WAAY,CAAEE,KAAM,eAC3C8I,EAAcC,SAASC,cAAc,SACrCC,EAAYC,IAAIC,gBAAgBP,GACtCE,EAAYM,IAAMH,EAClBH,EAAYzF,GAAKmF,EACjBM,EAAYO,OACZP,EAAYQ,OAEZP,SAASQ,KAAKC,YAAYV,GAC1BA,EAAYW,iBAAiB,SAAS,KAC9Bf,EACAI,EAAYQ,OAEZR,EAAYY,SAEZjB,GAEAA,GAAW,EAAMD,GAErBU,IAAIS,gBAAgBV,EAAU,IAC/B,EACP,CACJ,CAAE,MAAO9I,GACLC,QAAQD,MAAM,uBAAwBA,EAC1C,GAoBAyJ,YAlBgB/J,MAAO2I,EAAUqB,KACjC,IACI,MAAMC,EAAef,SAASgB,eAAevB,GACzCsB,IACAA,EAAaJ,SACTG,GACAA,GAAW,EAAMrB,GAErBU,IAAIS,gBAAgBG,EAAaV,KAEzC,CAAE,MAAOjJ,GACLC,QAAQD,MAAM,wBAAyBA,EAC3C,GAOH,C,sDC5EE,MAAMU,EAAkB,CAC3BmJ,gBAAK,EACLlJ,gBAAK,EACLI,gBAAK,EACLI,gBAAK,EACL2I,gBAAK,EACLC,gBAAK,GAIIC,EAA4B,CACrC,CAACtJ,EAAgBmJ,eAAM,EACvB,CAACnJ,EAAgBC,eAAM,EACvB,CAACD,EAAgBK,eAAM,EACvB,CAACL,EAAgBS,eAAM,EACvB,CAACT,EAAgBoJ,eAAM,EACvB,CAACpJ,EAAgBqJ,eAAM,GAIdnJ,EAAoB,CAC7BK,iCAAO,kBACPD,iCAAO,iBACPI,2BAAM,mBACNP,iCAAO,kBACPC,2BAAM,cACNI,2BAAM,gBAYMR,EAAgBK,cAIZH,EAAkBI,+BAKlBJ,EAAkBK,+BAKlBL,EAAkBQ,wB,sGC7C/B,MAAM6I,EAAgB,CACzBC,uCAAQ,EACRC,uCAAQ,EACRC,uCAAQ,GA6DZ,EAzDkBvG,KACd,MAAMtE,GAAWC,EAAAA,EAAAA,OACX,eAAEsD,IAAmBL,EAAAA,EAAAA,KA0BrBmB,EAAmBlE,UACrB,IACI,MAAMC,QAAY0K,EAAAA,EAAAA,OAClB1K,EAAI2K,kBAAoB3K,EAAI2K,kBAAkBC,QAAQ,MAAO,KACzD5K,GACAJ,EAAS,CAAEM,KAAM2K,EAAAA,GAAsBzK,MAAOJ,GAEtD,CAAE,MAAOK,GACLC,QAAQD,MAAMA,EAClB,GAaJ,MAAO,CACHyK,WA/Ce/K,gBACToD,IAEN,MAAM4H,QAAiBC,EAAAA,EAAAA,OAInBC,IAASX,EAAcC,sCACA,IAApBQ,EAASG,OAOQ,IAApBH,EAASG,OAKbtL,EAAS,CAAEM,KAAMiL,EAAAA,GAAsB/K,MAAOI,EAAAA,GAAgBG,2BAJ1Df,EAAS,CAAEM,KAAMiL,EAAAA,GAAsB/K,MAAOI,EAAAA,GAAgBE,2BAN9Dd,EAAS,CAAEM,KAAMiL,EAAAA,GAAsB/K,MAAOI,EAAAA,GAAgBC,sCAUG,EA4BrEwD,mBACAmH,mBAZuBrL,UACvB,UACUsL,EAAAA,EAAAA,KAAiBjD,GACvBnE,GACJ,CAAE,MAAO5D,GACLC,QAAQD,MAAMA,EAClB,GAOH,C,gFChEL,IAAIiL,GAAQ,EAEL,MAAML,EAAO,CAChBM,eAAI,SACJC,eAAI,WACJC,qBAAK,YAoCT,EAjCmB3G,KACf,MAAM4G,GAAgB1I,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,OAAOwI,gBAClD9L,GAAWC,EAAAA,EAAAA,MAEXgF,EAAoB9E,UACtB,MAAMC,QAAY2L,EAAAA,EAAAA,OAWlB,OAVI3L,IAEiB,WAAbA,EAAIiL,MAAqBK,IACzBM,EAAAA,EAAAA,OACAN,GAAQ,GAERA,GAAQ,EAEZ1L,EAAS,CAAEM,KAAM2L,EAAAA,GAAuBzL,MAAOJ,KAE5CA,CAAG,EAUd,MAAO,CACH0L,gBACA7G,oBACAiH,oBAVwB/L,gBACNgM,EAAAA,EAAAA,KAAkB3D,IAEhCvD,GACJ,EAOH,C", "sources": ["hooks/useHardware.js", "pages/sysHome/constants.js", "hooks/useSysHomeLayout.js", "hooks/useInitSystemRedux.js", "pages/subWindow/style.js", "pages/subWindow/index.js", "hooks/useAudio.js", "pages/sysHome/layout/constants.js", "hooks/useSystem.js", "hooks/useStandby.js"], "names": ["useHardware", "dispatch", "useDispatch", "initHardwareList", "async", "res", "getHardware", "type", "HARDWARE_LIST", "param", "error", "console", "log", "SYS_HOME_ID_MAP", "硬件配置首页", "单站首页", "多站首页", "从机单站首页", "从机多站首页", "LAYOUT_CONFIGS", "LAYOUT_TYPE_MAP", "布局2", "MODULE_TARGET_MAP", "硬件管理器", "站管理器", "布局3", "项目管理器", "模板管理器", "数据分析", "布局4", "全局监控", "station_or_home_layout_config_id", "station_or_home_id", "layout_config_json", "JSON", "stringify", "showSiderbar", "showStatuBar", "layoutConfig", "layoutType", "modulesConfig", "name", "target", "iconId", "mainTitle", "subTitle", "created_time", "updated_time", "created_user_id", "updated_user_id", "delete_flag", "useSysHomeLayout", "layoutList", "useSelector", "state", "global", "initLayoutList", "getSysHomeLayoutList", "UPDATE_SYS_HOME_LAYOUT", "updateHomeLayoutConfig", "id", "newLayoutConfig", "_ref", "layout", "find", "i", "newLayout", "parse", "putSysHomeLayoutList", "useInitSystemRedux", "initSystemConfig", "useSystem", "initUnitsData", "useUnit", "initWidget", "useWidget", "initModuleData", "useModuleDataSource", "initAudioData", "useAudio", "initStationInfo", "useStation", "initStandByConfig", "useStandby", "initGlobalProjectID", "useGlobalMonitoring", "initSystemRedux", "Promise", "all", "Container", "styled", "div", "ip<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_IS_BROWSER", "window", "require", "SubWindow", "saveLayout", "useTemplateLayout", "pageList", "template", "pageData", "projectId", "pageId", "useParams", "handleMsg", "initPublisher", "useSubTask", "initPageData", "usePage", "initTemplateMap", "useTemplateMap", "showSubWindow", "useElectron", "initRedux", "useInitRedux", "useEffect", "init", "GLOBAL_READONLY", "setProjectId", "initInputVariables", "setTimeout", "on", "event", "msg", "_msg$data", "isDebug", "subTopic", "data", "UICmd", "dialogData", "useMemo", "_jsx", "children", "SplitContent", "onResize", "config", "getAudioList", "sortedRes", "reverse", "AUDIO", "playAudio", "audio_id", "onAudioEnd", "isLoopPlay", "getAudioInfo", "file", "File", "audioPlayer", "document", "createElement", "objectURL", "URL", "createObjectURL", "src", "load", "play", "body", "append<PERSON><PERSON><PERSON>", "addEventListener", "remove", "revokeObjectURL", "removeAudio", "onCallBack", "audioElement", "getElementById", "布局1", "布局5", "布局6", "LAYOUT_TYPE_MODULE_NUMBER", "APP_WORK_MODE", "硬件配置模式", "主机工作模式", "从机工作模式", "getSystemConfig", "project_directory", "replace", "GLOBAL_SYSTEM_CONFIG", "afterLogin", "hostList", "getStationHostList", "mode", "length", "UPDATE_SYS_HOME_TYPE", "updateSystemConfig", "saveSystemConfig", "frist", "主机", "从机", "备用机", "standbyConfig", "getStandbyConfig", "standbySyncToShareHttp", "GLOBAL_STANDBY_CONFIG", "updateStandByConfig", "saveStandbyConfig"], "sourceRoot": ""}