{"version": 3, "file": "static/js/910.d13aea6f.chunk.js", "mappings": "2NAIA,MAoCA,EApCiBA,IAEV,IAFW,MACdC,EAAK,UAAEC,EAAS,GAAEC,EAAE,OAAEC,EAAM,OAAEC,EAAM,SAAEC,GAAW,KAAUC,GAC9DP,EACG,MAAOQ,EAAUC,IAAeC,EAAAA,EAAAA,UAAST,IAEzCU,EAAAA,EAAAA,YAAU,KACNF,EAAYR,EAAM,GACnB,CAACA,KACSW,EAAAA,EAAAA,aACTC,KAAS,CAACC,EAAWC,EAAOC,IAAUX,EAAOS,EAAWC,EAAOC,IAAQ,KACvE,IAUJ,OACIC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CACFf,GAAI,OAAOA,IAEXgB,QAAS,GACTC,WAAY,EACZhB,OAAQA,EACRF,UAAWI,OAAWe,EAXRC,CAACR,EAAWC,KAC9Bb,EAAUY,EAAWC,EAAM,EAWvBV,OAAQC,OAAWe,EAhBRE,CAACT,EAAWC,EAAOC,KAClCP,EAAYO,GACZX,EAAOS,EAAWC,EAAOC,EAAM,EAe3BQ,oBAAqBhB,EACrBF,SAAUA,KACNC,GARC,OAAOJ,IASd,C", "sources": ["components/split/splitHor/index.js"], "names": ["_ref", "sizes", "onDragEnd", "id", "render", "onDrag", "disabled", "rest", "initSize", "setInitSize", "useState", "useEffect", "useCallback", "debounce", "direction", "track", "style", "_jsx", "Split", "minSize", "snapOffset", "undefined", "handleDragEnd", "handleDrag", "gridTemplateColumns"], "sourceRoot": ""}