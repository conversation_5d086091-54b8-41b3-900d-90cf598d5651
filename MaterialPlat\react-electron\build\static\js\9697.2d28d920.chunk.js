"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[9697],{49697:(e,n,l)=>{l.r(n),l.d(n,{default:()=>M});var a=l(65043),i=l(80077),t=l(6051),r=l(56434),o=l.n(r),u=l(19853),d=l.n(u),s=l(36497),v=l(83720),c=l(81143),b=l(36950),h=l(70579);const m=(0,c.Ay)(s.A)`
    &.ant-select.ant-select-disabled .ant-select-selector {
        color: #000000 !important;
    }
`,p=e=>{var n,l,r,o,u;let{value:d,dimension:s,unit:c,onValueChange:p,onUnitChange:_,status:f,disabled:g,readOnly:x,number_tab:y}=e;const C=(0,i.d4)((e=>e.global.unitList)),j=(0,a.useMemo)((()=>{var e;const n=null===(e=C.find((e=>e.id===s)))||void 0===e?void 0:e.units;return null===n||void 0===n?void 0:n.filter((e=>{var n;return!(null!==y&&void 0!==y&&null!==(n=y.unit)&&void 0!==n&&n.lockChannels.includes(e.id))})).map((e=>({label:e.name,value:e.id,proportion:e.proportion})))}),[C,null===(n=y.unit)||void 0===n?void 0:n.unitType,null===y||void 0===y||null===(l=y.unit)||void 0===l?void 0:l.lockChannels,s]),w=(0,a.useMemo)((()=>!(!j||!c)&&j.some((e=>e.value===c))),[j,c]),[M,A]=(0,a.useState)("");(0,a.useEffect)((()=>{var e;A((0,b.jq)(null===(e=y.format)||void 0===e?void 0:e.formatType,(0,b.tJ)(d,s,c),(0,b.Vl)(y)))}),[d,s,c,y]);return(0,h.jsxs)(t.A,{style:{pointerEvents:x&&"none"},children:[(0,h.jsx)(v.A,{disabled:g,value:M,status:f,className:"input-width",onChange:e=>A(e.target.value),onBlur:e=>(()=>{var e;if(Number.isNaN(M))return;const n=null===C||void 0===C||null===(e=C.find((e=>e.id===s)))||void 0===e?void 0:e.default_unit_id;p((0,b.tJ)(Number(M),s,n,c))})(e.target.value),onKeyPress:e=>{const n=e.keyCode||e.which;(n<48||n>57)&&(46!==n||e.target.value.includes("."))&&45!==n&&(n<65||n>90)&&e.preventDefault()}}),"\u65e0"!==(null===(r=y.unit)||void 0===r?void 0:r.unitType)&&(0,h.jsx)(m,{showSearch:!0,status:f,optionFilterProp:"label",disabled:g||!(null!==(o=y.unit)&&void 0!==o&&o.isUserConversion),value:w?c:void 0,className:"input-width",options:j||[],onChange:(e,n)=>(e=>{var n;const l=null===C||void 0===C||null===(n=C.find((e=>e.id===s)))||void 0===n?void 0:n.default_unit_id;_({value:(0,b.tJ)(Number(M),s,l,c),unit:null===e||void 0===e?void 0:e.value,proportion:null===e||void 0===e?void 0:e.proportion})})(n),suffixIcon:g||null===(u=y.unit)||void 0===u||!u.isUserConversion?null:void 0})]})},_=e=>{var n,l,t,r;let{number_tab:o,disabled:u,value:d,onChange:v}=e;const c=(0,i.d4)((e=>e.template.signalGroups)),b=(0,a.useMemo)((()=>{var e,n;return null!==(e=null===(n=c.find((e=>{var n;return e.group_id===(null===(n=o.channel)||void 0===n?void 0:n.channelType)})))||void 0===n?void 0:n.variable_ids.filter((e=>{var n,l;return!(null!==(n=o.channel)&&void 0!==n&&null!==(l=n.lockChannels)&&void 0!==l&&l.includes(null===e||void 0===e?void 0:e.code))})).filter(Boolean))&&void 0!==e?e:[]}),[null===(n=o.channel)||void 0===n?void 0:n.channelType,null===(l=o.channel)||void 0===l?void 0:l.lockChannels]);return(0,h.jsx)(h.Fragment,{children:"\u65e0"!==(null===(t=o.channel)||void 0===t?void 0:t.channelType)&&(0,h.jsx)(s.A,{showSearch:!0,optionFilterProp:"variable_name",disabled:u||!(null!==(r=o.channel)&&void 0!==r&&r.isUserConversion),value:null===d||void 0===d?void 0:d.type,className:"input-width",fieldNames:{label:"variable_name",value:"code"},options:b||[],onChange:e=>v({type:e})})})};var f=l(56543);const g=e=>{let{type:n,calculate:l,currentValue:a}=e;switch(n){case f.Hv.MAX:return Math.max(...l);case f.Hv.MIN:return Math.min(...l);case f.Hv.AVG:return l.reduce(((e,n)=>e+n),0)/l.length;case f.Hv.MID:return(e=>{const n=e.slice().sort(((e,n)=>e-n)),l=n.length;return l%2===1?n[Math.floor(l/2)]:(n[l/2-1]+n[l/2])/2})(l);default:return a}},x=e=>{var n;let{value:l,onChange:a,status:i,number_tab:r,disabled:o}=e;const u=null===r||void 0===r||null===(n=r.multipleMeasurements)||void 0===n?void 0:n.measurementCounts;return u<=1?(0,h.jsx)(h.Fragment,{}):(0,h.jsx)(t.A,{direction:"vertical",children:new Array(u).fill(0).map(((e,n)=>{var t;return(0,h.jsx)(p,{value:null===l||void 0===l||null===(t=l.calculate)||void 0===t?void 0:t[n],dimension:l.unitType,unit:l.unit,disabled:o,number_tab:r,onValueChange:e=>((e,n)=>{var i;const t=null===l||void 0===l?void 0:l.calculate.map(((l,a)=>n===a?e:l)),o=g({type:null===(i=r.multipleMeasurements)||void 0===i?void 0:i.measurementType,calculate:t,currentValue:l.value});a({value:o,calculate:t})})(e,n),onUnitChange:a,status:i})}))})};var y=l(55518),C=l(13313);const j=e=>{var n;let{newValue:l,variable:a,unitList:i}=e;const{number_tab:t,reasonable_val_tab:r}=a,o=null===t||void 0===t||null===(n=t.multipleMeasurements)||void 0===n?void 0:n.measurementCounts;let u=(null===l||void 0===l?void 0:l.calculate)||[];return 1===o&&(u=[l.value]),(e=>{var n;let{value:l,number_tab:a,calculate:i}=e;switch(null===a||void 0===a||null===(n=a.format)||void 0===n?void 0:n.numberRequire){case f.Uk.NOT_0:return 0!==l&&null!==i&&void 0!==i&&i.every((e=>0!==e))?"":{errorMsg:"\u6570\u5b57\u4e0d\u80fd\u7b49\u4e8e0"};case f.Uk.GT_0:return l>0&&null!==i&&void 0!==i&&i.every((e=>e>0))?"":{errorMsg:"\u6570\u5b57\u4e0d\u80fd\u5c0f\u4e8e\u7b49\u4e8e0"};case f.Uk.GE_0:return l>=0&&null!==i&&void 0!==i&&i.every((e=>e>=0))?"":{errorMsg:"\u6570\u5b57\u4e0d\u80fd\u5c0f\u4e8e0"};default:return""}})({value:l.value,number_tab:t,calculate:u})||(e=>{var n,l,a;let{value:i,calculate:t,unitList:r,reasonable_val_tab:o,number_tab:u,variable:d={}}=e;const{minParam:s,maxParam:v,reasonableType:c}=o;if(c===f.Uk.EMPTY)return"";const h=(null===d||void 0===d?void 0:d.default_val)||{},m=(0,b.jq)(null===(n=u.format)||void 0===n?void 0:n.formatType,(0,b.tJ)(s,h.unitType,h.unit),(0,b.Vl)(u)),p=(0,b.jq)(null===(l=u.format)||void 0===l?void 0:l.formatType,(0,b.tJ)(v,h.unitType,h.unit),(0,b.Vl)(u));if(1===(null===(a=u.multipleMeasurements)||void 0===a?void 0:a.measurementCounts)){if(i<m)return{type:C.g.onExceedMin,errorMsg:"\u8d85\u4e0b\u9650"};if(i>p)return{type:C.g.onExceedMax,errorMsg:"\u8d85\u4e0a\u9650"}}else{if(i<m||t.some((e=>e<m)))return{type:C.g.onExceedMin,errorMsg:"\u8d85\u4e0b\u9650"};if(i>p||t.some((e=>e>p)))return{type:C.g.onExceedMax,errorMsg:"\u8d85\u4e0a\u9650"}}return""})({value:l.value,calculate:l.calculate,unitList:i,reasonable_val_tab:r,number_tab:t,variable:a})},w=e=>{var n;let{onChange:l,disabled:r,variable:o,onError:u}=e;const{default_val:s,number_tab:v,reasonable_val_tab:c}=o,b=(0,i.d4)((e=>e.global.unitList)),[m,f]=(0,a.useState)(s),[g,y]=(0,a.useState)("");(0,a.useEffect)((()=>{d()(s,m)||f(s)}),[s]);const C=e=>{const n={...m,...e},a=j({newValue:n,variable:o,unitList:b});u(a),y(a?"error":""),a||(f(n),l(n))};return(0,h.jsx)("div",{children:(0,h.jsxs)(t.A,{direction:"vertical",children:[(0,h.jsxs)(t.A,{children:[(0,h.jsx)(_,{number_tab:v,disabled:r,value:m,onChange:C}),(0,h.jsx)(p,{value:m.value,dimension:m.unitType,unit:m.unit,onValueChange:e=>C({value:e}),onUnitChange:e=>C({...e}),disabled:r,readOnly:(null===v||void 0===v||null===(n=v.multipleMeasurements)||void 0===n?void 0:n.measurementCounts)>1,number_tab:v,unitList:b,status:g})]}),(0,h.jsx)(x,{value:m,disabled:r,number_tab:v,onChange:C,status:g})]})})},M=e=>{let{variable:n,disabled:l,onChange:a,onError:t}=e;const r=(0,i.d4)((e=>e.template.signalGroups));return(0,h.jsx)(y.A,{variable:n,disabled:l,onChange:a,render:e=>{let{innerDisabled:l}=e;return(0,h.jsx)(w,{variable:n,disabled:l,onChange:e=>{(e=>{const{number_tab:l,default_val:i}=n,t=o()(n);if(t.default_val=e,i.unit!==e.unit&&(t.number_tab.unit.unit=i.unit),i.type!==e.type){var u;const n=null===(u=r.find((e=>e.group_id===l.channel.channelType)))||void 0===u?void 0:u.variable_ids.find((n=>n.code===e.type));n&&(t.number_tab.channel.channel=e.type,t.number_tab.unit.unitType=n.dimension_id,t.number_tab.unit.unit=n.unit_id,t.default_val.unit=n.unit_id,t.default_val.unitType=null===n||void 0===n?void 0:n.dimension_id)}a(t)})(e)},onError:t})}})}},55518:(e,n,l)=>{l.d(n,{A:()=>k});var a=l(65043),i=l(74117),t=l(56543),r=l(81143),o=l(68374),u=l(18650);const d=0,s="left";var v=l(70579);const c=r.Ay.div`
    width: ${(0,o.D0)("20px")};
    height: ${(0,o.D0)("20px")};
    background-size: ${(0,o.D0)("20px")} ${(0,o.D0)("20px")};
    background-image: url(${e=>{let{isConstant:n}=e;return n?u.fd:u.Mo}});
`,b=e=>{let{variable:n,onChange:l,disabled:a}=e;const{default_val:i,is_fx:t}=n;return!t||a?(0,v.jsx)(v.Fragment,{}):(0,v.jsx)(c,{isConstant:i.isConstant===d,onClick:()=>{l({...n,default_val:{...i,isConstant:0===(null===i||void 0===i?void 0:i.isConstant)?1:0}})}})};var h=l(95206),m=l(34154),p=l(67208),_=l(16090),f=l(36497),g=l(29977);const x=e=>{var n;let{disabled:l,variable:i,handleChange:t}=e;const r=(0,g.A)(),o=(0,a.useMemo)((()=>(null===r||void 0===r?void 0:r.filter((e=>e.variable_type===i.variable_type&&e.id!==i.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[r,i]);return(0,v.jsx)(f.A,{showSearch:!0,optionFilterProp:"labelName",disabled:l,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===i||void 0===i||null===(n=i.default_val)||void 0===n?void 0:n.variable_id,options:o,onChange:(e,n)=>t(n)})},y=e=>{let{disabled:n,content:l,buttonType:i,actionId:r,script:o}=e;const[u,d]=(0,a.useState)(!1),{startAction:s}=(0,_.A)(),c=()=>{i!==m.NR.\u52a8\u4f5c?i!==m.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{d(!0),await(0,p.O5k)({script:o,result_type:t.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{d(!1)}})():(async()=>{try{r&&(d(!0),await s({action_id:r}))}catch(e){console.log("err when handleSubmitAction",e)}finally{d(!1)}})()};return(0,v.jsx)(h.Ay,{loading:u,disabled:n,className:"button-width",onClick:()=>c(),children:l})},C=r.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:n}=e;return n?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,o.D0)("80px")};
        pointer-events: auto;
    }
`,j=e=>{let{disabled:n,variable:l,render:a,onChange:i,buttonShow:t}=e;const{button_variable_tab:r,default_val:o}=l;return(0,v.jsx)(C,{isLeft:(null===r||void 0===r?void 0:r.position)===s,children:1===o.isConstant?(0,v.jsx)(x,{disabled:n,variable:l,handleChange:e=>{i({...l,default_val:{...o,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,v.jsxs)(v.Fragment,{children:[t&&(null===r||void 0===r?void 0:r.isEnable)&&(0,v.jsx)(y,{...r,disabled:n}),a()]})})};var w=l(12624),M=l(32513);const A=e=>{let{variable:n,disabled:l=!1,onChange:a,usableShowType:i="checkbox"}=e;return null!==n&&void 0!==n&&n.is_enable?"switch"===i?(0,v.jsx)(w.A,{disabled:l,checked:null===n||void 0===n?void 0:n.is_feature,onChange:e=>{a({...n,is_feature:e})}}):(0,v.jsx)(M.A,{disabled:l,checked:null===n||void 0===n?void 0:n.is_feature,onChange:e=>{a({...n,is_feature:e.target.checked})}}):(0,v.jsx)(v.Fragment,{})},T=r.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,k=e=>{let{variable:n,disabled:l=!1,onChange:a,render:r,usableShow:o=!0,buttonShow:u=!0,fxShow:d=!0,nameShow:s=!0,usableShowType:c}=e;const{t:h}=(0,i.Bd)(),m=l||(n.variable_type===t.ps.\u5e03\u5c14\u578b?(null===n||void 0===n?void 0:n.is_enable)&&(null===n||void 0===n?void 0:n.is_feature):(null===n||void 0===n?void 0:n.is_enable)&&!(null!==n&&void 0!==n&&n.is_feature));return(0,v.jsxs)(T,{children:[(o||s)&&(0,v.jsx)("div",{className:"input-render-left",children:(0,v.jsxs)("div",{children:[o&&(0,v.jsx)(A,{variable:n,disabled:l,onChange:a,usableShowType:c}),s&&(0,v.jsx)("div",{className:"variable_name",children:h(n.name)})]})}),(0,v.jsxs)("div",{className:"input-render-right",children:[d&&(0,v.jsx)(b,{variable:n,onChange:a,disabled:m}),(0,v.jsx)(j,{disabled:m,variable:n,onChange:a,buttonShow:u,render:()=>r({innerDisabled:m})})]})]})}}}]);
//# sourceMappingURL=9697.2d28d920.chunk.js.map