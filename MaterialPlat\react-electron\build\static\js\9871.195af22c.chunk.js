"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[9871],{55518:(e,a,l)=>{l.d(a,{A:()=>N});var i=l(65043),n=l(74117),t=l(56543),d=l(81143),s=l(68374),o=l(18650);const r=0,u="left";var v=l(70579);const b=d.Ay.div`
    width: ${(0,s.D0)("20px")};
    height: ${(0,s.D0)("20px")};
    background-size: ${(0,s.D0)("20px")} ${(0,s.D0)("20px")};
    background-image: url(${e=>{let{isConstant:a}=e;return a?o.fd:o.Mo}});
`,c=e=>{let{variable:a,onChange:l,disabled:i}=e;const{default_val:n,is_fx:t}=a;return!t||i?(0,v.jsx)(v.Fragment,{}):(0,v.jsx)(b,{isConstant:n.isConstant===r,onClick:()=>{l({...a,default_val:{...n,isConstant:0===(null===n||void 0===n?void 0:n.isConstant)?1:0}})}})};var h=l(95206),p=l(34154),f=l(67208),x=l(16090),_=l(36497),g=l(29977);const w=e=>{var a;let{disabled:l,variable:n,handleChange:t}=e;const d=(0,g.A)(),s=(0,i.useMemo)((()=>(null===d||void 0===d?void 0:d.filter((e=>e.variable_type===n.variable_type&&e.id!==n.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[d,n]);return(0,v.jsx)(_.A,{showSearch:!0,optionFilterProp:"labelName",disabled:l,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===n||void 0===n||null===(a=n.default_val)||void 0===a?void 0:a.variable_id,options:s,onChange:(e,a)=>t(a)})},m=e=>{let{disabled:a,content:l,buttonType:n,actionId:d,script:s}=e;const[o,r]=(0,i.useState)(!1),{startAction:u}=(0,x.A)(),b=()=>{n!==p.NR.\u52a8\u4f5c?n!==p.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{r(!0),await(0,f.O5k)({script:s,result_type:t.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{r(!1)}})():(async()=>{try{d&&(r(!0),await u({action_id:d}))}catch(e){console.log("err when handleSubmitAction",e)}finally{r(!1)}})()};return(0,v.jsx)(h.Ay,{loading:o,disabled:a,className:"button-width",onClick:()=>b(),children:l})},C=d.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:a}=e;return a?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,s.D0)("80px")};
        pointer-events: auto;
    }
`,y=e=>{let{disabled:a,variable:l,render:i,onChange:n,buttonShow:t}=e;const{button_variable_tab:d,default_val:s}=l;return(0,v.jsx)(C,{isLeft:(null===d||void 0===d?void 0:d.position)===u,children:1===s.isConstant?(0,v.jsx)(w,{disabled:a,variable:l,handleChange:e=>{n({...l,default_val:{...s,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,v.jsxs)(v.Fragment,{children:[t&&(null===d||void 0===d?void 0:d.isEnable)&&(0,v.jsx)(m,{...d,disabled:a}),i()]})})};var j=l(12624),S=l(32513);const k=e=>{let{variable:a,disabled:l=!1,onChange:i,usableShowType:n="checkbox"}=e;return null!==a&&void 0!==a&&a.is_enable?"switch"===n?(0,v.jsx)(j.A,{disabled:l,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{i({...a,is_feature:e})}}):(0,v.jsx)(S.A,{disabled:l,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{i({...a,is_feature:e.target.checked})}}):(0,v.jsx)(v.Fragment,{})},A=d.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,N=e=>{let{variable:a,disabled:l=!1,onChange:i,render:d,usableShow:s=!0,buttonShow:o=!0,fxShow:r=!0,nameShow:u=!0,usableShowType:b}=e;const{t:h}=(0,n.Bd)(),p=l||(a.variable_type===t.ps.\u5e03\u5c14\u578b?(null===a||void 0===a?void 0:a.is_enable)&&(null===a||void 0===a?void 0:a.is_feature):(null===a||void 0===a?void 0:a.is_enable)&&!(null!==a&&void 0!==a&&a.is_feature));return(0,v.jsxs)(A,{children:[(s||u)&&(0,v.jsx)("div",{className:"input-render-left",children:(0,v.jsxs)("div",{children:[s&&(0,v.jsx)(k,{variable:a,disabled:l,onChange:i,usableShowType:b}),u&&(0,v.jsx)("div",{className:"variable_name",children:h(a.name)})]})}),(0,v.jsxs)("div",{className:"input-render-right",children:[r&&(0,v.jsx)(c,{variable:a,onChange:i,disabled:p}),(0,v.jsx)(y,{disabled:p,variable:a,onChange:i,buttonShow:o,render:()=>d({innerDisabled:p})})]})]})}},69871:(e,a,l)=>{l.r(a),l.d(a,{default:()=>r});var i=l(65043),n=l(56434),t=l.n(n),d=l(55518),s=l(70579);const o=e=>{const a=t()(e);return a.is_enable=!0,a.is_feature=a.default_val.value,a},r=e=>{var a;let{variable:l,disabled:n,onChange:r}=e;const[u,v]=(0,i.useState)(o(l));(0,i.useEffect)((()=>{v(o(l))}),[l]);return(0,s.jsx)(d.A,{variable:u,disabled:n,onChange:e=>{r((e=>{const a=t()(e);return a.is_enable=!1,a.default_val.value=!!a.is_feature,a})(e))},usableShow:!l.default_val.isConstant,buttonShow:!1,usableShowType:null===l||void 0===l||null===(a=l.boolean_tab)||void 0===a?void 0:a.showType,render:e=>{let{innerDisabled:a}=e;return(0,s.jsx)(s.Fragment,{})}})}}}]);
//# sourceMappingURL=9871.195af22c.chunk.js.map