{"version": 3, "file": "static/js/9950.14f5d3e2.chunk.js", "mappings": "4PAGO,MAAMA,EAAiBC,EAAAA,GAAOC,GAAG;;;;;;;;;;EAY3BC,EAAuBF,EAAAA,GAAOC,GAAG;;;;;;;;;;;;;EAejCE,EAA+BH,EAAAA,GAAOC,GAAG;;;;;;;;;;;iBCpBtD,MA2JA,EA3JcG,IAEP,IAFQ,GACXC,EAAE,KAAEC,EAAI,QAAEC,EAAO,cAAEC,GAAgB,EAAI,aAAEC,GAC5CL,EACG,MAAMM,GAAgBC,EAAAA,EAAAA,UAChBC,GAAWC,EAAAA,EAAAA,OACX,EAAEC,IAAMC,EAAAA,EAAAA,MAEd,OACIC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACIC,EAAAA,EAAAA,KAACpB,EAAc,CAEXM,GAAIA,EACJE,QAASA,EACTa,cAAgBC,IACZA,EAAEC,iBACFD,EAAEE,kBACF,MAAMC,EAAUC,SAASC,kBAAkB,QACvCF,GACAA,EAAQG,SAAQC,IACZA,EAAEC,MAAMC,QAAU,MAAM,IAG5BvB,GACAA,IAIJ,IAAI,QAAEwB,EAAO,QAAEC,GAAYX,EAC3BX,EAAcuB,QAAQJ,MAAMC,QAAU,QAEtC,MAAMI,EAAUC,OAAOC,WACjBC,EAAUF,OAAOG,YAEjBC,EAAiB7B,EAAcuB,QAAQO,YACvCC,EAAiB/B,EAAcuB,QAAQS,aAG7CX,EAAWG,EAAUH,EAAWQ,EAAiBR,EAAUA,EAAUQ,EACrEP,EAAWK,EAAUL,EAAWS,EAAiBT,EAAUA,EAAUS,EAErE/B,EAAcuB,QAAQJ,MAAMc,IAAM,GAAGX,MACrCtB,EAAcuB,QAAQJ,MAAMe,KAAO,GAAGb,MAEtCN,SAASoB,QAAU,KACXnC,GAAiBA,EAAcuB,SAAWvB,EAAcuB,QAAQJ,QAChEnB,EAAcuB,QAAQJ,MAAMC,QAAU,OACtCL,SAASoB,QAAU,KACvB,CACH,EACH3B,SAGDJ,EAAER,EAAKwC,OA1CHxC,EAAKD,IA4CbG,IAEOW,EAAAA,EAAAA,KAAC4B,EAAAA,EAAiB,CAAA7B,UACdC,EAAAA,EAAAA,KAACjB,EAAoB,CAAC8C,IAAKtC,EAAeoC,KAAK,OAAM5B,UACjDF,EAAAA,EAAAA,MAACb,EAA4B,CAAAe,SAAA,EACzBC,EAAAA,EAAAA,KAAA,OACI8B,UAAU,iBACV1C,QAASA,KACLK,EAAS,CAAEsC,KAAMC,EAAAA,GAAqCC,MAAO3C,IAC7DG,EAAS,CACLsC,KAAMG,EAAAA,GACND,MAAO,CACHF,KAAMI,EAAAA,EAAeC,UAE3B,EACJrC,SAEDJ,EAAE,mBAEPK,EAAAA,EAAAA,KAAA,OACI8B,UAAU,iBACV1C,QAASA,KACLK,EAAS,CACLsC,KAAMG,EAAAA,GACND,MAAO,CACHF,KAAMI,EAAAA,EAAeE,MAE3B,EACJtC,SAEDJ,EAAE,mBAGPK,EAAAA,EAAAA,KAAA,OACI8B,UAAU,iBACV1C,QAASA,KACLK,EAAS,CACLsC,KAAMG,EAAAA,GACND,MAAO,CACHF,KAAMI,EAAAA,EAAeG,SACrBC,cAAeC,EAAAA,GAAgBC,UAErC,EACJ1C,SAEDJ,EAAE,+BAGPK,EAAAA,EAAAA,KAAA,OACI8B,UAAU,iBACV1C,QAASA,KACLK,EAAS,CACLsC,KAAMG,EAAAA,GACND,MAAO,CACHF,KAAMI,EAAAA,EAAeG,SACrBC,cAAeC,EAAAA,GAAgBE,OAErC,EACJ3C,SAEDJ,EAAE,+BAGPK,EAAAA,EAAAA,KAAA,OACI8B,UAAU,iBACV1C,QAASA,KACLK,EAAS,CACLsC,KAAMG,EAAAA,GACND,MAAO,CACHF,KAAMI,EAAAA,EAAeQ,WACrBJ,cAAeC,EAAAA,GAAgBC,UAErC,EACJ1C,SAEDJ,EAAE,+BAGPK,EAAAA,EAAAA,KAAA,OACI8B,UAAU,iBACV1C,QAASA,KACLK,EAAS,CACLsC,KAAMG,EAAAA,GACND,MAAO,CACHF,KAAMI,EAAAA,EAAeQ,WACrBJ,cAAeC,EAAAA,GAAgBE,OAErC,EACJ3C,SAEDJ,EAAE,uCAO5B,C", "sources": ["pages/layout/editEmpty/style.js", "pages/layout/editEmpty/index.js"], "names": ["EmptyContainer", "styled", "div", "ContextMenuContainer", "ContextMenuChildrenContainer", "_ref", "id", "item", "onClick", "isContextMenu", "layoutConfig", "rightClickRef", "useRef", "dispatch", "useDispatch", "t", "useTranslation", "_jsxs", "_Fragment", "children", "_jsx", "onContextMenu", "e", "preventDefault", "stopPropagation", "menuDoc", "document", "getElementsByName", "for<PERSON>ach", "i", "style", "display", "clientX", "clientY", "current", "screenW", "window", "innerWidth", "screenH", "innerHeight", "rightClickRefW", "offsetWidth", "rightClickRefH", "offsetHeight", "top", "left", "onclick", "name", "PortalMenuContext", "ref", "className", "type", "SPLIT_CHANGE_CURRENT_CONTEXT_LAYOUT", "param", "SPLIT_OPERATION_TYPE", "OPERATION_TYPE", "CONTENT", "DLE", "VERTICAL", "directionType", "DIRECTION_SPLIT", "UNSHIFT", "PUSH", "HORIZONTAL"], "sourceRoot": ""}