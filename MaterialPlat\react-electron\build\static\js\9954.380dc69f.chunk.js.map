{"version": 3, "file": "static/js/9954.380dc69f.chunk.js", "mappings": "+SAMO,MAAMA,EAAiBC,EAAAA,GAAOC,GAAG;;;4BAGZC,EAAAA;;;iBCG5B,MA6CA,EA7CsBC,KAClB,MAAMC,GAAWC,EAAAA,EAAAA,OACX,kBAAEC,IAAsBC,EAAAA,EAAAA,KACxBC,GAAgBC,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,SAASH,gBACpDI,GAAWH,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,SAASC,YAC9CC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAErCC,EAAAA,EAAAA,YAAU,KACNC,OAAOC,eAAeC,QAAQ,YAAY,IAAIC,MAAOC,UAAU,GAChE,KAEHL,EAAAA,EAAAA,YAAU,KACN,GAAwB,IAApBJ,EAASU,OACT,OAGJ,MAAMC,EAAUX,EAASY,MAAKC,GAAKA,EAAEC,KAAOlB,IAIpC,IAADmB,EAFHJ,EACAT,EAAiB,OAAPS,QAAO,IAAPA,OAAO,EAAPA,EAASV,SAEnBe,EAAAA,GAAQC,MAAM,kCAEdzB,EAAS,CAAE0B,KAAMC,EAAAA,GAAiCC,MAAe,OAARpB,QAAQ,IAARA,GAAa,QAALe,EAARf,EAAW,UAAE,IAAAe,OAAL,EAARA,EAAeD,KAC5E,GACD,CAAClB,EAAeI,IAMnB,OACIqB,EAAAA,EAAAA,KAAClC,EAAc,CAAAmC,SAEPrB,IAEIoB,EAAAA,EAAAA,KAACE,EAAAA,EAAa,CACVC,OAAQvB,EACRwB,SAXEL,IAClB1B,EAAkB0B,EAAOxB,EAAc,KActB,C", "sources": ["pages/layoutContent/style.js", "pages/layoutContent/index.js"], "names": ["SplitContainer", "styled", "div", "GroupBac", "LayoutContent", "dispatch", "useDispatch", "subTemplateLayout", "useTemplateLayout", "currentPageId", "useSelector", "state", "template", "pageData", "layout", "setLayout", "useState", "useEffect", "window", "sessionStorage", "setItem", "Date", "getTime", "length", "optPage", "find", "i", "id", "_pageData$", "message", "error", "type", "TEMPLATE_CHANGE_CURRENT_PAGE_ID", "param", "_jsx", "children", "DynamicLayout", "config", "onResize"], "sourceRoot": ""}