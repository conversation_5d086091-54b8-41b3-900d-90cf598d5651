{"version": 3, "file": "static/js/reactPlayerFilePlayer.795cb5f0.chunk.js", "mappings": "wHAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAqB,CAAC,EAzBXC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAoB,CAC3BK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,QAC/BC,EAAeD,EAAQ,OACvBE,EAAkBF,EAAQ,OAC9B,MAAMG,EAAqC,qBAAdC,UACvBC,EAAcF,GAAwC,aAAvBC,UAAUE,UAA2BF,UAAUG,eAAiB,EAC/FC,EAASL,IAAkB,mBAAmBM,KAAKL,UAAUM,YAAcL,KAAiBM,OAAOC,SACnGC,EAAYV,GAAiB,iCAAiCM,KAAKL,UAAUM,aAAeC,OAAOC,SAOnGE,EAAoB,wBACpBC,EAA0B,sDAEhC,MAAMtB,UAAmBG,EAAaoB,UACpCC,WAAAA,GAAc,IAAAC,EACZC,SAASC,WAAUF,EAAAG,KAEnBxC,EAAcwC,KAAM,WAAW,kBAAaH,EAAKI,MAAMC,WAAQH,UAAQ,IACvEvC,EAAcwC,KAAM,UAAU,kBAAaH,EAAKI,MAAME,UAAOJ,UAAQ,IACrEvC,EAAcwC,KAAM,YAAY,kBAAaH,EAAKI,MAAMG,YAASL,UAAQ,IACzEvC,EAAcwC,KAAM,eAAe,kBAAaH,EAAKI,MAAMI,eAAYN,UAAQ,IAC/EvC,EAAcwC,KAAM,WAAW,kBAAaH,EAAKI,MAAMK,WAAQP,UAAQ,IACvEvC,EAAcwC,KAAM,WAAW,kBAAaH,EAAKI,MAAMM,WAAQR,UAAQ,IACvEvC,EAAcwC,KAAM,WAAW,kBAAaH,EAAKI,MAAMO,WAAQT,UAAQ,IACvEvC,EAAcwC,KAAM,wBAAyBS,GAAUT,KAAKC,MAAMS,qBAAqBD,EAAMzC,OAAO2C,gBACpGnD,EAAcwC,KAAM,eAAe,kBAAaH,EAAKI,MAAMW,eAAYb,UAAQ,IAC/EvC,EAAcwC,KAAM,gBAAiBa,IACnC,MAAM,aAAEC,EAAY,QAAEC,GAAYf,KAAKC,MACvCa,EAAaD,GACTE,GACFf,KAAKgB,MACP,IAEFxD,EAAcwC,KAAM,4BAA6Ba,IAC/C,GAAIb,KAAKiB,SAAU,EAAIrC,EAAasC,gCAAgClB,KAAKiB,QAAS,CAChF,MAAM,uBAAEE,GAA2BnB,KAAKiB,OACT,uBAA3BE,EACFnB,KAAKY,YAAYC,GACmB,WAA3BM,GACTnB,KAAKc,aAAaD,EAEtB,KAEFrD,EAAcwC,KAAM,UAAWa,IAC7Bb,KAAKC,MAAMmB,OAAOP,EAAE7C,OAAOqD,YAAY,IAEzC7D,EAAcwC,KAAM,QAAQ,KAC1BA,KAAKiB,OAAOK,OAAQ,CAAI,IAE1B9D,EAAcwC,KAAM,UAAU,KAC5BA,KAAKiB,OAAOK,OAAQ,CAAK,IAE3B9D,EAAcwC,KAAM,uBAAuB,CAACuB,EAAQC,IAC5B,kBAAXD,EACchD,EAAaJ,QAAQsD,cAAc,SAAU,CAAErE,IAAKoE,EAAOE,IAAKH,IAElEhD,EAAaJ,QAAQsD,cAAc,SAAU,CAAErE,IAAKoE,KAAUD,MAEvF/D,EAAcwC,KAAM,eAAe,CAAC2B,EAAOH,IAClBjD,EAAaJ,QAAQsD,cAAc,QAAS,CAAErE,IAAKoE,KAAUG,MAEtFnE,EAAcwC,KAAM,OAAQiB,IACtBjB,KAAKiB,SACPjB,KAAK4B,WAAa5B,KAAKiB,QAEzBjB,KAAKiB,OAASA,CAAM,GAExB,CACAY,iBAAAA,GACE7B,KAAKC,MAAM6B,SAAW9B,KAAKC,MAAM6B,QAAQ9B,MACzCA,KAAK+B,aAAa/B,KAAKiB,QACvB,MAAMS,EAAM1B,KAAKgC,UAAUhC,KAAKC,MAAMgC,KAClCP,IACF1B,KAAKiB,OAAOS,IAAMA,IAEhBvC,GAAUa,KAAKC,MAAMiC,OAAOC,kBAC9BnC,KAAKiB,OAAOmB,MAEhB,CACAC,kBAAAA,CAAmBC,GACbtC,KAAKuC,eAAevC,KAAKC,SAAWD,KAAKuC,eAAeD,KAC1DtC,KAAKwC,gBAAgBxC,KAAK4B,WAAYU,EAAUL,KAChDjC,KAAK+B,aAAa/B,KAAKiB,SAErBjB,KAAKC,MAAMgC,MAAQK,EAAUL,MAAQ,EAAIrD,EAAa6D,eAAezC,KAAKC,MAAMgC,MAAUjC,KAAKC,MAAMgC,eAAeS,QACtH1C,KAAKiB,OAAO0B,UAAY,KAE5B,CACAC,oBAAAA,GACE5C,KAAKiB,OAAO4B,gBAAgB,OAC5B7C,KAAKwC,gBAAgBxC,KAAKiB,QACtBjB,KAAK8C,KACP9C,KAAK8C,IAAIC,SAEb,CACAhB,YAAAA,CAAad,GACX,MAAM,IAAEgB,EAAG,YAAEe,GAAgBhD,KAAKC,MAClCgB,EAAOgC,iBAAiB,OAAQjD,KAAKG,QACrCc,EAAOgC,iBAAiB,UAAWjD,KAAKI,UACxCa,EAAOgC,iBAAiB,UAAWjD,KAAKK,aACxCY,EAAOgC,iBAAiB,QAASjD,KAAKM,SACtCW,EAAOgC,iBAAiB,SAAUjD,KAAKoB,QACvCH,EAAOgC,iBAAiB,QAASjD,KAAKO,SACtCU,EAAOgC,iBAAiB,QAASjD,KAAKQ,SACtCS,EAAOgC,iBAAiB,aAAcjD,KAAKkD,sBAC3CjC,EAAOgC,iBAAiB,wBAAyBjD,KAAKY,aACtDK,EAAOgC,iBAAiB,wBAAyBjD,KAAKc,cACtDG,EAAOgC,iBAAiB,gCAAiCjD,KAAKmD,0BACzDnD,KAAKoD,aAAanB,IACrBhB,EAAOgC,iBAAiB,UAAWjD,KAAKE,SAEtC8C,IACF/B,EAAOoC,aAAa,cAAe,IACnCpC,EAAOoC,aAAa,qBAAsB,IAC1CpC,EAAOoC,aAAa,iBAAkB,IAE1C,CACAb,eAAAA,CAAgBvB,EAAQgB,GACtBhB,EAAOqC,oBAAoB,UAAWtD,KAAKE,SAC3Ce,EAAOqC,oBAAoB,OAAQtD,KAAKG,QACxCc,EAAOqC,oBAAoB,UAAWtD,KAAKI,UAC3Ca,EAAOqC,oBAAoB,UAAWtD,KAAKK,aAC3CY,EAAOqC,oBAAoB,QAAStD,KAAKM,SACzCW,EAAOqC,oBAAoB,SAAUtD,KAAKoB,QAC1CH,EAAOqC,oBAAoB,QAAStD,KAAKO,SACzCU,EAAOqC,oBAAoB,QAAStD,KAAKQ,SACzCS,EAAOqC,oBAAoB,aAActD,KAAKkD,sBAC9CjC,EAAOqC,oBAAoB,wBAAyBtD,KAAKY,aACzDK,EAAOqC,oBAAoB,wBAAyBtD,KAAKc,cACzDG,EAAOqC,oBAAoB,gCAAiCtD,KAAKmD,0BAC5DnD,KAAKoD,aAAanB,IACrBhB,EAAOqC,oBAAoB,UAAWtD,KAAKE,QAE/C,CACAqC,cAAAA,CAAetC,GACb,OAAIA,EAAMiC,OAAOqB,cAGbtD,EAAMiC,OAAOsB,WAAWC,SAGrB5E,EAAgB6E,iBAAiBtE,KAAKa,EAAMgC,MAAQhC,EAAMiC,OAAOyB,YAC1E,CACAP,YAAAA,CAAanB,GACX,SAAIzC,GAAaQ,KAAKC,MAAMiC,OAAO0B,gBAAkB5D,KAAKC,MAAMiC,OAAO2B,YAGnE1E,IAAUa,KAAKC,MAAMiC,OAAOC,kBAGzBtD,EAAgBiF,eAAe1E,KAAK6C,IAAQvC,EAAwBN,KAAK6C,GAClF,CACA8B,aAAAA,CAAc9B,GACZ,OAAOpD,EAAgBmF,gBAAgB5E,KAAK6C,IAAQjC,KAAKC,MAAMiC,OAAO+B,SACxE,CACAC,YAAAA,CAAajC,GACX,OAAOpD,EAAgBsF,eAAe/E,KAAK6C,IAAQjC,KAAKC,MAAMiC,OAAOkC,QACvE,CACAhC,IAAAA,CAAKH,GACH,MAAM,WAAEoC,EAAU,WAAEC,EAAU,YAAEC,EAAW,WAAEC,GAAexE,KAAKC,MAAMiC,OAkDvE,GAjDIlC,KAAK8C,KACP9C,KAAK8C,IAAIC,UAEP/C,KAAKyE,MACPzE,KAAKyE,KAAKC,QAER1E,KAAKoD,aAAanB,KACpB,EAAIrD,EAAa+F,QAnKH,8DAmKuBC,QAAQ,UAAWP,GAlK3C,OAkKoEQ,MAAMC,IAQrF,GAPA9E,KAAK8C,IAAM,IAAIgC,EAAIR,GACnBtE,KAAK8C,IAAIiC,GAAGD,EAAIE,OAAOC,iBAAiB,KACtCjF,KAAKC,MAAMC,SAAS,IAEtBF,KAAK8C,IAAIiC,GAAGD,EAAIE,OAAOE,OAAO,CAACrE,EAAGsE,KAChCnF,KAAKC,MAAMO,QAAQK,EAAGsE,EAAMnF,KAAK8C,IAAKgC,EAAI,IAExCpF,EAAwBN,KAAK6C,GAAM,CACrC,MAAMmD,EAAKnD,EAAIoD,MAAM3F,GAAyB,GAC9CM,KAAK8C,IAAIwC,WArKe,qDAqKsBV,QAAQ,OAAQQ,GAChE,MACEpF,KAAK8C,IAAIwC,WAAWrD,GAEtBjC,KAAK8C,IAAIyC,YAAYvF,KAAKiB,QAC1BjB,KAAKC,MAAMuF,UAAU,IAGrBxF,KAAK+D,cAAc9B,KACrB,EAAIrD,EAAa+F,QApLF,wEAoLuBC,QAAQ,UAAWL,GAnL3C,UAmLsEM,MAAMY,IACxFzF,KAAKyE,KAAOgB,EAAOC,cAAcvJ,SACjC6D,KAAKyE,KAAKkB,WAAW3F,KAAKiB,OAAQgB,EAAKjC,KAAKC,MAAMc,SAClDf,KAAKyE,KAAKM,GAAG,QAAS/E,KAAKC,MAAMO,SAC7BoF,SAASrB,GAAe,EAC1BvE,KAAKyE,KAAKoB,WAAWC,wBAAuB,GAE5C9F,KAAKyE,KAAKsB,eAAe,CAAEC,MAAO,CAAEC,SAAUR,EAAOS,MAAMC,kBAE7DnG,KAAKC,MAAMuF,UAAU,IAGrBxF,KAAKkE,aAAajC,KACpB,EAAIrD,EAAa+F,QA/LH,8DA+LuBC,QAAQ,UAAWJ,GA9L3C,SA8LoEK,MAAMuB,IACrFpG,KAAKqG,IAAMD,EAAME,aAAa,CAAEC,KAAM,MAAOtE,QAC7CjC,KAAKqG,IAAIG,mBAAmBxG,KAAKiB,QACjCjB,KAAKqG,IAAItB,GAAGqB,EAAMpB,OAAOE,OAAO,CAACrE,EAAGsE,KAClCnF,KAAKC,MAAMO,QAAQK,EAAGsE,EAAMnF,KAAKqG,IAAKD,EAAM,IAE9CpG,KAAKqG,IAAIjE,OACTpC,KAAKC,MAAMuF,UAAU,IAGrBvD,aAAeS,MACjB1C,KAAKiB,OAAOmB,YACP,IAAI,EAAIxD,EAAa6D,eAAeR,GACzC,IACEjC,KAAKiB,OAAO0B,UAAYV,CAC1B,CAAE,MAAOpB,GACPb,KAAKiB,OAAOS,IAAMpC,OAAOmH,IAAIC,gBAAgBzE,EAC/C,CAEJ,CACAjB,IAAAA,GACE,MAAM2F,EAAU3G,KAAKiB,OAAOD,OACxB2F,GACFA,EAAQC,MAAM5G,KAAKC,MAAMO,QAE7B,CACAqG,KAAAA,GACE7G,KAAKiB,OAAO4F,OACd,CACAC,IAAAA,GACE9G,KAAKiB,OAAO4B,gBAAgB,OACxB7C,KAAKyE,MACPzE,KAAKyE,KAAKC,OAEd,CACAqC,MAAAA,CAAOC,GAA6B,IAApBC,IAAWlH,UAAAmH,OAAA,QAAAC,IAAApH,UAAA,KAAAA,UAAA,GACzBC,KAAKiB,OAAOI,YAAc2F,EACrBC,GACHjH,KAAK6G,OAET,CACAO,SAAAA,CAAUC,GACRrH,KAAKiB,OAAOqG,OAASD,CACvB,CACAE,SAAAA,GACMvH,KAAKiB,OAAOuG,yBAA2BC,SAASC,0BAA4B1H,KAAKiB,OACnFjB,KAAKiB,OAAOuG,2BACH,EAAI5I,EAAasC,gCAAgClB,KAAKiB,SAAkD,uBAAvCjB,KAAKiB,OAAOE,wBACtFnB,KAAKiB,OAAO0G,0BAA0B,qBAE1C,CACAC,UAAAA,GACMH,SAASI,sBAAwBJ,SAASC,0BAA4B1H,KAAKiB,OAC7EwG,SAASI,wBACA,EAAIjJ,EAAasC,gCAAgClB,KAAKiB,SAAkD,WAAvCjB,KAAKiB,OAAOE,wBACtFnB,KAAKiB,OAAO0G,0BAA0B,SAE1C,CACAG,eAAAA,CAAgBC,GACd,IACE/H,KAAKiB,OAAON,aAAeoH,CAC7B,CAAE,MAAOC,GACPhI,KAAKC,MAAMO,QAAQwH,EACrB,CACF,CACAC,WAAAA,GACE,IAAKjI,KAAKiB,OACR,OAAO,KACT,MAAM,SAAEiH,EAAQ,SAAEC,GAAanI,KAAKiB,OACpC,OAAIiH,IAAaE,KAAYD,EAASjB,OAAS,EACtCiB,EAASE,IAAIF,EAASjB,OAAS,GAEjCgB,CACT,CACAI,cAAAA,GACE,OAAKtI,KAAKiB,OAEHjB,KAAKiB,OAAOI,YADV,IAEX,CACAkH,gBAAAA,GACE,IAAKvI,KAAKiB,OACR,OAAO,KACT,MAAM,SAAEuH,GAAaxI,KAAKiB,OAC1B,GAAwB,IAApBuH,EAAStB,OACX,OAAO,EAET,MAAMmB,EAAMG,EAASH,IAAIG,EAAStB,OAAS,GACrCgB,EAAWlI,KAAKiI,cACtB,OAAII,EAAMH,EACDA,EAEFG,CACT,CACArG,SAAAA,CAAUC,GACR,MAAMwG,EAASzI,KAAKoD,aAAanB,GAC3ByG,EAAU1I,KAAK+D,cAAc9B,GAC7B0G,EAAS3I,KAAKkE,aAAajC,GACjC,KAAIA,aAAeS,QAAS,EAAI9D,EAAa6D,eAAeR,IAAQwG,GAAUC,GAAWC,GAGzF,OAAIlJ,EAAkBL,KAAK6C,GAClBA,EAAI2C,QAAQ,kBAAmB,6BAEjC3C,CACT,CACA2G,MAAAA,GACE,MAAM,IAAE3G,EAAG,QAAElB,EAAO,KAAE8H,EAAI,SAAEC,EAAQ,MAAExH,EAAK,OAAEY,EAAM,MAAE6G,EAAK,OAAEC,GAAWhJ,KAAKC,MAEtEgJ,EADWjJ,KAAKuC,eAAevC,KAAKC,OACf,QAAU,QAC/BiJ,EAAQ,CACZH,MAAiB,SAAVA,EAAmBA,EAAQ,OAClCC,OAAmB,SAAXA,EAAoBA,EAAS,QAEvC,OAAuBzK,EAAaJ,QAAQsD,cAC1CwH,EACA,CACEE,IAAKnJ,KAAKmJ,IACVzH,IAAK1B,KAAKgC,UAAUC,GACpBiH,QACAE,QAAS,OACTC,SAAUtI,QAAW,EACrB+H,WACAxH,QACAuH,UACG3G,EAAOsB,YAEZvB,aAAeS,OAAST,EAAIqH,IAAItJ,KAAKuJ,qBACrCrH,EAAOsH,OAAOF,IAAItJ,KAAKyJ,aAE3B,EAEFjM,EAAcY,EAAY,cAAe,cACzCZ,EAAcY,EAAY,UAAWS,EAAgB6K,QAAQC,K", "sources": ["../node_modules/react-player/lib/players/FilePlayer.js"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "FilePlayer_exports", "__export", "target", "all", "name", "default", "FilePlayer", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "HAS_NAVIGATOR", "navigator", "IS_IPAD_PRO", "platform", "maxTouchPoints", "IS_IOS", "test", "userAgent", "window", "MSStream", "IS_SAFARI", "MATCH_DROPBOX_URL", "MATCH_CLOUDFLARE_STREAM", "Component", "constructor", "_this", "super", "arguments", "this", "props", "onReady", "onPlay", "onBuffer", "onBufferEnd", "onPause", "onEnded", "onError", "event", "onPlaybackRateChange", "playbackRate", "onEnablePIP", "e", "onDisablePIP", "playing", "play", "player", "supportsWebKitPresentationMode", "webkitPresentationMode", "onSeek", "currentTime", "muted", "source", "index", "createElement", "src", "track", "prevPlayer", "componentDidMount", "onMount", "addListeners", "getSource", "url", "config", "forceDisableHls", "load", "componentDidUpdate", "prevProps", "shouldUseAudio", "removeListeners", "isMediaStream", "Array", "srcObject", "componentWillUnmount", "removeAttribute", "hls", "destroy", "playsinline", "addEventListener", "onPlayBackRateChange", "onPresentationModeChange", "shouldUseHLS", "setAttribute", "removeEventListener", "forceVideo", "attributes", "poster", "AUDIO_EXTENSIONS", "forceAudio", "forceSafariHLS", "forceHLS", "HLS_EXTENSIONS", "shouldUseDASH", "DASH_EXTENSIONS", "forceDASH", "shouldUseFLV", "FLV_EXTENSIONS", "forceFLV", "hlsVersion", "hlsOptions", "dashVersion", "flvVersion", "dash", "reset", "getSDK", "replace", "then", "Hls", "on", "Events", "MANIFEST_PARSED", "ERROR", "data", "id", "match", "loadSource", "attachMedia", "onLoaded", "dashjs", "MediaPlayer", "initialize", "parseInt", "getDebug", "setLogToBrowserConsole", "updateSettings", "debug", "logLevel", "Debug", "LOG_LEVEL_NONE", "flvjs", "flv", "createPlayer", "type", "attachMediaElement", "URL", "createObjectURL", "promise", "catch", "pause", "stop", "seekTo", "seconds", "keepPlaying", "length", "undefined", "setVolume", "fraction", "volume", "enablePIP", "requestPictureInPicture", "document", "pictureInPictureElement", "webkitSetPresentationMode", "disablePIP", "exitPictureInPicture", "setPlaybackRate", "rate", "error", "getDuration", "duration", "seekable", "Infinity", "end", "getCurrentTime", "getSecondsLoaded", "buffered", "useHLS", "useDASH", "useFLV", "render", "loop", "controls", "width", "height", "Element", "style", "ref", "preload", "autoPlay", "map", "renderSourceElement", "tracks", "renderTrack", "canPlay", "file"], "sourceRoot": ""}