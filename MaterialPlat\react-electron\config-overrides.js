// eslint-disable-next-line import/no-extraneous-dependencies
const {
    override, addLessLoader, addWebpackAlias, addWebpackPlugin
} = require('customize-cra')
const path = require('path')
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')

// 添加bundle analyzer配置
const addBundleAnalyzer = () => (config) => {
    if (process.env.ANALYZE === 'true') {
        config.plugins.push(
            new BundleAnalyzerPlugin({
                analyzerMode: 'server',
                analyzerHost: '127.0.0.1',
                analyzerPort: 8888,
                openAnalyzer: true,
                generateStatsFile: false,
                statsOptions: null,
                logLevel: 'info'
            })
        )
    }
    return config
}

// 禁用生产环境source map
const disableSourceMapInProduction = () => (config) => {
    if (process.env.NODE_ENV === 'production') {
        config.devtool = 'nosources-source-map'
    }
    return config
}

module.exports = override(
    addLessLoader({
        lessOptions: {
            javascriptEnabled: true
        }
    }),
    addWebpackAlias({
        '@': path.resolve(__dirname, 'src'),
        '@@': path.resolve(__dirname, 'src/visualPageEditor')
    }),
    addBundleAnalyzer(),
    disableSourceMapInProduction()
)
