import React, { useState, useEffect, useMemo } from 'react'
import VModal from '@/components/vModal/index'
import {
    Upload, Button, Space, message
} from 'antd'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import VButton from '@/components/vButton'
import { UploadOutlined } from '@ant-design/icons'
import SelectPath from '@/components/SelectPath'
import VTransfer from '@/components/vTransfer/index'
import { randomStr } from '@/utils/utils'
import { UPLOAD_MODAL_TYPE, controlSuffixArray } from '@/utils/uploadConstants'
import { UploadModalComponent } from './style'

/**
 *
 * @param {Boolean} open // 打开弹出
 * @param {Function} onCancel // 关闭弹出
 * @param {Function({path, files, targetKeys, all})} onOk // 确定回调
 * @param {String} type // 类型  UPLOAD_MODAL_TYPE常量
 * @param {Array} dataSource // 数组
 * @param {Array} suffixArray // 后缀
 * @param {Object} transferConfig // Transfer 属性
 * @param {Object} selectPathConfig // SelectPath 属性
 * @returns {Element}
 */
const UploadModal = ({
    open,
    onCancel,
    onOk,
    type,
    dataSource,
    suffixArray = controlSuffixArray,
    transferConfig,
    selectPathConfig,
    showPath = true
}) => {
    const [messageApi, contextHolder] = message.useMessage()
    const { t } = useTranslation()
    const [files, setFiles] = useState([])
    const [targetKeys, setTargetKeys] = useState([])
    const projectDirectory = useSelector(state => state.global.systemConfig?.project_directory)
    const [path, setPath] = useState(projectDirectory)

    useEffect(() => {
        if (!open) {
            setTargetKeys([])
            setFiles([])
            setPath('')
        }
    }, [open])

    const verifySuffix = (str) => {
        const suffix = str.substring(str.lastIndexOf('.'))
        return suffixArray.includes(suffix)
    }

    const beforeUpload = (file) => {
        const { name } = file
        if (!verifySuffix(name)) {
            messageApi.open({
                type: 'error',
                content: t(`请选择${suffixArray}类型文件`)
            })
            return false
        }
        setFiles([file])
        return false
    }
    const onPrimary = (all = false) => {
        if (all && dataSource.length === 0) {
            messageApi.open({
                type: 'error',
                content: t('没有信号变量数据，不能导出')
            })
            return
        }
        if (type === UPLOAD_MODAL_TYPE.导入 && files.length === 0) {
            messageApi.open({
                type: 'error',
                content: t(`请选择${suffixArray}类型文件`)
            })
            return
        }
        if (type === UPLOAD_MODAL_TYPE.导出 && !all && !targetKeys.length > 0) {
            messageApi.open({
                type: 'error',
                content: t('请选择导出的数据')
            })
            return
        }
        if (showPath && !path && type === UPLOAD_MODAL_TYPE.导出) {
            messageApi.open({
                type: 'error',
                content: t('请选择路径')
            })
            return
        }
        onOk({
            path,
            files,
            targetKeys,
            all
        })
    }

    const onRemove = () => {
        setFiles([])
    }

    const handleSelectPath = (param) => {
        setPath(param)
    }

    return (
        <VModal
            open={open}
            title={type === UPLOAD_MODAL_TYPE.导出 ? t('导出') : t('导入')}
            width={type === UPLOAD_MODAL_TYPE.导出 ? '50vw' : '450px'}
            onCancel={onCancel}
            footer={null}
        >
            {contextHolder}
            <UploadModalComponent>
                <div className="upload-modal-left">
                    <Space direction="vertical">
                        {type === UPLOAD_MODAL_TYPE.导出
                            ? (
                                <>
                                    {
                                        showPath && (
                                            <div className="path-layout">
                                                <div>
                                                    {t('导出路径')}
                                                    ：
                                                </div>
                                                <SelectPath
                                                    value={path}
                                                    param={{
                                                        defaultPath: `${randomStr()}${suffixArray[0]}`,
                                                        filters: [{ name: suffixArray[0], extensions: suffixArray.map(m => m.substring(1)) }]
                                                    }}
                                                    type="save-dialog"
                                                    {...selectPathConfig}
                                                    onChange={handleSelectPath}
                                                />
                                            </div>
                                        )
                                    }
                                    <VTransfer
                                        dataSource={dataSource}
                                        targetKeys={targetKeys}
                                        onChange={setTargetKeys}
                                        rowKey="id"
                                        {...transferConfig}
                                    />
                                </>
                            )
                            : (
                                <div className="path-layout">
                                    <div>
                                        {t('上传文件')}
                                        ：
                                    </div>
                                    <Upload
                                        accept={suffixArray[0]}
                                        fileList={files}
                                        beforeUpload={beforeUpload}
                                        onRemove={onRemove}
                                    >
                                        <Button icon={<UploadOutlined />}>{ t('上传') }</Button>
                                    </Upload>
                                </div>
                            )}
                    </Space>
                </div>
                <div className="upload-modal-right">
                    <Space direction="vertical">
                        <VButton block onClick={() => onPrimary()}>{t('确认')}</VButton>
                        {type === UPLOAD_MODAL_TYPE.导出
                        && <VButton block onClick={() => onPrimary(true)}>{t('全部导出')}</VButton>}
                        <VButton block onClick={onCancel}>{t('取消')}</VButton>
                    </Space>
                </div>
            </UploadModalComponent>

        </VModal>
    )
}

export default UploadModal
