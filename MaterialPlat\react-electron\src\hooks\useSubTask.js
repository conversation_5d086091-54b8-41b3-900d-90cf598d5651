/* eslint-disable no-continue */
/* eslint-disable no-restricted-syntax */
/* eslint-disable new-parens */
/* eslint-disable no-await-in-loop */
import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { notification } from 'antd'

import {
    UI_CMD_TYPE, TASK_TOPIC_TYPE, SAMPLE_STATUS_TYPE
} from '@/utils/constants'
import useDialog from '@/hooks/useDialog'
import {
    getInputVarDetail, updateInputVar, getInputVarList, logControlAddData, editSampleColor
} from '@/utils/services'
import { initInputVariables } from '@/redux/action/inputVariables'
import useInputVariables from '@/hooks/project/useInputVariables'
import {
    DIALOG_CUSTOM_MODAL, ALL_DIALOG_TYPE, DIALOG_LAYOUT_JSON, DIAL<PERSON><PERSON>_SELECT_STATION
} from '@/redux/constants/dialog'
import useTemplateLayout from '@/hooks/useTemplateLayout'
import useProjectHistory from '@/hooks/useProjectHistory'
import { ALL_SHORTCUT, SHORTCUT_MODAL } from '@/components/navbar/constants'
import { getProjectId, setTaskServerStart } from '@/utils/auth'
import { LAYOUT_TYPE } from '@/pages/dialog/pageModal/contants'
import store from '@/redux/store'
import { getCurrentTime, color16 } from '@/utils/utils'
import useElectron from '@/hooks/useElectron'
import * as msgpack from '@msgpack/msgpack'
import useSample from '@/hooks/useSample'

import { SYSTEM_UPDATE_PROJECT_RUNNING_STATUS } from '@/redux/constants/system'
import {
    SUB_TASK_STATUS,
    SUB_TASK_PROCESS_STATUS,
    SUB_TASK_CLEAR,
    SUB_TASK_SAMPLE,
    SUB_TASK_REPLACE_DATA,
    SUB_TASK_LOG,
    SUB_TASK_NEXT_STEP,
    SUB_TASK_SHORTCUT,
    SUB_TASK_IS_FINISH_MAIN,
    SUB_TASK_ERROR,
    SUB_TASK_START_VALIDATION,
    SUB_SAMPLE_INST_STATE_CHANGED,
    SUB_TASK_VIDEO_RECORDING,
    SUB_NEXT_STEP_CHANGED,
    SUB_TASK_OPEN_EXPERIMENT
} from '../redux/constants/subTask'

const UI_ACTION = {
    [UI_CMD_TYPE.SAVE]: ALL_SHORTCUT.保存,
    [UI_CMD_TYPE.SAVE_AS]: ALL_SHORTCUT.另存为,
    [UI_CMD_TYPE.OPEN_SETTING]: ALL_SHORTCUT.模板配置,
    [UI_CMD_TYPE.QUIT]: ALL_SHORTCUT.退出,
    [UI_CMD_TYPE.CLOSE]: ALL_SHORTCUT.关闭
}

const zmq = process.env.REACT_APP_IS_BROWSER !== 'true' ? window.require('zeromq') : {}
let clientPair // 订阅者 taskServer-> ui
let clientUiPair // ui->taskServer
let sockPublisher

// 允许在控制台控制日志的输出
window.isDebug = false

class Queue {
    queue = []

    socket

    max

    sending = false

    constructor(socket, max = 100) {
        this.socket = socket
        this.max = max
    }

    send(msg) {
        try {
            if (this.queue.length > this.max) {
                console.log('publisher 消息队列已满')
            }
            this.queue.push(msg)
            this.trySend()
        } catch (error) {
            console.log('publisher send error', error)
        }
    }

    async trySend() {
        try {
            if (this.sending) {
                return
            }

            this.sending = true

            while (this.queue.length > 0) {
                const firstMessage = this.queue.shift()
                await this.socket.send(firstMessage)
            }

            this.sending = false
        } catch (error) {
            console.log('publisher trySend error', error)
        }
    }
}

/**
 * 使用订阅-发布模式实现的子任务管理器。
 * 用于与消息队列服务器进行通信，接收和发送子任务状态、进度等信息。
 *
 * @returns 返回一个对象，包含初始化对等连接、初始化发布者、使用订阅者等功能方法。
 */
const useSubTask = () => {
    // 使用Redux中的dispatch方法和一系列自定义钩子
    const dispatch = useDispatch()
    const { updateInputVariable } = useInputVariables()
    const { syncResult } = useProjectHistory()
    const { subCurrentPageId } = useTemplateLayout()
    const { openDialog } = useDialog()
    const { openProjectSubWindow, sendMsgToSubWindow } = useElectron()
    const { initSampleTree } = useSample()

    // 内存超限
    const memoryExceed = useRef(false)

    const startCheckMemory = () => {
        // 检查是否支持performance.memory API（Chrome浏览器）
        if (!performance.memory) {
            console.warn('当前浏览器不支持内存监控API，无法检测内存阈值')
            return
        }

        let over50 = false
        let over70 = false

        // 每2秒检查一次内存使用情况
        setInterval(() => {
            const { usedJSHeapSize, jsHeapSizeLimit } = performance.memory

            // 计算内存使用百分比
            const memoryUsagePercent = (usedJSHeapSize / jsHeapSizeLimit) * 100

            if (memoryUsagePercent <= 50) {
                // 之前超出过70恢复二维数组和daq消息的显示处理
                if (over70) {
                    // 先销毁之前的通知 让新通知再最上面
                    notification.destroy('last70')
                    notification.success({
                        key: 'last70',
                        message: '内存检测',
                        description: '内存使用率下降到正常值，已恢复二维数组和daq消息的显示处理',
                        placement: 'topRight',
                        duration: null
                    })
                    logControlAddData({
                        ClassName: `project_${getProjectId()}`,
                        Significance: '高',
                        Type: '内存检测',
                        Grade: 'Error',
                        Content: '内存使用率下降到正常值，已恢复二维数组和daq消息的显示处理'
                    })

                    memoryExceed.current = false
                }
                over50 = false
                over70 = false
                return
            }

            // 当内存使用率超过50%时开始警告
            if (memoryUsagePercent >= 50 && memoryUsagePercent < 70) {
                // 第一次超过50%时发出警告
                if (!over50) {
                    notification.destroy('over50')
                    notification.warning({
                        key: 'over50',
                        message: '内存检测',
                        description: '警告：内存使用率已达到警告值',
                        placement: 'topRight',
                        duration: null
                    })
                    logControlAddData({
                        ClassName: `project_${getProjectId()}`,
                        Significance: '高',
                        Type: '内存检测',
                        Grade: 'Error',
                        Content: '警告：内存使用率已达到警告值'
                    })
                }

                over50 = true
                console.warn(`警告：内存使用率已达到${memoryUsagePercent.toFixed(1)}%`)
            }

            // 检查是否达到70%阈值
            if (memoryUsagePercent >= 70) {
                // 第一次超过70%时发出警告
                if (!over70) {
                    notification.destroy('over70')
                    notification.error({
                        key: 'over70',
                        message: '内存检测',
                        description: '内存使用率已达到阈值，停止对二维数组和daq消息的显示处理，试验正常运行，请及时处理',
                        placement: 'topRight',
                        duration: null
                    })

                    logControlAddData({
                        ClassName: `project_${getProjectId()}`,
                        Significance: '高',
                        Type: '内存检测',
                        Grade: 'Error',
                        Content: '内存使用率已达到阈值，停止对二维数组和daq消息的显示处理，试验正常运行，请及时处理'
                    })
                }

                over70 = true
                console.warn(`内存使用率达到${memoryUsagePercent.toFixed(1)}%，已达到70%阈值，停止对二维数组和daq消息的显示处理`)
                memoryExceed.current = true
            }
        }, 2000)
    }

    // 初始化对等连接，用于与后端服务进行双向通信, 后台给我发消息
    const initPair = () => {
        try {
            if (!clientPair) {
                clientPair = new zmq.Pair()
                clientPair.connect(process.env.REACT_APP_MQ_URL)
                startCheckMemory()

                receiverMsg()
            }
        } catch (error) {
            console.log(error)
        }
    }

    // 初始化对等连接，用于与后端服务进行双向通信，前端给后台发消息
    const initUiPair = () => {
        try {
            if (!clientUiPair) {
                clientUiPair = new zmq.Pair()
                clientUiPair.connect(process.env.REACT_APP_MQ_UI_URL)
            }
            // console.log('initPair----', zmq)
        } catch (error) {
            console.log(error)
        }
    }

    // 初始化发布者，用于向消息队列发送消息
    const initPublisher = async () => {
        try {
            if (!sockPublisher) {
                const publisher = new zmq.Publisher
                // 防止多窗口端口冲突报错后下面不赋值
                try {
                    await publisher.bind(process.env.REACT_APP_MQ_PUB)
                } catch (error) {
                    console.log('error', error)
                }

                // 处理消息队列
                sockPublisher = new Queue(publisher)
            }
        } catch (error) {
            console.log(error)
        }
    }

    // 创建订阅者，用于订阅特定主题的消息
    const useSubscriber = async (subTopic) => {
        // subTopic参数默认值是HEAD_TOPIC，可以更改为任何其他任务主题。
        const sock = new zmq.Subscriber // 创建subscription type的soc
        try {
            sock.connect(process.env.REACT_APP_MQ_SUB)
            sock.subscribe(subTopic)
            return sock
        } catch (error) {
            console.log(error)
        }
        return sock
    }

    // 消息接收器，用于接收来自对等连接的消息
    const receiverMsg = async () => {
        while (true) {
            const pairMsg = await clientPair.receive()

            const topic = pairMsg[0]
            const result = pairMsg[1]

            if (!result) {
                return
            }

            let data
            try {
                data = msgpack.decode(result)
            } catch (err) {
                try {
                    data = JSON.parse(result)
                } catch (e) {
                    console.error('JSON.parse解析失败', e, result)
                    continue
                }
            }
            const subTopic = decodeURIComponent(topic)

            await handleMsg(subTopic, data, result)
        }
    }

    // 处理消息
    const handleMsg = async (subTopic, data, originData) => {
        if (window.isDebug) {
            console.log(`subTopic: ${subTopic}, UICmd: ${data?.UICmd}`, data)
        }

        if (subTopic.includes('ControlCompUIData')) {
            syncMsgToSubWindow({ subTopic, data })
            // 如果内存超过70%，不处理
            if (!memoryExceed.current) {
                sockPublisher.send([subTopic, originData])
            }
            return
        }

        switch (subTopic) {
        // 是否连接
        case TASK_TOPIC_TYPE.UI_CONNECT:
            syncMsgToSubWindow({ subTopic, data })
            setTaskServerStart(true)
            break
            // UIError信息:脚本报错
        case TASK_TOPIC_TYPE.UI_ERROR:
            syncMsgToSubWindow({ subTopic, data })
            submitSubTaskErrorData(data)
            break
            // 项目运行状态
        case TASK_TOPIC_TYPE.PROJECT_STATE_NEWVAR:
            syncMsgToSubWindow({ subTopic, data })
            syncProjectRunningStatus(data)
            break
            // 试样参数脚本通知控件
        case TASK_TOPIC_TYPE.SAMPLE_PARAMS:
            syncMsgToSubWindow({ subTopic, data })
            await submitSubTaskSampleParams(data, originData)
            break
            // 输入变量通知控件
        case TASK_TOPIC_TYPE.INPUT_VAR:
            syncMsgToSubWindow({ subTopic, data })
            await submitSubTaskInputVar(data, originData)
            break
            // 结果变量通知控件
        case TASK_TOPIC_TYPE.RESULT_VAR:
            syncMsgToSubWindow({ subTopic, data })
            await submitSubTaskResultVar(data, originData)
            break
            // 主机状态 {hostId : "1", state : ""} 不需要映射,直接渲染
        case TASK_TOPIC_TYPE.HOST_STATE_VAR:
            syncMsgToSubWindow({ subTopic, data })
            await submitSubTaskStationStatus(data, originData)
            break
            // 日志控件数据
        case UI_CMD_TYPE.LOG_CONTROL_DATA:
            syncMsgToSubWindow({ subTopic, data })
            submitSubTaskLogContralData(data, originData)
            break
            // 二级监听
        default:
            await submitSubTaskUICmd(subTopic, data, originData)
            break
        }
    }

    // 处理-UI命令
    const submitSubTaskUICmd = async (subTopic, data, originData) => {
        switch (data?.UICmd) {
        // 弹框
        case UI_CMD_TYPE.OPEN_MODAL:
        case UI_CMD_TYPE.INPUT_VAR_DIALOG:
        case UI_CMD_TYPE.OPEN_DIALOG:
        case UI_CMD_TYPE.OPEN_INPUT_DIALOG:
            sockPublisher.send([TASK_TOPIC_TYPE.UI_OPEN_MODAL, originData])
            break
        case UI_CMD_TYPE.OPEN_HOST_DIALOG:
            // 打开选择主机弹窗 并存入消息
            store.dispatch({ type: DIALOG_SELECT_STATION, param: data })
            break
        // 子任务状态
        case UI_CMD_TYPE.TASK_STATUS:
            // console.log('submitSubTaskStatus', data)
            syncMsgToSubWindow({ subTopic, data })
            submitSubTaskStatus(data)
            break
        // 流程图状态
        case UI_CMD_TYPE.PROCESS_STATUS:
            // console.log('submitSubProcessStatus', data)
            syncMsgToSubWindow({ subTopic, data })
            submitSubProcessStatus(data)
            break
        // 动作打开弹框
        case UI_CMD_TYPE.ACTION_DIALOG:
            submitSubTaskDialog(data, originData)
            break
        // 播放音频
        // {"ProcessID":"project_1","SubTaskID":"removeYsj-f1288821-33a4-4807-b061-51bbc7ce972b","UICmd":"taskPlayAudio","UIParams":{"audioId":"1111"}}
        case UI_CMD_TYPE.TASK_PLAY_AUDIO:
            sockPublisher.send([TASK_TOPIC_TYPE.UI_TASK_PLAY_AUDIO, originData])
            break
        // 输入变量修改value
        case UI_CMD_TYPE.INPUT_VAR:
            syncMsgToSubWindow({ subTopic, data })
            handleUpdateInputVar(data?.UIParams)
            break
        // 替换表头数据
        case UI_CMD_TYPE.REPLACE_DATA:
            syncMsgToSubWindow({ subTopic, data })
            submitSubTaskReplaceData(data)
            break
        // 日志数据
        case UI_CMD_TYPE.LOG_DATA:
            syncMsgToSubWindow({ subTopic, data })
            submitSubTaskLogData(data)
            break
        // 下一步状态【废弃了目前没有用】
        case UI_CMD_TYPE.NEXT_STATUS:
            handleSubTaskNextStep(data)
            break
        // 快捷方式 +  UI操作子任务 保存、另存为、打开设置、退出、关闭
        // { UIParams: { shortcutCode: ALL_SHORTCUT.保存 } }
        case UI_CMD_TYPE.SHORTCUT_DATA:
        case UI_CMD_TYPE.SAVE:
        case UI_CMD_TYPE.SAVE_AS:
        case UI_CMD_TYPE.OPEN_SETTING:
        case UI_CMD_TYPE.QUIT:
        case UI_CMD_TYPE.CLOSE:
            handelShortcutData(data)
            break
        // 开始视频
        // {
        //     "ProcessID": "project_3",
        //     "SubTaskID": "SubTaskVideoRecording-d8e8c7e8-41fc-4800-9988-21d1f6a714a3",
        //     "UICmd": "EndOfVideoRecording",
        //     "UIParams": null
        // }
        case UI_CMD_TYPE.VIDEO_RECORDING:
            submitSubTaskVideoRecording(true)
            break
            // 结束视频
        // {
        //     "ProcessID": "project_3",
        //     "SubTaskID": "SubTaskVideoRecording-d8e8c7e8-41fc-4800-9988-21d1f6a714a3",
        //     "UICmd": "EndOfVideoRecording",
        //     "UIParams": null
        // }
        case UI_CMD_TYPE.END_OF_VIDEO_RECORDING:
            submitSubTaskVideoRecording(false)
            break
        // 验证开始
        // {"ProcessID":"project_1","SubTaskID":"removeYsj-f1288821-33a4-4807-b061-51bbc7ce972b","UICmd":"StartValidation","UIParams":null}
        case UI_CMD_TYPE.START_VALIDATION:
            submitStartValidation(data)
            break
            // 试样状态推送
            /**
                {
                "ProcessID": "project_4",
                "SubTaskID": null,
                "UICmd": "sampleInstStateChanged",
                "UIParams": {
                    "TargetState": "abort",
                    "InstCode": "sample_2fb02ef"
                }
            }
             */
        case UI_CMD_TYPE.SAMPLE_INST_STATE_CHANGED:
            submitSampleInstStateChanged(data)
            break
        case UI_CMD_TYPE.NEXT_STEP:
            submitNextStepChanged(data)
            break
        default:
            if (window.isDebug) {
                console.log('未监听到的消息', subTopic, data)
            }
            break
        }
    }

    const syncMsgToSubWindow = ({ subTopic, data }) => {
        // 没有pair说明是子窗口，不需要同步
        if (!clientPair) {
            return
        }
        sendMsgToSubWindow({ subTopic, data })
    }

    // 处理-修改输入变量参数
    // 1. 查询当前输入变量数据
    // 2. 修改输入变量
    // 3. 更新redux
    const handleUpdateInputVar = async (param) => {
        const inputVarData = await getInputVarList()
        if (inputVarData) {
            const inputVar = inputVarData?.find(f => param?.code === f?.code)
            if (inputVar) {
                const [detail] = await getInputVarDetail({ ids: [inputVar.id] })
                if (detail) {
                    const data = { ...detail, default_val: { ...detail.default_val, value: param?.value } }
                    const putRes = updateInputVar(data)
                    if (putRes) {
                        dispatch(initInputVariables(false))
                    }
                }
            }
        }
    }

    // 试验状态
    const syncProjectRunningStatus = async (data) => {
        const running = data.UIParams.runningStatus
        const projectId = data.ProcessID.split('_')[1]

        // 同步所有项目状态
        dispatch({
            type: SYSTEM_UPDATE_PROJECT_RUNNING_STATUS,
            param: {
                projectId,
                status: running
            }
        })
        // 更新当前项目状态
        dispatch({ type: SUB_TASK_OPEN_EXPERIMENT, param: running })

        // 修改当前项目信息
        if (Number(store.getState().project.projectId) === Number(projectId)) {
            if (running) {
                // 修改试样颜色
                const color = color16()
                let tempSample = store.getState().project.optSample

                if (tempSample.color === '#FFFFFF') {
                    await editSampleColor({
                        id: tempSample.key,
                        color,
                        status: running ? SAMPLE_STATUS_TYPE.RUNNING : undefined
                    })
                    tempSample = { ...tempSample, color }
                }

                initSampleTree(false)
                submitSubTaskSample(tempSample)
            } else {
                submitSubTaskSample(null)
            }
        }
    }

    // 信号变量
    const submitSignalVar = async (topic, data, originData) => {
        // // 当现在消息正在阻塞 丢弃信号变量
        // if (sockPublisher.sending) {
        //     console.warn('publisher正在发送消息，信号变量消息丢弃', data)
        //     return
        // }

        sockPublisher.send([topic, originData])
    }

    // 弹出通知
    const submitSubTaskDialog = async (data, originData) => {
        // console.log('动作打开弹框', data)
        const type = data?.UIParams?.type

        // 没选对模式则不处理
        if (!type) {
            return
        }

        // 定制化弹窗mq中还有显示内容
        if (type === DIALOG_CUSTOM_MODAL) {
            sockPublisher.send([TASK_TOPIC_TYPE.UI_ACTION_DIALOG, originData])
        } else if (Object.values(ALL_DIALOG_TYPE).includes(type)) {
            openDialog({ type })
        } else if (Object.values(SHORTCUT_MODAL).includes(type)) {
            submitSubTaskShortcutData({ ...data, UIParams: { shortcutCode: type } })
        } else {
            const [id, layoutType] = type.split('___')

            if (layoutType === LAYOUT_TYPE.弹窗) {
                // 打开组态弹窗
                openDialog({ type: DIALOG_LAYOUT_JSON, data: id })
            } else if (layoutType === LAYOUT_TYPE.预览窗口) {
                openSubWindow(id)
            } else {
                // 切换页面布局
                subCurrentPageId(id)
            }
        }
    }

    // 打开弹窗子窗口
    const openSubWindow = (id) => {
        const subWindow = store.getState().template.pageData.find(i => i.id === id)

        const { contentWidth, contentHeight } = JSON.parse(subWindow?.window_property)

        openProjectSubWindow({
            projectId: getProjectId(),
            pageId: id,
            width: contentWidth,
            height: contentHeight
        })
    }

    // 主机状态通知
    const submitSubTaskStationStatus = async (data, originData) => {
        const { hostId } = data
        const subTopic = `${hostId}_Station_STATUS`
        sockPublisher.send([subTopic, originData])
    }

    // 结果变量通知控件
    const submitSubTaskResultVar = async (data, originData) => {
        // 结果变量通知控件
        const {
            Code, InstCode, Value, Index, Error, ErrorMessage
        } = data

        sockPublisher.send([Code, originData])

        syncResult({
            sampleCode: InstCode, resultCode: Code, newValue: Value, index: Index, error: Error, errorMessage: ErrorMessage
        })
    }

    // 输入变量通知控件
    const submitSubTaskInputVar = async (data, originData) => {
        const { Code, Value } = data
        sockPublisher.send([Code, originData])
        updateVar(data)
    }

    const updateVar = (data) => {
        const dimension = store?.getState()?.global?.unitList?.find(i => i.code === data?.Dimension)

        updateInputVariable({
            code: data.Code,
            is_feature: data.IsCheck === false ? 0 : 1,
            default_val: {
                value: data.Value,
                isConstant: data.IsConstant === false ? 0 : 1,
                unitType: dimension?.id,
                unit: dimension?.units?.find(i => i.code === data?.Unit)?.id,
                type: data.Mode
            }
        }, undefined, true)
    }

    // 试样参数脚本通知控件
    const submitSubTaskSampleParams = async (data, originData) => {
        const {
            InstCode,
            Code
        } = data
        sockPublisher.send([`${InstCode}-${Code}`, originData])
    }

    // 替换信号变量数据源
    const submitSubTaskReplaceData = (data) => {
        dispatch({ type: SUB_TASK_REPLACE_DATA, param: data })
    }

    // 日志
    const submitSubTaskLogData = (data) => {
        dispatch({ type: SUB_TASK_LOG, param: data })
    }
    // 日志控件
    const submitSubTaskLogContralData = async (data, originData) => {
        sockPublisher.send([TASK_TOPIC_TYPE.LOG_CONTROL_DATA, originData])
    }

    // 编辑错误信息
    const submitSubTaskErrorData = (data) => {
        dispatch({ type: SUB_TASK_ERROR, param: { ...data, create_time: getCurrentTime() } })
    }

    // 处理快捷方式 + ui操作子任务
    const handelShortcutData = (data) => {
        if (data.UICmd === UI_CMD_TYPE.SHORTCUT_DATA) {
            submitSubTaskShortcutData(data)
        } else {
            submitSubTaskShortcutData({ ...data, UIParams: { shortcutCode: UI_ACTION[data.UICmd] } })
        }
    }

    // 快捷方式
    const submitSubTaskShortcutData = (data) => {
        dispatch({ type: SUB_TASK_SHORTCUT, param: data })
    }
    // 是否录制视频
    const submitSubTaskVideoRecording = (data) => {
        dispatch({ type: SUB_TASK_VIDEO_RECORDING, param: data })
    }

    // 提交开始校验
    const submitStartValidation = (param) => {
        dispatch({ type: SUB_TASK_START_VALIDATION, param })
    }
    // 试样状态推送
    const submitSampleInstStateChanged = (param) => {
        dispatch({ type: SUB_SAMPLE_INST_STATE_CHANGED, param })
    }

    // 试样状态推送
    const submitNextStepChanged = (param) => {
        dispatch({ type: SUB_NEXT_STEP_CHANGED, param })
    }

    // 子任务状态 如果是Array全更新，
    const submitSubTaskStatus = (data) => {
        let taskStatusTemp = data
        // 单个对象的更新逻辑
        const currentSubTaskStatus = store.getState().subTask.subTaskStatus || []
        const existingTaskIndex = currentSubTaskStatus.findIndex(f => f?.SubTaskID === data?.SubTaskID)
        taskStatusTemp = [...currentSubTaskStatus]
        if (existingTaskIndex > -1) {
            taskStatusTemp[existingTaskIndex] = {
                ...data,
                create_time: taskStatusTemp[existingTaskIndex].create_time,
                update_time: getCurrentTime()
            }
        } else {
            // 添加新任务
            taskStatusTemp.push({ ...data, create_time: getCurrentTime() })
        }
        dispatch({ type: SUB_TASK_STATUS, param: taskStatusTemp })
    }
    // 流程图状态
    const submitSubProcessStatus = data => {
        const currentSubProcessStatus = store.getState().subTask.subProcessStatus || []
        const subProcessStatusTemp = [...currentSubProcessStatus]
        const existingTaskIndex = currentSubProcessStatus.findIndex(f => f?.ProcessID === data?.ProcessID)
        if (existingTaskIndex > -1) {
            subProcessStatusTemp[existingTaskIndex] = data
        } else {
            subProcessStatusTemp.push(data)
        }
        // console.log('subProcessStatusTemp', subProcessStatusTemp)
        dispatch({ type: SUB_TASK_PROCESS_STATUS, param: subProcessStatusTemp })
    }

    // 清空
    const clear = () => {
        dispatch({ type: SUB_TASK_CLEAR, param: [] })
        submitSubTaskNextStep([])
        // 只留运行中
        dispatch({ type: SUB_TASK_STATUS, param: store.getState().subTask.subTaskStatus.filter(i => i?.UIParams?.status === 'start-running') })
    }

    // 保存当前做实验的试样code
    const submitSubTaskSample = (data) => {
        dispatch({ type: SUB_TASK_SAMPLE, param: data })
    }

    // 是否完成主流程图
    const submitIsFinishMain = (data = false) => {
        dispatch({ type: SUB_TASK_IS_FINISH_MAIN, param: data })
    }

    // 保存下一步任务
    const submitSubTaskNextStep = (data) => {
        dispatch({ type: SUB_TASK_NEXT_STEP, param: data })
    }

    // 处理下一步任务
    const handleSubTaskNextStep = (data) => {
        const { subTaskNextStep = [] } = store.getState().subTask

        const taskIndex = subTaskNextStep.findIndex(m => m.key === data.SubTaskID)

        // 直接操作匹配的任务
        const updatedNextStep = [...subTaskNextStep]
        if (taskIndex === -1) {
            updatedNextStep.push(data) // 添加新任务
        } else {
            updatedNextStep[taskIndex] = {
                ...updatedNextStep[taskIndex],
                flag: data.UIParams.flag
            }
        }
        // 提交更新
        submitSubTaskNextStep(updatedNextStep)
    }

    // 发送消息

    // (string ProcessID, string? SubTaskID, string Target, string? Action, string? JsonArgs)
    const send = async (param) => {
        try {
            await clientUiPair.send(param)
        } catch (error) {
            console.log('error', error)
        }
    }

    // 关闭连接
    const close = () => {
        clientPair?.close()
        clientUiPair?.close()
        clientPair = undefined
        clientUiPair = undefined
    }

    return {
        initPair,
        close,
        send,
        clear,
        submitSubTaskSample,
        submitIsFinishMain,
        submitSubTaskNextStep,
        initPublisher,
        useSubscriber,
        submitSubTaskStatus,
        submitStartValidation,
        submitSubTaskVideoRecording,
        submitSampleInstStateChanged,
        submitNextStepChanged,
        initUiPair,
        handleMsg
    }
}

export default useSubTask
