import React, {
    useEffect, useMemo, useRef, forwardRef, useImperativeHandle
} from 'react'
import { useSelector } from 'react-redux'

import { getAllSample } from '@/module/layout/controlComp/lib/CurveDaqBuffer/utils/sample'
import { config2ChartOption } from '../utils/config2ChartOption'
import { SOURCE_TYPE } from '../../constants/constants'

const filterSampleCurve = (config, sampleMap) => {
    const getCurves = (curves) => {
        return Object.fromEntries(
            Object.entries(curves)
                // 过滤空试样曲线
                .filter(([key, value]) => !!sampleMap[key])
                // 白色曲线应用试样颜色
                .map(([key, value]) => {
                    return [
                        key,
                        {
                            ...value,
                            lines: value.lines.map(i => {
                                if (i.color === '#FFFFFF') {
                                    return {
                                        ...i,
                                        color: sampleMap[key].color
                                    }
                                }
                                return i
                            })
                        }
                    ]
                })
        )
    }
    return {
        ...config,
        curveGroup: {
            ...config.curveGroup,
            yAxis: {
                ...config.curveGroup.yAxis,
                curves: getCurves(config.curveGroup.yAxis.curves)

            },
            y2Axis: {
                ...config.curveGroup.y2Axis,
                curves: getCurves(config.curveGroup.y2Axis.curves)
            }
        }

    }
}

const filterCurrvetSamplePointTag = (config, optSample) => {
    const getCurves = (curves) => {
        return Object.fromEntries(
            Object.entries(curves)
                // 白色曲线应用试样颜色
                .map(([key, value]) => {
                    if (key === optSample.code) {
                        return [key, value]
                    }
                    return [
                        key,
                        {
                            ...value,
                            lines: value.lines.map(i => {
                                return {
                                    ...i,
                                    pointTags: i?.pointTags?.filter(t => {
                                        return !t.isSample || (key === optSample.code)
                                    }) ?? []
                                }
                            })
                        }
                    ]
                })
        )
    }

    return {
        ...config,
        curveGroup: {
            ...config.curveGroup,
            yAxis: {
                ...config.curveGroup.yAxis,
                curves: getCurves(config.curveGroup.yAxis.curves)
            },
            y2Axis: {
                ...config.curveGroup.y2Axis,
                curves: getCurves(config.curveGroup.y2Axis.curves)
            }
        }

    }
}

const useChartConfig = ({
    config, compStatus, isBufferCurve
}) => {
    const openExperiment = useSelector(state => state.subTask.openExperiment)
    const multiSample = useSelector(state => state.project.multiSample)
    const optSample = useSelector(state => state.project.optSample)
    const sampleData = useSelector(state => state.project.sampleData)

    const sampleMap = useMemo(() => {
        // buffer 结果文件需要根据所有试样/多选试样 过滤出没有试验的试样
        if (isBufferCurve && config?.base?.sourceType === SOURCE_TYPE.多数据源) {
            const sampleList = sampleData?.map(m => m.children)?.flat()

            const res = {}

            sampleList.forEach(item => {
                if (item.status === 'finished' && (!multiSample.length > 0 || multiSample.includes(item.id))) {
                    res[item.code] = item
                }
            })

            return res
        }

        return null
    }, [openExperiment, isBufferCurve, config?.base?.sourceType, multiSample, sampleData])

    // config整理成曲线要的格式
    const filteredConfig = useMemo(() => {
        if (!config?.base) {
            return undefined
        }

        let c = { ...config }

        if (isBufferCurve && config?.base?.sourceType === SOURCE_TYPE.多数据源) {
            // 过滤空试样曲线
            if (sampleMap) {
                c = filterSampleCurve(c, sampleMap)
            }

            // 过滤直流当前试样的点标注
            c = filterCurrvetSamplePointTag(c, optSample)
        }

        // 试验中去掉点标注块标注
        if (openExperiment) {
            c = {
                ...c,
                pointTag: {
                    ...(c?.pointTag ?? {}),
                    open: false
                },
                chunkTag: {
                    ...(c?.chunkTag ?? {}),
                    open: false
                }
            }
        }

        return c
    }, [config, openExperiment, sampleMap, isBufferCurve, optSample])

    const chartOption = useMemo(() => {
        if (!filteredConfig?.base) {
            return undefined
        }

        return config2ChartOption(filteredConfig, compStatus, isBufferCurve)
    }, [filteredConfig, isBufferCurve])

    return {
        filteredConfig,
        chartOption
    }
}

export default useChartConfig
