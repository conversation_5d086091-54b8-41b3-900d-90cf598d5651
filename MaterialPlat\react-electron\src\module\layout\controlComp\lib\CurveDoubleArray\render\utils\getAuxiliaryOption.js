import {
    auxiliaryLineType, lineConfig, lineType, auxiliaryDataType
} from '@/pages/dialog/auxiliaryLineModal/components/saveModal/constants'
import { INPUT_VARIABLE_TYPE } from '@/utils/constants'
import store from '@/redux/store'
import { getCurrentResultByCode } from '@/hooks/project/resultVariable/utils'

const list = [
    // {
    //     b_value: null,
    //     updated_user_id: 1,
    //     channel_type: 'array',
    //     name: '预制裂纹辅助线1',
    //     created_time: '2025-02-25 14:18:09',
    //     x_channel: 'bianxing',
    //     array_code: 'input_yzlw_fh_bx',
    //     type: 'segment',
    //     updated_time: '2025-06-18 15:52:47',
    //     c_value: {
    //         input_code: '',
    //         value: 0,
    //         is_fx: 0
    //     },
    //     a_value: {
    //         input_code: '',
    //         value: 1,
    //         is_fx: 0
    //     },
    //     dot2: {
    //         y: 0,
    //         is_fx_x: 1,
    //         result_code: '',
    //         input_code_y: 'input_yzlw_fzx1',
    //         input_code_x: 'input_yzlw_fzx_zd',
    //         is_fx_y: 1,
    //         x: 10000,
    //         is_fx: 0
    //     },
    //     config_type: 'segment',
    //     line_type: 'dashed',
    //     id: 'c4f05034-4fb7-4b7a-88ba-eae048456fb9',
    //     y_channel: 'fuhe',
    //     delete_flag: 0,
    //     dot: {
    //         y: 0,
    //         is_fx_x: 1,
    //         result_code: 'result_loop1_XZDRD',
    //         input_code_y: 'input_yzlw_fzx1',
    //         input_code_x: 'input_yzlw_fzx_qd',
    //         is_fx_y: 1,
    //         x: -999,
    //         is_fx: 0
    //     },
    //     line_color: '#000000',
    //     created_user_id: 1
    // },
    // ...
]

const getAuxiliaryOption = (config) => {
    try {
        const { auxiliary, curveGroup } = config
        if (!auxiliary || auxiliary.length === 0) {
            return []
        }

        const ids = auxiliary

        // 从 Redux store 获取辅助线列表数据
        const state = store.getState()
        const auxiliaryLineList = state.template.auxiliaryLineList || []

        const result = []
        ids.forEach(id => {
            const item = auxiliaryLineList.find(f => f.id === id)
            if (item) {
                // 转换为图表组件需要的格式
                const auxiliaryConfig = {
                    name: item.name,
                    id: item.id,
                    type: item.type === auxiliaryLineType.直线 ? 'straight' : 'segment',
                    xAxisId: item.x_channel || 'x1',
                    yAxisId: item.y_channel || 'y1',
                    style: {
                        line_color: item.line_color || '#000000',
                        // eslint-disable-next-line no-nested-ternary
                        line_type: item.line_type === lineType.实线 ? 'solid'
                            : item.line_type === lineType.虚线 ? 'dashed' : 'dotted',
                        thickness: item.thickness || 2
                    }
                }

                // 辅助函数：获取变量值，支持 is_fx 判断
                const getVariableValue = (valueObj, inputCode) => {
                    if (valueObj?.is_fx && inputCode) {
                        const variable = store.getState().inputVariable.inputVariableMap.get(inputCode)
                        return variable?.default_val?.value
                    }
                    return valueObj?.value ?? 0
                }

                // 辅助函数：获取坐标点值，支持 is_fx_x 和 is_fx_y 判断
                const getPointValue = (dot) => {
                    let x = dot?.x ?? 0
                    let y = dot?.y ?? 0

                    // 处理 x 坐标的 is_fx 判断
                    if (dot?.is_fx_x && dot?.input_code_x) {
                        const variable = store.getState().inputVariable.inputVariableMap.get(dot.input_code_x)
                        x = variable?.default_val?.value ?? x
                    }

                    // 处理 y 坐标的 is_fx 判断
                    if (dot?.is_fx_y && dot?.input_code_y) {
                        const variable = store.getState().inputVariable.inputVariableMap.get(dot.input_code_y)
                        y = variable?.default_val?.value ?? y
                    }

                    // 如果 dot.is_fx 为 true，则从结果数据中获取下标
                    if (dot?.is_fx && dot?.result_code) {
                        const resultData = getCurrentResultByCode(dot.result_code)
                        if (resultData && typeof resultData.index === 'number') {
                            return { index: resultData.index, isIndexPoint: true }
                        }
                    }

                    return { x, y }
                }

                // 根据辅助线配置类型设置数据和配置类型
                if (item.config_type === lineConfig.两点配置) {
                    const point1 = getPointValue(item.dot)
                    const point2 = getPointValue(item.dot2)

                    // 检查是否有下标点，如果有则使用混合配置或下标配置
                    const hasIndexPoint1 = point1?.isIndexPoint
                    const hasIndexPoint2 = point2?.isIndexPoint

                    if (hasIndexPoint1 || hasIndexPoint2) {
                        let lineId

                        if (curveGroup.yAxis.curves?.optSample) {
                            lineId = curveGroup.yAxis.curves?.optSample?.lines?.[0].id
                        } else {
                            lineId = Object.values(curveGroup.yAxis.curves)?.[0]?.lines?.[0]?.id
                        }

                        // 当使用下标配置时，需要指定lineId
                        auxiliaryConfig.lineId = lineId
                    }

                    // 统一使用segment配置类型，不再区分indexSegment
                    auxiliaryConfig.configType = 'segment'
                    auxiliaryConfig.data = [
                        hasIndexPoint1 ? { index: point1.index } : { x: point1.x, y: point1.y },
                        hasIndexPoint2 ? { index: point2.index } : { x: point2.x, y: point2.y }
                    ]
                } else if (item.config_type === lineConfig.垂直X轴配置) {
                    // 垂直X轴配置使用xStraight类型
                    auxiliaryConfig.configType = 'xStraight'
                    const xValue = getVariableValue(item.c_value, item.c_value?.input_code)
                    auxiliaryConfig.data = [{ x: xValue }]
                } else if (item.config_type === lineConfig.斜率配置) {
                    auxiliaryConfig.configType = 'slopeStraight'
                    const point = getPointValue(item.dot)
                    const slope = getVariableValue(item.a_value, item.a_value?.input_code)

                    // 检查斜率配置中的点是否为下标点
                    if (point?.isIndexPoint) {
                        // 当使用下标配置时，需要指定lineId
                        auxiliaryConfig.lineId = item.array_code || '1'
                        auxiliaryConfig.data = [{
                            index: point.index,
                            slope // 使用slope作为斜率参数名
                        }]
                    } else {
                        auxiliaryConfig.data = [{
                            x: point.x,
                            y: point.y,
                            slope // 使用slope作为斜率参数名
                        }]
                    }
                }

                result.push(auxiliaryConfig)
            }
        })

        return result
    } catch (error) {
        console.log('error', error)
        return []
    }
}

export { getAuxiliaryOption }
