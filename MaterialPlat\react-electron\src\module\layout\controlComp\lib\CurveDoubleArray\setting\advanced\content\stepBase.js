import React, { useMemo, forwardRef } from 'react'
import {
    Form, Row, Col, Select, Checkbox, Input, InputNumber, message
} from 'antd'
import { useTranslation } from 'react-i18next'

import useDoubleArrayInputVariable from '@/hooks/project/inputVariable/useDoubleArrayInputVariable'
import useDoubleArrayListInputVariable from '@/hooks/project/inputVariable/useDoubleArrayListInputVariable'
import useNumberInputVariable from '@/hooks/project/inputVariable/useNumberInputVariable'
import useBufferInputVariable from '@/hooks/project/inputVariable/useBufferInputVariable'

import { initBufferCurve } from '@/module/layout/controlComp/lib/CurveDaqBuffer/utils/initCurve'

import { initArrayCurve } from '../../../arrayUtils/initCurve'

import { SOURCE_TYPE } from '../../constants'

const { Item } = Form

/**
 * 第一步，曲线图
 */
const StepBase = forwardRef(({ isBufferCurve }) => {
    const { t } = useTranslation()
    const form = Form.useFormInstance()

    const enableSettingName = Form.useWatch(['base', 'isName'], form)
    const sourceType = Form.useWatch(['base', 'sourceType'], form)

    const inputVariableNumber = useNumberInputVariable()
    const inputVariableDoubleArray = useDoubleArrayInputVariable()
    const inputVariableDoubleArrayList = useDoubleArrayListInputVariable()
    const inputVariableBuffer = useBufferInputVariable()

    const sourceTypeOptions = useMemo(() => {
        if (isBufferCurve) {
            return inputVariableBuffer
        }

        return sourceType === SOURCE_TYPE.单数据源 ? inputVariableDoubleArray : inputVariableDoubleArrayList
    }, [sourceType, isBufferCurve])

    const resetAxis = () => {
        // 清空 x/y/y2轴名称和单位
        form.setFieldValue(['xAxis', 'name'], '')
        form.setFieldValue(['xAxis', 'unit'], '')
        form.setFieldValue(['yAxis', 'name'], '')
        form.setFieldValue(['yAxis', 'unit'], '')
        form.setFieldValue(['y2Axis', 'name'], '')
        form.setFieldValue(['y2Axis', 'unit'], '')
        // 清空 曲线组上绑定的xy轴信号
        form.setFieldValue(['curveGroup', 'yAxis', 'xSignal'], '')
        form.setFieldValue(['curveGroup', 'yAxis', 'ySignal'], [])
        form.setFieldValue(['curveGroup', 'y2Axis', 'xSignal'], '')
        form.setFieldValue(['curveGroup', 'y2Axis', 'ySignal'], [])
    }

    return (
        <div className="step-base">
            <div className="centre">
                <Row>
                    <Col span={8}>
                        <Item
                            name={['base', 'isName']}
                            label={t('曲线控件名称')}
                            valuePropName="checked"
                            labelCol={{ span: 12 }}
                        >
                            <Checkbox />
                        </Item>
                    </Col>
                    <Col span={12} pull={3}>
                        <Item
                            name={['base', 'name']}
                        >
                            <Input disabled={!enableSettingName} />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['base', 'sourceType']}
                            label={isBufferCurve ? t('显示') : t('关联数据源')}
                        >
                            <Select
                                options={[
                                    { label: isBufferCurve ? t('试样') : t('二维数组'), value: SOURCE_TYPE.单数据源 },
                                    { label: isBufferCurve ? t('结果文件') : t('二维数组集合'), value: SOURCE_TYPE.多数据源 }
                                ]}
                                onChange={() => {
                                    resetAxis()
                                    // 清空 数据源变量
                                    form.setFieldValue(['base', 'sourceInputCode'], '')
                                    form.setFieldValue(['pointTag', 'open'], false)

                                    // 清空曲线
                                    form.setFieldValue(['curveGroup', 'yAxis', 'curves'], {})
                                    form.setFieldValue(['curveGroup', 'y2Axis', 'curves'], {})
                                }}
                            />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['base', 'sourceInputCode']}
                            label={isBufferCurve ? t('buffer') : t('数据源变量')}
                        >
                            <Select
                                options={sourceTypeOptions}
                                fieldNames={{ label: 'name', value: 'code' }}
                                onChange={(v, o) => {
                                    resetAxis()

                                    let newCurves

                                    if (isBufferCurve) {
                                        newCurves = initBufferCurve({
                                            sourceType
                                        })
                                    } else {
                                        newCurves = initArrayCurve({
                                            sourceType,
                                            arrayVar: o
                                        })
                                    }

                                    message.info('更新数据源后，需要重新配置 曲线组信号|标注结果|坐标源')

                                    // 关闭点标签
                                    form.setFieldValue(['pointTag', 'open'], false)
                                    // 重置坐标源
                                    form.setFieldValue(['defineAxis'], {
                                        isDefineAxis: false,
                                        inputCode: '',
                                        source: []
                                    })
                                    form.setFieldValue(['curveGroup', 'yAxis', 'curves'], newCurves)
                                    form.setFieldValue(['curveGroup', 'y2Axis', 'curves'], newCurves)
                                }}
                            />
                        </Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['base', 'updateFreq']}
                            label={t('显示更新频率')}
                        >
                            <Select
                                options={[
                                    { label: '0.1s', value: 90 },
                                    { label: '0.2s', value: 180 },
                                    { label: '0.5s', value: 470 },
                                    { label: '1s', value: 900 },
                                    { label: '2s', value: 1900 }
                                ]}
                            />
                        </Item>
                    </Col>
                </Row>
                {
                    (isBufferCurve && sourceType === SOURCE_TYPE.多数据源) && (
                        <>
                            <Row>
                                <Col span={12}>
                                    <Item
                                        name={['base', 'xOffset']}
                                        label={t('x-偏移')}
                                    >
                                        <InputNumber />
                                    </Item>
                                </Col>
                            </Row>
                            <Row>
                                <Col span={12}>
                                    <Item
                                        name={['base', 'yOffset']}
                                        label={t('y-偏移')}
                                    >
                                        <InputNumber />
                                    </Item>
                                </Col>
                            </Row>
                        </>
                    )
                }
            </div>
            <div className="centre">
                <div className="title">{t('激活十字线关联变量')}</div>
                <Row>
                    <Col span={12}>
                        <Item
                            name={['base', 'crossInputCode']}
                            label={t('绑定变量')}
                        >
                            <Select
                                allowClear
                                fieldNames={{ label: 'name', value: 'code' }}
                                options={inputVariableNumber}
                            />
                        </Item>
                    </Col>
                </Row>
            </div>
        </div>
    )
})

export default StepBase
