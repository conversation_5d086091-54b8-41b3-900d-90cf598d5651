import React, {
    useCallback, useEffect, useMemo, useRef, useState
} from 'react'
import { useTranslation } from 'react-i18next'
import {
    Form, Row, Col, Switch, InputNumber, Select, Checkbox
} from 'antd'
import styled from 'styled-components'
import isEqual from 'lodash/isEqual'

import VModal from '@/components/vModal/index'
import SelectInputVariableCode from '@/components/formItems/selectInputVariableCode/index'
import SelectActionId from '@/components/formItems/selectActionId/index'
import BindInputVariable from '@/components/formItems/bindInputVariable'
import useDoubleArrayInputVariable from '@/hooks/project/inputVariable/useDoubleArrayInputVariable'
import { INPUT_VARIABLE_TYPE } from '@/utils/constants'

import { DATA_SHOW_TYPE } from '../constants'
import TablePreview from './tablePreview/index'
import TableSettingButton from './TableSettingButton/index'

const { Item, useForm } = Form

const DATA_SHOW_TYPE_OPTIONS = Object.keys(DATA_SHOW_TYPE).map(key => ({
    label: key,
    value: DATA_SHOW_TYPE[key]
}))

const Style = styled.div`
    .bind-input-variable{
        display: block;
        .bind-value-span{
            margin-bottom: 4px ;
        }
    }
`

// 配置的弹窗
const SettingDialog = ({
    open, setOpen, config, updateConfig
}) => {
    const { t } = useTranslation()

    const inputList = useDoubleArrayInputVariable()

    const inputListRef = useRef(null)

    const [form] = useForm()

    useEffect(() => {
        // TODO 初始化时，获取二维数组的配置，与当前配置对比有无新增删除
    }, [])

    useEffect(() => {
        initFormData()
    }, [config, inputList])

    const initFormData = useCallback(() => {
        if (!isEqual(inputList, inputListRef?.current)) {
            inputListRef.current = inputList

            let colsConfigList = []

            // 输入变量监听 - 二维数组变量  表格列合并
            if (inputList && config?.dataSourceCode && config?.colsConfig) {
                // 从输入变量中查找当前数据源配置
                const valueObj = inputList?.find(v => v.code === config?.dataSourceCode)
                if (valueObj) {
                    // 获取二维数组的列信息
                    const columns = valueObj.double_array_tab?.columns

                    // 过滤并更新已存在的列配置
                    colsConfigList = config?.colsConfig
                        .filter((it) => columns.some(i => i.code === it.code))
                        .map((it) => {
                            const obj = columns.find(i => i.code === it.code) || {}
                            return {
                                ...it,
                                showName: obj.showName,
                                openCorrelation: obj.openCorrelation,
                                correlationCode: obj.correlationCode,
                                type: obj.type,
                                typeParam: obj.typeParam
                            }
                        })

                    // 添加新地列到配置列表
                    const addList = columns.filter(it => !config?.colsConfig.some(i => i.code === it.code))
                    addList.forEach((it) => {
                        colsConfigList.push({
                            code: it.code,
                            colTitle: it.showName,
                            showName: it.showName,
                            type: it.type,
                            typeParam: it.typeParam,
                            openCorrelation: it.openCorrelation,
                            correlationCode: it.correlationCode,
                            show: true,
                            isEdit: false,
                            isTriggerAction: false
                        })
                    })
                }
            }

            form.setFieldsValue({
                ...config,
                colsConfig: colsConfigList
            })
        }
    }, [inputList])

    // 初始化列的数据
    const initCols = (code) => {
        const columns = inputList.find(i => i.code === code)?.double_array_tab?.columns
        const newColsConfig = columns.map(i => ({
            ...i,
            colTitle: i.showName,
            isEdit: false,
            isTriggerAction: false,
            show: true
        }))

        form.setFieldValue('colsConfig', newColsConfig)
    }

    const onOk = async () => {
        try {
            const res = await form.validateFields()
            updateConfig(res)
            setOpen(false)
        } catch (error) {
            console.log('err', error)
        }
    }

    const onCancel = () => {
        setOpen(false)
    }

    const isSaveNewData = Form.useWatch('isSaveNewData', form)
    const dataShowType = Form.useWatch('dataShowType', form)

    return (
        <VModal
            open={open}
            title={t('二维数组表格')}
            maskClosable={false}
            width="50vw"
            onOk={onOk}
            onCancel={onCancel}
        >
            <Style>
                <Form
                    form={form}
                    labelCol={{
                        span: 8
                    }}
                    wrapperCol={{
                        span: 16
                    }}
                    onValuesChange={(newVal) => {
                        if ('dataSourceCode' in newVal) {
                            initCols(newVal?.dataSourceCode)
                        }
                    }}
                >
                    <Row>
                        <Col span={12}>
                            <Item
                                label="数据源"
                                name="dataSourceCode"
                            >
                                <SelectInputVariableCode inputVariableType={INPUT_VARIABLE_TYPE.二维数组} />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="显示更新频率"
                                name="updateFreq"
                                initialValue={1000}
                            >
                                <Select
                                    options={[
                                        { label: '0.1s', value: 90 },
                                        { label: '0.2s', value: 180 },
                                        { label: '0.5s', value: 470 },
                                        { label: '1s', value: 900 },
                                        { label: '2s', value: 1900 }
                                    ]}
                                />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="表格输入动作"
                                name="tableInputActionId"
                            >
                                <SelectActionId allowClear />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="显示行数"
                                name="showRowNumber"
                            >
                                <InputNumber max={50} min={1} />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="数据显示"
                                name="dataShowType"
                                initialValue={DATA_SHOW_TYPE.默认行数显示}
                            >
                                <Select options={DATA_SHOW_TYPE_OPTIONS} />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="滚动位置绑定"
                                name="scrollToValue"
                            >
                                <BindInputVariable inputVariableType={INPUT_VARIABLE_TYPE.数字型} />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="倒序排列"
                                name="isReverse"
                                valuePropName="checked"
                            >
                                <Switch />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                style={{ paddingLeft: '20px' }}
                                label=""
                                name="isSaveNewData"
                                valuePropName="checked"
                            >
                                <Checkbox>只保留显示最新数据</Checkbox>
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="显示条数"
                                name="showNumber"
                            >
                                <InputNumber disabled={!isSaveNewData} min={0} />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="左侧固定列数"
                                name="leftFixedNumber"
                            >
                                <InputNumber disabled={dataShowType !== DATA_SHOW_TYPE.滚动条显示} min={0} allowClear />
                            </Item>
                        </Col>
                        <Col span={12}>
                            <Item
                                label="上方固定行数"
                                name="topFixedNumber"
                            >
                                <InputNumber disabled={dataShowType !== DATA_SHOW_TYPE.滚动条显示} min={0} allowClear />
                            </Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Item
                                label="表格预览"
                                name="colsConfig"
                                labelCol={{
                                    span: 4
                                }}
                                wrapperCol={{
                                    span: 20
                                }}
                            >
                                <TableSettingButton />
                            </Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={20} offset={4}>
                            <TablePreview />
                        </Col>
                    </Row>
                </Form>
            </Style>

        </VModal>
    )
}

export default SettingDialog
