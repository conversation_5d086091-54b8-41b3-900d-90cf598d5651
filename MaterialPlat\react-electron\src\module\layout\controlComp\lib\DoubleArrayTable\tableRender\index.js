import React, { useEffect, useMemo, useState } from 'react'
import { Form, Pagination } from 'antd'

import { isEmpty } from '@/utils/utils'

import { DATA_SHOW_TYPE } from '../constants'
import LayoutTable from './layoutTable'

/**
 * @description 表格渲染组件
 * @param colsConfig 列配置
 * @param dataSource 数据源
 * @param dataShowType 数据展示类型
 * @param showRowNumber 显示行数
 * @param onInput 输入后的回调
*/
const TableRender = ({
    showData, total, upDataRange,
    config, colsConfig, onInput, isPdf, isEdit
}) => {
    const [form] = Form.useForm()

    const [pageNumber, setPageNumber] = useState(1)

    // 初始化状态
    useEffect(() => {
        setPageNumber(1)
    }, [config])

    useEffect(() => {
        form.setFieldValue('dataSourceLength', total)
        if (total === 0) {
            setPageNumber(1)
        }
    }, [total])

    useEffect(() => {
        if (config?.dataShowType === DATA_SHOW_TYPE.默认行数显示) {
            setPageNumber(1)
        }
    }, [config?.dataShowType])

    // 效果优化 倒序订阅时 自动移动下标
    useEffect(() => {
        // 当数据总数发生变化时，pageNumber不为0时 页码更新至对应位置
        if (config?.dataShowType === DATA_SHOW_TYPE.页码显示 && config?.isReverse) {
            setPageNumber((prev) => {
                if (prev === 1) return 1
                return prev + 1
            })
        }
    }, [Math.floor((total / config?.showRowNumber)), config?.dataShowType, config?.isReverse])

    // 同步form中的真实数据
    useEffect(() => {
        // 可编辑表格直接同步
        if (isEdit) {
            form.setFieldsValue({ value: showData.slice((pageNumber - 1) * config?.showRowNumber, pageNumber * config?.showRowNumber) })
        } else {
            form.setFieldsValue({ value: showData })
        }
    }, [showData, isEdit, pageNumber])

    // 给表格数据添加index，明确行号
    const tableData = useMemo(() => {
        try {
            const list = new Array(config?.showRowNumber).fill(0)

            const listData = []
            list.forEach((i, index) => {
                let key

                listData.push({
                    index,
                    key
                })
            })

            return listData
        } catch (err) {
            console.log('err', err)
            return []
        }
    }, [config?.showRowNumber])

    // 需要在这里根据total 正序倒序计算对应数据源的下标
    const onValuesChange = (changedValues, allValues) => {
        try {
            const rowIndex = changedValues.value.length - 1
            const columnCode = Object.keys(changedValues.value[rowIndex])?.[0]
            const { value } = changedValues.value[rowIndex][columnCode]

            const v = typeof value === 'object' ? value.value : value

            // 计算带页码的表格中数据的下标
            const tableIndex = (pageNumber - 1) * config?.showRowNumber + rowIndex

            // 计算数据源中的下标 判断正序倒序
            const dataIndex = config?.isReverse ? total - tableIndex : tableIndex

            onInput({
                rowIndex: dataIndex,
                columnCode,
                value: v
            })
        } catch (error) {
            console.log('err', error)
        }
    }

    return (
        <Form
            form={form}
            onValuesChange={onValuesChange}
            style={{
                width: '100%',
                height: '100%',
                overflow: 'hidden',
                display: 'flex',
                flexDirection: 'column'
            }}
        >
            <Form.Item
                name={['dataSourceLength']}
                hidden
            />
            <div style={{ flex: '1', overflow: 'hidden' }}>
                <LayoutTable
                    config={config}
                    colsConfig={colsConfig}
                    tableData={tableData}
                    dataShowType={config?.dataShowType}
                    showRowNumber={config?.showRowNumber}
                    isPdf={isPdf}
                />
            </div>

            {
                config?.dataShowType === DATA_SHOW_TYPE.页码显示 ? (
                    <Pagination
                        style={{
                            marginTop: 12
                        }}
                        align="end"
                        showSizeChanger={false}
                        total={total}
                        current={pageNumber}
                        pageSize={config?.showRowNumber}
                        onChange={
                            (page, pageSize) => {
                                setPageNumber(page)

                                // 更新范围  获取对应数据
                                if (upDataRange) {
                                    if (config?.isReverse) {
                                        upDataRange({
                                            startIndex: total - page * pageSize,
                                            endIndex: total - (page - 1) * pageSize
                                        })
                                    } else {
                                        upDataRange({
                                            startIndex: (page - 1) * pageSize,
                                            endIndex: page * pageSize
                                        })
                                    }
                                }
                            }
                        }
                    />
                ) : null
            }

        </Form>
    )
}

export default React.memo(TableRender)
