import {
    useCallback, useEffect, useMemo, useState, useRef
} from 'react'

import useSubScriberCompMsg from '@/hooks/subscribe/useSubScriberCompMsg'
import { uisubscriptionGetData } from '@/utils/services'
import { getProcessID } from '@/utils/utils'
import { DATA_SROUCE_TYPE_MAP } from '@/hooks/controlComp/useLifecycleAPI'
import isEqual from 'lodash/isEqual'

// 合并处理显示数据
const mergeShowData = ({
    // 表格配置
    isEdit, isReverse, isSaveNewData, showNumber,
    // 数据
    msgData, resfulRecords, oldShowData,
    // 当前范围
    range
}) => {
    // 可编辑表格 -------------------------------------------------------
    if (isEdit) {
        let showData = msgData

        // 如果是保存最新数据
        if (isSaveNewData) {
            // 只取指定的最新数据
            showData = msgData.slice(-showNumber)
        }

        // 如果是倒序
        if (isReverse) {
            showData = showData.reverse()
        }

        return showData
    }

    // 只读表格 -----------------------------------------------------------------------------------------------------------

    // 判断显示最新数据 -----------------------------------------
    if (isReverse && !range) { // 没有指定范围 并且 倒序
        let showData = msgData

        // 如果是保存最新数据
        if (isSaveNewData) {
            // 只取指定的最新数据
            showData = msgData.slice(-showNumber)
        }

        // 倒序
        showData = showData.reverse()

        return showData
    }

    // 显示指定范围数据 ------------------------------------------
    if (range) {
        const { startIndex, endIndex } = range

        // 生成范围表格数据
        const tableData = new Array(endIndex - startIndex).fill(0).map((i, index) => {
            // 生成表格数据对应的下标  判断倒序
            const dataSourceIndex = isReverse ? endIndex - index : startIndex + index

            // 在数据源中找到对应数据
            const record = msgData.find(d => d?.index?.value === dataSourceIndex)// 先找订阅给的最新数据
                || resfulRecords?.find(r => r?.index?.value === dataSourceIndex)// 找接口数据中的对应数据
                || oldShowData.find(s => s?.index?.value === dataSourceIndex)

            return {
                index: dataSourceIndex,
                ...(record ?? {})
            }
        })

        return tableData
    }

    console.warn('二维数组内部状态有误')

    return []
}

const formatData = (data) => {
    // 动态获取所有字段的数组长度
    const fieldNames = Object.keys(data)
    const maxLength = Math.max(
        ...fieldNames.map(field => data[field]?.length || 0)
    )

    // 将数组数据转换为目标格式
    const transformedData = []

    for (let i = 0; i < maxLength; i += 1) {
        const dataItem = {}

        // 动态处理每个字段
        fieldNames.forEach(fieldName => {
            dataItem[fieldName] = {
                value: data[fieldName]?.[i]
            }
        })

        transformedData.push(dataItem)
    }

    return transformedData
}

const useData = ({
    controlCompId, config, isEdit
}) => {
    const [showData, setShowData] = useState([])
    const [total, setTotal] = useState(0)

    // 最新的数据
    const msgData = useRef([])
    // 接口数据
    const resfulRecords = useRef([])

    const range = useRef() // { startIndex: 0, endIndex: 10}

    useEffect(() => {
        if (!isEdit) {
            init()
        }
    }, [config?.showRowNumber, isEdit])

    // 配置变化时 初始化状态
    useEffect(() => {
        init()
    }, [
        config?.isReverse,
        config?.isSaveNewData,
        config?.showNumber,
        config?.dataSourceCode
    ])

    const init = () => {
        setTotal(0)
        msgData.current = []
        resfulRecords.current = []
        setShowData([])

        if (config?.isReverse) {
            // 倒序默认显示最新数据 需要清空range
            range.current = null
        } else {
            // 正序  需要默认显示第一页 并且请求第一页的数据
            upDataRange({ startIndex: 0, endIndex: config?.showRowNumber ?? 10 })
        }
    }

    const syncTableData = useCallback(() => {
        setShowData((prev) => {
            // 处理表格显示数据
            const newShowData = mergeShowData({
                oldShowData: prev,
                isEdit,
                isReverse: config?.isReverse,
                isSaveNewData: config?.isSaveNewData,
                showNumber: config?.showNumber,
                msgData: msgData.current,
                resfulRecords: resfulRecords.current,
                range: range.current
            })

            // if (isEqual(newShowData, prev)) {
            //     return prev
            // }

            return newShowData
        })
    }, [config])

    // 订阅最新数据 条数是用户设置条数
    useSubScriberCompMsg({
        controlCompId,
        onMessage: useCallback((msg) => {
            const { mode, data, totalCount } = msg

            if (mode === 0 && JSON.stringify(data) === '{}') {
                init()

                return
            }

            const transformedData = formatData(data)

            setTotal(totalCount)

            switch (mode) {
            case 0:
                msgData.current = transformedData
                break
            case 1:
                msgData.current = [...msgData.current, ...transformedData]
                break
            default:
                break
            }

            // 处理表格显示数据
            syncTableData()
        }, [config])
    })

    const upDataRange = async ({ startIndex, endIndex }) => {
        console.log('startIndex, endIndex', startIndex, endIndex, total)

        // 可编辑表格 不处理范围变化 直接走最新数据
        if (isEdit) {
            return
        }

        // 当倒序并且结束下标大于当前所有条数时   清空range 应用最新数据
        if (config?.isReverse && endIndex >= total) {
            range.current = null
            syncTableData()
            return
        }

        // 指定范围获取数据
        const res = await uisubscriptionGetData({
            templateName: getProcessID(),
            dataSourceType: DATA_SROUCE_TYPE_MAP.二维数组,
            dataSourceCode: config?.dataSourceCode,
            dataCodes: [
                ...config?.colsConfig?.map(c => c?.code)
            ],
            startIndex,
            endIndex,
            sampleCode: '',
            doubleArrayIndex: 0
        })

        const newResfulRecords = formatData(res.data)

        resfulRecords.current = newResfulRecords.map((i, index) => ({ ...i, index: { value: startIndex + index } }))

        // 更新范围
        range.current = {
            startIndex,
            endIndex
        }

        // 同步到最新数据
        syncTableData()
    }

    return {
        showData,
        total,
        upDataRange
    }
}

export default useData
