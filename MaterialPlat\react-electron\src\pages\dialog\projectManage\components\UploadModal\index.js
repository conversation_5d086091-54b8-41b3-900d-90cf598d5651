import React, { useState } from 'react'
import VModal from '@/components/vModal/index'
import {
    Upload, Button, Space, message
} from 'antd'
import { useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import VButton from '@/components/vButton'
import { UploadOutlined } from '@ant-design/icons'
import { getCurrentPath } from '@/utils/auth'
import SelectPath from '@/components/SelectPath'
import { UploadModalComponent } from './style'
import { suffixArray } from '../../constants'

const UploadModal = ({ open, onCancel, onOk }) => {
    const [messageApi, contextHolder] = message.useMessage()
    const { t } = useTranslation()
    const [files, setFiles] = useState([])
    const projectDirectory = useSelector(state => state.global.systemConfig?.project_directory)
    const [path, setPath] = useState(projectDirectory)

    const verifySuffix = (str) => {
        const suffix = str.substring(str.lastIndexOf('.'))
        return suffixArray.includes(suffix)
    }

    const beforeUpload = (file) => {
        const { name } = file
        if (!verifySuffix(name)) {
            messageApi.open({
                type: 'error',
                content: t('请选择模板文件')
            })
            return false
        }
        setFiles([file])
        return false
    }

    const onPrimary = () => {
        if (files.length === 0) {
            messageApi.open({
                type: 'error',
                content: t('请选择模板')
            })
        } else if (!path) {
            messageApi.open({
                type: 'error',
                content: t('请选择路径')
            })
        } else {
            onOk(files, path)
        }
    }

    const onRemove = () => {
        setFiles([])
    }
    const handleSelectPath = (param) => {
        setPath(param)
    }

    return (
        <VModal
            open={open}
            title={t('导入项目')}
            width={700}
            onCancel={onCancel}
            footer={null}
        >
            {contextHolder}
            <UploadModalComponent>
                <div className="upload-modal-left">
                    <Space direction="vertical">

                        <div className="path-layout">
                            <div>
                                {t('上传文件')}
                                ：
                            </div>
                            <Upload
                                accept=".texport, .pexport"
                                fileList={files}
                                beforeUpload={beforeUpload}
                                onRemove={onRemove}
                            >
                                <Button icon={<UploadOutlined />}>{ t('上传') }</Button>
                            </Upload>
                        </div>

                        <div className="path-layout">
                            <div>
                                {t('导入路径')}
                                ：
                            </div>
                            <SelectPath value={path} onChange={handleSelectPath} />
                        </div>
                    </Space>
                </div>
                <div className="upload-modal-right">
                    <Space direction="vertical">
                        <VButton block onClick={onPrimary}>{t('确认')}</VButton>
                        <VButton block onClick={onCancel}>{t('取消')}</VButton>
                    </Space>
                </div>
            </UploadModalComponent>

        </VModal>
    )
}

export default UploadModal
