/* eslint-disable consistent-return */
import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
    Form, Descriptions, Input, Row, Col, message, Space, Modal
} from 'antd'

import { ExclamationCircleFilled } from '@ant-design/icons'
import { getTemplateId, getProjectId, getUserInfo } from '@/utils/auth'
import {
    getSamplesList, postSamples, deleteSamples,
    getSamplesData, postImportSamples, updateTemplateSample, samplesHidden,
    exportSystemSample, importSystemSample
} from '@/utils/services'
import { formItemModalProps, DELETEFLAG } from '@/utils/constants'
import VModal from '@/components/vModal/index'
import VButton from '@/components/vButton/index'
import VTable from '@/components/vTable/index'
import SelectImg from '@/components/selectImg/index'
import VPage from '@/components/vPage/index'
import {
    unitAdd, unitDel, unitDown, unitUp
} from '@/static/img/index'
import { PROJECT_SAMPLE_LIST } from '@/redux/constants/project'
import { randomStr, handleCode } from '@/utils/utils'
import { codePrefix } from '@/utils/codeConstants'
import useCopy from '@/hooks/useCopy'
import useSample from '@/hooks/useSample'
import UploadModal from '@/components/uploadModal'
import { UPLOAD_MODAL_TYPE, sampleSuffixArray } from '@/utils/uploadConstants'
import useUnit from '@/hooks/useUnit'
import VTransfer from '@/components/vTransfer/index'

import { tableColumns, inputStyle, DATA_TYPE } from './constants'
import { SampleManageContainer, SampleModelContainer, SystemContainer } from './style'
import TabsBox from './TabsBox'

const { confirm } = Modal

const SampleManage = ({ open, setOpen }) => {
    const dispatch = useDispatch()
    const location = useLocation()
    const { t } = useTranslation()
    const { copy } = useCopy()
    const [form] = Form.useForm()
    const [sonForm] = Form.useForm()
    const unitList = useSelector(state => state.global.unitList)
    const { optSample, defaultSample } = useSelector(state => state.project)
    const { updateOptSample } = useSample()
    const { initUnitsData } = useUnit()
    // 单位数据列表
    const [sampleList, setSampleList] = useState([])
    // 所选择的当前单位数据
    const [selectedInfo, setSelectedInfo] = useState(null)
    // 新增编辑表单的数据
    const [formInfo, setFormInfo] = useState({})
    // 所选择的当前单位子内容数据
    const [infoTableSon, setInfoTableSon] = useState(null)
    // 新建纲类弹出框
    const [isSampleModal, setIsSampleModal] = useState(false)
    // 从系统库中选择 弹出窗
    const [systemBaseOpen, setSystemBaseOpen] = useState(false)
    const [systemData, setSystemData] = useState([])
    const [projectSamplesList, setProjectSamplesList] = useState([])
    const [targetKeys, setTargetKeys] = useState([])
    const [sampleKeys, setSampleKeys] = useState([])

    const [messageApi, contextHolder] = message.useMessage()
    const [buttonType, setButtonType] = useState('create')
    const [selectImgOpen, setSelectImgOpen] = useState(false)
    // change时的数据源数组
    const [dataResource, setDataResource] = useState([])
    const [delIds, setDelIds] = useState([])

    const [uploadModalOpen, setUploadModalOpen] = useState(false)
    const [uploadModalType, setUploadModalType] = useState(UPLOAD_MODAL_TYPE.导入)

    useEffect(() => {
        getSamples()
    }, [])

    useEffect(() => {
        sonForm.resetFields()
        sonForm.setFieldsValue(infoTableSon)
    }, [infoTableSon])

    // 获取单位数据列表
    const getSamples = async () => {
        try {
            if (location.pathname === '/') {
                const res = await getSamplesData()
                if (res) {
                    setSampleList(res)
                    dispatch({ type: PROJECT_SAMPLE_LIST, param: res })
                    setSelectedInfo(res[0])
                }
            } else {
                const res = await getSamplesList()
                if (res) {
                    setSampleList(res)
                    dispatch({ type: PROJECT_SAMPLE_LIST, param: res })
                    setSelectedInfo(res[0])
                }
            }
        } catch (error) {
            console.log(error)
            throw (error)
        }
    }

    // 处理后的infoTable
    const disposeInfoTable = () => {
        const { parameters } = formInfo
        if (parameters) {
            // 排序
            const data = [...parameters]?.sort((a, b) => a.order_num - b.order_num)
            // 过滤逻辑删除
            return data?.filter(i => !i.delete_flag)
        }
        return []
    }

    // 删除指定量纲
    const deleteSampleInfo = async () => {
        try {
            if (selectedInfo?.sample_id) {
                confirm({
                    title: t('确定删除这条数据吗?'),
                    icon: <ExclamationCircleFilled />,
                    onOk: async () => {
                        if (selectedInfo?.sample_id) {
                            const res = await deleteSamples({ sample_id: selectedInfo.sample_id })
                            if (res) {
                                getSamples()
                            }
                        }
                    }
                })
            } else {
                message.warning(t('请选择试样'))
            }
        } catch (error) {
            console.log(error)
            throw error
        }
    }
    // 打开弹出框
    const setModalOpen = (type) => {
        // 判断新增还是修改
        if (type === 'create') {
            setButtonType('create')
            const sample_id = crypto.randomUUID()
            setFormInfo({ sample_id, parameters: [] })
            form.setFieldsValue({ sample_id })
            setIsSampleModal(true)
            setSelectedInfo({
                parameters: []
            })
        }
        if (type === 'update') {
            setButtonType('update')
            if (!selectedInfo?.sample_id) {
                message.warning(t('请选择试样'))
            } else {
                const temp = { ...handleCode(selectedInfo, codePrefix.SAMPLE_TYPE, true), parameters: selectedInfo.parameters.map(parameter => (handleCode(parameter, codePrefix.SAMPLE_PARAM, true))) }
                form.setFieldsValue(temp)
                if (temp?.parameters?.length > 0) {
                    temp.parameters = temp.parameters.map(i => ({ ...i, new: false }))
                    const data = temp?.parameters[0]
                    setInfoTableSon(data)
                }
                setFormInfo(temp)
                setIsSampleModal(true)
            }
        }
    }

    const getSetSystemData = (data) => {
        const obj = {}
        return data.reduce((prev, item) => {
            if (!obj[item.sample_id]) {
                prev.push(item)
                obj[item.sample_id] = true
            }
            return prev
        }, [])
    }

    // 从系统库选择
    const openSystemBase = async () => {
        setSystemBaseOpen(true)
        const res = await getSamplesData()
        const data = await getSamplesList()
        if (res) {
            dispatch({ type: PROJECT_SAMPLE_LIST, param: data })
            setSystemData(getSetSystemData([...res, ...data]))
            setProjectSamplesList(data.map(i => i.sample_id))
            setTargetKeys(data.map(i => i.sample_id))
            setSampleKeys(data.map(i => i.sample_id))
        }
    }

    const systemBaseEnter = async () => {
        const defaultSampleId = defaultSample.data?.[0]?.sample_id

        // 新增的
        const sample_ids = targetKeys.filter(it => !projectSamplesList.includes(it)) || []
        // 删除的
        const del_sample_ids = projectSamplesList.filter(it => !targetKeys.includes(it)) || []

        if (del_sample_ids.includes(defaultSampleId)) {
            const sampleName = sampleList.find(f => f.sample_id === defaultSampleId).sample_name
            messageApi.open({
                type: 'error',
                content: `${t('正在使用的')}【${sampleName}】${t('不能删除')}`
            })
            return
        }

        const res = await postImportSamples({
            sample_ids,
            del_sample_ids
        })
        if (res) {
            messageApi.open({
                type: 'success',
                content: t('操作成功')
            })
            getSamples()
            setDataResource([])
            setDelIds([])
            setSystemBaseOpen(false)
        }
    }

    const systemBaseCancel = () => {
        setSystemBaseOpen(false)
        setDataResource([])
        setDelIds([])
    }

    const onChange = (keys, direction, moveKeys) => {
        if (direction === 'right') {
            setDataResource([...dataResource, ...moveKeys])
            setDelIds(delIds.filter(f => !moveKeys.includes(f)))
        }
        if (direction === 'left') {
            setDataResource(dataResource.filter(f => !moveKeys.includes(f)))
            const sampleFlag = sampleList.map(i => moveKeys.includes(i.sample_id))
            if (sampleFlag.includes(true)) {
                setDelIds(sampleList.filter(f => moveKeys.includes(f.sample_id)).map(m => m.sample_id))
            }
        }
        setTargetKeys(keys)
    }

    const onChangeDelWay = (item) => {
        setTargetKeys(targetKeys.filter(it => it !== item.sample_id))
    }

    const generateTree = () => {
        const treeData = systemData?.filter(obj => !sampleKeys.includes(obj.sample_id))?.filter(f => !targetKeys.includes(f.sample_id))
        return treeData.map((item) => ({
            ...item,
            disabled: targetKeys.includes(item.sample_id),
            title: item.sample_name,
            key: item.sample_id
        }))
    }
    // 新建纲类单位确定
    const handleOk = async () => {
        console.log(123123)
        let data = null
        try {
            data = await form.validateFields()
        } catch (err) {
            console.log(err)
        }

        if (!data) {
            return
        }

        let sonFormData = null
        try {
            sonFormData = await sonForm.validateFields()
        } catch (err) {
            sonFormData = null
        }
        if (!sonFormData) {
            return
        }
        const c_formInfo = await onSonFinish(sonFormData)

        if (!c_formInfo) {
            return
        }

        const { parameters } = c_formInfo
        // 获取到提交之前的
        const beforeSelectInfo = { ...selectedInfo }

        if (data) {
            try {
                const parameterData = parameters?.map(i => (handleCode(i, codePrefix.SAMPLE_PARAM)))
                const res = await postSamples(handleCode({
                    ...data, new: buttonType === 'create', parameters: parameterData
                }, codePrefix.SAMPLE_TYPE))
                if (res) {
                    getSamples()
                    // 进行编辑、新增确认之后不会从第一个单位量纲类开始
                    const sampleData = await getSamplesList()
                    setSampleList(sampleData)
                    const isData = sampleData.find(item => item.sample_id === beforeSelectInfo.sample_id)
                    setSelectedInfo(isData)
                    handleCancel()
                }
            } catch (error) {
                console.log(error)
            }
        }
    }
    // 新建纲类单位取消
    const handleCancel = () => {
        setIsSampleModal(false)
        form.resetFields()
        sonForm.resetFields()
    }
    // 新增单位子级
    const addSampleSon = async () => {
        const addSampleSonHandle = (formData) => {
            const formInfoData = formData || formInfo
            const parameter_id = crypto.randomUUID()
            const { parameters } = formInfoData
            const data = {
                parameter_id,
                parameter_name: `s${parameters.length + 1}`,
                default_val: 0,
                parameter_img: '',
                func: '',
                code: `s${parameters.length + 1}${randomStr()}`,
                dimension_id: '',
                units_id: '',
                created_user_id: getUserInfo()?.id,

                data_type: DATA_TYPE['输入值']
            }
            console.log(123, {
                ...formInfoData,
                parameters: [...parameters, {
                    ...data,
                    new: true,
                    delete_flag: DELETEFLAG.添加,
                    sample_id: formInfoData.sample_id,
                    order_num: parameters.length > 0
                        ? parameters[parameters.length - 1]?.order_num + 1
                        : 0
                }]
            })
            setFormInfo({
                ...formInfoData,
                parameters: [...parameters, {
                    ...data,
                    new: true,
                    delete_flag: DELETEFLAG.添加,
                    sample_id: formInfoData.sample_id,
                    order_num: parameters.length > 0
                        ? parameters[parameters.length - 1]?.order_num + 1
                        : 0
                }]
            })
            sonForm.setFieldsValue(data)
            // 新增条件
            setInfoTableSon(data)
        }

        if (disposeInfoTable()?.length <= 0) {
            // 没有数据，不用校验，直接新建
            addSampleSonHandle()
        } else {
            // 多条数据切换时先校验表单，在校验参数名称内部名称是否重复
            let sonData = null
            try {
                sonData = await sonForm.validateFields()
            } catch (err) {
                sonData = null
            }
            if (sonData) {
                const formData = await onSonFinish(sonData)
                if (formData) {
                    addSampleSonHandle(formData)
                }
            }
        }
    }

    // 单位子级提交
    const onSonFinish = async (data) => {
        const res = data || await sonForm.validateFields()
        if (res) {
            const { parameters } = formInfo
            const parameter_names = parameters.filter(f => f.parameter_id !== res.parameter_id && !f.delete_flag)?.map(m => m.parameter_name)
            // 当前试样内部名重复检查
            const codes = parameters.filter(f => f.parameter_id !== res.parameter_id && !f.delete_flag)?.map(m => m.code)
            if (parameter_names.some(parameter_name => parameter_name?.trim() === res.parameter_name?.trim())) {
                message.error(t('参数名称重复'))
                return false
            }
            if (codes.some(code => code?.trim() === res.code?.trim())) {
                message.error(t('与当前试样中的参数内部名重复'))
                return false
            }
            // 所有试样中的参数内部名重复检查
            const allParameters = sampleList.flatMap(sample => sample.parameters || [])
            const conflictParameter = allParameters.find(item => {
                const fullCode = `sampleParam_${res.code}`
                return item.parameter_id !== res.parameter_id
                && !item.delete_flag
                && item.code?.trim() === fullCode?.trim()
            })

            if (conflictParameter) {
                const conflictSample = sampleList.find(sample => sample.parameters && sample.parameters.some(p => p.parameter_id === conflictParameter.parameter_id))
                const sampleName = conflictSample?.sample_name || '未知试样'
                const parameterName = conflictParameter.parameter_name || '未知参数'
                message.error(t(`与试样：${sampleName}中的参数：${parameterName}内部名重复`))
                return false
            }

            if (parameters.some(i => i.parameter_id === infoTableSon?.parameter_id)) {
                const c_formInfo = {
                    ...formInfo,
                    new: false,
                    parameters: parameters.map(i => (
                        i.parameter_id === res.parameter_id ? { ...i, ...res } : i
                    ))
                }
                setFormInfo(c_formInfo)
                return c_formInfo
            }
            const c_formInfo = {
                ...formInfo,
                new: true,
                parameters: [...parameters, {
                    ...res,
                    new: true,
                    delete_flag: DELETEFLAG.添加,
                    sample_id: formInfo.sample_id,
                    order_num: parameters.length > 0
                        ? parameters[parameters.length - 1]?.order_num + 1
                        : 0
                }]
            }
            setFormInfo(c_formInfo)
            return c_formInfo
        }
    }

    // 删除单位子级
    const deleteSampleSon = () => {
        const { parameters } = formInfo
        if (infoTableSon?.parameter_id) {
            confirm({
                title: t('确定删除这条数据吗?'),
                icon: <ExclamationCircleFilled />,
                onOk() {
                    const tempParameters = parameters.map(i => (
                        i.parameter_id === infoTableSon.parameter_id ? { ...i, delete_flag: DELETEFLAG.删除 } : i
                    ))
                    setFormInfo({
                        ...formInfo,
                        parameters: tempParameters
                    })
                    const surplus = tempParameters.filter(f => f.delete_flag !== DELETEFLAG.删除)
                    if (surplus && surplus.length > 0) {
                        // 排序 找到当前参数的上一条参数
                        const prevParamInfo = surplus.filter(f => f.order_num < infoTableSon.order_num).sort((a, b) => a.order_num - b.order_num).at(-1)
                        // 显示上一条参数 或者 第一个参数
                        setInfoTableSon(prevParamInfo ?? surplus[0])
                    } else {
                        setInfoTableSon(null)
                    }
                }
            })
        } else {
            messageApi.open({
                type: 'warning',
                content: t('请选择一个参数')
            })
        }
    }

    // 移动
    const moveSampleSon = (type) => {
        const { parameters } = formInfo
        // 判断是否为正在新增的数据
        const res = parameters.some(i => i.parameter_id === infoTableSon.parameter_id)
        if (res) {
            if (type === 'up' && infoTableSon.parameter_id === disposeInfoTable()[0]?.parameter_id) {
                // 判断是否为第一个
                messageApi.open({
                    type: 'warning',
                    content: t('当前为第一个')
                })
                return
            }
            if (type === 'down' && infoTableSon.parameter_id === disposeInfoTable()[disposeInfoTable().length - 1]?.parameter_id) {
                // 判断是否为最后一个
                messageApi.open({
                    type: 'warning',
                    content: t('当前为最后一个')
                })
                return
            }
            // 交换的数据
            let swapData = null
            if (type === 'up') {
                const data = disposeInfoTable().filter(i => i.order_num < infoTableSon.order_num)
                swapData = data[data.length - 1]
            } else {
                swapData = disposeInfoTable().find(i => i.order_num > infoTableSon.order_num)
            }
            const newSamples = parameters.map(i => {
                if (i.parameter_id === infoTableSon.parameter_id) {
                    setInfoTableSon({ ...i, order_num: swapData.order_num })
                    return { ...i, order_num: swapData.order_num }
                }
                if (i.parameter_id === swapData.parameter_id) {
                    return { ...i, order_num: infoTableSon.order_num }
                }
                return i
            })
            setFormInfo({ ...formInfo, parameters: newSamples })
        }
    }

    const syncSample = async () => {
        await updateTemplateSample()
        message.success(t('更新成功'))
        getSamples()
    }

    const updateInfoTable = async (item) => {
        let data = null
        try {
            data = await sonForm.validateFields()
        } catch (err) {
            data = null
        }
        if (data) {
            const formData = await onSonFinish(data)
            if (formData) {
                setInfoTableSon(item)
            }
        }
    }

    const interiorLayoutColProps = {
        xs: { span: 8 },
        sm: { span: 8 },
        md: { span: 8 }
    }

    const interiorFormItemModalProps = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: 9 }
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: 15 }
        }
    }
    const onSwitchChange = async (checked, record) => {
        await samplesHidden({
            hidden_flag: !checked,
            parameter_id: record.parameter_id
        })
        updateOptSample({ ...optSample, data: optSample.data.map(m => (m.parameter_id === record.parameter_id ? { ...m, hidden_flag: !checked } : m)) })
        const res = await getSamplesList()
        if (res) {
            dispatch({ type: PROJECT_SAMPLE_LIST, param: res })
            setSampleList(res)
            setSelectedInfo(res.find(f => f.sample_id === selectedInfo.sample_id))
        }
    }

    const onUploadModalCancel = () => {
        setUploadModalOpen(false)
    }

    const handleExport = () => {
        setUploadModalType(UPLOAD_MODAL_TYPE.导出)
        setUploadModalOpen(true)
    }

    const handleImport = () => {
        setUploadModalType(UPLOAD_MODAL_TYPE.导入)
        setUploadModalOpen(true)
    }

    const onUploadModalOk = async ({
        path,
        targetKeys: keys,
        all,
        files
    }) => {
        const handleNext = () => {
            initUnitsData()
            getSamples()
            onUploadModalCancel()
            message.success(t('操作成功'))
        }
        if (uploadModalType === UPLOAD_MODAL_TYPE.导入) {
            try {
                await importSystemSample({ file_path: files[0].path })
                handleNext()
            } catch (e) {
                console.error(e)
            }
        } else {
            try {
                await exportSystemSample({
                    file_path: path,
                    ids: all ? null : keys
                })
                handleNext()
            } catch (e) {
                console.error(e)
            }
        }
    }

    return (
        <>
            <VModal
                open={open}
                width="70vw"
                onCancel={() => setOpen(false)}
                title={t('试样管理')}
                footer={null}
            >
                <SampleManageContainer>
                    {contextHolder}
                    <div className="sample-container">
                        <Space align="start">
                            <VPage title={t('试样列表')}>
                                <div className="sample-list">
                                    <div className="sample-list-layout">
                                        {sampleList?.map(item => (
                                            <div
                                                className={item.sample_id === selectedInfo?.sample_id ? 'name-layout  selected' : 'name-layout'}
                                                key={item.sample_id}
                                                onClick={() => setSelectedInfo(item)}
                                            >
                                                {t(item.sample_name)}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </VPage>
                            <div className="sample-param">
                                <Space direction="vertical">
                                    <VPage title={t('试样参数')}>
                                        <div className="sample-param-container">
                                            <div className="sample-param-content">
                                                <Descriptions column={4}>
                                                    <Descriptions.Item span={1} label={t('试样形状')}>{t(selectedInfo?.sample_name)}</Descriptions.Item>
                                                    <Descriptions.Item span={2} label={t('内部名')}>
                                                        <span onClick={() => copy(selectedInfo?.code)}>{selectedInfo?.code}</span>
                                                    </Descriptions.Item>
                                                    <Descriptions.Item span={1} label={t('试样例图')}>
                                                        {selectedInfo?.img && <img src={selectedInfo?.img} alt="#" />}
                                                    </Descriptions.Item>
                                                </Descriptions>
                                            </div>
                                            <div className="sample-param-table">
                                                <VTable
                                                    rowKey="parameter_id"
                                                    scroll={{ y: '37vh' }}
                                                    columns={tableColumns({
                                                        t,
                                                        unitList,
                                                        copy,
                                                        isTemp: (getTemplateId() || getProjectId()),
                                                        onSwitchChange

                                                    })}
                                                    dataSource={selectedInfo?.parameters}
                                                    pagination={false}
                                                />
                                            </div>
                                        </div>
                                    </VPage>
                                </Space>
                            </div>
                            <div className="sample-operate">
                                <Space direction="vertical">
                                    {((getTemplateId() || getProjectId())) ? (
                                        <>
                                            <VButton block onClick={openSystemBase}>{t('从系统库选择')}</VButton>
                                            <VButton block onClick={syncSample}>{t('更新')}</VButton>
                                        </>
                                    ) : (
                                        <>
                                            <VButton block onClick={() => setModalOpen('create')}>{t('新建')}</VButton>
                                            <VButton block onClick={() => setModalOpen('update')}>{t('编辑')}</VButton>
                                            <VButton block onClick={deleteSampleInfo}>{t('删除')}</VButton>
                                            <VButton block onClick={handleImport}>{t('导入')}</VButton>
                                            <VButton block onClick={handleExport}>{t('导出')}</VButton>
                                        </>
                                    )}
                                </Space>
                                <Space direction="vertical">
                                    <VButton type="else" block>{t('帮助')}</VButton>
                                </Space>
                            </div>
                            <VModal
                                title={t('从系统库中选择')}
                                open={systemBaseOpen}
                                onOk={systemBaseEnter}
                                width="50vw"
                                onCancel={systemBaseCancel}
                                footer={null}
                            >
                                <SystemContainer>
                                    <VTransfer
                                        rowKey="sample_id"
                                        render={(item) => item.sample_name}
                                        dataSource={systemData}
                                        showSearch
                                        searchName="sample_name"
                                        targetKeys={targetKeys}
                                        onChange={onChange}
                                        onChangeDelWay={onChangeDelWay}
                                        generateTree={generateTree}
                                        oneWayLabel="sample_name"
                                        oneWay
                                    />
                                    <div className="operate-btn">
                                        <div>
                                            <VButton className="operate-item-frist" onClick={systemBaseEnter}>{t('确定')}</VButton>
                                            <VButton className="operate-item" onClick={systemBaseCancel}>{t('取消')}</VButton>
                                        </div>
                                        <div>
                                            <VButton className="operate-item" onClick={systemBaseCancel}>{t('帮助')}</VButton>
                                        </div>
                                    </div>
                                </SystemContainer>
                            </VModal>
                        </Space>
                    </div>

                </SampleManageContainer>
            </VModal>
            <VModal
                title={t('试样')}
                open={isSampleModal}
                width="65vw"
                onOk={handleOk}
                onCancel={handleCancel}
                footer={null}
            >
                <SampleModelContainer>
                    <div className="left-layout">
                        <VPage title={t('试样参数')}>
                            <div className="param-layout">
                                <Form
                                    form={form}
                                    name="sample_rule"
                                    labelAlign="left"
                                    {...interiorFormItemModalProps}
                                >
                                    <Form.Item hidden label="ID" name="sample_id">
                                        <Input placeholder={t('请输入')} />
                                    </Form.Item>
                                    <Row>
                                        <Col {...interiorLayoutColProps}>
                                            <Form.Item
                                                label={t('名称')}
                                                name="sample_name"
                                                rules={[
                                                    { required: true, message: t('请填写名称') },
                                                    {
                                                        validator: (_, value) => {
                                                            if (value && value.includes(' ')) {
                                                                return Promise.reject(new Error(t('名称不能包含空格')))
                                                            }
                                                            return Promise.resolve()
                                                        }
                                                    }
                                                ]}
                                            >
                                                <Input disabled={buttonType === 'update'} placeholder={t('请输入')} style={{ width: '10vw' }} />
                                            </Form.Item>
                                        </Col>
                                        <Col {...interiorLayoutColProps}>
                                            <Form.Item
                                                label={t('内部名称')}
                                                name="code"
                                                rules={[{ required: true, message: t('请填写内部名称') }]}
                                            >
                                                <Input disabled={buttonType === 'update'} prefix={codePrefix.SAMPLE_TYPE} placeholder={t('请输入')} style={{ width: '10vw' }} />
                                            </Form.Item>
                                        </Col>
                                        <Col {...interiorLayoutColProps}>
                                            <Form.Item
                                                label={t('上传试样例图')}
                                                name="img"
                                                // rules={[{ required: true, message: t('请选择试样例图') }]}
                                            >
                                                <SelectImg
                                                    open={selectImgOpen}
                                                    btnCLick={() => setSelectImgOpen(true)}
                                                    onCancel={() => setSelectImgOpen(false)}
                                                />

                                            </Form.Item>
                                        </Col>
                                    </Row>
                                </Form>
                            </div>
                        </VPage>
                        <div className="model-sample-list">
                            <VPage title={t('参数列表')}>
                                <div className="sample-list">
                                    <div className="sample-list-layout">
                                        <div className="list-info">
                                            <div className="sample-info-layout">
                                                {disposeInfoTable()?.map(item => (
                                                    <div
                                                        className={item?.parameter_id === infoTableSon?.parameter_id ? 'name-layout selected' : 'name-layout'}
                                                        key={item?.parameter_id}
                                                        onClick={() => updateInfoTable(item)}
                                                    >
                                                        {t(item?.parameter_name)}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </VPage>
                            <div className="list-button">
                                <div onClick={addSampleSon} className="button-layout">
                                    <img src={unitAdd} alt="" />
                                    <div>{t('新建')}</div>
                                </div>
                                <div onClick={deleteSampleSon} className="button-layout">
                                    <img src={unitDel} alt="" />
                                    <div>{t('删除')}</div>
                                </div>
                                <div onClick={() => moveSampleSon('up')} className="button-layout">
                                    <img src={unitUp} alt="" />
                                    <div>{t('上移')}</div>
                                </div>
                                <div onClick={() => moveSampleSon('down')} className="button-layout">
                                    <img src={unitDown} alt="" />
                                    <div>{t('下移')}</div>
                                </div>
                            </div>
                            <div className="property-layout" style={{ flex: 1 }}>
                                <div className="property-info">
                                    <Form
                                        disabled={disposeInfoTable()?.length <= 0}
                                        form={sonForm}
                                        name="sample_rule"
                                        labelAlign="left"
                                        {...formItemModalProps}
                                    >
                                        <Form.Item hidden label={t('ID')} name="parameter_id" rules={[{ required: true, message: t('请填写参数id') }]}>
                                            <Input disabled style={inputStyle} />
                                        </Form.Item>
                                        <TabsBox codeDisabled={disposeInfoTable()?.length <= 0 || (buttonType === 'update' && !infoTableSon?.new)} form={sonForm} />
                                    </Form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="sample-operate">
                        <Space direction="vertical">
                            <VButton block onClick={() => handleOk()}>{t('确认')}</VButton>
                            <VButton block onClick={() => handleCancel()}>{t('取消')}</VButton>
                        </Space>
                        <Space direction="vertical">
                            <VButton block onClick={() => handleCancel()}>{t('帮助')}</VButton>
                        </Space>
                    </div>
                </SampleModelContainer>
            </VModal>
            {
                uploadModalOpen && (
                    <UploadModal
                        dataSource={sampleList}
                        type={uploadModalType}
                        open={uploadModalOpen}
                        onCancel={onUploadModalCancel}
                        onOk={onUploadModalOk}
                        suffixArray={sampleSuffixArray}
                        transferConfig={{
                            rowKey: 'sample_id',
                            render: (item) => t(item.sample_name)
                        }}
                    />
                )
            }
        </>
    )
}

export default SampleManage
