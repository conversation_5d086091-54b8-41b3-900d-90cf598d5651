import React, { useState, useEffect } from 'react'
import {
    Form, Card, Descriptions, Select,
    Button, Modal, Input, Row,
    Col, Switch, message, Space, Divider
} from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'

import { getProjectId, getTemplateId, getUserInfo } from '@/utils/auth'
import {
    getUnitsList, getApplysList, postUnitsSave, deleteUnitsRemove,
    postApplyStandards, putApplyStandards, deleteApplyStandards,
    getUnitsSystemList, updateTemplateDimensionUnits, postBatchUnitsSave, importSystemUnit, exportSystemUnit
} from '@/utils/services'
import { layoutColProps, DELETEFLAG } from '@/utils/constants'
import VModal from '@/components/vModal/index'
import VPage from '@/components/vPage/index'
import VButton from '@/components/vButton/index'
import VTable from '@/components/vTable/index'
import {
    unitAdd, unitDel, unitDown, unitUp
} from '@/static/img/index'
import { randomStr, handleCode } from '@/utils/utils'
import { codePrefix } from '@/utils/codeConstants'
import useUnit from '@/hooks/useUnit'
import VTransfer from '@/components/vTransfer'
import useCopy from '@/hooks/useCopy'
import UploadModal from '@/components/uploadModal'
import { unitSuffixArray, UPLOAD_MODAL_TYPE } from '@/utils/uploadConstants'

import {
    tableColumns, APPLYTABLECOLUMNS, inputStyle, LABEL_STYLE, CONTENT_STYLE_ITEM,
    FORM_ITEM_MODAL_PROPS, TOP_FORM_COLS, unitColumns
} from './constants'
import {
    UnitManageContainer, UnitModelContainer, ApplyModelContainer, ApplicationModelContainer,
    SystemContainer
} from './style'

const { confirm } = Modal
const UnitManage = ({ open, setOpen }) => {
    const { t } = useTranslation()
    const { initUnitsData, initSysUnitsData } = useUnit()
    const [form] = Form.useForm()
    const [sonForm] = Form.useForm()
    const [applyForm] = Form.useForm()
    const { copy } = useCopy()
    const location = useLocation()

    // 系统模板库弹出框
    const [systemBaseOpen, setSystemBaseOpen] = useState(false)
    const [systemData, setSystemData] = useState([])
    const [targetKeys, setTargetKeys] = useState([])
    // 单位数据列表
    const [unitList, setUnitList] = useState([])
    // 当前所选的所属应用类
    const [selectStandard, setSelectStandard] = useState('ALL')
    // 所选择的当前单位数据
    const [selectedInfo, setSelectedInfo] = useState(null)
    // 新增编辑表单的数据
    const [formInfo, setFormInfo] = useState({})
    // 所选择的当前单位子内容数据
    const [infoTableSon, setInfoTableSon] = useState(null)
    // 新建纲类弹出框
    const [isUnitModal, setIsUnitModal] = useState(false)
    // 应用类列表
    const [applyList, setApplyList] = useState([])
    // 应用类弹出框
    const [isApplyModal, setIsApplyModal] = useState(false)
    // 所选择的应用类
    const [selectedApply, setSelectedApply] = useState(null)
    // 应用类新建编辑弹出框
    const [isApplyHandleModal, setIsApplyHandleModal] = useState(false)
    const [messageApi, contextHolder] = message.useMessage()
    // 判断单位是否是新增
    const [isCreate, setIsCreate] = useState('create')
    const [paramDisabled, setParamDisabled] = useState(true)
    const [isAdd, setIsAdd] = useState(false)
    // change时的数据源数组
    const [dataResource, setDataResource] = useState([])
    // 系统库现在选中的数据
    const [wayData, setWayData] = useState({})
    const [selectedRowKeys, setSelectedRowKeys] = useState([])

    const [uploadModalOpen, setUploadModalOpen] = useState(false)
    const [uploadModalType, setUploadModalType] = useState(UPLOAD_MODAL_TYPE.导入)

    useEffect(() => {
        getUnits()
        getApplys()
    }, [])

    useEffect(() => {
        if (infoTableSon) {
            sonForm.setFieldsValue(infoTableSon)
        }
    }, [infoTableSon])

    // 获取单位数据列表
    const getUnits = async () => {
        try {
            if (location.pathname === '/') {
                const res = await getUnitsSystemList()
                if (res) {
                    setUnitList(res)
                    initSysUnitsData()
                    setSelectedInfo(res?.[0])
                }
            } else {
                const res = await getUnitsList()
                if (res) {
                    setUnitList(res)
                    initUnitsData()
                    setSelectedInfo(res?.[0])
                }
            }
        } catch (error) {
            console.log(error)
            throw (error)
        }
    }

    // 获取应用类数据列表
    const getApplys = async () => {
        try {
            const res = await getApplysList()
            if (res) {
                setApplyList(res)
            }
        } catch (error) {
            console.log(error)
            throw (error)
        }
    }

    // 当前所属应用类的列表
    const currentInfoTable = () => {
        if (selectStandard === 'ALL') {
            return selectedInfo?.units
        }
        return selectedInfo?.units?.filter(i => i.standard_id === selectStandard)
    }

    // 处理应用类明细
    const handleApplyList = () => {
        if (selectedApply) {
            const { units } = selectedApply
            // 添加key，使树状表格不一块展开
            return units.map((item, index) => ({ id: crypto.randomUUID(), dimension_name: item.dimension, children: item.units }))
        }
        return []
    }

    // 处理后的infoTable
    const disposeInfoTable = () => {
        const { units } = formInfo
        if (Array.isArray(units)) {
            const data = [...units]
                // 排序
                .sort((a, b) => a.order_num - b.order_num)
                // 过滤逻辑删除与不可见
                .filter(i => !i.delete_flag)
            return data
        }
        return []
    }

    // 删除指定量纲
    const deleteUnitInfo = async () => {
        if (selectedInfo?.id) {
            confirm({
                title: `${t('确定删除这条数据吗')}?`,
                icon: <ExclamationCircleFilled />,
                onOk: async () => {
                    try {
                        const res = await deleteUnitsRemove({ id: selectedInfo.id })
                        if (res) {
                            messageApi.open({
                                type: 'success',
                                content: t('删除成功')
                            })
                            setSelectedInfo({
                                parameters: []
                            })
                            getUnits()
                        }
                    } catch (error) {
                        console.log(error)
                    }
                }
            })
        } else {
            messageApi.open({
                type: 'warning',
                content: t('请选择单位量纲')
            })
        }
    }
    // 打开弹出框
    const setModalOpen = (type) => {
        setIsAdd(false)
        setIsCreate(type)
        // 判断新增还是修改
        if (type === 'create') {
            const id = crypto.randomUUID()
            setFormInfo({ id, units: [] })
            form.setFieldsValue({ id })
            setIsUnitModal(true)
        }
        if (type === 'update') {
            if (selectedInfo?.id) {
                const temp = { ...handleCode(selectedInfo, codePrefix.DIMENSION, true), units: selectedInfo.units.map(unit => (handleCode(unit, codePrefix.UNIT, true))) }
                if (temp?.units?.length > 0) {
                    temp.units = temp.units.map(i => ({ ...i, new: false }))
                    setParamDisabled(false)
                    setInfoTableSon(temp?.units[0])
                }
                form.setFieldsValue(temp)
                setFormInfo(temp)
                setIsUnitModal(true)
            } else {
                messageApi.open({
                    type: 'warning',
                    content: t('请选择单位量纲')
                })
            }
        }
    }

    // 打开应用类弹出框
    const setApplyModalOpen = async () => {
        if (applyList.length > 0) {
            setSelectedApply(applyList[0])
        }
        setIsApplyModal(true)
    }

    // 新建纲类单位确定
    const handleOk = async () => {
        try {
            const data = await form.validateFields()

            let sonFormData = null
            try {
                sonFormData = await sonForm.validateFields()
            } catch (err) {
                sonFormData = null
            }
            if (!sonFormData) {
                return
            }
            const c_formInfo = await onSonFinish(sonFormData)

            setIsAdd(false)
            const { units } = c_formInfo

            // 检查单位内部名称是否重复
            if (units && units.length > 0) {
                const validUnits = units.filter(unit => !unit.delete_flag)
                const unitCodes = validUnits.map(unit => unit.code?.trim()).filter(code => code)
                const duplicateCodes = unitCodes.filter((code, index) => unitCodes.indexOf(code) !== index)

                if (duplicateCodes.length > 0) {
                    messageApi.open({
                        type: 'error',
                        content: `${t('单位内部名称不能重复') }: ${ [...new Set(duplicateCodes)].join(', ')}`
                    })
                    return
                }
            }

            if (data) {
                try {
                    // 获取到提交之前的
                    const beforeSelectInfo = { ...selectedInfo }
                    const unitCode = units.map(m => (handleCode(m, codePrefix.UNIT)))
                    const res = await postUnitsSave(handleCode({ ...data, new: !isEdit(), units: unitCode }, codePrefix.DIMENSION))
                    if (res) {
                        // 进行编辑、新增确认之后不会从第一个单位量纲类开始
                        const unitData = await getUnitsList()
                        initUnitsData()
                        setUnitList(unitData)
                        const isData = unitData.find(item => item.id === beforeSelectInfo.id)
                        setSelectedInfo(isData)
                        getApplys()
                    }
                } catch (error) {
                    console.log(error)
                    throw (error)
                }
                handleCancel()
            }
        } catch (error) {
            console.log('err', error)
        }
    }

    // 新建纲类单位取消
    const handleCancel = () => {
        setIsUnitModal(false)
        form.resetFields()
        sonForm.resetFields()
        // 取消之后把参数设置设置为disabled
        setParamDisabled(true)
        setIsAdd(false)
    }

    // 新增单位子级
    const addUnitSon = async () => {
        // 没有列表时，直接新建
        if (paramDisabled) {
            const id = crypto.randomUUID()
            const { units } = formInfo
            const data = {
                id,
                name: `dw${units.length + 1}`,
                code: `cdw${units.length + 1}${randomStr()}`,
                visible: true,
                proportion: 1,
                standard_id: applyList[0]?.id || '',
                created_user_id: getUserInfo()?.id
            }
            sonForm.setFieldsValue(data)
            setFormInfo({
                ...formInfo,
                new: true,
                units: [...units, {
                    ...data,
                    delete_flag: DELETEFLAG.添加,
                    dimension_id: formInfo.id,
                    order_num: units.length > 0 ? units[units.length - 1]?.order_num + 1 : 0,
                    new: true
                }]
            })
            setInfoTableSon(data)
            // 新增条件
            setIsAdd(true)
            setParamDisabled(false)
            return
        }

        // 有列表时，先保存当前编辑的，再新建
        let sonFormData = null
        try {
            sonFormData = await sonForm.validateFields()
        } catch (err) {
            sonFormData = null
        }
        const c_formInfo = await onSonFinish(sonFormData)

        const id = crypto.randomUUID()
        const { units } = c_formInfo
        const data = {
            id,
            name: `dw${units.length + 1}`,
            code: `cdw${units.length + 1}${randomStr()}`,
            visible: true,
            proportion: 1,
            standard_id: applyList[0]?.id || '',
            created_user_id: getUserInfo()?.id
        }
        sonForm.setFieldsValue(data)

        setFormInfo({
            ...c_formInfo,
            new: true,
            units: [...units, {
                ...data,
                delete_flag: DELETEFLAG.添加,
                dimension_id: c_formInfo.id,
                order_num: units.length > 0 ? units[units.length - 1]?.order_num + 1 : 0,
                new: true
            }]
        })
        setInfoTableSon(data)
        // 新增条件
        setIsAdd(true)
        setParamDisabled(false)
    }

    // 单位子级提交
    const onSonFinish = async (data) => {
        const val = data || await sonForm.validateFields()
        const { units } = formInfo
        if (units.some(i => i.id === infoTableSon?.id)) {
            const newUnits = units.map(i => (i.id === val.id ? { ...i, ...val } : { ...i }))
            const defaultUnit = newUnits.find(f => f.id === form.getFieldValue('default_unit_id'))
            const c_formInfo = {
                ...formInfo,
                default_unit_id: defaultUnit?.visible ? formInfo.default_unit_id : null,
                new: false,
                units: newUnits
            }
            setFormInfo(c_formInfo)
            // 任务XJRS-1146 涉及到不可见单位不可选, 需要清空选择默认值
            if (!defaultUnit?.visible) {
                form.setFieldValue('default_unit_id', null)
            }
            return c_formInfo
        }
        const c_formInfo = {
            ...formInfo,
            new: true,
            units: [...units, {
                ...val,
                delete_flag: DELETEFLAG.添加,
                dimension_id: formInfo.id,
                order_num: units.length > 0 ? units[units.length - 1]?.order_num + 1 : 0,
                new: true
            }]
        }
        setFormInfo(c_formInfo)
        return c_formInfo
    }

    // 删除单位子级
    const deleteUnitSon = () => {
        setIsAdd(false)
        const { units } = formInfo
        // 判断是否为正在新增的数据
        const res = disposeInfoTable().some(i => i.id === infoTableSon?.id)
        if (res) {
            confirm({
                title: `${t('确定删除这条数据吗')}?`,
                icon: <ExclamationCircleFilled />,
                onOk() {
                    if (form.getFieldValue('default_unit_id') === infoTableSon.id) {
                        form.setFieldValue('default_unit_id', null)
                    }
                    const unitsList = units.map(i => (
                        i.id === infoTableSon?.id ? { ...i, delete_flag: DELETEFLAG.删除 } : i
                    ))
                    setFormInfo({
                        ...formInfo,
                        units: unitsList
                    })
                    sonForm.resetFields()

                    const showUnitsList = unitsList.sort((a, b) => a.order_num - b.order_num)
                        .filter(i => !i.delete_flag) || []
                    if (showUnitsList?.length > 0) {
                        setInfoTableSon(showUnitsList?.[0])
                    } else {
                        setParamDisabled(true)
                    }
                }
            })
        }
    }

    // 移动
    const moveUnitSon = async (type) => {
        // 先保存当前编辑的
        let sonFormData = null
        try {
            sonFormData = await sonForm.validateFields()
        } catch (err) {
            sonFormData = null
        }
        const c_formInfo = await onSonFinish(sonFormData)

        const { units } = c_formInfo
        // 判断是否为正在新增的数据
        const res = units.some(i => i.id === infoTableSon?.id)
        if (res) {
            if (type === 'up' && infoTableSon?.id === disposeInfoTable()[0]?.id) {
                // 判断是否为第一个
                messageApi.open({
                    type: 'warning',
                    content: t('当前为第一个')
                })
                return
            }
            if (type === 'down' && infoTableSon?.id === disposeInfoTable()[disposeInfoTable().length - 1]?.id) {
                // 判断是否为最后一个
                messageApi.open({
                    type: 'warning',
                    content: t('当前为最后一个')
                })
                return
            }
            // 交换的数据
            let swapData = null
            if (type === 'up') {
                const data = disposeInfoTable().filter(i => i.order_num < infoTableSon?.order_num)
                swapData = data[data.length - 1]
            } else {
                swapData = disposeInfoTable().find(i => i.order_num > infoTableSon?.order_num)
            }
            const newUnits = units.map(i => {
                if (i.id === infoTableSon?.id) {
                    setInfoTableSon({ ...i, order_num: swapData.order_num })
                    return { ...i, order_num: swapData.order_num }
                }
                if (i.id === swapData.id) {
                    return { ...i, order_num: infoTableSon?.order_num }
                }
                return i
            })
            setFormInfo({ ...c_formInfo, units: newUnits })
        }
    }
    // 打开应用类新增编辑弹框
    const onApplyHandleModal = async (val) => {
        if (val === 'create') {
            setIsApplyHandleModal(true)
            return
        }
        if (selectedApply) {
            if (val === 'update') {
                setIsApplyHandleModal(true)
                applyForm.setFieldsValue(selectedApply)
            }
        }
        if (val === 'delete') {
            if (selectedApply.units?.length > 0) {
                message.error(t('不能删除已经使用的应用类'))
            } else {
                confirm({
                    title: `${t('确定删除这条数据吗')}?`,
                    icon: <ExclamationCircleFilled />,
                    onOk: async () => {
                        try {
                            if (selectedApply?.id) {
                                const res = await deleteApplyStandards({ id: selectedApply.id })
                                if (res) {
                                    messageApi.open({
                                        type: 'success',
                                        content: t('删除成功')
                                    })
                                    getApplys()
                                    setSelectedApply(null)
                                }
                            } else {
                                messageApi.open({
                                    type: 'warning',
                                    content: t('请选择应用类')
                                })
                            }
                        } catch (error) {
                            console.log(error)
                        }
                    }
                })
            }
        }
    }

    // 应用类新增编辑
    const onAllpyFinish = async () => {
        const data = await applyForm.validateFields()
        if (data) {
            let res
            if (data.id) {
                res = await putApplyStandards(data)
            } else {
                res = await postApplyStandards({ name: data.name })
            }
            if (res) {
                getApplys()
                setSelectedApply(null)
                onAllpyCancel()
            }
        }
    }

    const selectItemHandle = async (item) => {
        let data = null
        if (paramDisabled) {
            setInfoTableSon(item)
            setParamDisabled(false)
            return
        }

        try {
            data = await sonForm.validateFields()
        } catch (err) {
            data = null
        }
        if (data) {
            onSonFinish(data)
            setIsAdd(item.new)
            if (
                item.id === data.id
            ) {
                setInfoTableSon({
                    ...item,
                    ...data
                })
            } else {
                setInfoTableSon(item)
            }
            setParamDisabled(false)
        }
    }

    // 新建纲类单位取消
    const onAllpyCancel = () => {
        setIsApplyHandleModal(false)
        applyForm.resetFields()
    }

    // 判断量纲是否是编辑
    const isEdit = () => {
        return isCreate !== 'create'
    }
    const getSetSystemData = (data) => {
        const obj = {}
        return data.reduce((prev, item) => {
            if (!obj[item.id]) {
                prev.push(item)
                obj[item.id] = true
            }
            return prev
        }, [])
    }

    // 打开选择单位量纲类弹窗
    const openSystemBase = async () => {
        setSystemBaseOpen(true)
        const res = await getUnitsSystemList()
        const data = await getUnitsList()
        if (res) {
            setSystemData(getSetSystemData([...res, ...data]))
            setDataResource(data)
            setTargetKeys(data.map(i => i.id))
        }
    }

    /**
     * 单位接口都是前段来判断是否删除标志的
     * 1. 找到删除的量纲delete_flag设置1
     * 2. 找到删除的单位delete_flag设置1
     */
    const systemBaseEnter = async () => {
        // 找到删除的量纲
        const delDimensions = unitList
            .filter(dimension => !dataResource.map(m => m.id).includes(dimension.id))
            .map(m => ({
                ...m, new: false, delete_flag: DELETEFLAG.删除, user_id: getUserInfo()?.id, units: m.units.map(unit => ({ ...unit, delete_flag: DELETEFLAG.删除 }))
            }))

        let batch = dataResource.map(data => {
            const dimension = unitList.find(f => f.id === data.id)
            let units = data.units.map(unit => ({ ...unit, new: !dimension?.units?.some(s => s?.id === unit?.id) ?? true }))
            // 找到删除的单位
            const delUnits = dimension?.units
                .filter(f => !data.units.map(m => m.id).includes(f.id))
                .map(f => ({ ...f, delete_flag: DELETEFLAG.删除, new: false }))
            if (delUnits && delUnits.length > 0) {
                units = [...units, ...delUnits]
            }
            return {
                ...data,
                new: !dimension,
                user_id: getUserInfo()?.id,
                units
            }
        })
        if (delDimensions && delDimensions.length > 0) {
            batch = [...batch, ...delDimensions]
        }
        await postBatchUnitsSave({
            batch
        })
        messageApi.open({
            type: 'success',
            content: t('操作成功')
        })
        getUnits()
        setDataResource([])
        setSelectedRowKeys([])
        setWayData({})
        setSystemBaseOpen(false)
    }

    const syncUnit = async () => {
        const data = await updateTemplateDimensionUnits()

        if (data === null) {
            message.success(t('更新成功'))
            getUnits()
        }
    }

    const systemBaseCancel = () => {
        setSystemBaseOpen(false)
        setSelectedRowKeys([])
        setWayData({})
    }

    // 从系统库中选择change事件
    const onChange = (keys, _, moveKeys) => {
        setDataResource(state => [...state, ...systemData.filter(f => moveKeys.includes(f.id))])

        setTargetKeys(keys)
    }

    const onChangeDelWay = (data) => {
        setDataResource(state => state.filter(f => f.id !== data.id))
    }
    const onChangeWay = (data) => {
        if (data) {
            setWayData(data)
            setSelectedRowKeys(dataResource.find(f => f.id === data.id).units.map(i => i.id))
        } else {
            setWayData({})
            setSelectedRowKeys([])
        }
    }
    const onSelectChange = (newSelectedRowKeys) => {
        setSelectedRowKeys(newSelectedRowKeys)
        setDataResource(dimensions => dimensions.map(dimension => {
            if (wayData.id === dimension.id) {
                return { ...dimension, units: wayData.units.filter(f => newSelectedRowKeys.includes(f.id)) }
            }
            return dimension
        }))
    }

    const onUploadModalCancel = () => {
        setUploadModalOpen(false)
    }

    const onUploadModalOk = async ({
        path,
        targetKeys: keys,
        all,
        files
    }) => {
        if (uploadModalType === UPLOAD_MODAL_TYPE.导入) {
            await importSystemUnit({ file_path: files[0].path })
        } else {
            await exportSystemUnit({
                file_path: path,
                ids: all ? null : keys
            })
        }
        getUnits()
        onUploadModalCancel()
        getApplys()
        message.success(t('操作成功'))
    }
    const handleExport = () => {
        setUploadModalType(UPLOAD_MODAL_TYPE.导出)
        setUploadModalOpen(true)
    }

    const handleImport = () => {
        setUploadModalType(UPLOAD_MODAL_TYPE.导入)
        setUploadModalOpen(true)
    }

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        selections: applyList.map((i) => {
            return {
                key: i.id,
                text: t(i.name),
                onSelect: () => {
                    const list = wayData?.units?.filter(f => f.standard_id === i.id) || []
                    setSelectedRowKeys(list.map(l => l.id))
                }
            }
        })
    }

    return (
        <VModal
            open={open}
            width="70vw"
            onCancel={() => setOpen(false)}
            title={t('单位量纲类')}
            footer={null}
        >
            <UnitManageContainer>
                {contextHolder}
                <div className="unit-container">
                    <VPage title={t('单位量纲类')}>
                        <div className="unit-list">
                            <div className="unit-list-layout">
                                {unitList?.map(item => (
                                    <div
                                        className={item.id === selectedInfo?.id ? 'selected name-layout' : 'name-layout'}
                                        key={item.id}
                                        onClick={() => setSelectedInfo(item)}
                                    >
                                        {t(item.name)}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </VPage>
                    <VPage title={t('表属性')}>
                        <div className="unit-param">
                            <Descriptions column={4} labelStyle={LABEL_STYLE} size="small">
                                <Descriptions.Item span={1} label={t('名称')} contentStyle={CONTENT_STYLE_ITEM}>{t(selectedInfo?.name)}</Descriptions.Item>
                                <Descriptions.Item span={2} label={t('内部名称')} contentStyle={CONTENT_STYLE_ITEM}>{selectedInfo?.code}</Descriptions.Item>
                                <Descriptions.Item span={1} label={t('缺省单位')} contentStyle={CONTENT_STYLE_ITEM}>{selectedInfo?.units?.find(i => i.id === selectedInfo?.default_unit_id)?.name}</Descriptions.Item>
                            </Descriptions>
                            <Divider />
                            <div className="unit-param-select">
                                <Select
                                    showSearch
                                    optionFilterProp="label"
                                    style={{ width: 120 }}
                                    size="small"
                                    defaultValue={selectStandard}
                                    onChange={val => setSelectStandard(val)}
                                    options={[
                                        { value: 'ALL', label: t('全部') },
                                        ...applyList.map(i => ({ value: i.id, label: t(i.name) }))
                                    ]}
                                />
                            </div>
                            <VTable
                                rowKey="id"
                                columns={tableColumns({
                                    t, copy
                                })}
                                size="small"
                                dataSource={currentInfoTable()}
                                scroll={{ x: '20vw', y: '30vh' }}
                                bordered
                                pagination={false}
                            />
                        </div>
                    </VPage>
                    <div className="unit-operate">
                        <Space direction="vertical">
                            {((getTemplateId() || getProjectId())) ? (
                                <>
                                    <VButton block onClick={openSystemBase}>{t('从系统库选择')}</VButton>
                                    <VButton block onClick={syncUnit}>{t('更新')}</VButton>
                                </>
                            ) : (
                                <>
                                    <VButton block onClick={() => setModalOpen('create')}>{t('新建')}</VButton>
                                    <VButton block onClick={() => setModalOpen('update')}>{t('编辑')}</VButton>
                                    <VButton block onClick={deleteUnitInfo}>{t('删除')}</VButton>
                                    <VButton block onClick={handleImport}>{t('导入')}</VButton>
                                    <VButton block onClick={handleExport}>{t('导出')}</VButton>
                                </>
                            )}
                        </Space>
                        <Space direction="vertical">
                            {!(getTemplateId() || getProjectId()) && <VButton type="else" block onClick={setApplyModalOpen}>{t('应用类')}</VButton> }
                            <VButton type="else" block>{t('帮助')}</VButton>
                        </Space>
                        {systemBaseOpen
                            && (
                                <VModal
                                    title={t('从系统库中选择')}
                                    open={systemBaseOpen}
                                    onOk={systemBaseEnter}
                                    width="70vw"
                                    onCancel={systemBaseCancel}
                                    footer={null}
                                >
                                    <SystemContainer>
                                        <VTransfer
                                            dataSource={systemData}
                                            targetKeys={targetKeys}
                                            listStyle={{
                                                width: '16vw',
                                                height: '40vh'
                                            }}
                                            onChange={onChange}
                                            onChangeWay={onChangeWay}
                                            onChangeDelWay={onChangeDelWay}
                                            oneWay
                                            oneWayLabel="name"
                                            render={(item) => item.name}
                                            rowKey="id"
                                        />
                                        <div className="table-content">
                                            <VTable
                                                dataSource={wayData?.units}
                                                columns={unitColumns({
                                                    t
                                                })}
                                                rowSelection={rowSelection}
                                                scroll={{ x: true, y: '30vh' }}
                                                pagination={false}
                                                rowKey="id"
                                            />

                                        </div>

                                        <div className="operate-btn">
                                            <Space direction="vertical">
                                                <VButton block onClick={systemBaseEnter}>{t('确定')}</VButton>
                                                <VButton block onClick={systemBaseCancel}>{t('取消')}</VButton>
                                            </Space>
                                            <Space direction="vertical">
                                                <VButton block onClick={systemBaseCancel}>{t('帮助')}</VButton>
                                            </Space>
                                        </div>
                                    </SystemContainer>
                                </VModal>
                            )}

                    </div>
                </div>
                <VModal
                    title={isEdit() ? t('编辑量纲单位') : t('新建量纲单位')}
                    open={isUnitModal}
                    onOk={handleOk}
                    width="80vw"
                    onCancel={handleCancel}
                    footer={null}
                >
                    <UnitModelContainer>
                        <div className="unit-modal-left">
                            <Card>
                                <Form
                                    form={form}
                                    name="unit_rule"
                                    labelAlign="left"
                                    {...TOP_FORM_COLS}
                                >
                                    <Form.Item hidden label="ID" name="id">
                                        <Input placeholder={t('请输入')} />
                                    </Form.Item>
                                    <Row>
                                        <Col span={8}>
                                            <Form.Item label={t('名称')} name="name" rules={[{ required: true, message: t('请填写名称') }]}>
                                                <Input placeholder={t('请输入')} style={inputStyle} />
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item label={t('内部名称')} name="code" rules={[{ required: true, message: t('请填写内部名称') }]}>
                                                <Input disabled={isEdit()} prefix={codePrefix.DIMENSION} placeholder={t('请输入')} style={inputStyle} />
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            <Form.Item label={t('缺省单位')} name="default_unit_id" rules={[{ required: true, message: t('请选择缺省单位') }]}>
                                                <Select
                                                    style={inputStyle}
                                                    showSearch
                                                    optionFilterProp="label"
                                                    options={disposeInfoTable()
                                                        .map(i => (
                                                            { value: i.id, label: i.name, disabled: !i.visible }
                                                        ))}
                                                />
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                </Form>
                            </Card>
                            <div className="model-unit-list">
                                <div className="unit-list">
                                    <VPage title={t('单位列表')}>
                                        <div className="unit-list-layout">
                                            <div className="list-info">
                                                <div className="unit-info-layout">
                                                    {disposeInfoTable()?.map(item => (
                                                        <div
                                                            className={item.id === infoTableSon?.id ? 'selected name-layout' : 'name-layout'}
                                                            key={item.id}
                                                            onClick={() => {
                                                                selectItemHandle(item)
                                                                // setIsAdd(item.new)
                                                                // setInfoTableSon(item)
                                                                // setParamDisabled(false)
                                                            }}
                                                        >
                                                            {t(item.name)}
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>

                                        </div>
                                    </VPage>
                                </div>
                                <div className="list-button">
                                    <div onClick={addUnitSon} className="button-layout">
                                        <img src={unitAdd} alt="" />
                                        <div>{t('新建')}</div>
                                    </div>
                                    <div onClick={deleteUnitSon} className="button-layout">
                                        <img src={unitDel} alt="" />
                                        <div>{t('删除')}</div>
                                    </div>
                                    <div onClick={() => moveUnitSon('up')} className="button-layout">
                                        <img src={unitUp} alt="" />
                                        <div>{t('上移')}</div>
                                    </div>
                                    <div onClick={() => moveUnitSon('down')} className="button-layout">
                                        <img src={unitDown} alt="" />
                                        <div>{t('下移')}</div>
                                    </div>
                                </div>
                                <div className="unit-property">
                                    <VPage title={t('单位属性')}>
                                        <div className="property-layout">
                                            <div className="property-info">
                                                <Form
                                                    form={sonForm}
                                                    name="son_rule"
                                                    layout="horizontal"
                                                    labelCol={{ span: 9 }}
                                                    wrapperCol={{ span: 14 }}
                                                >
                                                    <Form.Item hidden label="ID" name="id">
                                                        <Input placeholder={t('请输入')} />
                                                    </Form.Item>
                                                    <Row>
                                                        <Col {...layoutColProps}>
                                                            <Form.Item label={t('单位名称')} name="name" rules={[{ required: true, message: t('请填写名称') }]}>
                                                                <Input disabled={paramDisabled} placeholder={t('请输入')} style={inputStyle} />
                                                            </Form.Item>
                                                        </Col>
                                                        <Col {...layoutColProps}>
                                                            <Form.Item label={t('内部名称')} name="code" rules={[{ required: true, message: t('请填写内部名称') }]}>
                                                                <Input disabled={paramDisabled || !isAdd} prefix={codePrefix.UNIT} placeholder={t('请输入')} style={inputStyle} />
                                                            </Form.Item>
                                                        </Col>
                                                        <Col {...layoutColProps}>
                                                            <Form.Item label={t('是否可见')} name="visible" valuePropName="checked" rules={[{ required: true, message: t('请选择是否可见') }]}>
                                                                <Switch disabled={paramDisabled} />
                                                            </Form.Item>
                                                        </Col>
                                                        <Col {...layoutColProps}>
                                                            <Form.Item label={t('基本数值X')} name="proportion" rules={[{ required: true, message: t('请填写基本数值X') }]}>
                                                                <Input disabled={paramDisabled} placeholder={t('请输入')} style={inputStyle} />
                                                            </Form.Item>
                                                        </Col>
                                                        <Col {...layoutColProps}>
                                                            <Form.Item label={t('所属应用类')} name="standard_id" rules={[{ required: true, message: t('请选择所属应用类') }]}>
                                                                <Select
                                                                    showSearch
                                                                    optionFilterProp="label"
                                                                    disabled={paramDisabled}
                                                                    style={inputStyle}
                                                                    options={applyList.map(i => ({ value: i.id, label: t(i.name) }))}
                                                                />
                                                            </Form.Item>
                                                        </Col>
                                                    </Row>
                                                </Form>
                                            </div>
                                        </div>
                                    </VPage>
                                </div>
                            </div>
                        </div>
                        <div className="unit-modal-right">
                            <Space direction="vertical">
                                <VButton block onClick={() => handleOk()}>{t('保存')}</VButton>
                                <VButton block onClick={() => handleCancel()}>{t('取消')}</VButton>
                            </Space>
                            <Space direction="vertical">
                                <VButton block type="else">{t('帮助')}</VButton>
                            </Space>
                        </div>
                    </UnitModelContainer>
                </VModal>
                <VModal
                    title={t('应用类')}
                    width="70vw"
                    open={isApplyModal}
                    footer={false}
                    onCancel={() => setIsApplyModal(false)}
                >
                    <ApplyModelContainer>
                        <VPage title={t('应用类列表')}>
                            <div className="apply-list">
                                <div className="apply-list-layout">
                                    {applyList?.map(item => (
                                        <div
                                            className={item.id === selectedApply?.id ? 'selected name-layout' : 'name-layout'}
                                            key={item.id}
                                            onClick={() => setSelectedApply(item)}
                                        >
                                            {t(item.name)}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </VPage>
                        <VPage title={t('应用类明细')}>
                            <div className="apply-info">
                                <VTable
                                    rowKey="id"
                                    columns={APPLYTABLECOLUMNS({ t })}
                                    size="small"
                                    defaultExpandAllRows
                                    dataSource={handleApplyList()}
                                    scroll={{ x: '20vw', y: '47vh' }}
                                    pagination={false}
                                />
                            </div>
                        </VPage>
                        <div className="apply-button">
                            <Space direction="vertical">
                                <VButton block onClick={() => onApplyHandleModal('create')}>{t('新建')}</VButton>
                                <VButton block onClick={() => onApplyHandleModal('update')}>{t('编辑')}</VButton>
                                <VButton block onClick={() => onApplyHandleModal('delete')}>{t('删除')}</VButton>
                            </Space>
                            <Space direction="vertical">
                                <VButton block type="else">{t('帮助')}</VButton>
                            </Space>
                        </div>
                    </ApplyModelContainer>
                </VModal>
                <VModal
                    title={t('应用类')}
                    width="40vw"
                    open={isApplyHandleModal}
                    onCancel={onAllpyCancel}
                    footer={null}
                >
                    <ApplicationModelContainer>
                        <div className="layout-left">
                            <Form
                                form={applyForm}
                                name="apply_rule"
                                labelAlign="right"
                                {...FORM_ITEM_MODAL_PROPS}
                            >
                                <Form.Item hidden label="ID" name="id">
                                    <Input placeholder={t('请输入')} />
                                </Form.Item>

                                <Form.Item
                                    label={t('应用类名称')}
                                    name="name"
                                    rules={[{ required: true, message: t('请填写名称') }]}
                                >
                                    <Input placeholder={t('请输入应用类名称')} />
                                </Form.Item>
                            </Form>
                        </div>
                        <div className="layout-right">
                            <Space>
                                <Button block type="primary" onClick={onAllpyFinish}>{t('确认')}</Button>
                                <Button block onClick={onAllpyCancel}>{t('取消')}</Button>
                            </Space>

                        </div>
                    </ApplicationModelContainer>
                </VModal>
            </UnitManageContainer>

            {
                uploadModalOpen && (
                    <UploadModal
                        dataSource={unitList}
                        type={uploadModalType}
                        open={uploadModalOpen}
                        onCancel={onUploadModalCancel}
                        onOk={onUploadModalOk}
                        suffixArray={unitSuffixArray}
                        transferConfig={{
                            rowKey: 'id',
                            render: (item) => t(item.name)
                        }}
                    />
                )
            }
        </VModal>
    )
}

export default UnitManage
